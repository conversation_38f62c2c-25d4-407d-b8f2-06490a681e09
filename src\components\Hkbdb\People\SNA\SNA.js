import React, { useContext, useState, useEffect, createRef } from "react";
import { FormattedMessage, injectIntl } from "react-intl";
import { intlMsgs } from "../../EntityComponent/entityIntlMsgs";
import { Graph } from "react-d3-graph";
import {
    Form,
    Grid,
    Button,
    Checkbox,
    Dimmer,
    Loader,
    Input,
    Label
} from "semantic-ui-react";
import { peopleUrl, orgUrl } from "../../../../services/common";
import PropTypes from "prop-types";
import targetCircle from "./svg/targetCircle.svg";
import circle from "./svg/circle.svg";
import square from "./svg/square.svg";
import { queryDepthSNARelation } from "../action";
import Act from "../../../../store/actions";
import { isEmpty } from "../../../../common/codes";
import { cvtSNAdataByLocale } from "./snaHelper";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import { removeStartPrefix } from "../../EntityComponent/helper";
import { idToUriEncId } from "../../../../common/codes/jenaHelper";

/**
 * people and organization page 共用 SNA component
 *
 * @param intl
 * @param name  => 當前 page 的 people id (已移除 prefix) / organization id (已移除 prefix)
 * @param className => 'Person'/ 'Organization'
 * @param ReducerContext
 * @returns {JSX.Element|null}
 * @constructor
 */
const SNA = ({ intl, name, className, ReducerContext }) => {
    const [state, dispatch] = useContext(ReducerContext);
    const {
        SNAData,
        depth,
        fetchSnaDataStatus,
        step1SnaData,
        step2SnaData
    } = state.personInformation;
    const { user, property } = state;
    const { locale } = user;
    const { propertyObj } = property;
    const defaultSnaWidth = 1400;
    const defaultSnaHeight = 800;
    const [snaWidth, setSnaWidth] = useState(window.innerWidth);
    const [snaHeight, setSnaHeight] = useState(window.innerHeight);
    const [snaConfig, setSnaConfig] = useState({});
    const [renderLabel, setRenderLabel] = useState(false);
    const [isFullScreen, setIsFullScreen] = useState(false);
    const { formatMessage } = intl;
    const [nodeFontSize, setnNodeFontSize] = useState(12);
    const [linkFontSize, setnLinkFontSize] = useState(12);
    const [loading, setLoading] = useState(false);
    const [reFetchSignal, setReFetchSignal] = useState(null); // locale 變換時, 重新 fetch API 的 signal

    const snaBoxRef = createRef();

    const defaultSnaConfig = {
        nodeHighlightBehavior: true,
        directed: true,
        maxZoom: 8,
        panAndZoom: true,
        width: snaWidth || defaultSnaWidth,
        height: snaHeight,
        node: {
            color: "lightgreen",
            size: 280,
            highlightStrokeColor: "blue",
            fontSize: nodeFontSize,
            labelProperty: "label"
        },
        link: {
            highlightColor: "blue",
            renderLabel: renderLabel,
            labelProperty: "label",
            fontSize: linkFontSize,
            type: "CURVE_SMOOTH"
        },
        d3: {
            alphaTarget: 0.05,
            gravity: -700,
            linkLength: 100,
            linkStrength: 1
        }
    };

    const updateWidthAndHeight = () => {
        // console.log('window.innerWidth', window.innerWidth)
        // 變更 sna svg 的長寬
        setSnaWidth(window.innerWidth);
        setSnaHeight(window.innerHeight);

        // 變更 sna config 設定
        const snaConfigCh = Object.assign({}, defaultSnaConfig);
        snaConfigCh.width = window.innerWidth;
        snaConfigCh.height = window.innerHeight;
        setSnaConfig(snaConfigCh);
    };

    useEffect(() => {
        // 人名(name)變更時, 需重新 fetch API
        // 語系變換時, 需要重新 fetch API, 依照語系取得節點的名稱
        dispatch({
            type: Act.FETCH_SNA_DATA_STATUS,
            payload: {
                step1: false,
                step2: false
            }
        });
        dispatch({
            type: Act.SET_SNA_DEPTH,
            payload: 1
        });
        setTimeout(() => {
            setReFetchSignal({
                signalTime: Date.now()
            });
        }, 200);

        // 離開此頁面時, reset SNA_DATA_STATUS 及 reset SNA_DEPTH to 1
        return () => {
            dispatch({
                type: Act.SET_SNA_DEPTH,
                payload: 1
            });
            dispatch({
                type: Act.FETCH_SNA_DATA_STATUS,
                payload: {
                    step1: false,
                    step2: false
                }
            });
        };
    }, [locale, name]);

    // update sna div width and height when window resize
    useEffect(() => {
        window.addEventListener("resize", updateWidthAndHeight);
        return () => window.removeEventListener("resize", updateWidthAndHeight);
    });

    // sna deptch radio change
    const handleChange = (e, { value }) => {
        dispatch({
            type: Act.SET_SNA_DEPTH,
            payload: value
        });
    };

    // click sna sna depth label
    const onStepLabelClick = val => {
        dispatch({
            type: Act.SET_SNA_DEPTH,
            payload: val
        });
    };

    // 是否顯示關係 (link label)
    const handlesetRenderLabelChange = () => {
        setRenderLabel(!renderLabel);
    };

    // 顯示關係 (link label) 狀態變更後, 變更snaConfig
    useEffect(() => {
        // 變更 sna config 設定
        const snaConfigCh = Object.assign({}, defaultSnaConfig);
        snaConfigCh.link.renderLabel = renderLabel;
        setSnaConfig(snaConfigCh);
    }, [renderLabel]);

    const onClickNode = (nodeId, node) => {
        if ((node.type || "").toLowerCase() === "center") return;
        const uriEncIdNoPrefix =
            (node.type || "").toLowerCase() ===
            CLASS_PREFIX.Person.toLowerCase()
                ? removeStartPrefix(idToUriEncId(nodeId), CLASS_PREFIX.Person)
                : removeStartPrefix(
                      idToUriEncId(nodeId),
                      CLASS_PREFIX.Organization
                  );
        const nodeFind = SNAData.nodes.find(node => node.id === nodeId);
        if (nodeFind) {
            // 依據人物或組織連結至該頁面
            document.location.href =
                (node.type || "").toLowerCase() ===
                CLASS_PREFIX.Person.toLowerCase()
                    ? peopleUrl(uriEncIdNoPrefix)
                    : orgUrl(uriEncIdNoPrefix);
        }
    };

    const fullScreen = () => {
        if (!isFullScreen) {
            setIsFullScreen(true);
            requestFullScreen();
        } else {
            setIsFullScreen(false);
            exitFullscreen();
        }
    };

    const requestFullScreen = () => {
        const de = document.getElementById("sna-graph");
        if (de.requestFullscreen) {
            de.requestFullscreen();
        } else if (de.mozRequestFullScreen) {
            de.mozRequestFullScreen();
        } else if (de.webkitRequestFullScreen) {
            de.webkitRequestFullScreen();
        }
        setSnaWidth(de.clientWidth);
    };

    const exitFullscreen = () => {
        const de = document;
        if (de.exitFullscreen) {
            de.exitFullscreen();
        } else if (de.mozCancelFullScreen) {
            de.mozCancelFullScreen();
        } else if (de.webkitCancelFullScreen) {
            de.webkitCancelFullScreen();
        }
    };

    const watchFullScreen = () => {
        document.addEventListener(
            "fullscreenchange",
            function() {
                setIsFullScreen(document.fullscreen);
            },
            false
        );

        document.addEventListener(
            "mozfullscreenchange",
            function() {
                setIsFullScreen(document.mozFullScreen);
            },
            false
        );

        document.addEventListener(
            "webkitfullscreenchange",
            function() {
                setIsFullScreen(document.webkitIsFullScreen);
            },
            false
        );
    };

    const onMouseOverNode = function(nodeId, node) {
        // setnNodeFontSize(14);
    };

    const onMouseOutNode = function(nodeId, node) {
        // setnNodeFontSize(12);
    };

    const customSnaDataDispatch = (
        ActType,
        snaData,
        convertOnLocale = false
    ) => {
        if (!ActType) return;
        if (isEmpty(snaData)) return;
        if (convertOnLocale) {
            dispatch({
                type: ActType,
                payload: Object.assign(
                    {},
                    {
                        nodes: snaData.nodes,
                        links: cvtSNAdataByLocale(
                            snaData.links,
                            propertyObj,
                            locale
                        ),
                        orgList: snaData.orgList
                    }
                )
            });
        } else {
            dispatch({
                type: ActType,
                payload: Object.assign({}, snaData)
            });
        }
    };

    useEffect(() => {
        //
        const snaDiv = document.getElementById("sna-graph");
        //
        if (snaDiv) {
            setSnaWidth(snaDiv.clientWidth);
        }
        //
        if (name === "" && isEmpty(propertyObj)) {
            return;
        }
        /**
         * global state:
         * SNAData => 存放目前在畫面中的 sna data  !! 要轉換語系
         * step1SnaData => 存放 fetch step1 的 sna data  !! 不用轉換語系
         * step2SnaData => 存放 fetch step2 的 sna data  !! 不用轉換語系
         * fetchSnaDataStatus => 代表 step1 及 step2 是否已向 API fetch 的狀態
         * */
        // 若 step1 及 stpe2 皆已 fetch, 則不用再 fetch
        if (fetchSnaDataStatus.step1 && fetchSnaDataStatus.step2) {
            // console.log("Fetch Status: step1 V + step2 V");
            if (depth === 1 || depth === "1") {
                customSnaDataDispatch(
                    Act.USER_FETCH_SNA_DATA,
                    step1SnaData,
                    true
                );
            } else if (depth === 2 || depth === "2") {
                // step2SnaData 包含 step1 所有的 nodes 及 links
                customSnaDataDispatch(
                    Act.USER_FETCH_SNA_DATA,
                    step2SnaData,
                    true
                );
            }
        } else if (fetchSnaDataStatus.step1 && !fetchSnaDataStatus.step2) {
            // console.log("Fetch Status: step1 V + step2 X");
            if (depth === 1 || depth === "1") {
                customSnaDataDispatch(
                    Act.USER_FETCH_SNA_DATA,
                    step1SnaData,
                    true
                );
            } else if (depth === 2 || depth === "2") {
                // fetch API 時才要 setLoading(true)
                setLoading(true);

                // fetch step 2 sna data
                // combine names
                // nodes may like:
                // {color:"black", symbolType: "square", type: "ORG", id: "中山大學"}
                // {color:"green", symbolType: "circle", type: "PER", id: "古蒼梧"}
                const safeNodes =
                    step1SnaData &&
                    step1SnaData.nodes &&
                    step1SnaData.nodes.length === 0
                        ? []
                        : step1SnaData.nodes.filter(nd => nd.id !== name);

                queryDepthSNARelation({
                    depth, // depth = 2
                    className, // Person/ Organization
                    center: name, // Person id(已移除prefix) / Organization id(已移除prefix)
                    nodes: safeNodes // fetch 2step 時, nodes 可能有資料也可能為 []
                })
                    .then(res => {
                        const { snaData } = res;
                        customSnaDataDispatch(
                            Act.SET_SETP2_SNA_DATA,
                            snaData,
                            false
                        );

                        // snaData(step2SnaData) 包含 step1 所有的 nodes 及 links
                        customSnaDataDispatch(
                            Act.USER_FETCH_SNA_DATA,
                            snaData,
                            true
                        );
                        dispatch({
                            type: Act.FETCH_SNA_DATA_STATUS,
                            payload: {
                                step1: true,
                                step2: true
                            }
                        });
                    })
                    .catch(err => {
                        console.log(err);
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        } else if (!fetchSnaDataStatus.step1 && !fetchSnaDataStatus.step2) {
            setLoading(true);
            queryDepthSNARelation({
                depth: 1,
                className, // Person/ Organization
                center: name, // Person id(已移除prefix) / Organization id(已移除prefix)
                nodes: [] // 第一次 fetch 時, nodes = []
            })
                .then(res => {
                    setLoading(false);
                    const { snaData } = res;
                    customSnaDataDispatch(
                        Act.USER_FETCH_SNA_DATA,
                        snaData,
                        true
                    );
                    customSnaDataDispatch(
                        Act.SET_SETP1_SNA_DATA,
                        snaData,
                        false
                    );
                    dispatch({
                        type: Act.FETCH_SNA_DATA_STATUS,
                        payload: {
                            step1: true,
                            step2: false
                        }
                    });
                })
                .catch(err => {
                    console.log(err);
                });
        }
    }, [name, depth, locale, propertyObj.en, propertyObj.zh, reFetchSignal]);

    useEffect(() => {
        if (isFullScreen) {
            watchFullScreen();
        }
    }, [isFullScreen]);

    if (name === "") {
        return null;
    }

    return (
        <div id="sna-graph" ref={snaBoxRef} style={{ width: "100%" }}>
            {loading && (
                <Dimmer active inverted>
                    <Loader inverted content="Loading" />
                </Dimmer>
            )}
            <Grid columns="equal">
                <Grid.Row>
                    <Grid.Column width={4}>
                        <Form>
                            <Grid columns="equal">
                                <Grid.Row>
                                    <Grid.Column>
                                        <Form.Field inline>
                                            <Input
                                                type={"radio"}
                                                value={1}
                                                name={"radio-step"}
                                                checked={
                                                    depth === 1 || depth === "1"
                                                }
                                                onChange={handleChange}
                                            />
                                            <Label
                                                as={"a"}
                                                color={"blue"}
                                                onClick={() =>
                                                    onStepLabelClick(1)
                                                }
                                            >
                                                <FormattedMessage
                                                    id="sna.1step"
                                                    defaultMessage="1 step"
                                                />
                                            </Label>
                                        </Form.Field>
                                    </Grid.Column>
                                    <Grid.Column>
                                        <Form.Field inline>
                                            <Input
                                                type={"radio"}
                                                value={2}
                                                name={"radio-step"}
                                                checked={
                                                    depth === 2 || depth === "2"
                                                }
                                                onChange={handleChange}
                                            />
                                            <Label
                                                as={"a"}
                                                color={"blue"}
                                                onClick={() =>
                                                    onStepLabelClick(2)
                                                }
                                            >
                                                <FormattedMessage
                                                    id="sna.2step"
                                                    defaultMessage="2 step"
                                                />
                                            </Label>
                                        </Form.Field>
                                    </Grid.Column>
                                </Grid.Row>
                            </Grid>
                        </Form>
                    </Grid.Column>
                    <Grid.Column width={3}>
                        {formatMessage(intlMsgs["snaTarget"])}
                        <img
                            src={targetCircle}
                            alt="Target"
                            style={{
                                width: "25px",
                                verticalAlign: "middle",
                                marginLeft: "15px"
                            }}
                        />
                    </Grid.Column>
                    <Grid.Column width={3}>
                        {formatMessage(intlMsgs["snaPeople"])}
                        <img
                            src={circle}
                            alt="Family"
                            style={{
                                width: "25px",
                                verticalAlign: "middle",
                                marginLeft: "15px"
                            }}
                        />
                    </Grid.Column>
                    <Grid.Column width={3}>
                        {formatMessage(intlMsgs["snaOrganization"])}
                        <img
                            src={square}
                            alt="organization"
                            style={{
                                width: "23px",
                                verticalAlign: "middle",
                                marginLeft: "15px"
                            }}
                        />
                    </Grid.Column>
                    <Grid.Column width={2}>
                        <Button
                            content={
                                isFullScreen ? (
                                    <FormattedMessage
                                        id="sna.exitFullScreen"
                                        defaultMessage="Exit Fullscreen"
                                    />
                                ) : (
                                    <FormattedMessage
                                        id="sna.fullscreen"
                                        defaultMessage="Fullscreen"
                                    />
                                )
                            }
                            basic
                            onClick={fullScreen}
                        />
                    </Grid.Column>
                    <Grid.Column width={5}>
                        <div
                            style={{ paddingTop: "10px", paddingLeft: "20px" }}
                        >
                            {formatMessage(intlMsgs["snaLinkLabel"])}
                            <Checkbox
                                toggle
                                onChange={handlesetRenderLabelChange}
                                style={{
                                    width: "30px",
                                    verticalAlign: "middle",
                                    marginLeft: "15px"
                                }}
                            />
                        </div>
                    </Grid.Column>
                </Grid.Row>
            </Grid>
            {SNAData && SNAData.nodes.length > 0 ? (
                <Graph
                    id="sna-graph-inner"
                    data={SNAData}
                    config={snaConfig}
                    onClickNode={onClickNode}
                    onMouseOverNode={onMouseOverNode}
                    onMouseOutNode={onMouseOutNode}
                />
            ) : null}
        </div>
    );
};

SNA.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    type: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    ReducerContext: PropTypes.objectOf(PropTypes.any).isRequired
};
export default injectIntl(SNA);
