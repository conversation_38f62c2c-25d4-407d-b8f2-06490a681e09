import React, { useContext, useState, useEffect } from "react";

// ui
import { Menu, Popup, Button, Icon } from "semantic-ui-react";

// custom
import CustomDeleteMenu from "./CustomDeleteModalFlex";
import CustomEditMenu from "./CustomEditModalFlex";
import SugFlexDeleteModal from "./SugComponents/SugFlexDeleteModal";

import { FormattedMessage } from "react-intl";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// auth
import authority from "../../../../App-authority";

// common
import { isNotEmpty, isTrue, safeGet } from "../../../../common/codes";
import { bs64EncodeId } from "../../../../common/codes/jenaHelper";
import { displayMemberName, SPECIAL_HEADER } from "../../Organization/action";
import allRoles from "../../../../App-role";

const CustomContextMenu = ({
    rowData,
    ontologyDomain,
    ontologyType,
    headers
}) => {
    //
    const [popupOpen, setPopupOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    //
    const [state] = useContext(StoreContext);
    const { personId } = state.information;
    const { uid, role } = state.user;
    const { fieldSetting } = state.setting;
    const { ontologyDefined } = state.property;
    const { memberInvert } = state.personInformation;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;

    const memberNameGroup = memberInvert ? displayMemberName(memberInvert) : [];
    //
    const ProcessData = () => {
        // get firebase prop setting
        const fieldAttrRecords = safeGet(
            fieldAttr,
            [ontologyDomain, ontologyType],
            []
        );
        // get firebase prop sorted records
        const fieldPropSortedRecords = safeGet(
            fieldProp?.sortedRecords,
            [ontologyDomain, ontologyType],
            []
        );
        //
        const eventId = rowData.hasOwnProperty("_eventId")
            ? bs64EncodeId(rowData["_eventId"])
            : "";
        //
        const newRowData = Object.keys(rowData)
            // 如果為空值，不需設定
            // .filter(propertyName => data[propertyName].length > 0)
            // 重新整理一下格式方便顯示
            .map(propertyBindRangeStr => {
                //
                let roles = safeGet(
                    fieldAttrRecords,
                    [propertyBindRangeStr, "roles"],
                    []
                );
                // 組織成員表單特殊欄位處理 memberName__Person
                if (memberNameGroup.includes(propertyBindRangeStr)) {
                    roles = safeGet(
                        fieldAttrRecords,
                        [SPECIAL_HEADER, "roles"],
                        []
                    );
                }
                const isAllowProp = roles.includes(role);
                //
                const propArr = propertyBindRangeStr.split("__");
                const [propName, propRange] = propArr;
                //
                const value = rowData[propertyBindRangeStr];
                //
                let newValue;
                if (Array.isArray(value)) {
                    newValue = value.sort();
                } else {
                    newValue = [value].sort();
                }
                //
                return {
                    srcId: eventId || personId,
                    eventId,
                    personId,
                    isAllowProp,
                    propertyRange: propRange,
                    property: propName || "",
                    propertyBindRangeStr: propertyBindRangeStr || "",
                    values: isNotEmpty(newValue) ? newValue : [],
                    graph: rowData?.graph || ""
                };
            })
            // 在重組資料格式中塞入 graph 了，這邊可以不用再放入
            .filter(
                item =>
                    item.property !== "graph" &&
                    item.property !== "Source Dataset"
            )
            // 排序
            .slice(0)
            .sort((a, b) => {
                const order = fieldPropSortedRecords.map(item =>
                    item.toUpperCase()
                );
                const nameA = a.propertyBindRangeStr.toUpperCase();
                const nameB = b.propertyBindRangeStr.toUpperCase();
                if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                return 0;
            })
            // 加入 rowId 方便查找(mapping to createdId or updatedId)以及更新(CRUD)
            .map((item, itemIdx) => ({ ...item, rowId: itemIdx }));
        const selectedProps = newRowData
            .map(row => {
                const { propertyBindRangeStr } = row;
                return propertyBindRangeStr;
            })
            .filter(prop => isNotEmpty(prop) && prop !== "_eventId");
        // 從 ontology
        const ontology = ontologyDefined[ontologyType]
            .map(item => {
                //
                const { propertyBindRangeStr } = item;
                const roles = safeGet(
                    fieldAttrRecords,
                    [propertyBindRangeStr, "roles"],
                    []
                );
                const isAllowProp = roles.includes(role);
                //
                return {
                    ...item,
                    isAllowProp
                };
            })
            .filter(item => {
                const { propertyBindRangeStr, isAllowProp } = item;
                const isSelectedProp = selectedProps.includes(
                    propertyBindRangeStr
                );
                return isAllowProp && !isSelectedProp;
            });
        //
        return {
            // init and reformat data
            rowData: newRowData,
            //
            createdData: {},
            createdRowIds: [],
            isCreated: false,
            // record changed data
            changedData: {},
            updatedRowIds: [],
            isUpdated: false,
            // record deleted data
            deletedData: [],
            deletedRowIds: [],
            isDeleted: false,
            // edit menu
            ontology: ontology || [],
            selectedProperties: []
        };
    };
    //
    const [editData, setEditData] = useState(ProcessData);
    //
    useEffect(() => {
        setEditData(ProcessData(rowData));
    }, [rowData]);
    //
    const handleItemClick = (e, { name }) => {
        switch (name) {
            case "edit":
                // open editModal
                setEditModalOpen(true);
                break;
            case "delete":
                // open deleteModal
                setDeleteModalOpen(true);
                break;
            default:
                break;
        }
        // close popup
        setPopupOpen(false);
    };
    const handleClose = () => {
        setPopupOpen(false);
    };
    //
    const PopupButton = (
        <Button
            icon="ellipsis vertical"
            size="mini"
            // style={{ backgroundColor: "#f9fafb" }}
            onClick={() => setPopupOpen(true)}
        />
    );
    //
    if (
        isTrue(process.env.REACT_APP_CRUD_NODE) &&
        isNotEmpty(uid) &&
        authority.People_Information.includes(role)
    ) {
        return (
            <React.Fragment>
                <Popup
                    flowing
                    hoverable
                    hideOnScroll
                    trigger={PopupButton}
                    open={popupOpen}
                    style={{ zIndex: "1" }}
                    onClose={handleClose}
                >
                    <Menu size="mini" vertical>
                        {/* 不開放suggester使用 */}
                        {authority.EditButton.includes(role) && (
                            <Menu.Item
                                name="edit"
                                onClick={handleItemClick}
                                // disabled={isDisabled}
                            >
                                <Icon name="edit" />
                                <FormattedMessage
                                    id={"people.Information.menu.edit"}
                                    defaultMessage={"Edit"}
                                />
                            </Menu.Item>
                        )}
                        <Menu.Item
                            name="delete"
                            onClick={handleItemClick}
                            // disabled={isDisabled}
                        >
                            <Icon name="delete" />
                            <FormattedMessage
                                id={"people.Information.menu.delete"}
                                defaultMessage={"Delete"}
                            />
                        </Menu.Item>
                    </Menu>
                </Popup>

                {/* 不開放suggester使用 */}
                {authority.EditButton.includes(role) && (
                    <CustomEditMenu
                        open={editModalOpen}
                        setOpen={setEditModalOpen}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                )}

                {role !== allRoles.suggester && deleteModalOpen && (
                    <CustomDeleteMenu
                        open={deleteModalOpen}
                        setOpen={setDeleteModalOpen}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                )}

                {role === allRoles.suggester && deleteModalOpen && (
                    <SugFlexDeleteModal
                        open={deleteModalOpen}
                        setOpen={setDeleteModalOpen}
                        data={rowData}
                        ontologyType={ontologyType}
                        headers={headers}
                        type="flex"
                    />
                )}
            </React.Fragment>
        );
    } else {
        return null;
    }
};

export default CustomContextMenu;
