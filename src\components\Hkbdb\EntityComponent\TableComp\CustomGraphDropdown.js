import React, { useContext, useState } from "react";

// ui
import CreatableSelect from "react-select/creatable";
import { Input, Label } from "semantic-ui-react";

// custom
import MenuList from "./MenuList";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { cvtDatasetLocale, isEmpty } from "../../../../common/codes";

// lang
import { FormattedMessage } from "react-intl";

const CustomGraphDropdown = ({ setCreateData }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { dataset } = state.source;
    //
    const [createState, setCreateState] = useState(() => {
        const options = dataset.map(item => ({
            label: cvtDatasetLocale(item.dataset, dataset),
            value: item.dataset
        }));
        return {
            options: options,
            selected: options[0]
        };
    });
    //
    const handleChange = selectedItem => {
        // console.log("onChange:", selectedItem);
        setCreateData(prevData => ({
            ...prevData,
            willCreatedData: {
                ...prevData.willCreatedData,
                graph: selectedItem?.value || ""
            }
        }));
    };
    //
    const customStyles = {
        container: styles => ({
            ...styles,
            width: "100%"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            borderTopLeftRadius: "0",
            borderBottomLeftRadius: "0",
            borderLeftColor: "transparent",
            border: "1px solid rgba(34,36,38,.15)"
            // borderRadius: "unset"
        })
    };
    //
    return (
        <Input fluid labelPosition="left" type="text" onChange={handleChange}>
            <Label color="orange">
                <FormattedMessage
                    id={"people.Information.formTable.dataset"}
                    defaultMessage={"Dataset"}
                />
            </Label>
            <CreatableSelect
                isClearable
                placeholder={
                    <FormattedMessage
                        id={"people.Information.dropDown.placeholder"}
                        defaultMessage={"Select..."}
                    />
                }
                styles={customStyles}
                options={createState.options}
                onChange={handleChange}
                components={{ MenuList }}
            />
        </Input>
    );
};

export default CustomGraphDropdown;
