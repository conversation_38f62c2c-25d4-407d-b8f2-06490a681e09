import React from "react";

// ui
import { Menu, Tab } from "semantic-ui-react";

// custom
import UserTable from "./UserTable";
import UserNumLabel from "./UserNumLabel";
import ApplicantTable from "./ApplicantTable";
import ApplicantNumLabel from "./ApplicantNumLabel";

const panes = [
    {
        menuItem: (
            <Menu.Item key="userTable-messages">
                使用者
                <UserNumLabel />
            </Menu.Item>
        ),
        render: () => (
            <Tab.Pane attached={false}>
                <UserTable key="UserTable" />
            </Tab.Pane>
        )
    },
    {
        menuItem: (
            <Menu.Item key="applicantTable-messages">
                申請者
                <ApplicantNumLabel />
            </Menu.Item>
        ),
        render: () => (
            <Tab.Pane attached={false}>
                <ApplicantTable key="ApplicantTable" />
            </Tab.Pane>
        )
    }
];

const CustomTab = () => <Tab menu={{ pointing: true }} panes={panes} />;

export default CustomTab;
