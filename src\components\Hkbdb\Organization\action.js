import Act from "../../../store/actions";
import {
    Api,
    eventAPIs,
    eventidsAPIs,
    eventSearchIdsAPIs,
    getHkbdbAxios,
    readHkbdbData
} from "../../../api/hkbdb/Api";

import { gedcomize } from "../../../common/codes/genealogy";
import {
    bs64Encode,
    isEmpty,
    isNotEmpty,
    safeGet
} from "../../../common/codes";
import { processSNADataSlim } from "./SNA/snaHelper";
import base64url from "base64url";
import isString from "lodash/isString";
import isArray from "lodash/isArray";

// Query Organization Information
// const queryClass = [{ className: "organization" }];
const { organization: orgEventAPIs } = eventAPIs;
const { organization: orgEventIdsAPIs } = eventidsAPIs;
const { organization: orgEventSearchIdsAPIs } = eventSearchIdsAPIs;
export const SPECIAL_HEADER = "memberName__Person";

const queryClass = {
    organization: Api.getOrganizationBasicInfo(),
    namenode: Api.getOrganizationBasicInfo(),
    memberInvert: Api.getOrganizationMemberInvert()
};

export const displayMemberName = memberInvert =>
    [
        ...new Set(
            Object.keys(memberInvert).map(key => memberInvert[key].property)
        )
    ] || [];

export const queryInverseRelationInfo = (api, dispatch) => {
    //
    const timeout = 30 * 1000;
    //
    readHkbdbData(api, timeout)
        .then(response => {
            //
            // console.log("queryInverseRelationInfo data:", response);
            // 預處理
            dispatch({
                type: Act.INFORMATION_INVERSE_RELATION_DATA_SET,
                payload: response?.data || []
            });
        })
        .catch(() => {
            console.log("timeout", timeout);
        });
};

export const queryOntologyOneOfThemInfo = (api, dispatch) => {
    //
    const timeout = 30 * 1000;
    //
    readHkbdbData(api, timeout)
        .then(response => {
            // 預處理
            const formattedData = safeGet(response, ["data"], []).reduce(
                (prevObj, item) => {
                    const { class: className, property } = item;
                    const lowClassName = `${className}`.toLowerCase();
                    return {
                        ...prevObj,
                        [lowClassName]: [
                            ...safeGet(prevObj, [lowClassName], []),
                            property
                        ]
                    };
                },
                {}
            );
            //
            dispatch({
                type: Act.INFORMATION_ONTOLOGY_ONE_OF_THEM_DATA_SET,
                payload: formattedData
            });
        })
        .catch(() => {
            console.log("timeout", timeout);
        });
};

const transformers = (className, rawData) => {
    //
    if (!Array.isArray(rawData)) {
        console.error("transformers func: rawData is not an Array");
        return [];
    }

    const groupObj = rawData.reduce((prevObj, item) => {
        //
        let {
            graph,
            _personId,
            _eventId,
            property,
            range,
            value,
            // eslint-disable-next-line camelcase
            label_zh
            // label_en
        } = item;
        //
        if (isEmpty(value)) {
            return prevObj;
        }
        //
        let groupId = _eventId || _personId;
        const groupGraphId = `${groupId}-${graph}`;
        //
        let prevItem = safeGet(prevObj, [groupGraphId], {});
        let nextItem;
        //
        if (
            `${property}`.toLowerCase() === "label" ||
            `${property}`.toLowerCase() === "label_zh"
        ) {
            let oriClassName;
            if (className === "event") {
                oriClassName = className;
            } else {
                oriClassName = `${className}`
                    .toLowerCase()
                    .replace("event", "");
            }
            property = `${property}_${oriClassName}`;
            // range = config.DEF_ORG_RANGE;
        }
        //
        let propertyKey = `${property}__${range}`;
        //
        let contentArr = [
            ...safeGet(prevItem, [propertyKey], []),
            {
                label: label_zh,
                value: value
            }
        ];
        //
        nextItem = {
            graph,
            _eventId,
            // _personId,
            [propertyKey]: contentArr
        };
        //
        return {
            ...prevObj,
            [groupGraphId]: {
                ...prevItem,
                ...nextItem
            }
        };
    }, {});
    //
    return Object.keys(groupObj).map(groupId =>
        safeGet(groupObj, [groupId], {})
    );
};

export const queryOrgInfo = async (personName, dispatch) => {
    //
    if (!personName) return;
    //
    dispatch({
        type: Act.USER_FETCH_INFO_LOADING,
        payload: true
    });
    let fixedTable = false;
    //
    const classNames = Object.keys(queryClass);
    //
    const promises = classNames.map(className => {
        //
        let apiStr;
        fixedTable = true;
        //
        apiStr = safeGet(queryClass, [className], "").replace(
            "{name}",
            bs64Encode(personName)
        );
        //
        return readHkbdbData(
            apiStr,
            30 * 1000,
            false,
            false,
            undefined,
            false,
            fixedTable
        );
    });
    //
    const results = await Promise.allSettled(promises).then(res => res);
    //
    let headers = {};
    const formattedData = results.reduce((prevObj, res, resIdx) => {
        const className = safeGet(classNames, [resIdx], "");
        if (isNotEmpty(className)) {
            if (isNotEmpty(res)) {
                const { status, value } = res;
                if (status === "fulfilled") {
                    let content;
                    if (["memberInvert"].includes(className)) {
                        const res = safeGet(value, ["data"], []);
                        content = res.reduce((acc, cur) => {
                            acc[cur.key] = {
                                property: cur.property,
                                classtype: cur.classtype
                            };
                            return acc;
                        }, {});
                    } else {
                        content = safeGet(value, ["data"], []);
                        const header = safeGet(value, ["head"], []);

                        if (isNotEmpty(header)) {
                            headers = { ...headers, [className]: header };
                        }
                    }
                    //
                    if (isNotEmpty(content)) {
                        return {
                            ...prevObj,
                            [className]: content
                        };
                    }
                }
            }
        }
        return prevObj;
    }, {});
    //
    // console.log("formattedData", formattedData);
    //
    dispatch({
        type: Act.USER_FETCH_INFO_LOADING,
        payload: false
    });
    //
    dispatch({
        type: Act.USER_FETCH_INFO_HEADER,
        payload: headers
    });
    //
    dispatch({
        type: Act.USER_FETCH_INFO,
        payload: formattedData
    });
};

/**
 *
 * @param personName
 * @param className
 * @param dispatch
 * @returns {Promise<void>}
 */
export const queryOrgInfoV2 = async (
    personName,
    className,
    limit,
    offset,
    keyword,
    dispatch
) => {
    if (!personName) return;

    let classNames;

    // 依照參數的有無: 單獨執行或是多個執行
    if (isEmpty(className)) {
        classNames = [
            "article",
            "publication",
            "event",
            "otherwork",
            "namenode",
            "member"
        ];
    } else {
        classNames = [className];
    }

    const _axios = getHkbdbAxios();

    _axios.defaults.timeout = 60 * 1000;

    classNames.map(className => {
        let idsAPI;

        // switch api method
        if (isEmpty(keyword)) {
            // 因有些實體名稱含有 & 的符號, 必須就地 encode url
            // idsAPI = encodeUrl(safeGet(eventidsAPIs, [className], ""));
            idsAPI = safeGet(orgEventIdsAPIs, [className], "")
                .replace("{limit}", limit)
                .replace("{offset}", offset)
                .replace("{name}", bs64Encode(personName));
            //
            // loading
            dispatch({
                type: Act.USER_FETCH_DATA_LOAD,
                payload: {
                    [className]: true
                }
            });
        } else {
            // 因有些實體名稱含有 & 的符號, 必須就地 encode url
            // idsAPI = encodeUrl(safeGet(eventSearchIdsAPIs, [className]));

            idsAPI = safeGet(orgEventSearchIdsAPIs, [className])
                .replace("{limit}", limit)
                .replace("{offset}", offset)
                .replace("{name}", bs64Encode(personName))
                .replace("{keyword}", bs64Encode(keyword));
            //
            // loading
            dispatch({
                type: Act.USER_FETCH_SEARCH_DATA_LOAD,
                payload: {
                    [className]: true
                }
            });
        }

        _axios
            .get(idsAPI)
            .then(data => {
                const _eventIds = safeGet(data, ["data", "data"], []).map(
                    item => {
                        // FIXME 暫時先改組織成員
                        if (className === "member") {
                            return `${item._eventId}`;
                        }
                        return `hkbdb:${item._eventId}`;
                    }
                );
                const _pageTotal = safeGet(data, ["data", "total"], 1);
                // console.log("_pageTotal:", className, _pageTotal);

                if (isEmpty(keyword)) {
                    // eventIds
                    dispatch({
                        type: Act.USER_FETCH_IDS_INFO,
                        payload: {
                            [className]: _eventIds
                        }
                    });

                    // page total
                    dispatch({
                        type: Act.USER_FETCH_DATA_PAGE_TOTAL_INFO,
                        payload: {
                            [className]: _pageTotal
                        }
                    });
                } else {
                    // eventIds
                    dispatch({
                        type: Act.USER_FETCH_SEARCH_IDS_INFO,
                        payload: {
                            [className]: _eventIds
                        }
                    });

                    // page total
                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_PAGE_TOTAL_INFO,
                        payload: {
                            [className]: _pageTotal
                        }
                    });
                }

                // let dataAPI = safeGet(eventAPIs, [className], "");
                // dataAPI = encodeUrl(
                //     dataAPI.replace("{eventids}", _eventIds.join(" "))
                // );

                // Gary: 2023.2.9 因為 dataAPI 可能超過瀏覽器可以接受的 url 長度,所以分批處理
                // Peri: 2023.6.14 之後考慮都改成 POST
                const dataAPIdef = safeGet(orgEventAPIs, [className], "");

                let allQuery = [];

                if (Array.isArray(_eventIds)) {
                    const isMember = className === "member";
                    let eventids = [];
                    // url 長度限制:2000 character
                    const URL_LEN_LIMIT = 2000;

                    _eventIds.forEach(evtId => {
                        eventids.push(evtId);

                        const joinedIds = isMember
                            ? eventids.join(",")
                            : eventids.join(" ");
                        const encodedIds = isMember
                            ? bs64Encode(joinedIds)
                            : base64url(joinedIds);

                        if (
                            encodedIds.length + dataAPIdef.length >
                            URL_LEN_LIMIT
                        ) {
                            const popId = eventids.pop();
                            const batchJoined = isMember
                                ? eventids.join(",")
                                : eventids.join(" ");
                            const batchEncoded = isMember
                                ? bs64Encode(batchJoined)
                                : base64url(batchJoined);

                            const thisDataAPI = dataAPIdef
                                .replace(
                                    isMember ? "{ids}" : "{eventids}",
                                    batchEncoded
                                )
                                .replace(
                                    "{name}",
                                    bs64Encode(
                                        isMember
                                            ? `ORG${personName}`
                                            : personName
                                    )
                                );

                            allQuery.push(_axios.get(thisDataAPI));

                            eventids = [popId];
                        }
                    });

                    if (eventids.length > 0) {
                        const batchJoined = isMember
                            ? eventids.join(",")
                            : eventids.join(" ");
                        const batchEncoded = isMember
                            ? bs64Encode(batchJoined)
                            : base64url(batchJoined);

                        const thisDataAPI = dataAPIdef
                            .replace(
                                isMember ? "{ids}" : "{eventids}",
                                batchEncoded
                            )
                            .replace(
                                "{name}",
                                bs64Encode(
                                    isMember ? `ORG${personName}` : personName
                                )
                            );

                        allQuery.push(_axios.get(thisDataAPI));
                    }
                }

                Promise.allSettled(allQuery)
                    .then(allRes => {
                        let _data = [];
                        allRes.forEach(res => {
                            const { status, value } = res;
                            if (status === "fulfilled") {
                                _data = _data.concat(
                                    safeGet(value, ["data", "data"], [])
                                );
                            }
                        });
                        if (isEmpty(keyword)) {
                            // data
                            dispatch({
                                type: Act.USER_FETCH_DATA_INFO,
                                payload: {
                                    [className]: transformers(className, _data)
                                }
                            });
                        } else {
                            // data
                            dispatch({
                                type: Act.USER_FETCH_SEARCH_DATA_INFO,
                                payload: {
                                    [className]: transformers(className, _data)
                                }
                            });
                        }
                    })
                    // _axios
                    //     .get(dataAPI)
                    //     .then(data => {
                    //         const _data = safeGet(data, ["data", "data"], []);
                    //
                    //         if (isEmpty(keyword)) {
                    //             // data
                    //             dispatch({
                    //                 type: Act.USER_FETCH_DATA_INFO,
                    //                 payload: {
                    //                     [className]: transformers(className, _data)
                    //                 }
                    //             });
                    //         } else {
                    //             // data
                    //             dispatch({
                    //                 type: Act.USER_FETCH_SEARCH_DATA_INFO,
                    //                 payload: {
                    //                     [className]: transformers(className, _data)
                    //                 }
                    //             });
                    //         }
                    //     })
                    .catch(e => {
                        // log
                        console.log(e);
                        // data
                        dispatch({
                            type: Act.USER_FETCH_DATA_INFO,
                            payload: {
                                [className]: []
                            }
                        });
                    })
                    .finally(() => {
                        // loading
                        if (isEmpty(keyword)) {
                            // loading
                            dispatch({
                                type: Act.USER_FETCH_DATA_LOAD,
                                payload: {
                                    [className]: false
                                }
                            });
                        } else {
                            // loading
                            dispatch({
                                type: Act.USER_FETCH_SEARCH_DATA_LOAD,
                                payload: {
                                    [className]: false
                                }
                            });
                        }

                        // payload reload function
                        dispatch({
                            type: Act.USER_FETCH_DATA_RELOAD_FUNC,
                            payload: {
                                // 傳遞一個 function (已經塞好除了 offset 參數的方法)
                                [className]: (
                                    pageIndex = -1,
                                    _keyword = null
                                ) => {
                                    // console.log("reload func!!")
                                    const _offset =
                                        pageIndex === -1 || pageIndex === null
                                            ? offset
                                            : (pageIndex - 1) * limit;
                                    // console.log(personName, className, limit, offset, _offset, _keyword);
                                    return queryOrgInfoV2(
                                        personName,
                                        className,
                                        limit,
                                        _offset,
                                        _keyword,
                                        dispatch
                                    );
                                }
                            }
                        });

                        // payload search function
                        dispatch({
                            type: Act.USER_FETCH_DATA_SEARCH_FUNC,
                            payload: {
                                // 傳遞一個 function (已經塞好除了 keyword 參數的方法)
                                [className]: _keyword => {
                                    // console.log("search func!!")
                                    const currKeyword = isEmpty(_keyword)
                                        ? keyword
                                        : _keyword;
                                    const _offset = isEmpty(_keyword)
                                        ? offset
                                        : 0;
                                    // console.log(personName, className, limit, _offset, currKeyword);
                                    return queryOrgInfoV2(
                                        personName,
                                        className,
                                        limit,
                                        _offset,
                                        currKeyword,
                                        dispatch
                                    );
                                }
                            }
                        });
                        // console.log("finally");
                    });
                // console.log("ids", _eventIds);
            })
            .catch(e => {
                // print error
                console.log(e);
                // loading
                if (isEmpty(keyword)) {
                    // loading
                    dispatch({
                        type: Act.USER_FETCH_DATA_LOAD,
                        payload: {
                            [className]: false
                        }
                    });
                } else {
                    // loading
                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_LOAD,
                        payload: {
                            [className]: false
                        }
                    });
                }
            });
    });
};

export const reloadInfo = async (instanceName, className, dispatch) => {
    if (!instanceName) return;
    //
    let apiStr = "";
    let fixedTable = false;
    if (Object.keys(queryClass).includes(className)) {
        fixedTable = true;
        apiStr = safeGet(queryClass, [className], "").replace(
            "{name}",
            bs64Encode(instanceName)
        );
    } else {
        apiStr = Api.getOrganizationBasicInfo().replace(
            "{name}",
            bs64Encode(instanceName)
        );
    }

    await readHkbdbData(
        apiStr,
        30 * 1000,
        false,
        false,
        undefined,
        false,
        fixedTable
    )
        // await readHkbdbData(apiStr, 30 * 1000)
        .then(res => {
            //
            let content;
            let headers;
            if (["memberInvert"].includes(className)) {
                const value = safeGet(res, ["data"], []);
                content = value.reduce((acc, cur) => {
                    acc[cur.key] = {
                        property: cur.property,
                        classtype: cur.classtype
                    };
                    return acc;
                }, {});
            } else {
                content = safeGet(res, ["data"], []);
                headers = safeGet(res, ["head"], []);
            }
            //
            dispatch({
                type: Act.USER_FETCH_INFO_HEADER,
                payload: { [className]: headers }
            });

            dispatch({
                type: Act.USER_FETCH_INFO,
                payload: { [className]: content }
            });
            // 通知編輯後是否顯示 isLoading(這是區域重新載入，不是整個頁面，e.g. information's publication)
            dispatch({ type: Act.INFORMATION_DATA_IS_LOADING_CEL });
        })
        .catch(err => {
            // print
            console.error("readHkbdbData func:err:", err.message);
            // 通知編輯後是否顯示 isLoading(這是區域重新載入，不是整個頁面，e.g. information's publication)
            dispatch({ type: Act.INFORMATION_DATA_IS_LOADING_CEL });
        });
};

export const getPropertDefined = dispatch => {
    const api = Api.getOntologyDefined();
    //
    readHkbdbData(api).then(res => {
        if (!isEmpty(res)) {
            //
            const hierarchyObj = res?.data?.reduce((hrcObj, item) => {
                const { domain, range, property } = item;
                const lowerDoamin = domain?.toLowerCase() || "";
                const propertyBindRangeStr = `${property}__${range}`;
                // add class
                if (isEmpty(hrcObj[lowerDoamin])) {
                    hrcObj[lowerDoamin] = [
                        { range, property, propertyBindRangeStr }
                    ];
                } else {
                    hrcObj[lowerDoamin] = [
                        ...hrcObj[lowerDoamin],
                        { range, property, propertyBindRangeStr }
                    ];
                }
                return hrcObj;
            }, {});
            //
            // console.log(JSON.stringify(hierarchyObj["person"], null, 2));
            //
            if (!isEmpty(hierarchyObj)) {
                dispatch({
                    type: Act.PROTEGE_DEFINED_DATA_SET,
                    payload: hierarchyObj
                });
                // console.log("ontology defined: ", hierarchyObj);
            }
        }
    });
};

export const getDataset = (dispatch, locale) => {
    const api = Api.getDataset();
    readHkbdbData(api, null, null, null, locale).then(res => {
        if (!isEmpty(res?.data)) {
            dispatch({
                type: Act.SET_DATASET,
                payload: res.data || []
            });
        }
    });
};

// 取得使用的 database 資料: 正式站 or 測試站
export const getDatabase = (dispatch, locale) => {
    const api = Api.getDatabase();
    readHkbdbData(api).then(res => {
        if (!isEmpty(res?.data)) {
            dispatch({
                type: Act.SET_DATABASE,
                payload: res.data[0] || []
            });
        }
    });
};

export const queryPropertyMap = dispatch => {
    const api = Api.getPropertyMap();

    readHkbdbData(api)
        .then(response => {
            const resObj = {};
            response.data.forEach(rd => {
                const labelLang = rd.label.split("@");
                const label = labelLang[0];
                const lang = labelLang[1];
                if (!resObj.hasOwnProperty(lang)) {
                    resObj[lang] = {};
                }
                resObj[lang][rd.property] = label;
            });
            dispatch({
                type: Act.PROPERTY,
                payload: resObj
            });
        })
        .catch(() => {
            dispatch({
                type: Act.PROPERTY,
                payload: {}
            });
        });
};

export const queryTimeLine = (name, dispatch, setIsLoading) => {
    // 因為 有些 name 帶有空白字元(e.g. "The Chinese University of Hong Kong__Chung Chi College__Chinese Language & Literature")
    // 所以塞進 url 時必須先 encode
    // 然後使用 readHkbdbData(不會協助 encode) ,useEncodeUrl參數為 false
    const api = Api.getOrganizationTimelineData().replace(
        "{name}",
        base64url.encode(name)
    );

    return readHkbdbData(api, undefined, false, false, undefined, false)
        .then(async response => {
            //
            const resData = response.data;

            // 使用 titleId, subTitleId fetch API 取得 bestKnownName (依據語系)
            const dataLocale = await formatPersonsByLocale(
                resData,
                ["titleId", "subTitleId"],
                { titleId: "title", subTitleId: "subTitle" }
            );

            dispatch({
                type: Act.USER_FETCH_TIMELINE_DATA,
                payload: {
                    timelineData: {
                        vars: [],
                        bindings: dataLocale
                    }
                }
            });
        })
        .catch(() => {
            dispatch({
                type: Act.USER_FETCH_TIMELINE_DATA,
                payload: {
                    timelineData: {
                        vars: [],
                        bindings: []
                    }
                }
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};

const CLASS_NAME_CVT = {
    PER: "Person",
    ORG: "Organization"
};

export const SNA_CLASS_ACT = {
    Person: {
        name: "Person",
        classPrefix: "PER",
        api: Api.getDepthSNARelation1s()
    },
    Organization: {
        name: "Organization",
        classPrefix: "ORG",
        api: Api.getDepthOrgSNARelation1s()
    }
};

/**
 * 依據 ids, 取得 bestKnowName by locale
 * @param ids {string[]} 所有人物及組織的 id
 * @returns {Promise<null|unknown extends (object & {then(onfulfilled: infer F): any}) ? (F extends ((value: infer V, ...args: any) => any) ? Awaited<V> : never) : unknown|*[]>}
 */
const getNameByLocale = async ids => {
    try {
        if (!ids) return null;
        // url 有長度限制(2048 characters), 所以 10 個人(或組織)為一組
        const asyncQuery = [];

        for (let i = 0; i < ids.length; i += 10) {
            // let idsStr = mapAndJoinList(perIdsList.slice(i, i+10))
            const idsStr = "\n" + ids.slice(i, i + 10).join("\n");
            if (idsStr.length > 0) {
                const url = Api.getNameByLocaleSna().replace(
                    "{ids}",
                    bs64Encode(idsStr)
                );
                // console.log("getNameByLocale url", url);
                asyncQuery.push(
                    readHkbdbData(
                        url,
                        undefined,
                        false,
                        false,
                        undefined,
                        false
                    )
                );
            }
        }
        const response = await Promise.all(asyncQuery);
        return response.reduce((acc, cur) => {
            return acc.concat(cur.data);
        }, []);
    } catch (err) {
        return [];
    }
};

/**
 * 從 val 中找出第一個符合的 separator
 *
 * @param val {string}
 * @param separator {[]|string}
 * @returns {null|string}
 */
const findSeparator = (val, separator = []) => {
    let findSep = null;
    if (!(isString(val) && val.length > 0)) return findSep;
    let stop = false;
    const separators = isArray(separator) ? separator : [separator];
    separators.forEach(o => {
        if (!stop && val.indexOf(o) >= 0) {
            findSep = o;
            stop = true;
        }
    });
    return findSep;
};

/**
 * 將文字依據 separator 拆開成 list
 *
 * @val {string}
 * @separator  {string|[]} e.g. "\n"  or ['、', ',', '，']
 *
 * 範例:
 * authors: Pai Chiu__PER2733__en\nCheng Chou-yu__PER2733__en\nLo Fu__PER2733__en
 * => ["Pai Chiu__PER2733__en","Cheng Chou-yu__PER2733__en","Lo Fu__PER2733__en"],
 * */
export const splitVal = (val, separator = "\n") => {
    if (!(isString(val) && val.length > 0)) return [];
    const findSep = findSeparator(val, separator);
    return findSep ? val.split(findSep) : [val];
};

// 用於 queryDepthSNARelation
// 將 fetch 取得 的 srcId, dstId=> 依據 locale, 取得 srcName, dstName
const formatPersonsByLocale = async (data = [], idKeys, replaceNameKeysMap) => {
    try {
        // 擷取所有 ids, 預備用來 fetch API
        const getIdsAry = (persons = []) => {
            const tmpIdKeys = idKeys || ["srcId", "dstId"];
            return Array.from(
                new Set(
                    persons.reduce((acc, cur) => {
                        tmpIdKeys.forEach(key => {
                            if (cur[key] && !acc.includes(cur[key])) {
                                acc.push(cur[key]);
                            }
                        });
                        return acc;
                    }, [])
                )
            );
        };

        // idsAry: [PER蔣復璁,PER金庸,ORG浙江省立聯合高中,ORG上海東吳法學院插班]
        const idsAry = getIdsAry(data);
        // get name by locale
        // namesLocale: [{perId:"", srcName:""}, {},...]
        const namesByLocale = await getNameByLocale(idsAry);
        // console.log("namesByeLocale", namesByLocale);

        // persons: [srcId ,srcName ,srcType ,dstId ,dstName ,dstType ,relation]
        // namesLocale: [{perId:"", srcName:""}, {},...]
        const correctPersonName = (persons = [], namesLocale = []) => {
            const mapKey = "perId";
            // {perId1: {perId:"", srcName:""}, perId2: {},...}
            const namesLocaleMap = (namesLocale || []).reduce((acc, cur) => {
                if (cur[mapKey] && !(cur[mapKey] in acc)) {
                    acc[cur[mapKey]] = cur;
                }
                return acc;
            }, {});
            const tmpReplaceNameKeysMap = replaceNameKeysMap || {
                srcId: "srcName",
                dstId: "dstName"
            };
            const srcNameKey = "srcName";
            return (persons || []).reduce((acc, cur) => {
                let ele = { ...cur, srcName: "", dstName: "" };
                Object.entries(tmpReplaceNameKeysMap).forEach(
                    ([idKey, name]) => {
                        if (idKey in ele && ele[idKey] in namesLocaleMap) {
                            ele = {
                                ...ele,
                                // [name]: namesLocaleMap[ele[idKey]][srcNameKey],
                                [name]: splitVal(
                                    namesLocaleMap[ele[idKey]][srcNameKey]
                                )[0]
                            };
                        }
                    }
                );
                acc.push(ele);
                return acc;
            }, []);
        };

        return correctPersonName(data, namesByLocale);
    } catch (err) {
        console.log("err", err);
        return data;
    }
};

const getIdByBestKnowName = async name => {
    try {
        if (!name) return null;
        // 因為 有些 keyword帶有空白字元(e.g. "The Chinese University of Hong Kong__Chung Chi College__Chinese Language & Literature")
        // 所以塞進 url 時必須先 encode
        // 然後使用 readHkbdbData(不會協助 encode) ,useEncodeUrl參數為 false
        const url = Api.findIdByValue()
            .replace(
                "{class}",
                base64url.encode(SNA_CLASS_ACT.Organization.name)
            )
            .replace("{keyword}", base64url.encode(name));

        const res = await readHkbdbData(
            url,
            undefined,
            false,
            false,
            undefined,
            false
        );
        // [{label:"金庸", value:"PER金庸"}]
        return res?.data[0]?.value;
    } catch (err) {
        console.log("err", err);
        return null;
    }
};

/**
 * Step1,2 共用 queryDepthSNARelation
 * People, Organization 共用 queryDepthSNARelation
 *
 * @param depth  => 1 or 2
 * @param className => Person/ Organization
 * @param center  => Person name / Organization name
 * @param nodes => fetch step1 時, nodes = [], fetch step2 時, nodes 為與 center 相連的 nodes
 * @returns {Promise<never>|Promise<{snaData: {nodes: *[], orgList: *[], links: *[]}}>}
 */
export const queryDepthSNARelation = async ({
    depth: depth = 1,
    className: className = CLASS_NAME_CVT.PER,
    center,
    nodes: nodes = []
}) => {
    if (!(nodes && className)) {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject({ error: "parameters error" });
    }

    // get id by bestKnowName
    // const centerId = await getIdByBestKnowName(center);
    const centerId = className === "Person" ? `PER${center}` : `ORG${center}`;
    // 取得中心節點的 bestKnownName
    const centerNameRes = await getNameByLocale([centerId]);
    const centerName = centerNameRes?.[0]?.srcName || center;

    let perIdsList = [];
    // 先把 centerId 放進 orgIdsList
    let orgIdsList = centerId ? [centerId] : [];

    if (nodes.length > 0) {
        let _names = Array.isArray(nodes) ? nodes : [nodes];
        // 區分 PER , ORG, 分開 fetch API
        const orgNodes = [];
        const perNodes = [];
        _names.forEach(na => {
            if (na.type === SNA_CLASS_ACT.Person.classPrefix) {
                perNodes.push(na);
            } else if (na.type === SNA_CLASS_ACT.Organization.classPrefix) {
                orgNodes.push(na);
            }
        });
        perIdsList = perIdsList.concat(perNodes.map(pn => pn._id));
        orgIdsList = orgIdsList.concat(orgNodes.map(pn => pn._id));
    }
    // url 有長度限制(2048 characters), 所以 10 個人(或組織)為一組
    const asyncQuery = [];

    for (let i = 0; i < perIdsList.length; i += 10) {
        // let idsStr = mapAndJoinList(perIdsList.slice(i, i+10))
        const idsStr = perIdsList.slice(i, i + 10).join("\n");
        if (idsStr.length > 0) {
            const curApi = SNA_CLASS_ACT.Person.api.replace(
                "{ids}",
                bs64Encode(idsStr)
            );
            asyncQuery.push(
                readHkbdbData(curApi, undefined, false, false, undefined, false)
            );
        }
    }
    for (let i = 0; i < orgIdsList.length; i += 10) {
        // let idsStr = mapAndJoinList(perIdsList.slice(i, i+10))
        const idsStr = orgIdsList.slice(i, i + 10).join("\n");
        if (idsStr.length > 0) {
            const curApi = SNA_CLASS_ACT.Organization.api.replace(
                "{ids}",
                bs64Encode(idsStr)
            );
            asyncQuery.push(
                readHkbdbData(curApi, undefined, false, false, undefined, false)
            );
        }
    }

    return Promise.all(asyncQuery)
        .then(async responses => {
            // console.log("queryDepthSNARelation", responses);
            let data = [];
            responses.forEach(res => {
                data = data.concat(res.data);
            });
            // keys of item of data: ?srcId ?srcType ?dstId ?dstType ?relation

            // 利用 fetch 取得 的 srcId, dstId => 依據 locale, 取得 srcName, dstName
            const personsLocale = await formatPersonsByLocale(data);

            return Promise.resolve({
                snaData: Object.assign(
                    {},
                    processSNADataSlim(
                        personsLocale,
                        centerName,
                        centerId,
                        depth
                    )
                )
            });
        })
        .catch(err => {
            // eslint-disable-next-line prefer-promise-reject-errors
            return Promise.reject({ err: err.message });
        });
};

export const queryGenealogy = (depth = 1, name, dispatch) => {
    if (!name) return;

    const api =
        depth === 1
            ? Api.getDepthGenealogy1().replace("{personId}", name)
            : Api.getDepthGenealogy2().replace("{personId}", name);

    return readHkbdbData(api)
        .then(response => {
            // console.log("USER_FETCH_GENEALOGY: ", response.data);
            dispatch({
                type: Act.USER_FETCH_GENEALOGY,
                payload: gedcomize(response.data, name)
            });
        })
        .catch(() => {
            dispatch({
                type: Act.USER_FETCH_GENEALOGY,
                payload: ""
            });
        });
};

/**
 * 取得組織的 常見名稱
 * @param ontologyType {string} 人物id 或 組織id, e.g. PER金庸, PERJohn_F._JONES
 * @param id {string} 人物id 或 組織id, e.g. PER金庸, PERJohn_F._JONES
 * @param queryStrName {string} 常見名稱 query string 的 name
 * @param setName {function}
 */
export const queryBestKnownName = (ontologyType, id, queryStrName, setName) => {
    // ontologyType 若為 null, 或 "person",  "namenode", 則 fetch API
    if (!id || (ontologyType && !["person", "namenode"].includes(ontologyType)))
        return;
    // console.log('ontologyType', ontologyType)
    // console.log('queryStrName', queryStrName)

    const api = Api.getBasicInfo().replace("{id}", bs64Encode(id));
    //
    const timeout = 30 * 1000;
    readHkbdbData(api, timeout, false, false, undefined, false)
        .then(response => {
            if (response?.data && response.data.length > 0) {
                const perLen = response.data.length;
                const find = response.data.find(
                    o => o.bestKnownName === queryStrName
                );
                const firstName = response.data[0].bestKnownName;
                if (find) {
                    setName(perLen > 1 ? `${queryStrName},...` : queryStrName);
                } else {
                    setName(perLen > 1 ? `${firstName}...` : firstName);
                }
            } else {
                setName("");
            }
        })
        .catch(() => {
            console.log("timeout", timeout);
            setName("");
        });
};
