import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_author: props => ({
        marginLeft: "10px",
        // color: "blue",
        // textDecoration: "underline",
        // cursor: "pointer",
        whiteSpace: "nowrap",
        display: "flex",
        alignItems: "center",
        ...props.hkvayb_author
    }),
    hkvayb_author_div: props => ({
        display: "flex",
        alignItems: "center",
        flexWrap: "wrap",
        ...props.hkvayb_author_div
    })
});

export default useStyles;
