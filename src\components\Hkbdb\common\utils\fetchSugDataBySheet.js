import allRoles from "../../../../App-role";
import { Api, readHkbdbData } from "../../../../api/hkbdb/Api";
import { bs64DecodeId } from "../../../../common/codes/jenaHelper";
import { sheetList } from "../../../../api/hkbdb/sheetList";
import config from "../../../../config/config";

/** According to suggester select sheet to fetch hkbdb_draft DB data */
const fetchSugDataBySheet = async (user, titleProps, personId, sheetName) => {
    const { role } = user;
    if (role !== allRoles.suggester || titleProps.active) return;
    let apiStr = "";
    let promises;
    let response;
    let idArr;

    switch (sheetName) {
        case sheetList.namenode:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.nameNodeProp[0]
            );
            break;

        case sheetList.educationevent:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.educationeventProp[0]
            );
            break;

        case sheetList.employmentevent:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.employmenteventProp[0]
            );
            break;

        case sheetList.publication:
            apiStr = Api.getDraftInfoAtObj(bs64DecodeId(personId), sheetName);
            response = await readHkbdbData(apiStr);
            idArr = response?.data?.map(i => i.id) || [];
            promises = idArr.map(id => {
                return readHkbdbData(Api.getDraftInfo(id, sheetName));
            });
            return Promise.all(promises).then(res => {
                const flattenedArray = res.reduce((acc, result) => {
                    acc.push(...result.data);
                    return acc;
                }, []);
                return flattenedArray;
            });

        case sheetList.article:
            apiStr = Api.getDraftInfoAtObj(bs64DecodeId(personId), sheetName);
            response = await readHkbdbData(apiStr);
            idArr = response?.data?.map(i => i.id) || [];
            promises = idArr.map(id => {
                return readHkbdbData(Api.getDraftInfo(id, sheetName));
            });
            return Promise.all(promises).then(res => {
                const flattenedArray = res.reduce((acc, result) => {
                    acc.push(...result.data);
                    return acc;
                }, []);
                return flattenedArray;
            });

        case sheetList.otherwork:
            apiStr = Api.getDraftInfoAtObj(bs64DecodeId(personId), sheetName);
            response = await readHkbdbData(apiStr);
            idArr = response?.data?.map(i => i.id) || [];
            promises = idArr.map(id => {
                return readHkbdbData(Api.getDraftInfo(id, sheetName));
            });

            return Promise.all(promises).then(res => {
                const flattenedArray = res.reduce((acc, result) => {
                    acc.push(...result.data);
                    return acc;
                }, []);
                return flattenedArray;
            });

        case sheetList.organizationevent:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.organizationeventProp[0]
            );
            break;

        case sheetList.relationevent:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.relationeventProp[0]
            );
            response = await readHkbdbData(apiStr);
            idArr = response?.data?.map(i => i.id) || [];
            promises = idArr.map(id => {
                return readHkbdbData(Api.getDraftInfo(id, sheetName));
            });
            return Promise.all(promises).then(res => {
                const flattenedArray = res.reduce((acc, result) => {
                    acc.push(...result.data);
                    return acc;
                }, []);
                return flattenedArray;
            });

        case sheetList.event:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.eventProp[0]
            );
            break;

        case sheetList.awardevent:
            apiStr = Api.getDraftInfoHasR(
                bs64DecodeId(personId),
                sheetName,
                config.awardeventProp[0]
            );
            break;

        default:
            apiStr = Api.getDraftInfo(bs64DecodeId(personId), sheetName);
            break;
    }

    return new Promise((resolve, reject) => {
        readHkbdbData(apiStr)
            .then(res => {
                resolve(res.data);
            })
            .catch(err => reject(err));
    });
};

export default fetchSugDataBySheet;
