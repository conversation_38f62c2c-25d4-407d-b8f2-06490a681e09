import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_input: props => ({
        marginRight: "16px!important",
        width: "100%",
        height: "48px",
        background: "#fff",
        border: 0,
        "& .MuiOutlinedInput-root": {
            borderRadius: "unset!important"
        },
        "& .MuiInputLabel-root": {
            fontFamily: "NotoSansHK",
            color: "#b79d79",
            fontSize: "16px",
            lineHeight: "unset"
        },
        // "& .MuiFormLabel-root": {
        //     fontSize: "16px",
        //     lineHeight: "1em"
        // },
        "& .MuiFilledInput-input": {
            // padding: "14.5px 14px"
            paddingTop: "21px"
        },
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            marginRight: "0px!important"
        },
        ...props.hkvayb_input
    })
});

export default useStyles;
