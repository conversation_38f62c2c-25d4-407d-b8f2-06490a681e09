import React, { useContext } from "react";

// ui
import { Container, Segment, Header, Label, Divider } from "semantic-ui-react";

// common
import {
    cvtDatasetLocale,
    getProperty,
    isEmpty
} from "../../../../common/codes";

// store
import { StoreContext } from "../../../../store/StoreProvider";
// common
import CustomInput from "./CustomInputFlexMenu";
import CustomDateInput from "./CustomDateInputChanged";
import CustomDropdown from "./CustomDropdownFlexEdit";
import { displayMemberName, SPECIAL_HEADER } from "../../Organization/action";
import { convertNormalColHeaderName } from "../../common/utils/convertSugOptions";
import CustomEditCoordinatesModal from "./CustomEditCoordinatesModal";

const CustomRawDataSegment = ({
    rowIdx,
    eventId,
    cellData,
    editData,
    setEditData,
    ontologyType
}) => {
    //
    const {
        property,
        propertyRange,
        propertyBindRangeStr,
        graph,
        values
    } = cellData;
    //
    const [state, dispatch] = useContext(StoreContext);
    const { memberInvert } = state.personInformation;
    const { property: propData, source } = state;
    const { dataset: globalDataset } = source;
    //
    const memberNameGroup = memberInvert ? displayMemberName(memberInvert) : [];
    //
    const dividerStyle = {
        margin: ".5rem 0"
    };
    const segmentStyle = () => {
        if (editData.updatedRowIds.includes(rowIdx)) {
            return {
                // backgroundColor: "#f8ffff"
                backgroundColor: "#fcfff5"
            };
        } else {
            return {};
        }
    };
    //
    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(propData?.propertyObj)
            ? _property
            : getProperty(_property, propData.propertyObj);
    };
    //
    const showHeader = _property => {
        if (
            ontologyType === "member" &&
            memberNameGroup.includes(`${_property}__Person`)
        ) {
            return safeGetProperty(SPECIAL_HEADER.split("__")[0]);
        }

        return safeGetProperty(_property);
    };

    const title = convertNormalColHeaderName(
        propertyBindRangeStr,
        ontologyType,
        safeGetProperty(property)
    );

    const showForm = type => {
        switch (type) {
            case "Place":
            case "Organization":
                return (
                    <CustomEditCoordinatesModal
                        title={title}
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propRange={type}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "string":
            case "float":
                return (
                    <CustomInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "DateEvent":
                return (
                    <CustomDateInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            default:
                return (
                    <CustomDropdown
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propRange={propertyRange}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
        }
    };
    //
    return (
        <Segment style={segmentStyle()}>
            <Header as="h5">{title}</Header>
            <Container>{showForm(propertyRange)}</Container>
            <Divider hidden style={dividerStyle} />
            <Label color="orange">
                {/* {graph} */}
                {cvtDatasetLocale(graph, globalDataset)}
            </Label>
        </Segment>
    );
};

export default CustomRawDataSegment;
