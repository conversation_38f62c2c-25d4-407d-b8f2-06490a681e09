// react
import React, { useContext } from "react";

// ui
import { Accordion } from "semantic-ui-react";

// custom
import CustomContent from "./CustomContent";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// common
import { isEmpty, isNotEmpty } from "../../../../../common/codes";

const CustomPublicAccordion = () => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { uid } = state.user;
    const { queries, searchString } = state.query;
    //
    let publicPanels = [];
    //
    if (!isEmpty(uid)) {
        publicPanels = queries
            // filter if private is false
            .filter(query => !query.private)
            // filter if search keyword is exist
            .filter(publicQuery => {
                const { displayName, email } = publicQuery.author;
                const { en: titleEn, zh: titleZh } = publicQuery.title;
                if (isEmpty(searchString)) {
                    return true;
                } else {
                    const reg = new RegExp(searchString, "i");
                    return (
                        email.search(reg) !== -1 ||
                        displayName.search(reg) !== -1 ||
                        titleEn.search(reg) !== -1 ||
                        titleZh.search(reg) !== -1
                    );
                }
            })
            .sort((a, b) => {
                let titleA, titleB;
                if (isNotEmpty(a?.title.zh)) {
                    titleA = a?.title?.zh?.toUpperCase() || ""; // ignore upper and lowercase
                    titleB = b?.title?.zh?.toUpperCase() || ""; // ignore upper and lowercase
                } else {
                    titleA = a?.title?.en?.toUpperCase() || ""; // ignore upper and lowercase
                    titleB = b?.title?.en?.toUpperCase() || ""; // ignore upper and lowercase
                }
                if (titleA < titleB) return -1;
                if (titleA > titleB) return 1;
                // names must be equal
                return 0;
            })
            .map((publicQuery, idx) => {
                const {
                    id: docId,
                    query: queryString,
                    author,
                    title
                } = publicQuery;
                const { en: titleEn, zh: titleZh } = title;
                const { uid: authorId, displayName, email } = author;
                return {
                    key: `private-panel-${idx}-${docId}`,
                    title: titleEn,
                    content: {
                        content: (
                            <CustomContent
                                docId={docId}
                                authorId={authorId}
                                query={queryString}
                                email={email}
                                displayName={displayName}
                                translation={titleZh}
                            />
                        )
                    }
                };
            });
    }
    //
    return <Accordion.Accordion panels={publicPanels} />;
};

export default CustomPublicAccordion;
