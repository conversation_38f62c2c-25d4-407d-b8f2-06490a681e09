import React, { useCallback, useState, useEffect, memo } from "react";
import { FeatureGroup, useMap } from "react-leaflet";
import PointsLineWithRotatedMarker from "./PointsLineWithRotatedMarker";
import L from "leaflet";
import { debounce } from "../MapHelper";

const MIN_ZOOM_FOR_ARROWS = 15; // 設定顯示箭頭的最小縮放級別

// 解析浮點數
const safeParseFloat = value => {
    // 先處理可能的科學記號字串
    const stringValue = String(value);
    const num = parseFloat(stringValue);
    return Number.isNaN(num) ? 0 : num; // 如果解析失敗返回 0
};

const OptimizedLineRenderer = memo(
    ({
        lines, // 所有線條數據陣列
        renderThreshold = 10000 // 同時渲染的最大線條數閾值
    }) => {
        const map = useMap();
        const [visibleLines, setVisibleLines] = useState([]);
        const [currentZoom, setCurrentZoom] = useState(() => map.getZoom()); // 初始 zoom
        const [mapVersion, setMapVersion] = useState(0);
        const [shouldShowArrows, setShouldShowArrows] = useState(false);
        const [zoomDashParams, setZoomDashParams] = useState([]);
        // const [debugInfo, setDebugInfo] = useState({
        //     // 除錯資訊狀態
        //     totalLines: lines.length,
        //     visibleLinesCount: 0,
        //     zoomLevel: currentZoom,
        //     renderThreshold: renderThreshold
        // });

        // 判斷線段是否在視窗內
        const isLineInViewport = useCallback((line, mapBounds) => {
            // 優先使用預計算的邊界
            if (line.bounds && line.bounds.length === 4) {
                const [
                    lineMinLng,
                    lineMinLat,
                    lineMaxLng,
                    lineMaxLat
                ] = line.bounds;
                // 創建 Leaflet Bounds 物件以便使用 intersects 方法
                const lineLatLngBounds = L.latLngBounds(
                    [lineMinLat, lineMinLng],
                    [lineMaxLat, lineMaxLng]
                );
                // 檢查線條邊界是否與地圖邊界（稍微擴大）相交
                return mapBounds.pad(0.1).intersects(lineLatLngBounds); // 增加 10% 緩衝區
            }

            // 如果沒有預計算的邊界，動態計算簡易邊界框檢查
            if (!line.srcPoint || !line.dstPoint) return false; // 確保點存在

            try {
                const srcLat = safeParseFloat(line.srcPoint.lat);
                const srcLong = safeParseFloat(line.srcPoint.long);
                const dstLat = safeParseFloat(line.dstPoint.lat);
                const dstLong = safeParseFloat(line.dstPoint.long);

                // 創建 Leaflet LatLng 物件
                const startPoint = L.latLng(srcLat, srcLong);
                const endPoint = L.latLng(dstLat, dstLong);

                // 使用 Leaflet 的方法檢查點是否在邊界內 (更精確)
                // 或者檢查線段的邊界框是否與地圖邊界相交
                const lineBounds = L.latLngBounds(startPoint, endPoint);
                return mapBounds.pad(0.1).intersects(lineBounds); // 增加 10% 緩衝區
            } catch (e) {
                console.error("Error calculating line bounds:", e, line);
                return false;
            }
        }, []);

        // 過濾可見線段的回調函數
        const filterAndSetVisibleLines = useCallback(
            (currentBounds, currentZoomLevel) => {
                // 根據縮放級別動態調整渲染閾值
                const dynamicThreshold = Math.min(
                    renderThreshold
                    // 基於 zoom 的動態調整邏輯，例如放大時顯示更多(OPTIONAL)
                    // 100 + Math.pow(currentZoomLevel, 2)
                );
                // 過濾出在視窗內的線條
                const linesInView = lines.filter(line =>
                    isLineInViewport(line, currentBounds)
                );

                // 更新除錯資訊
                // setDebugInfo({
                //     totalLines: lines.length,
                //     visibleLinesCount: linesInView.length, // 顯示過濾後的數量
                //     zoomLevel: currentZoomLevel,
                //     renderThreshold: dynamicThreshold
                // });

                // 根據閾值限制最終渲染的線條數量
                setVisibleLines(linesInView.slice(0, dynamicThreshold));
            },
            [lines, isLineInViewport, renderThreshold]
        );

        // Define dash size parameters for different zoom levels
        const zoomConfig = {
            // zoom level: [number of segments, dash-to-gap ratio]
            5: [10, 10], // Zoomed way out: fewer, bigger dashes
            8: [10, 10], // Mid zoom: medium dashes
            12: [5, 2], // Zoomed in: more, smaller dashes
            16: [30, 10] // Zoomed way in: many small dashes
        };

        // 曲線dash
        function getZoomParams(zoom) {
            // console.log("zoom: ", zoom);
            // Find the closest defined zoom levels
            const definedZooms = Object.keys(zoomConfig)
                .map(Number)
                .sort((a, b) => a - b);

            if (zoom <= definedZooms[0]) {
                return zoomConfig[definedZooms[0]];
            }

            if (zoom >= definedZooms[definedZooms.length - 1]) {
                return zoomConfig[definedZooms[definedZooms.length - 1]];
            }

            // Find surrounding zoom levels for interpolation
            let lowerZoom = definedZooms[0];
            let upperZoom = definedZooms[definedZooms.length - 1];

            for (let i = 0; i < definedZooms.length - 1; i++) {
                if (zoom >= definedZooms[i] && zoom <= definedZooms[i + 1]) {
                    lowerZoom = definedZooms[i];
                    upperZoom = definedZooms[i + 1];
                    break;
                }
            }

            // Interpolate between the two zoom levels
            const lowerParams = zoomConfig[lowerZoom];
            const upperParams = zoomConfig[upperZoom];
            const zoomFraction = (zoom - lowerZoom) / (upperZoom - lowerZoom);

            return [
                Math.round(
                    lowerParams[0] +
                    (upperParams[0] - lowerParams[0]) * zoomFraction
                ),
                lowerParams[1] +
                (upperParams[1] - lowerParams[1]) * zoomFraction
            ];
        }

        // 當 zoom 變化時，更新是否顯示箭頭的狀態
        useEffect(() => {
            if (currentZoom !== null) {
                const show = currentZoom >= MIN_ZOOM_FOR_ARROWS;
                setShouldShowArrows(show);
                // console.log(`Current Zoom: ${currentZoom}, Show Arrows: ${show}`); // 除錯日誌
                setZoomDashParams(getZoomParams(currentZoom));
            }
        }, [currentZoom]);

        // 地圖事件處理 Effect
        useEffect(() => {
            if (!map) return;

            // 更新可見線條和 Zoom 狀態的函數
            const updateHandler = () => {
                try {
                    const currentBounds = map.getBounds();
                    const currentZoomLevel = map.getZoom();
                    setCurrentZoom(currentZoomLevel); // 更新 Zoom 狀態
                    setMapVersion(prev => prev + 1); // 地圖更新時新增版本
                    filterAndSetVisibleLines(currentBounds, currentZoomLevel); // 過濾並設置線條
                } catch (error) {
                    console.error("Error updating visible lines:", error);
                }
            };

            // 使用防抖處理減少不必要的更新
            const debouncedUpdate = debounce(updateHandler, 150);

            // 綁定事件
            map.on("moveend", debouncedUpdate);
            map.on("zoomend", debouncedUpdate);

            // 初始加載時執行一次
            updateHandler();

            // 清理函數：移除事件監聽器
            return () => {
                map.off("moveend", debouncedUpdate);
                map.off("zoomend", debouncedUpdate);
            };
        }, [map, filterAndSetVisibleLines]);

        return (
            <>
                {/* 除錯資訊面板 */}
                {/* <div */}
                {/*    style={{ */}
                {/*        position: "absolute", */}
                {/*        top: 10, */}
                {/*        right: 10, */}
                {/*        backgroundColor: "rgba(255, 255, 255, 0.8)", // 半透明背景 */}
                {/*        padding: "8px", */}
                {/*        borderRadius: "4px", */}
                {/*        zIndex: 1000, // 確保在最上層 */}
                {/*        fontSize: "12px", // 稍小字體 */}
                {/*        pointerEvents: "none" // 避免阻擋地圖操作 */}
                {/*    }} */}
                {/* > */}
                {/*    <h4>除錯資訊</h4> */}
                {/*    <p>總線條數: {debugInfo.totalLines}</p> */}
                {/*    <p>視窗內線條: {debugInfo.visibleLinesCount}</p> */}
                {/*    <p>縮放級別: {debugInfo.zoomLevel}</p> */}
                {/*    <p>渲染上限: {debugInfo.renderThreshold}</p> */}
                {/*    <p>顯示箭頭: {shouldShowArrows ? "是" : "否"}</p> */}
                {/* </div> */}

                {/* 渲染可見的線條 */}
                <FeatureGroup>
                    {visibleLines.map(line => (
                        <PointsLineWithRotatedMarker
                            key={
                                line.id ||
                                `${line.srcPoint?.lat}-${line.srcPoint?.long}-${line.dstPoint?.lat}-${line.dstPoint?.long}`
                            }
                            srcPoint={line.srcPoint}
                            dstPoint={line.dstPoint}
                            style={line.style}
                            // showArrow={shouldShowArrows}
                            showArrow={true}
                            mapVersion={mapVersion}
                            zoomDashParams={zoomDashParams}
                        />
                    ))}
                </FeatureGroup>
            </>
        );
    }
);

// 為了讓 memo 生效，最好給元件一個 displayName
OptimizedLineRenderer.displayName = "OptimizedLineRenderer";

export default OptimizedLineRenderer;
