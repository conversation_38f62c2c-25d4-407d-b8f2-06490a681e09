// react
import React, { useContext, useEffect } from "react";

import axios from "axios";

// ui
import { Accordion } from "semantic-ui-react";

// custom
import CustomPrivateAccordion from "./CustomPrivateAccordion";
import CustomPublicAccordion from "./CustomPublicAccordion";

// firebase cloud api
import cloudStorage from "../../../../../api/firebase/cloudFirestore/Api";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { isPermitting } from "../../../../../App-header";
import authority from "../../../../../App-authority";
import { injectIntl } from "react-intl";

const CustomStoredQuery = ({ intl }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // get query from state
    const { query, user } = state;
    const { renderSignal } = query;
    // get user params
    const { uid, role } = user;
    // handleGetUserQueries
    const handleGetUserQueries = async () => {
        // console.log("I am handleGetUserQueries", uid);
        // if (uid) {
        const pubQueriesResponse = await axios.get(
            process.env.REACT_APP_GET_PUBLIC_SPARQL_IN_FIREBASE_ENDPOINT
        );

        const pubQueries = pubQueriesResponse?.data || [];

        const userQueriesResponse = await axios.get(
            process.env.REACT_APP_GET_USER_SPARQL_IN_FIREBASE_ENDPOINT.replace(
                "{uid}",
                uid
            )
        );

        const userQueries = userQueriesResponse?.data || [];

        // 由於 userQueries 已經包含了自己的資料了(包含公開與非公開)，
        // 所以 pubQueries 要過濾掉自己並免出現重複資料
        const newPubQueries = pubQueries.filter(
            query => query?.author?.uid !== uid
        );
        // 合併兩個 queries 並儲存
        // ps. 也可以不要合併分別存放
        dispatch({
            type: Act.QUERY_QUERIES_SET,
            payload: [...userQueries, ...newPubQueries]
        });
        // }
    };
    //
    useEffect(() => {
        handleGetUserQueries();
    }, [uid, renderSignal]);
    //
    const rootPanels = [
        {
            key: "panel-1",
            title: intl.formatMessage({
                id: "query.myQueries",
                defaultMessage: "My Queries"
            }),
            content: {
                content: <CustomPrivateAccordion />
            },
            authority: authority.Query_advance
        },
        {
            key: "panel-2",
            title: intl.formatMessage({
                id: "query.publicQueries",
                defaultMessage: "Public Queries"
            }),
            content: {
                content: <CustomPublicAccordion />
            },
            authority: null // null 代表未定義權限, 所有 role 皆可看到
        }
    ];

    // 依 role 過濾要顯示的 panel
    const customPanels = rootPanels.filter(
        rp => rp.authority === null || isPermitting(role, rp.authority)
    );
    // return <Accordion defaultActiveIndex={0} panels={rootPanels} styled />;
    return <Accordion panels={customPanels} styled />;
};

export default injectIntl(CustomStoredQuery);
