.homePage {

  h3.ui.header.homePage__heading--en-header {
    //margin-bottom: 4rem;
  }
  h3.ui.header.homePage__heading--zh-header {
    //margin-bottom: 0.5rem;
  }
  .entitySearch {
    &__input {
      height: 48px;
      border-radius: 4px;
      ::placeholder{
        color: #89ACBB !important;
      }
    }
    .ui.fluid.input>input{
      color: #89ACBB;
    }
    &__select.ui.selection.dropdown {
      min-width: 9rem;
      max-width: 14rem;
      .text {
        line-height: 24px;
        color: #104860;
      }
      i {
        line-height: 24px;
      }
      .menu{
        border: none;
      }
    }
    &__submit {
      margin-left: 8px;
      border-radius: 4px;
      background-color: #104860;
      &:hover {
        background-color: #043348;
      }
    }
    &__desc {
      display: none;
      visibility: hidden;
      width: 0;
      &--text {
        cursor: pointer;
        color: #4183c4;
        display: none;
        visibility: hidden;
        span {
          display: none;
          visibility: hidden;
        }
      }
    }
  }
  .resultsEntity {
    max-height: 150px;
    overflow-y: scroll;
    text-align: left;
    &__placeholder {
      margin-top: 14px;
      background-color: #fff;
      padding-top: 16px;
      padding-bottom: 16px;
      padding-left: 16px;
      padding-right: 16px;
    }
    &__showBtn {
      display: none;
    }
  }
  &__background{
    &__box{
      &__backdrop{
        &__display{
          display:flex;
          width: 50px;
          height: 50px;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          position: absolute;
          transform: translate(-50%, -50%);
          pointer-events: none;
          z-index: 999;
        }
        &__notDisplay{
          display: none;
        }
      }
    }
  }
  &__imgBox{
    &__effect{
        transition: all 0.1s ease;
        transform:scale(1.1);
        filter: blur(5px);
      button{
        transition: all 0.3s ease;
        display: flex;
      }
    }
  }
}


@media screen and (max-width: 801px)  {
  .homePage {
    .entitySearch {
      &__input {
        height: 48px;
        border-radius: 4px;
      }
      &__select.ui.selection.dropdown {
        min-width: 4rem;
        max-width: 8rem;
        .text {
          line-height: 24px;
        }
      }
    }
    .resultsEntity {

    }
  }
}

.homepage{
  &__background{
    &__box{
      overflow:hidden;
      //cursor:pointer;
      z-index: 901;
      img{
        width:100%;
        height:100%;
        transition: all 0.1s ease;
        z-index: 900;
        &:hover::before{
          content: '前往';
          display: block;
          position: absolute;
          top: -20px; /* 調整位置以擺放在游標上方 */
          left: -30px; /* 調整位置以擺放在游標左方 */
          background-color: #000; /* 背景顏色 */
          color: #fff; /* 文字顏色 */
          padding: 5px; /* 內間距 */
          border-radius: 3px; /* 圓角 */
          font-size: 12px; /* 字體大小 */
        }
      }
      &__back{
        width: 100%;
        height: 100%;
        display: flex;
        margin: auto;
        position: absolute;
        top: 0;
        &__btn{
          display: none;
          width: 150px;
          height: 150px;
          //background-color: rgba(0, 0, 0, 0.5);
          background-color: transparent;
          color: #104860;
          //border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          pointer-events: none;
          z-index: 9999;
          margin: auto;
          text-align: center;
          align-items: center;
          justify-content: center;
          //color: #fff;
          font-size: 1.5rem;
          border:none;
        }
    }
      //&:hover{
      //  img{
      //    transition: all 0.1s ease;
      //    transform:scale(1.1);
      //    filter: blur(5px);
      //  }
      //  button{
      //    transition: all 0.3s ease;
      //    display: flex;
      //  }
      //}
    }
    &__normalbox{
      overflow:hidden;
      img{
        width:100%;
        height:100%;
      }
  }
  }
}