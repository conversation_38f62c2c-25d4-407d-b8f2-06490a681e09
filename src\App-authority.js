import role from "./App-role";

const allPermit = Object.values(role);

const authority = {
    Home: allPermit,
    Login: allPermit,
    Logout: allPermit,
    Signup: allPermit,
    Sources: allPermit,
    About: allPermit,
    Error: allPermit,
    Browse: allPermit,
    People: allPermit,
    Organization: allPermit,
    Query: [role.admin, role.editor, role.reader, role.developer],
    Help: allPermit,
    Privacy: allPermit,
    User: [role.admin, role.editor, role.developer],
    Management: [role.admin, role.developer],
    Ontology: [role.admin, role.developer],
    API: [role.admin, role.developer],
    Hkvayb: allPermit,
    HotSearch: allPermit,
    FocusPoint: allPermit,
    Collection: allPermit,
    ManuScript: allPermit,
    FeaturedPub: allPermit,
    Map: allPermit,

    // 後台
    BackendWeb: [role.admin, role.developer]
};

// query page: 那些 role 可以使用進階功能
authority.Query_advance = [
    role.admin,
    role.editor,
    role.developer,
    role.reader
];
// subPage authority
authority.People_Information = [
    role.admin,
    role.editor,
    role.developer,
    role.suggester
];

// suggester不開放編輯按鈕
authority.EditButton = authority.People_Information.filter(
    val => ![role.suggester].includes(val)
);

export default authority;
