import React, { useContext } from "react";

import { useHistory } from "react-router-dom";

import useStyles from "./style";

import CusModal from "../CusModal";
import CusImage from "../CusImage";
import CusLineArrow from "../CusLineArrow";

import { StoreContext } from "../../../../store/StoreProvider";

const CusCard = ({
    src,
    backupSrc,
    primary,
    secondary,
    category,
    content,
    lineArrow,
    underLine,
    style = {},
    onClick = () => {},
    width = 200,
    height = 200
}) => {
    const history = useHistory();
    const classes = useStyles(style);
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    const handleOnClick = category => {
        if (category) {
            history.push(`/${locale}/HkvaybResult/${category}`);
        }
    };

    return (
        <div
            onClick={onClick}
            className={classes.hkvayb_card}
            key={`${primary}`}
        >
            <div
                className={classes.hkvayb_div}
                key={primary}
                onClick={() => handleOnClick(category)}
            >
                <CusModal
                    image={
                        <CusImage
                            style={{
                                maxWidth: "100%",
                                maxHeight: height * 1.5,
                                display: "flex",
                                alignContent: "center",
                                justifyContent: "space-evenly",
                                flexWrap: "wrap"
                            }}
                            alt={primary}
                            src={src}
                            backupSrc={backupSrc}
                        />
                    }
                    title={primary}
                    content={content}
                    style={style}
                >
                    <CusImage
                        style={{ width: width, height: height }}
                        alt={primary}
                        src={src}
                        backupSrc={backupSrc}
                    />
                    <div>
                        <div className={classes.hkvayb_card_box}>
                            <div className={classes.hkvayb_card_title_group}>
                                <span className={classes.hkvayb_card_title_zh}>
                                    {primary}
                                </span>
                                <span className={classes.hkvayb_card_title_en}>
                                    {secondary}
                                </span>
                            </div>
                            {lineArrow && <CusLineArrow />}
                        </div>
                        {underLine && (
                            <div className={classes.hkvayb_card_under_line} />
                        )}
                    </div>
                </CusModal>
            </div>
        </div>
    );
};

export default CusCard;
