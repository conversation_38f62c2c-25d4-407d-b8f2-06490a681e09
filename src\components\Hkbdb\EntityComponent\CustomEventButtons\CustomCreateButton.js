import React, { useContext, useEffect, useState } from "react";

// ui
import { Button, Modal, Label, Input, Select, Popup } from "semantic-ui-react";
import CreatableSelect from "react-select/creatable";

// api
import { doRestCreate } from "../../../../api/hkbdb/Api";
import { fetchOptionList, getNextNameId } from "../commonAction";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import {
    bs64Encode,
    cvtDatasetLocale,
    isEmpty,
    isNotEmpty,
    isTrue
} from "../../../../common/codes";

// custom
import CustomDebounce from "../TableComp/CustomDeBounce";
import CustomAlertMessage from "../TableComp/CustomAlertMessage";

import { CLASS_PREFIX } from "../../../../config/config-ontology";
// auth
import authority from "../../../../App-authority";

// common
import MenuList from "../TableComp/MenuList";

// lang
import { FormattedMessage, injectIntl } from "react-intl";

// nameNode lib
import { createNameNode } from "../nameNodeLib";
import {
    decodeURIComponentSafe,
    idToUriEncBs64EncId,
    idToUriEncId
} from "../../../../common/codes/jenaHelper";
import { displayInstanceName } from "../helper";
import people from "../../People/People";
import allRoles from "../../../../App-role";

//
const CustomCreateButton = ({ type, entitySetting, intl }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { dataset } = state.source;
    const { user } = state;
    const { uid, role } = user;
    //
    const [open, setOpen] = useState(false);
    //
    const [createState, setCreateState] = useState(() => {
        return {
            isLoading: false,
            options: [],
            willCreatedData: { value: { label: "", bestKnownName: "" } }
        };
    });
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 2 * 1000
    }));
    const curType = type && type === "Person" ? "people" : "organization";
    //
    const [personValue, setPersonValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debPersonValue = CustomDebounce(personValue, 500);
    // dataset options
    const datasetOptions = dataset.map(item => ({
        key: item.dataset,
        value: item.dataset,
        text: cvtDatasetLocale(item.dataset, dataset)
    }));
    //
    const handleInitState = () => {
        setCreateState(prevState => ({
            ...prevState,
            willCreatedData: {}
        }));
    };
    //
    const handleOpen = () => {
        handleInitState();
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
    const handleCancel = () => {
        //
        handleInitState();
        //
        setOpen(false);
    };
    const handleCreate = async () => {
        setCreateState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        if (!isEmpty(createState.willCreatedData)) {
            // console.log(`create ${createState.willCreatedData}`);

            // create nameNode
            const { graph, value } = createState.willCreatedData;
            const { bestKnownName } = value;
            // 取得下一筆 nameId: e.g. 3xxxxxx(person), 4xxxxxx(org)
            // 取得 nameId 後必須馬上新增 nameId, 及 nameNode
            const nextNameId = await getNextNameId(type);
            const { srcId } = createState.willCreatedData || {};
            const addNameNodeRes = await createNameNode(
                srcId,
                nextNameId,
                graph,
                bestKnownName,
                user
            );

            const entry = {
                ...createState.willCreatedData
            };
            entry.value = {
                ...(entry.value || {}),
                nameId: nextNameId
                // nameNode id 已經在 createNameNode() fetch API 後自動產生並建立 person 與 nameNode 的連結
                // 這邊就不用再帶 hasNameId__NameNode
                // hasNameId__NameNode: createNameNodeId(nextNameId)
            };
            //
            const result = await doRestCreate(user, entry);
            //
            if (addNameNodeRes.state && result.state) {
                const createEntity =
                    createState.willCreatedData.value.bestKnownName[0];
                // close
                dispatch({
                    type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                    payload: {
                        target: entitySetting.reloadInfo,
                        signal: `created-${new Date().getTime()}`
                    }
                });
                //
                setAlertMsg(prevMsg => ({
                    ...prevMsg,
                    title: "創建新的 property",
                    type: "success",
                    content: `即將跳轉至「${createEntity}」頁面`,
                    renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
                }));
                //
                setTimeout(() => {
                    // 這邊不用另外 bs64encode , bs64encode 已包在 peopleUrl func. 裡面
                    document.location.href = entitySetting.href(
                        idToUriEncId(createEntity)
                    );
                }, alertMsg.ttl);
            }
        }
        //
        setCreateState(prevState => ({
            ...prevState,
            isLoading: false
        }));
    };
    const handleDatasetChange = (event, { value }) => {
        // console.log(value);
        setCreateState(prevState => ({
            ...prevState,
            willCreatedData: {
                ...prevState.willCreatedData,
                graph: value || ""
            }
        }));
        //
    };
    const handleChange = async () => {
        setCreateState(prevState => ({
            ...prevState,
            willCreatedData: {
                ...prevState.willCreatedData,
                srcId:
                    `${CLASS_PREFIX[type]}${idToUriEncBs64EncId(
                        debPersonValue
                    )}` || "",
                classType: type,
                // "label" 為創建新 instance 的特殊 property
                value: {
                    // label: [debPersonValue],
                    bestKnownName: [debPersonValue]
                }
            }
        }));
    };
    const handleInputChange = value => {
        setPersonValue(value);
    };
    //
    useEffect(() => {
        if (isEmpty(type) || isEmpty(debPersonValue)) {
            return;
        }

        // open dropdown Loading
        setCreateState(prevState => ({
            ...prevState,
            isLoading: true
        }));

        fetchOptionList(type, debPersonValue, 100, 30 * 1000)
            .then(res => {
                setCreateState(prevState => ({
                    ...prevState,
                    options: res.data,
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setCreateState(prevState => ({
                    ...prevState,
                    options: [],
                    isLoading: false
                }));
            });
        // handleUpdatePersonValue();
    }, [debPersonValue]);
    //
    const customButtonStyle = {
        marginLeft: ".8em",
        marginTop: "1em",
        marginBottom: "1em",
        padding: ".3em"
    };
    //
    //
    const customStyles = {
        container: styles => ({
            ...styles,
            width: "100%"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            borderTopLeftRadius: "0",
            borderBottomLeftRadius: "0",
            borderLeftColor: "transparent",
            border: "1px solid rgba(34,36,38,.15)"
            // borderRadius: "unset"
        })
    };
    //
    if (
        isTrue(process.env.REACT_APP_CRUD_NODE) &&
        isNotEmpty(uid) &&
        authority.People_Information.includes(role)
    ) {
        return (
            <div>
                <Modal
                    size="tiny"
                    open={open}
                    onOpen={handleOpen}
                    onClose={handleClose}
                    dimmer="inverted"
                >
                    <Modal.Header>
                        {intl.formatMessage(
                            {
                                id: `${curType}.Information.create.${type.toLowerCase()}`,
                                defaultMessage: `Create {type}`
                            },
                            {
                                type: type.toLowerCase()
                            }
                        )}
                        {/* <FormattedMessage */}
                        {/*    // id={entitySetting.infoModalHeader} */}
                        {/*    // defaultMessage={`Create ${type}`} */}
                        {/*    // id={"people.Information.create.person"} */}
                        {/*    // defaultMessage={`Create Person`} */}
                        {/*    // id={`${type.toLowerCase()}.Information.create.${type.toLowerCase()}`} */}
                        {/*    // defaultMessage={`Create ${type.lowerCase()}`} */}

                        {/*    id={"{type}.ModalYesNo.{type}"} */}
                        {/*    defaultMessage={`Create {type}`} */}
                        {/*    values={{ */}
                        {/*        type: type && type.toLowerCase() */}
                        {/*    }} */}
                        {/* /> */}
                    </Modal.Header>
                    <Modal.Content image>
                        <Modal.Description>
                            <CustomAlertMessage
                                alertMsg={alertMsg}
                                setAlertMsg={setAlertMsg}
                            />
                            {/* <pre>{JSON.stringify(createState, null, 2)}</pre> */}
                            <Input fluid labelPosition="left" type="text">
                                <Label color="orange">
                                    <FormattedMessage
                                        id={"browse.filterDatasetLabel"}
                                        defaultMessage={`Dataset`}
                                        // id={
                                        //     "people.Information.formTable.dataset"
                                        // }
                                        // defaultMessage={"Dataset"}
                                    />
                                </Label>
                                <Select
                                    fluid
                                    placeholder={
                                        intl.formatMessage(
                                            {
                                                id: `${curType}.Information.dropDown.placeholder`,
                                                defaultMessage: `Select...`
                                            },
                                            {
                                                type: type.toLowerCase()
                                            }
                                        )
                                        // <FormattedMessage
                                        //     id={`{type}.Information.dropDown.placeholder`}
                                        //     defaultMessage={"Select..."}
                                        //     values={{
                                        //         type: type && type.toLowerCase()
                                        //     }}
                                        //     // id={
                                        //     //     "people.Information.dropDown.placeholder"
                                        //     // }
                                        //     // defaultMessage={"Select..."}
                                        // />
                                    }
                                    options={datasetOptions}
                                    onChange={handleDatasetChange}
                                />
                            </Input>
                            <br />
                            <Input
                                fluid
                                labelPosition="left"
                                type="text"
                                loading={createState.isLoading}
                            >
                                <Label>
                                    {intl.formatMessage(
                                        {
                                            id: `${curType}.Information.label.${type.toLowerCase()}`,
                                            defaultMessage: `{type}`
                                        },
                                        {
                                            type: type
                                        }
                                    )}
                                    {/* <FormattedMessage */}
                                    {/*    id={`{type}.Information.label.{type}`} */}
                                    {/*    defaultMessage={"{type}"} */}
                                    {/*    values={{ */}
                                    {/*        type: type && type.toLowerCase() */}
                                    {/*    }} */}
                                    {/*    // id={entitySetting.infoInputLabel} */}
                                    {/*    // defaultMessage={type} */}
                                    {/*    // id={"people.Information.label.person"} */}
                                    {/*    // defaultMessage={"Person"} */}
                                    {/* /> */}
                                </Label>
                                <CreatableSelect
                                    isClearable
                                    placeholder={
                                        intl.formatMessage(
                                            {
                                                id: `${curType}.Information.create.placeholder`,
                                                defaultMessage: `Please type an {type} name`
                                            },
                                            {
                                                type: type.toLowerCase()
                                            }
                                        )
                                        // <FormattedMessage
                                        //     // id={
                                        //     //     entitySetting.infoCreatePlaceHolder
                                        //     // }
                                        //     // defaultMessage={`Please type an ${type} name`}
                                        //     id={`{type}.Information.create.placeholder`}
                                        //     defaultMessage={`Please type an {type} name`}
                                        //     values={{
                                        //         type: type && type.toLowerCase()
                                        //     }}
                                        //     // id={
                                        //     //     "people.Information.create.placeholder"
                                        //     // }
                                        //     // defaultMessage={`Please type an Person name`}
                                        // />
                                    }
                                    styles={customStyles}
                                    options={createState.options}
                                    onChange={handleChange}
                                    onInputChange={handleInputChange}
                                    components={{ MenuList }}
                                />
                            </Input>
                        </Modal.Description>
                    </Modal.Content>
                    <Modal.Actions>
                        <Button
                            disabled={isEmpty(
                                createState.willCreatedData?.value
                                    ?.bestKnownName
                            )}
                            loading={createState.isLoading}
                            onClick={handleCreate}
                            color="green"
                        >
                            <FormattedMessage
                                id={"people.Information.button.confirm"}
                                defaultMessage={"Confirm"}
                            />
                        </Button>
                        <Button color="red" onClick={handleCancel}>
                            <FormattedMessage
                                id={"people.Information.button.cancel"}
                                defaultMessage={"Cancel"}
                            />
                        </Button>
                    </Modal.Actions>
                </Modal>
                <Popup
                    content={
                        intl.formatMessage(
                            {
                                id: `${curType}.Information.create.${type.toLowerCase()}`,
                                defaultMessage: `Create {type}`
                            },
                            {
                                type: type.toLowerCase()
                            }
                        )
                        // <FormattedMessage
                        //     // id={entitySetting.infoCreated}
                        //     // defaultMessage={`Create ${type}`}
                        //     id={`{type}.Information.create.{type}`}
                        //     defaultMessage={`Create {type}`}
                        //     values={{
                        //         type: type && type.toLowerCase()
                        //     }}
                        //     // id={"people.Information.create.person"}
                        //     // defaultMessage={`Create Person`}
                        // />
                    }
                    key={"create"}
                    trigger={
                        <Button
                            size="mini"
                            color="green"
                            icon="plus"
                            style={customButtonStyle}
                            onClick={handleOpen}
                        />
                    }
                />
            </div>
        );
    } else {
        return null;
    }
};

export default injectIntl(CustomCreateButton);
