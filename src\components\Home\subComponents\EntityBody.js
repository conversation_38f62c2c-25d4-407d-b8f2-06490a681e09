import React, { useState, useEffect } from "react";
//
import { List } from "semantic-ui-react";
import { browseOrgUrl, browsePerUrl } from "../../../services/common";
import { CLASS_PREFIX } from "../../../config/config-ontology";
import { useHistory } from "react-router-dom";

/**
 * remove prefix form id
 *
 * @param {string} id. e.g. PER金庸
 * @param {string} prefix. e.g. PER
 * @returns {string} . e.g. 金庸
 */
const removeStartPrefix = (id, prefix) => {
    if (id && id.startsWith(prefix)) {
        return id.replace(prefix, "");
    }
    return id;
};

// 適用頁面: 首頁
// 點擊搜尋結果連結到 browse page
function EntityBody({ bindings, classType }) {
    // route
    const history = useHistory();
    //
    const [list, setList] = useState([]);

    //
    useEffect(() => {
        if (Array.isArray(bindings)) {
            const tmpList = bindings.map(b => {
                let url = "";
                if ((classType || "").toLowerCase() === "person") {
                    // 連結到搜尋頁
                    url = browsePerUrl(
                        removeStartPrefix(b.perId, CLASS_PREFIX.Person)
                    );
                } else {
                    // 連結到搜尋頁
                    url = browseOrgUrl(
                        removeStartPrefix(b.perId, CLASS_PREFIX.Organization)
                    );
                }
                return {
                    ...(b || {}),
                    url: url
                };
            });
            setList(tmpList);
        }
    }, [bindings]);
    //
    const onItemClick = item => e => {
        e.preventDefault();
        //
        if (item?.url) {
            history.push(item.url);
        }
    };

    if (!(Array.isArray(list) && list.length > 0)) return null;

    return (
        <List size="small" className={"entityBody__list"}>
            {list.map((b, idx) => {
                return (
                    <List.Item
                        key={idx}
                        value={b.name}
                        className={"entityBody__list--item"}
                    >
                        <List.Content
                            as="a"
                            href={b.url}
                            className={"entityBody__list--item-content"}
                            onClick={onItemClick(b)}
                        >
                            {b.name}
                        </List.Content>
                    </List.Item>
                );
            })}
        </List>
    );
}

export default EntityBody;
