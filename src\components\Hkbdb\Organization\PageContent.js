/* eslint-disable react/display-name */
import React, { useContext, useState, useEffect } from "react";

// ui
import { Container, Segment, Tab } from "semantic-ui-react";

// custom
import { MainLoader } from "../EntityComponent/MainLoader";

// lang
import { injectIntl } from "react-intl";
import { intlMsgs } from "../EntityComponent/entityIntlMsgs";

// action
import {
    queryOrgInfo,
    queryOrgInfoV2,
    queryTimeLine,
    queryInverseRelationInfo,
    queryOntologyOneOfThemInfo
} from "./action";

// component
import Information from "./Information/Information";
import Timeline from "./Timeline/Timeline";
import SNA from "./SNA/SNA";

//
import PropTypes from "prop-types";

import UnderConstruction from "../../../common/components/UnderConstruction";

// store
import { StoreContext } from "../../../store/StoreProvider";

// api
import { Api } from "../../../api/hkbdb/Api";
import config from "../../../config/config";

/**
 * @typedef {object} OrganizationPageContentProps
 * @property {string} intl
 * @property {string} name 已經移除 prefix 的 instance id
 * @property {string} permission
 * @property {string} className
 */

/**
 *
 * @param props {OrganizationPageContentProps}
 * @returns {JSX.Element}
 * @constructor
 */
const OrganizationPageContent = ({ intl, name, permission, className }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { renderSignal } = state.information;
    const { user } = state;
    const { locale } = user;
    //
    const [isLoading, setIsLoading] = useState(false);
    const { formatMessage } = intl;
    //
    useEffect(() => {
        if (name === "") {
            return;
        }
        const send = async () => {
            // 頁面載入
            // setIsLoading(true); 已處理，取消這邊的 loading 狀態
            // 取得組織資訊
            await queryOrgInfo(name, dispatch);
            // 非同步獲取資料(先取得先顯示)
            queryOrgInfoV2(name, null, -1, 0, null, dispatch);
            // 取得人物資訊
            // await queryPersonNameNode(name, dispatch);
            // 取得人際關係的相反關係
            queryInverseRelationInfo(Api.getInverseRelation(), dispatch);
            // 取得 publication, article and otherWork needs list
            queryOntologyOneOfThemInfo(
                Api.getHkbdbOntologyOneOfThemInfo(),
                dispatch
            );
        };
        send();
    }, [name]);

    useEffect(() => {
        if (name === "") {
            return;
        }
        const send = async () => {
            queryTimeLine(name, dispatch, setIsLoading); // put setIsLoading as args
        };
        send();
    }, [name, renderSignal, locale]);

    const createPane = (tabName, Component, accessible = false) => {
        // console.log("I am createPane");
        return {
            menuItem: tabName,
            render: () => (
                <Tab.Pane
                    style={{
                        minHeight: "550px",
                        paddingBottom: "100px",
                        overflow: "auto"
                    }}
                >
                    {accessible && (
                        <Component
                            intl={intl}
                            type={config.DEF_ORG_TYPE}
                            name={name}
                            className={className}
                            treeData={state.personInformation.familyTreeData}
                            permission={permission}
                            ReducerContext={StoreContext}
                        />
                    )}

                    {/* 該區塊暫不開放(僅適用於開發狀態) */}
                    {!accessible && (
                        <Segment style={{ padding: "4em 0em" }} vertical>
                            <Container textAlign="justified">
                                <UnderConstruction
                                    title={tabName}
                                    cardHeader={"資料準備中"}
                                    cardDescription={""}
                                />
                            </Container>
                        </Segment>
                    )}
                </Tab.Pane>
            )
        };
    };
    //
    const panes = [
        createPane(formatMessage(intlMsgs["information"]), Information, true),
        createPane(formatMessage(intlMsgs["timeline"]), Timeline, true),
        createPane(formatMessage(intlMsgs["SNA"]), SNA, true)
        // createPane(formatMessage(intlMsgs["familyTree"]), Tree, true)
    ];
    //
    return isLoading ? (
        <MainLoader />
    ) : (
        <Tab panes={panes} style={{ minHeight: "75vh" }} />
    );
};
OrganizationPageContent.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    name: PropTypes.string.isRequired,
    permission: PropTypes.number,
    className: PropTypes.string
};

export default injectIntl(OrganizationPageContent);
