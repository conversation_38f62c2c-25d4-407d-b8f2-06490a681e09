import React, { useContext, useEffect, useState } from "react";
import { StoreContext } from "../../../../../store/StoreProvider";

// semantic ui
import { Table } from "semantic-ui-react";

// config
import role from "../../../../../App-role";

// component
import CustomContentMenu from "../CustomContextMenuFixed";
import CustomTableLabel from "../CustomTableLabel";

// utils
import { safeGetProperty } from "../../../common/utils/safeGetProperty";
import convertTableBodyVal from "../../../common/utils/convertTableBodyVal";
import { isEmpty } from "../../../../../common/codes";

// hook
import useGetUsers from "../../../../../hook/useGetUsers";
import {
    convertPersonColName,
    convertSugOptions
} from "../../../common/utils/convertSugOptions";
import SugContextMenuFixed from "./SugContextMenuFixed";
import Act from "../../../../../store/actions";
import {
    Api,
    deleteHkbdbData,
    readHkbdbData
} from "../../../../../api/hkbdb/Api";
import { bs64EncodeId } from "../../../../../common/codes/jenaHelper";

/** @description 只用在顯示從hkbdb_draft資料庫抓到的資料 */
function SugFixedTableBody({ headers, ontologyDomain, ontologyType }) {
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation, property, user } = state;
    const { suggestInfo, tempUpdateSuggestInfo } = personInformation;
    const { ontologyDefined } = property;
    const { uid: userUid } = user;
    // suggester name list
    const allUsers = useGetUsers();
    const [sugNames, setSugNames] = useState({});
    const [filterData, setFilterData] = useState(suggestInfo);
    const [tempUpdateData, setTempUpdateData] = useState(suggestInfo);

    const getSpecificClassTypeData = (tempUpdateSuggestInfo, classType) => {
        // 檢查 tempUpdateSuggestInfo 是否存在，且是否包含指定的 classType
        if (
            tempUpdateSuggestInfo &&
            tempUpdateSuggestInfo[classType] &&
            Array.isArray(tempUpdateSuggestInfo[classType])
        ) {
            // 如果條件都滿足，直接回傳該 classType 的資料
            return tempUpdateSuggestInfo[classType];
        }

        // 如果找不到資料，回傳空陣列
        return [];
    };

    useEffect(() => {
        const temp = getSpecificClassTypeData(
            tempUpdateSuggestInfo,
            ontologyType
        );

        setTempUpdateData(temp);
    }, [tempUpdateSuggestInfo, ontologyType]);

    useEffect(() => {
        if (isEmpty(allUsers)) return;
        const uidMap = allUsers
            .filter(el => el.role && el.role === role.suggester)
            .reduce((acc, next) => {
                const { uid, displayName } = next;
                return { ...acc, [uid]: displayName };
            }, {});
        setSugNames(uidMap);
    }, [allUsers]);

    useEffect(() => {
        if (ontologyType !== "relationevent") return;
        if (isEmpty(suggestInfo)) return; // ?

        const uniqueCombos = {};
        const uniqueData = suggestInfo?.filter(item => {
            const key = item.prop + "_" + item.val;
            if (!uniqueCombos[key]) {
                uniqueCombos[key] = true;
                return true;
            }
            return false;
        });
        if (!uniqueData) return;
        setFilterData(uniqueData);
    }, [suggestInfo]);

    const handleDelete = async (idToRemove, updateObject, tmpKey) => {
        const newTempUpdateData = tempUpdateData
            .map(item => {
                if (item.id === idToRemove) {
                    const updatedItem = { ...item };
                    Object.keys(updateObject).forEach(key => {
                        if (updatedItem[key] === updateObject[key]) {
                            delete updatedItem[key];
                        }
                    });
                    return updatedItem;
                }
                return item;
            })
            .filter(item => {
                // filter掉obj內只剩id的
                const nonEmptyKeys = Object.entries(item).filter(
                    ([key, value]) => key !== "id" && value !== ""
                );
                return nonEmptyKeys.length > 0;
            });

        setTempUpdateData(newTempUpdateData);

        const curIds = newTempUpdateData.map(item => item.id);
        const updatedAllData = Object.fromEntries(
            Object.entries(tempUpdateSuggestInfo).map(([key, value]) => [
                key,
                Array.isArray(value)
                    ? value
                          // .filter(item => curIds.includes(item.id))
                          .map(item => {
                              if (item.id === idToRemove) {
                                  const updatedItem = { ...item };
                                  Object.keys(updateObject).forEach(key => {
                                      if (
                                          updatedItem[key] === updateObject[key]
                                      ) {
                                          delete updatedItem[key];
                                      }
                                  });
                                  return updatedItem;
                              }
                              return item;
                          })
                          .filter(item => {
                              const nonEmptyKeys = Object.entries(item).filter(
                                  ([key, value]) => key !== "id" && value !== ""
                              );
                              return nonEmptyKeys.length > 0;
                          })
                    : value
            ])
        );

        dispatch({
            type: Act.UPDATE_TEMP_UPDATE_SUGGESTINFO,
            payload: updatedAllData
        });

        const handleUrlTransformation = input => {
            const decodedBase64 = Buffer.from(input, "base64").toString("utf8");
            const withPrefix = `PER${decodedBase64}`;
            const urlEncoded = withPrefix.replace(/[\u4e00-\u9fa5]/g, char => {
                return encodeURIComponent(char);
            });

            return urlEncoded;
        };

        const values = newTempUpdateData[0].graph.split("/");

        const desiredValue = `PER${values[values.length - 1]}`;
        const params = new URLSearchParams(window.location.search);
        const encodedName = params.get("name");
        const transformed = handleUrlTransformation(encodedName);
        const apiStr = Api.getDraftIDType(transformed, ontologyType);
        // const { data } = await readHkbdbData(apiStr);
        // const [{ type }] = data;

        const entry = {
            classType: "type",
            srcId: desiredValue,
            graph: newTempUpdateData[0].graph,
            value: updateObject
        };
        const res =
            ontologyType === "relationevent"
                ? await deleteHkbdbData(Api.deleteGraph(), entry)
                : // : await deleteHkbdbData(Api.restfulDraft(), entry);
                  "";

        if (res && !res.state) {
            throw new Error("delete failed");
        }
    };

    return (
        <React.Fragment>
            {!isEmpty(filterData) &&
                filterData?.map((item, idx) => {
                    const { graph, val, prop, uid, sheetName } = item;
                    if (prop === "hasRelation" || prop === "relationRemarks")
                        return;
                    if (uid !== userUid) return;

                    const relationRemarksData =
                        filterData &&
                        filterData.find(
                            obj =>
                                obj.prop === "relationRemarks" &&
                                obj.graph === graph
                        );

                    return (
                        <Table.Row key={`basic-table-row-${graph}-${idx}`}>
                            <Table.Cell key={`table-row-cell-edit-${idx}`}>
                                <CustomContentMenu
                                    data={item}
                                    ontologyDomain={ontologyDomain}
                                    ontologyType={ontologyType}
                                    headers={headers}
                                />
                            </Table.Cell>
                            {/* property */}
                            <Table.Cell>
                                {ontologyType === "person"
                                    ? convertPersonColName(prop, ontologyType)
                                    : safeGetProperty(property, prop)}
                            </Table.Cell>
                            {/* value */}
                            <Table.Cell>
                                <CustomTableLabel
                                    key={`value-label-${graph}-${idx}`}
                                    value={convertTableBodyVal(
                                        val,
                                        prop,
                                        ontologyDefined[ontologyType]
                                    )}
                                    color="grey"
                                />
                            </Table.Cell>
                            {sheetName === "relationevent" &&
                                (!isEmpty(relationRemarksData) ? (
                                    <Table.Cell>
                                        {relationRemarksData?.val}
                                    </Table.Cell>
                                ) : (
                                    <Table.Cell></Table.Cell>
                                ))}
                            {/* uid */}
                            <Table.Cell>
                                <CustomTableLabel
                                    key={`value-label-${graph}-${idx}`}
                                    value={`Suggester: ${sugNames[uid] || ""}`}
                                    color="orange"
                                />
                            </Table.Cell>
                        </Table.Row>
                    );
                })}
            {!isEmpty(tempUpdateData) &&
                tempUpdateData?.map((item, idx) => {
                    return Object.entries(item)
                        .filter(([key]) => key !== "id") // 排除 id
                        .map(([key, value]) => {
                            if (isEmpty(value)) return;
                            if (
                                key === "hasRelation" ||
                                key === "relationRemarks__string" ||
                                key === "graph"
                            )
                                return;
                            const relationRemarksData =
                                item?.relationRemarks__string;
                            const result =
                                item[key] === value
                                    ? { [key]: item[key] }
                                    : null;
                            if (!isEmpty(relationRemarksData)) {
                                result.relationRemarks__string = relationRemarksData;
                            }

                            return (
                                <Table.Row key={`basic-table-row-${value}`}>
                                    <Table.Cell
                                        key={`table-row-cell-edit-${value}`}
                                    >
                                        <SugContextMenuFixed
                                            data={result}
                                            ontologyDomain={ontologyDomain}
                                            ontologyType={ontologyType}
                                            headers={headers}
                                            handleDelete={() => {
                                                handleDelete(
                                                    item.id,
                                                    result,
                                                    key
                                                );
                                            }}
                                        />
                                    </Table.Cell>
                                    {/* property */}
                                    <Table.Cell>
                                        {ontologyType === "person"
                                            ? convertPersonColName(
                                                  key,
                                                  ontologyType
                                              )
                                            : safeGetProperty(
                                                  property,
                                                  key.split("__")[0]
                                              )}
                                    </Table.Cell>
                                    {/* value */}
                                    <Table.Cell>
                                        <CustomTableLabel
                                            key={`value-label-${value}`}
                                            value={convertTableBodyVal(
                                                value,
                                                key,
                                                ontologyDefined[ontologyType]
                                            )}
                                            color="grey"
                                        />
                                    </Table.Cell>
                                    {ontologyType === "relationevent" &&
                                        (!isEmpty(relationRemarksData) ? (
                                            <Table.Cell>
                                                {relationRemarksData}
                                            </Table.Cell>
                                        ) : (
                                            <Table.Cell></Table.Cell>
                                        ))}
                                    {/* uid */}
                                    <Table.Cell>
                                        <CustomTableLabel
                                            key={`value-label-${value}`}
                                            value={`Suggester: ${sugNames[
                                                userUid
                                            ] || ""}`}
                                            color="orange"
                                        />
                                    </Table.Cell>
                                </Table.Row>
                            );
                        });
                })}
        </React.Fragment>
    );
}

export default SugFixedTableBody;
