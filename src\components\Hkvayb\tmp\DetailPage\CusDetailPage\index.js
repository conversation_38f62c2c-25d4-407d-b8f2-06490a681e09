import React, { useEffect, useState } from "react";

// material ui
import {
    CssBaseline,
    // Typography,
    // CircularProgress,
    Container
} from "@material-ui/core";

// api
import { url, method } from "../../../../../api/hkvayb";

// common code
import {
    isEmpty,
    isNotEmpty,
    bs64Encode,
    safeGet
} from "../../../../../common/codes";

// cus component
import CusDetailContent from "../CusDetailContent";

const CusDetailPage = props => {
    // try to extract searchId from props
    const {
        match: {
            params: { searchId }
        }
    } = props;
    //
    const [isLoading, setIsLoading] = useState(false);
    const [detailData, setDetailData] = useState([]);
    //
    useEffect(() => {
        //
        let cancel = false;
        //
        const fetchDetail = async () => {
            //
            setIsLoading(true);
            // encode
            const encodedId = `${bs64Encode(searchId)}`;
            // replace url content
            const apiUrl = url.hkvayb.DETAILPAGE_DETAIL_INFORMATION.replace(
                "{keyword}",
                encodedId
            );
            // query hkvayb
            const result = await method.hkvaybQuery(apiUrl, 10 * 1000, cancel);
            // secure access to data from result
            const resData = safeGet(result, ["data", "data"], []);
            //
            if (isNotEmpty(resData)) {
                // preprocessing
                const groupedData = resData.reduce((prevObj, item) => {
                    //
                    const { eventId, property, value } = item;
                    //
                    if (isEmpty(prevObj)) {
                        return {
                            [eventId]: {
                                [property]: [value]
                            }
                        };
                    } else {
                        return {
                            ...prevObj,
                            [eventId]: {
                                ...safeGet(prevObj, [eventId], {}),
                                [property]: [
                                    ...safeGet(
                                        prevObj,
                                        [eventId, property],
                                        []
                                    ),
                                    value
                                ]
                            }
                        };
                    }
                }, []);
                // save
                setDetailData(safeGet(groupedData, [searchId], {}));
            }
            //
            setIsLoading(false);
        };
        // call
        fetchDetail();
        // cancel
        return () => (cancel = true);
    }, [searchId]);

    // if (isLoading) {
    //     return <CircularProgress />;
    // }

    return (
        <React.Fragment>
            <CssBaseline />
            <Container maxWidth="md">
                <CusDetailContent data={detailData} />
            </Container>
        </React.Fragment>
    );
};

export default CusDetailPage;
