import React, { useEffect, useState } from "react";
import { But<PERSON>, Divider } from "semantic-ui-react";
import {
    exportImageHand<PERSON>,
    exportTCsv,
    EXPORT_FILE_NAME
} from "../../ontoUtils/ontoCommon";
import { FormattedMessage } from "react-intl";
import { PropertyRef } from "../subComponents";
import { scrollTop } from "./utils";

const PROP_REF_HEAD = [
    "propertyRef",
    "className",
    "source",
    "target",
    "type",
    "properties"
];

const PropRefDisplay = ({ propertyRef }) => {
    // local state
    const [isImgExporting, setIsImgExporting] = useState(false);

    return (
        <React.Fragment>
            <div
                style={{
                    display: "flex",
                    justifyContent: "flex-end"
                }}
            >
                <Button
                    loading={isImgExporting || undefined}
                    onClick={() => {
                        scrollTop();
                        setIsImgExporting(true);
                        exportImageHandler(
                            document.getElementById("property-refs"),
                            EXPORT_FILE_NAME.propertyRef
                        )
                            .then(res => {
                                console.log(res);
                            })
                            .catch(err => {
                                console.log(err);
                            })
                            .finally(() => {
                                setIsImgExporting(false);
                            });
                    }}
                    color={"blue"}
                >
                    <FormattedMessage
                        id={"ontology.exportPropRefs"}
                        defaultMessage={"Export Porperty Refs (.png)"}
                    />
                </Button>
                <Button
                    onClick={() => {
                        exportTCsv(
                            PROP_REF_HEAD,
                            propertyRef,
                            EXPORT_FILE_NAME.propertyRef,
                            "tsv"
                        );
                    }}
                    color={"teal"}
                >
                    <FormattedMessage
                        id={"ontology.exportPropRefsTsv"}
                        defaultMessage={"Export Porperty Refs (.tsv)"}
                    />
                </Button>
            </div>
            <Divider hidden />
            <div id={"property-refs"}>
                <PropertyRef propertyRef={propertyRef} />
            </div>
        </React.Fragment>
    );
};

export default PropRefDisplay;
