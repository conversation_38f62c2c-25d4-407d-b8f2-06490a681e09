import React, { useContext } from "react";

import { IconButton } from "@material-ui/core";

import LoopIcon from "@material-ui/icons/Loop";

import { makeStyles } from "@material-ui/core/styles";

import { StoreContext } from "../../../../../../store/StoreProvider";

import act from "../../../../../../store/actions";

const useStyles = makeStyles(theme => ({
    iconButton: {
        padding: theme.spacing(1)
    }
}));

const CusReset = () => {
    const classes = useStyles();
    const [, dispatch] = useContext(StoreContext);

    const handleOnClick = async () => {
        dispatch({ type: act.SET_SEARCHPAGE_SEARCHBAR_RESET });
    };

    return (
        <IconButton
            type="submit"
            className={classes.iconButton}
            onClick={handleOnClick}
        >
            <LoopIcon />
        </IconButton>
    );
};

export default CusReset;
