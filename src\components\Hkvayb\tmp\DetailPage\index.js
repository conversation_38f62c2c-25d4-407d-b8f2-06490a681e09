import React from "react";

// semantic ui
import { Container } from "semantic-ui-react";

// RWD Layout
import { ResponsiveContainer } from "../../../../layout/Layout";

// custom component
import CusDetailPage from "./CusDetailPage";

const index = props => {
    return (
        <ResponsiveContainer {...props}>
            <Container style={{ paddingBottom: "100px", width: "100%" }}>
                <CusDetailPage {...props} />
            </Container>
        </ResponsiveContainer>
    );
};

export default index;
