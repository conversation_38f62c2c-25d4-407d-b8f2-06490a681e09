import React, { useContext } from "react";
import {
    createMuiTheme,
    ThemeProvider,
    makeStyles
} from "@material-ui/core/styles";
import Chip from "@material-ui/core/Chip";
import { teal, red } from "@material-ui/core/colors";
import CloseIcon from "@material-ui/icons/Close";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";
const theme = createMuiTheme({
    palette: {
        primary: teal,
        secondary: { main: red[500] }
    }
});
const useStyles = makeStyles(theme => ({
    root: {
        display: "flex",
        justifyContent: "center",
        flexWrap: "wrap",
        listStyle: "none",
        padding: theme.spacing(0.5),
        margin: 0
    },
    chip: {
        margin: theme.spacing(0.5),
        fontSize: "1rem",
        fontWeight: "600"
    }
}));

const Filter = () => {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { label, labelTranslate } = state.searchPage;
    const handleDelete = name => {
        dispatch({
            type: act.SET_LABEL,
            payload: { ...label, [name]: false }
        });
        dispatch({
            type: act.MINUS_COUNT
        });
        dispatch({
            type: act.SET_CHECKSELECTALL,
            payload: false
        });
        console.info(name);
    };

    const Labelfield = props => {
        const { item } = props;
        const keysArray = Object.keys(item);
        const valuesArray = Object.values(item);

        const data = valuesArray.map((element, index) => {
            if (element === true) {
                return (
                    <Chip
                        key={index}
                        color="primary"
                        variant="outlined"
                        label={labelTranslate[keysArray[index]]}
                        className={classes.chip}
                        deleteIcon={<CloseIcon />}
                        onDelete={() => handleDelete(keysArray[index])}
                    />
                );
            }
            return null;
        });
        return <div className={classes.root}>{data}</div>;
    };

    return (
        <ThemeProvider theme={theme}>
            <Labelfield item={label} />
        </ThemeProvider>
    );
};
export default Filter;
