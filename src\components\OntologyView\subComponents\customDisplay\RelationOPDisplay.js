import React, { useContext, useState, useEffect } from "react";
import { <PERSON><PERSON>, Divider } from "semantic-ui-react";
import { RelationOP } from "../subComponents";
import {
    exportImageHandler,
    exportTCsv,
    EXPORT_FILE_NAME
} from "../../ontoUtils/ontoCommon";
import { FormattedMessage } from "react-intl";
import { StoreContext } from "../../../../store/StoreProvider";
import { scrollTop } from "./utils";

const LOCALE_INDEX = {
    zh: ["zh", "zh-hans", "zh-hant"],
    en: ["en"]
};

const getLocaleAbbrev = (locale, localeIndex) => {
    let localeAbbrev = "en";
    if (!locale) return localeAbbrev;
    Object.keys(localeIndex).forEach(key => {
        if (localeIndex[key].indexOf(locale) >= 0) {
            localeAbbrev = key;
        }
    });
    return localeAbbrev;
};

const HEADERS = {
    op: {
        key: "op",
        name: "property",
        display: true
    },
    subPropertyOf: {
        key: "subPropertyOf",
        name: "subPropertyOf",
        display: true
    },
    label: {
        key: "label",
        name: "label",
        display: true
    },
    comment: {
        key: "comment",
        name: "comment",
        display: true
    },
    domain: {
        key: "domain",
        name: "domain",
        display: true
    },
    range: {
        key: "range",
        name: "range",
        display: true
    }
};

// 匯出 tsv 需要用到的 headers
const headersForTsv = Object.values(HEADERS)
    .filter(h => h.display)
    .map(item => item.key);

const RelationOPDisplay = ({ relationOP }) => {
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    // local state
    const [relationOpLocale, setRelationOpLocale] = useState(null);
    const [isImgExporting, setIsImgExporting] = useState(false);

    useEffect(() => {
        const localeAbbrev = getLocaleAbbrev(locale, LOCALE_INDEX);
        if (!Array.isArray(relationOP)) return;
        if (!locale) return;
        const _relOpLocale = relationOP.filter(op => op.lang === localeAbbrev);
        setRelationOpLocale(_relOpLocale);
    }, [locale, relationOP]);

    return (
        <React.Fragment>
            <div
                style={{
                    display: "flex",
                    justifyContent: "flex-end"
                }}
            >
                <Button
                    loading={isImgExporting || undefined}
                    onClick={() => {
                        // 必須先 scroll top 再匯出, 才不會被截圖
                        scrollTop();
                        setIsImgExporting(true);
                        exportImageHandler(
                            document.getElementById("relation-op"),
                            EXPORT_FILE_NAME.relationOP
                        )
                            .then(res => {
                                console.log(res);
                            })
                            .catch(err => {
                                console.log(err);
                            })
                            .finally(() => {
                                setIsImgExporting(false);
                            });
                    }}
                    color={"blue"}
                >
                    <FormattedMessage
                        id={"ontology.exportRelationOP"}
                        defaultMessage={"Export Relation property (.png)"}
                    />
                </Button>

                {/* button => export csv file */}
                <Button
                    onClick={() => {
                        exportTCsv(
                            headersForTsv,
                            relationOpLocale,
                            EXPORT_FILE_NAME.relationOP,
                            "tsv"
                        );
                    }}
                    color={"teal"}
                >
                    <FormattedMessage
                        id={"ontology.exportRelationOPTsv"}
                        defaultMessage={"Export Relation property (.tsv)"}
                    />
                </Button>
            </div>
            <Divider hidden />
            <div id={"relation-op"} style={{ padding: "10px" }}>
                <RelationOP relationOP={relationOpLocale} headers={HEADERS} />
            </div>
        </React.Fragment>
    );
};

export default RelationOPDisplay;
