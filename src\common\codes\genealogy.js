const genderObj = { Male: "M", Female: "F" };
const getGender = genderName => {
    let gender = "";
    if (genderName === "男") {
        gender = genderObj.Male;
    } else if (genderName === "女") {
        gender = genderObj.Female;
    }
    return gender;
};

const getPersonId = (name, personObj) =>
    Object.keys(personObj).indexOf(name) + 1;

const replaceSpacesInKeys = obj => {
    // console.log("Original object keys:", Object.keys(obj));
    const updatedObject = {};
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            // 去除鍵前後的空格，並替換中間的空格為下劃線
            const cleanKey = key.trim().replace(/ /g, "_");
            updatedObject[cleanKey] = obj[key];
        }
    }
    // console.log("Updated object keys:", Object.keys(updatedObject));
    return updatedObject;
};

// 處理名字中的空格，將空格替換為下劃線(有英文名稱，因此要以_取代)
const processName = name => {
    if (!name) return "";
    return name.trim().replace(/ /g, "_");
};

const personGedcom = (name, personObj, familyArr) => {
    // 確保名字中的空格被替換為下劃線
    const cleanName = name ? processName(name) : "";

    let gedcomStr = "";
    const newPersonObj = replaceSpacesInKeys(personObj);
    // console.log("In personGedcom, name:", cleanName);
    // console.log("Keys in newPersonObj:", Object.keys(newPersonObj));

    const personId = getPersonId(cleanName, newPersonObj);
    if (personId === 0) {
        // the name doesn't exist in the personObj.
        console.log(`Name "${cleanName}" not found in personObj`);
        return gedcomStr;
    }

    const { gender, adopted } = newPersonObj[cleanName];
    const adoptedStr = adopted
        ? adopted.length === 0
            ? ""
            : `（${adopted}）`
        : "";

    gedcomStr += `0 @I${personId}@ INDI\n`;
    gedcomStr += `1 NAME ${cleanName}${adoptedStr}\n`;
    // nameId
    gedcomStr += `1 NOTE NAMEID=${cleanName}\n`;
    if (gender && gender.length > 0) {
        gedcomStr += `1 SEX ${gender}\n`;
    }

    // FAMS, has how many families.
    const fams = familyArr.map((fam, famId) => {
        if (fam.HUSB === personId || fam.WIFE === personId) {
            return famId;
        }
        return -1;
    });
    fams.forEach(fm => {
        if (fm >= 0) {
            gedcomStr += `1 FAMS @F${fm}@\n`;
        }
    });

    // FAMC, the family comes from.
    const famc = familyArr.map((fam, famId) => {
        if (fam.CHIL && fam.CHIL.find(child => child.id === personId)) {
            return famId;
        }
        return -1;
    });
    famc.forEach(fm => {
        if (fm >= 0) {
            gedcomStr += `1 FAMC @F${fm}@\n`;
        }
    });
    return gedcomStr;
};

const familyGedcom = familyArr => {
    let gedcomStr = "";
    familyArr.forEach((fam, famId) => {
        const { HUSB, WIFE, CHIL } = fam;

        gedcomStr += `0 @F${famId}@ FAM\n`;
        if (HUSB >= 0) {
            gedcomStr += `1 HUSB @I${HUSB}@\n`;
        }
        if (WIFE >= 0) {
            gedcomStr += `1 WIFE @I${WIFE}@\n`;
        }

        if (CHIL) {
            CHIL.forEach(cid => {
                gedcomStr += `1 CHIL @I${cid.id}@\n`;
            });
        }
    });
    return gedcomStr;
};
// {
// relationOP: "hasSon",
// sGender: "男",
// sPerson: "王一賢",
// tGender: "男",
// tPerson: "王雅萱"
// }
const gedcomize = (data, rootName) => {
    // console.log("Original data:", data);

    // 檢查帶空格的名字(debug用)
    // const namesWithSpaces = data.filter(
    //     d => d.sPerson.trim() !== d.sPerson || d.tPerson.trim() !== d.tPerson
    // );
    // if (namesWithSpaces.length > 0) {
    //     console.log("Names with spaces:", namesWithSpaces);
    // }

    const spouseList = ["hasWife", "hasHusband"];
    const childList = [
        "hasSon",
        "hasDaughter",
        "hasFirstSon",
        "hasFirstDaughter",
        "hasAdoptedDaughter",
        "hasAdoptedSon",
        "hasSonInLaw",
        "hasAdoptedRelativeSon",
        "hasShuSon"
    ];
    // const brotherSisterList = ["hasBrother", "hasBrotherInLaw"];
    const parentList = ["hasFather", "hasMother", "hasFatherInLaw"];
    const adoptedObj = {
        // hasAdoptedDaughter: "養女",
        // hasAdoptedSon: "養子",
        // hasSonInLaw: "女婿",
        // hasFatherInLaw: "岳父",
        // hasAdoptedRelativeSon: "過繼子",
        // hasShuSon: "庶子"
    };

    // 預處理數據，將所有人名中的空格替換為下劃線
    const cleanedData = data.map(d => ({
        ...d,
        sPerson: processName(d.sPerson),
        tPerson: processName(d.tPerson)
    }));

    // 取 人物 unique id
    const personObj = {};
    cleanedData.forEach(d => {
        const { sPerson, sGender, relationOP, tPerson, tGender } = d;
        const adopted =
            Object.keys(adoptedObj).indexOf(relationOP) < 0
                ? ""
                : adoptedObj[relationOP];

        if (Object.keys(personObj).indexOf(sPerson) < 0) {
            personObj[sPerson] = { gender: getGender(sGender), adopted: "" };
        }
        if (Object.keys(personObj).indexOf(tPerson) < 0) {
            personObj[tPerson] = { gender: getGender(tGender), adopted };
        }
    });

    // console.log("Person object:", personObj);

    // 確保 rootName 也處理空格
    const cleanRootName = processName(rootName);

    // hasWife, hasHusband 放前面
    cleanedData.sort((a, b) =>
        spouseList.indexOf(a.relationOP) > spouseList.indexOf(b.relationOP)
            ? -1
            : 1
    );

    // 創建父母關係映射
    const parentChildMap = new Map();

    // 先處理父母關係
    cleanedData.forEach(d => {
        const { sPerson, relationOP, tPerson } = d;

        if (parentList.indexOf(relationOP) >= 0) {
            const child = sPerson;
            const parent = tPerson;

            if (!parentChildMap.has(child)) {
                parentChildMap.set(child, { father: null, mother: null });
            }

            if (relationOP === "hasFather") {
                parentChildMap.get(child).father = parent;
            } else if (relationOP === "hasMother") {
                parentChildMap.get(child).mother = parent;
            }
        }
    });

    // 取 family pair
    // {'HUSB':'xxx', 'WIFE':'ooo', 'CHIL':['aaa', 'bbb', 'ccc']}
    const familyArr = [];

    // 先創建父母家庭
    parentChildMap.forEach((parents, child) => {
        const { father, mother } = parents;
        if (father || mother) {
            const childId = getPersonId(child, personObj);
            const fatherId = father ? getPersonId(father, personObj) : -1;
            const motherId = mother ? getPersonId(mother, personObj) : -1;

            // 檢查是否已存在包含這對父母的家庭
            const existingFamily = familyArr.find(
                fam =>
                    (fam.HUSB === fatherId && fam.WIFE === motherId) ||
                    (fam.HUSB === fatherId && motherId === -1) ||
                    (fam.WIFE === motherId && fatherId === -1)
            );

            if (existingFamily) {
                // 將子女加到現有家庭
                if (!existingFamily.CHIL.some(c => c.id === childId)) {
                    existingFamily.CHIL.push({ id: childId, type: "" });
                }

                // 更新父母資料
                if (fatherId !== -1) existingFamily.HUSB = fatherId;
                if (motherId !== -1) existingFamily.WIFE = motherId;
            } else {
                // 創建新家庭
                familyArr.push({
                    HUSB: fatherId,
                    WIFE: motherId,
                    CHIL: [{ id: childId, type: "" }]
                });
            }
        }
    });

    // 處理其他關係
    cleanedData.forEach(d => {
        const { sPerson, relationOP, tPerson } = d;

        const sId = getPersonId(sPerson, personObj);
        const sGender = personObj[sPerson].gender;
        const tId = getPersonId(tPerson, personObj);
        const tGender = personObj[tPerson].gender;

        // 處理配偶關係
        if (spouseList.indexOf(relationOP) >= 0) {
            let husband = -1;
            let wife = -1;

            if (relationOP === "hasWife") {
                husband = sId;
                wife = tId;
            } else if (relationOP === "hasHusband") {
                husband = tId;
                wife = sId;
            }

            // 檢查是否已存在包含這對夫妻的家庭
            const foundPair = familyArr.find(fam => {
                if (
                    (fam.HUSB === husband && fam.WIFE === wife) ||
                    (fam.HUSB === husband && fam.WIFE === -1) ||
                    (fam.HUSB === -1 && fam.WIFE === wife)
                ) {
                    return true;
                }
                return false;
            });

            if (foundPair) {
                // 更新現有家庭
                foundPair.HUSB = husband;
                foundPair.WIFE = wife;
            } else {
                // 創建新家庭
                familyArr.push({ HUSB: husband, WIFE: wife, CHIL: [] });
            }
        }
        // 處理子女關係（除了父母關係外的）
        else if (childList.indexOf(relationOP) >= 0) {
            let parent = sId;
            let parentGender = sGender;
            let child = tId;
            const adopted =
                Object.keys(adoptedObj).indexOf(relationOP) < 0
                    ? ""
                    : adoptedObj[relationOP];

            // 檢查是否已存在包含該父母的家庭
            const foundPair = familyArr.find(
                fam => fam.HUSB === parent || fam.WIFE === parent
            );

            const childObj = { id: child, type: adopted };
            if (!foundPair) {
                // 創建新家庭
                if (parentGender === genderObj.Male) {
                    familyArr.push({
                        HUSB: parent,
                        WIFE: -1,
                        CHIL: [childObj]
                    });
                } else {
                    familyArr.push({
                        HUSB: -1,
                        WIFE: parent,
                        CHIL: [childObj]
                    });
                }
            } else {
                // 將子女加到現有家庭
                if (!foundPair.CHIL.some(c => c.id === childObj.id)) {
                    foundPair.CHIL.push(childObj);
                }
            }
        }
    });
    // // hasBrother, hasSister
    // familyArr.forEach(fa => {
    //     const { CHIL } = fa;
    //
    //     data.forEach(d => {
    //         const { sPerson, relationOP, tPerson } = d;
    //
    //         const sId = Object.keys(personObj).indexOf(sPerson);
    //         const tId = Object.keys(personObj).indexOf(tPerson);
    //
    //         if (brotherSisterList.indexOf(relationOP) >= 0) {
    //             const foundChil = CHIL.find(c => c.id === sId || c.id === tId);
    //             if (foundChil) {
    //                 const bschild1 = { id: sId, type: "" };
    //                 const bschild2 = { id: tId, type: "" };
    //
    //                 if (CHIL.indexOf(bschild1) < 0) {
    //                     CHIL.push(bschild1);
    //                 }
    //                 if (CHIL.indexOf(bschild2) < 0) {
    //                     CHIL.push(bschild2);
    //                 }
    //             }
    //         }
    //     });
    // });

    // #20210805#, Vincent, Bug Fixed: Duplicated items in Genealogy, for instance, 尹振雄.
    const uniqueFamilyArr = familyArr.reduce((acc, current) => {
        const x = acc.find(
            item => item.HUSB === current.HUSB && item.WIFE === current.WIFE
        );
        if (!x) {
            return acc.concat([current]);
        } else {
            // 合併子女列表
            x.CHIL = [
                ...new Set(
                    [...x.CHIL, ...current.CHIL].map(c => JSON.stringify(c))
                )
            ].map(c => JSON.parse(c));
            return acc;
        }
    }, []);
    // 將選擇的人名放最上面，才能自動跳轉到該人名。
    let gedcomStr = "";
    gedcomStr += personGedcom(cleanRootName, personObj, uniqueFamilyArr);

    // 該人名不在列表裡，回傳該人物結果
    if (gedcomStr === "") {
        console.log(`Root name "${cleanRootName}" not found in personObj`);
        return `0 @I0@ INDI
1 NAME ${cleanRootName}
1 NOTE NAMEID=${cleanRootName}`;
    }

    // data are well prepared. let's generate data for Gedcom.
    // Generate personId list
    const personKeys = Object.keys(personObj);
    // console.log("Person keys before generating GEDCOM:", personKeys);

    personKeys.forEach(name => {
        if (name === cleanRootName) {
            return;
        }
        const personGedcomStr = personGedcom(name, personObj, uniqueFamilyArr);
        if (personGedcomStr === "") {
            console.log(`Warning: Could not generate GEDCOM for "${name}"`);
        } else {
            gedcomStr += personGedcomStr;
        }
    });

    // Generate family list
    gedcomStr += familyGedcom(uniqueFamilyArr);

    return gedcomStr;
};

export { gedcomize };
