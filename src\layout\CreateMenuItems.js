import React, { useContext, useState, useEffect } from "react";
import { MenuItem } from "./MenuItem";
import { StoreContext } from "../store/StoreProvider";
import { Api } from "../api/hkbdb/Api";
import { isPermitting } from "../App-header";

const CreateMenuItems = ({
    intl,
    activeItem,
    onMenuItemClick,
    mobile,
    webStyle,
    menuItems
}) => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const { locale, role } = user;
    const [localLocale, setLocalLocale] = useState(
        Api.locale_lang.LOCALE_DEFAULT
    );

    useEffect(() => {
        if (locale) {
            setLocalLocale(locale);
        }
    }, [locale]);

    return (
        <React.Fragment>
            {menuItems &&
                menuItems.map(menu => (
                    <MenuItem
                        hasPermit={
                            isPermitting(role, menu.authority) && menu.public
                        }
                        name={`${menu.path.replace(":locale", localLocale)}`}
                        key={menu.id}
                        label={menu.labelIntl18}
                        activeItem={activeItem}
                        onClick={onMenuItemClick}
                        mobile={mobile}
                        webStyle={webStyle}
                    />
                ))}
        </React.Fragment>
    );
};

export default CreateMenuItems;
