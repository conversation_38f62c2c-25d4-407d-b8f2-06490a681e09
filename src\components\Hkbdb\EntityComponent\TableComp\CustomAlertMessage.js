import React, { Fragment, useState, useEffect } from "react";

// ui
import { Message } from "semantic-ui-react";

const CustomAlertMessage = ({ alertMsg, setAlertMsg }) => {
    const { title, content, type, renderSignal, ttl } = alertMsg;
    //
    const [open, setOpen] = useState(false);
    // ttl
    // const ttl = 3 * 1000;
    //
    const CustomMessage = ({ ...rest }) => {
        const handleClose = () => {
            setOpen(false);
        };
        return (
            <Message
                {...rest}
                // style={customMsgStyle}
                header={title}
                content={content}
                onDismiss={handleClose}
            />
        );
    };
    //
    const SwitchMessage = () => {
        switch (type) {
            case "info":
                return <CustomMessage info />;
            case "success":
                return <CustomMessage success />;
            case "warning":
                return <CustomMessage warning />;
            case "error":
                return <CustomMessage error />;
            default:
                return <CustomMessage />;
        }
    };
    useEffect(
        () => {
            if (title && content) {
                // open
                setOpen(true);
                // Set debouncedValue to value (passed in) after the specified delay
                const handler = setTimeout(() => {
                    // close
                    setOpen(false);
                    //
                    setAlertMsg(prevMsg => ({
                        // 不變更 renderSignal 將其他參數初始化
                        // 否則會再次觸發 useEffect 效果
                        ...prevMsg,
                        title: "",
                        content: "",
                        type: ""
                    }));
                }, ttl);
                return () => {
                    clearTimeout(handler);
                };
            }
        },
        // Only re-call effect if value changes
        [renderSignal]
    );
    //
    return <Fragment>{open ? <SwitchMessage /> : null}</Fragment>;
};

export default CustomAlertMessage;
