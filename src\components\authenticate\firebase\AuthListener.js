import { useContext, useEffect, useState, useCallback } from "react";

// store
import { StoreContext } from "../../../store/StoreProvider";
import act from "../../../store/actions";

// commons code
import { getFormatUser, isEmpty } from "../../../common/codes";

// firebase
import { getUser, setUser } from "./realtimeDatabase";

import role from "../../../App-role";
import { Api } from "../../../api/hkbdb/Api";

const AuthListener = ({ firebaseAuth }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { main, user } = state;
    const { production, refreshAnonymousToken } = main;
    const { role: userRole } = user;

    useEffect(() => {
        // 不要儲存 token 在 localStorage
        localStorage.removeItem("token");
    }, []);

    useEffect(() => {
        if (!firebaseAuth) {
            return;
        }

        // observe for signin and signout
        firebaseAuth.onAuthStateChanged(async user => {
            // 若 firebase 開啟匿名登入, 則 user 有資料, 反之, user 為 null
            if (user) {
                const userInfo = getFormatUser(user);
                const { uid, displayName, email } = userInfo;

                // 若 firebase 開啟匿名登入, 未登入狀態,
                // user.isAnonymous 為 true. userInfo 會帶 token, token 用於 API authorization
                if (user.isAnonymous) {
                    dispatch({
                        type: act.FIREBASE_LOGIN_USER,
                        payload: { currentUser: user, ...userInfo }
                    });
                } else if (uid && (displayName || email)) {
                    dispatch({
                        type: act.FIREBASE_LOGIN_USER,
                        payload: { currentUser: user, ...userInfo }
                    });
                }
                // localStorage
                localStorage.setItem("isLogin", JSON.stringify(true));
                // 若 user 已登入, check realtime DB 是否該使用者的資料, 若沒有, 則將 user 資料寫入
                const data = await getUser(uid);
                if (!user.isAnonymous && isEmpty(data)) {
                    // 新增 role key
                    const _userInfo = { ...userInfo, role: role.anonymous };
                    await setUser(uid, _userInfo);
                }
            } else {
                // 若user 是 null,包括 auth 失效(儲存在瀏覽器indexedDB)、user登出等各種原因
                // 則使用匿名登入,取得 user 資訊,進一步可以取得 token
                localStorage.removeItem("isLogin");
                firebaseAuth
                    .signInAnonymously()
                    .then(() => {
                        console.log("signInAnonymously");
                        // 匿名登入後,會再一次觸發 firebaseAuth.onAuthStateChanged
                        // Signed in..
                    })
                    .catch(error => {
                        var errorCode = error.code;
                        var errorMessage = error.message;
                        // ...
                    });
            }
        });

        const idTokenNextObserver = async user => {
            if (user) {
                // token: expiration time: 60 mins
                const token = await user.getIdToken();
                dispatch({
                    type: act.FIREBASE_USER_TOKEN,
                    payload: token
                });
            }
        };
        const onIdTokenChangeError = () => {
            alert("連線逾時,請重新整理網頁");
        };
        const onIdTokenChangeComplete = () => {
            // do nothing
        };
        // observe for token change
        firebaseAuth.onIdTokenChanged(
            idTokenNextObserver,
            onIdTokenChangeError,
            onIdTokenChangeComplete
        );
    }, [firebaseAuth, dispatch]);

    return null;
};

export default AuthListener;
