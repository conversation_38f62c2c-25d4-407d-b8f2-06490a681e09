import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>rid,
    <PERSON><PERSON>,
    Message,
    Segment
} from "semantic-ui-react";
import { login, sendPasswordResetEmail } from "../services/authentication";
import { defineMessages, FormattedMessage, injectIntl } from "react-intl";
import { ResponsiveContainer } from "../../../layout/Layout";

// type State = {
//     email: string,
//     password: string,
//     error: string | null,
//     message: string | null
// };

defineMessages({
    password: {
        id: "password",
        defaultMessage: "Password"
    },
    passwordResetSent: {
        id: "login.password-reset-sent",
        defaultMessage:
            "Password reset email has been sent, please check your mail box."
    }
});

class LoginPage extends React.Component {
    // formatMessage: ({ id: string, defaultMessage?: string }) => string;

    constructor(props) {
        super(props);
        this.state = {
            email: "",
            password: "",
            error: null,
            message: null
        };
        this.formatMessage = this.props.intl.formatMessage;
    }

    handleEmail(event) {
        this.setState({
            email: event.target.value,
            error: null,
            message: null
        });
    }

    handlePassword(event) {
        this.setState({
            password: event.target.value,
            error: null,
            message: null
        });
    }

    login() {
        login(this.state.email, this.state.password)
            .then(() => {
                this.props.history.push("/");
            })
            .catch(error => {
                this.setState({ error: error.message });
            });
    }

    onPasswordResetEmailSent() {
        this.setState(() => {
            return {
                message: this.formatMessage({ id: "login.password-reset-sent" })
            };
        });
    }

    resetPassword() {
        sendPasswordResetEmail(this.state.email)
            .then(this.onPasswordResetEmailSent.bind(this))
            .catch(error => {
                console.error(error);
                this.setState({ error: error.message });
            });
    }

    render() {
        return (
            <ResponsiveContainer {...this.props}>
                <div className="login-form">
                    <style>{`
                  body > div,
                  body > div > div,
                  body > div > div > div.login-form {
                    height: 100%;
                  }
                `}</style>
                    <Grid
                        textAlign="center"
                        style={{ height: "100%" }}
                        verticalAlign="middle"
                    >
                        <Grid.Column style={{ maxWidth: 450 }}>
                            <Header as="h2" color="teal" textAlign="center">
                                <FormattedMessage
                                    id="login.heading"
                                    defaultMessage="Login to HKBDB"
                                />
                            </Header>
                            <Form size="large">
                                <Segment stacked>
                                    <Form.Input
                                        fluid
                                        icon="mail"
                                        iconPosition="left"
                                        placeholder="E-mail address"
                                        value={this.state.email}
                                        onChange={this.handleEmail.bind(this)}
                                    />
                                    <Form.Input
                                        fluid
                                        icon="lock"
                                        iconPosition="left"
                                        placeholder={this.formatMessage({
                                            id: "password"
                                        })}
                                        type="password"
                                        value={this.state.password}
                                        onChange={this.handlePassword.bind(
                                            this
                                        )}
                                    />
                                    <Button
                                        color="teal"
                                        fluid
                                        size="large"
                                        onClick={this.login.bind(this)}
                                    >
                                        <FormattedMessage id="button.login" defaultMessage="Login" />
                                    </Button>
                                </Segment>
                            </Form>
                            <br />
                            <Button
                                color="teal"
                                fluid
                                size="large"
                                onClick={this.resetPassword.bind(this)}
                                disabled={!this.state.email}
                            >
                                <FormattedMessage
                                    id="login.send-reset-email"
                                    defaultMessage="Send Password Reset Email"
                                />
                            </Button>
                            {this.state.error ? (
                                <Message negative>{this.state.error}</Message>
                            ) : null}
                            {this.state.message ? (
                                <Message info>{this.state.message}</Message>
                            ) : null}
                        </Grid.Column>
                    </Grid>
                </div>
            </ResponsiveContainer>
        );
    }
}

export default injectIntl(LoginPage);
