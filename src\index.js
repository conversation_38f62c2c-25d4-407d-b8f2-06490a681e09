// react
import React from "react";
import ReactD<PERSON> from "react-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { Provider } from "react-redux";

// app
import App from "./App";
import reduxStore from "./reduxStore/store";

// css
// import "semantic-ui-css/semantic.min.css";
import "./style/index.scss";

// store
import StoreProvider from "./store/StoreProvider";

import FirebaseLayer from "./api/firebase/FirebaseLayer";
// import registerServiceWorker from './services/registerServiceWorker';

// I18n lang
import { I18nContextProvider } from "./i18n";

ReactDOM.render(
    <StoreProvider>
        <Provider store={reduxStore}>
            <I18nContextProvider>
                <Router>
                    <FirebaseLayer>
                        <App />
                    </FirebaseLayer>
                </Router>
            </I18nContextProvider>
        </Provider>
    </StoreProvider>,
    document.getElementById("root")
);

// registerServiceWorker();
