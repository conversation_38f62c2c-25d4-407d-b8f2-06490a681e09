import React, { useEffect, useState } from "react";
import { Modal, Button } from "semantic-ui-react";

const WarningModal = ({ isSaved, tempUpdateSuggestInfo, onClose }) => {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const areAllArraysEmpty = obj => {
            if (typeof obj !== "object" || obj === null) {
                return false;
            }
            return Object.values(obj).every(
                value => Array.isArray(value) && value.length === 0
            );
        };

        // 沒新增過suggestions，則不須跳出警示訊息
        const isEmptySuggestions = areAllArraysEmpty(tempUpdateSuggestInfo);
        if (isEmptySuggestions) return;

        const handleBeforeUnload = e => {
            if (!isSaved) {
                // 顯示modal而不是瀏覽器的預設提示
                e.preventDefault();
                setIsOpen(true);
                // 這行確保瀏覽器會顯示確認對話框，直到用戶在modal中做出選擇
                e.returnValue = "";
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, [isSaved, tempUpdateSuggestInfo]);

    const handleConfirm = () => {
        setIsOpen(false);
        window.close();
    };

    const handleCancel = () => {
        setIsOpen(false);
    };

    return (
        <Modal open={isOpen} onClose={handleCancel} size="tiny">
            <Modal.Header>警告</Modal.Header>
            <Modal.Content>
                <p>尚未提交，是否依舊關閉視窗？</p>
            </Modal.Content>
            <Modal.Actions>
                <Button variant="outline" onClick={handleCancel}>
                    取消
                </Button>
                <Button onClick={handleConfirm}>確定</Button>
            </Modal.Actions>
        </Modal>
    );
};

export default WarningModal;
