function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $& means the whole matched string
}

// 適用 SPARQL 語法中 FILTER(REGEX(?name, '''[keyword]'''))
// e.g. 搜尋"柯靈|陳蝶衣" => 柯靈\\\\|陳蝶衣
// SPARQL 語法 => FILTER (REGEX(?personName, '''柯靈\\\\|陳蝶衣''', "i"))
// result => 可已找到 "柯靈|陳蝶衣" 這個 instance
function escapeRegExpKeyword(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\\\$&"); // $& means the whole matched string
}

export { escapeRegExp, escapeRegExpKeyword };
