import { removePrefix } from "./rdf";
import { Api } from "../api/hkbdb/Api";
import { bs64Encode } from "../common/codes";
import { CLASS_NAME } from "../config/config-ontology";
const _ = require("lodash");

export function checkDateValid(date) {
    const dmy = date.match(/([0-9]*?)\/([0-9]*?)\/([0-9]*)/);
    if (
        !dmy ||
        !dmy[1] ||
        dmy[1].length !== 2 ||
        !dmy[2] ||
        dmy[2].length !== 2 ||
        !dmy[3] ||
        dmy[3].length !== 4
    ) {
        return false;
    }

    const day = parseInt(dmy[1], 10);
    if (day !== 0 && (day < 0 || day > 31)) {
        return false;
    }

    const month = parseInt(dmy[2], 10);
    if (month !== 0 && (month < 0 || month > 13)) {
        return false;
    }

    const year = parseInt(dmy[3], 10);
    if (year < 0) {
        return false;
    }
    return true;
}

function getElementValue(obj, elm, dft) {
    return obj[elm] ? removePrefix(obj[elm].value) : dft;
}

function getDayMonthYear(obj, d, m, y) {
    const year = getElementValue(obj, y, "0000");
    const month = getElementValue(obj, m, "00");
    const day = getElementValue(obj, d, "00");
    return `${day}/${month}/${year}`;
}

function createElement(b, xmlLang, value, op) {
    const graph = removePrefix(b["graph"].value);
    return {
        graph,
        op,
        xmlLang,
        value
    };
}

// export function addToMap(vmap, basic, b) {
//     const pdt = basic.predicate;
//     const { birth, death } = basicElementList;
//
//     if (!b[pdt]) {
//         return;
//     }
//
//     const xmlLang = b[pdt]["xml:lang"] ? b[pdt]["xml:lang"] : null;
//     if (pdt === birth.predicate || pdt === death.predicate) {
//         let dayMonthYear = "";
//         if (pdt === birth.predicate) {
//             dayMonthYear = getDayMonthYear(b, "bday", "bmonth", "byear");
//         } else {
//             dayMonthYear = getDayMonthYear(b, "dday", "dmonth", "dyear");
//         }
//
//         if (vmap[pdt]) {
//             vmap[pdt].values.push(
//                 createElement(b, xmlLang, dayMonthYear, b[pdt].value)
//             );
//         } else {
//             vmap[pdt] = {
//                 title: basic.title,
//                 values: [createElement(b, xmlLang, dayMonthYear, b[pdt].value)]
//             };
//         }
//     } else {
//         if (vmap[pdt]) {
//             vmap[pdt].values.push(
//                 createElement(
//                     b,
//                     xmlLang,
//                     removePrefix(b[pdt].value, b[pdt].value),
//                     b[pdt].value
//                 )
//             );
//         } else {
//             vmap[pdt] = {
//                 title: basic.title,
//                 values: [
//                     createElement(
//                         b,
//                         xmlLang,
//                         removePrefix(b[pdt].value, b[pdt].value),
//                         b[pdt].value
//                     )
//                 ]
//             };
//         }
//     }
// }

export function addGraphs(bvars, bindings, ignoreCompare) {
    for (let idx = 0; idx < bindings.length; idx++) {
        const curBinding = bindings[idx];
        let graphs = [curBinding.graph];

        if (curBinding.graphs === null) {
            continue;
        }

        for (let nxtIdx = idx + 1; nxtIdx < bindings.length; nxtIdx++) {
            let notTheSame = false;
            const nxtBinding = bindings[nxtIdx];

            for (let vidx = 0; vidx < bvars.length; vidx++) {
                const op = bvars[vidx];
                if (ignoreCompare.includes(op)) {
                    continue;
                }

                if (curBinding[op] && !nxtBinding[op]) {
                    notTheSame = true;
                    break;
                }
                if (!curBinding[op] && nxtBinding[op]) {
                    notTheSame = true;
                    break;
                }
                if (
                    curBinding[op] &&
                    nxtBinding[op] &&
                    curBinding[op].value !== nxtBinding[op].value
                ) {
                    notTheSame = true;
                    break;
                }
            }

            if (!notTheSame) {
                nxtBinding.graphs = null;
                graphs.push(nxtBinding.graph);
            }
        }
        curBinding.graphs = graphs;
    }
}

export function getValue(INvar) {
    if (INvar && INvar.value) {
        return INvar.value;
    }
    return "";
}

export function isAddTriple(oldBinding) {
    let badd = true;
    Object.keys(oldBinding).forEach(key => {
        if (key === "graph") {
            return;
        }
        if (oldBinding[key] && oldBinding[key].length !== 0) {
            badd = false;
        }
    });
    return badd;
}

export function getBindingName(oldBinding) {
    let newBinding = {};
    Object.keys(oldBinding).forEach(k => {
        if (k !== "graph" && oldBinding[k]) {
            newBinding[k] = removePrefix(oldBinding[k]);
        } else {
            newBinding[k] = oldBinding[k];
        }
    });
    return newBinding;
}

export function isIncludedInArray(objArray, value) {
    return objArray.find(nb => {
        return nb.value === value;
    });
}

export function newOption(value) {
    return {
        key: value,
        text: value,
        value: value
    };
}

export function getEntityStr(oriEntities, newEntity) {
    let addNewEntity = "";
    if (!isIncludedInArray(oriEntities, newEntity)) {
        addNewEntity = newEntity;
    }
    return addNewEntity;
}

export function isObjEqual(oldBinding, newBinding) {
    const bindingWoPrefix = getBindingName(oldBinding);
    return _.isEqual(bindingWoPrefix, newBinding);
}

// check if word is base64
export function isBase64(word) {
    const base64regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;
    return base64regex.test(word);
}

/**
 *
 * @param name {string} id(移除 prefix)
 * @param type {string} e.g. Person, Organization
 * @returns {string}
 */
export function browseUrl(name, type) {
    const _name = name || "";
    const locale = Api.getLocale();
    let url = "";
    if (_name === "") {
        url = `/${locale}/browse`;
    } else {
        // 若 name 尚未 encode,則 encode 之
        const encodedName = bs64Encode(_name || "");
        url = `/${locale}/browse?name=${encodedName}&type=${type}`;
    }
    return url;
}

export function browsePerUrl(name, type) {
    return browseUrl(name, CLASS_NAME.Person);
}

export function browseOrgUrl(name, type) {
    return browseUrl(name, CLASS_NAME.Organization);
}

/**
 *
 * @param name {string} id(移除 prefix)
 * @param queryStr {string}
 * @returns {string}
 */
export function peopleUrl(name, queryStr) {
    const _name = name || "";
    const locale = Api.getLocale();
    let url = "";
    if (_name === "") {
        url = `/${locale}/people`;
    } else {
        // 若 name 尚未 encode,則 encode 之
        const encodedName = bs64Encode(_name || "");
        url = `/${locale}/people/${encodedName}`;
        if (queryStr) {
            url += `?${queryStr}`;
        }
    }
    return url;
}

export function organizationUrl(name, queryStr) {
    const _name = name || "";
    const locale = Api.getLocale();
    let url = "";
    if (_name === "") {
        url = `/${locale}/organization`;
    } else {
        // 若 name 尚未 encode,則 encode 之
        const encodedName = bs64Encode(_name || "");
        url = `/${locale}/organization/${encodedName}`;
        if (queryStr) {
            url += `?${queryStr}`;
        }
    }
    return url;
}

/**
 *
 * @param name {string} id(移除 prefix)
 * @param queryStr {string}
 * @returns {string}
 */
export function orgUrl(name, queryStr) {
    const _name = name || "";
    const locale = Api.getLocale();
    let url = "";
    if (_name === "") {
        url = `/${locale}/organization`;
    } else {
        // 若 name 尚未 encode,則 encode 之
        const encodedName = bs64Encode(_name || "");
        url = `/${locale}/organization/${encodedName}`;
        if (queryStr) {
            url += `?${queryStr}`;
        }
    }
    return url;
}

export function showEmptyWithBnode(value) {
    return value.startsWith("bnode_") ? "" : removePrefix(value);
}
