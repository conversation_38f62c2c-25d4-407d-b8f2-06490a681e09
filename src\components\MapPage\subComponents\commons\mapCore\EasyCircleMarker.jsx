/* eslint-disable no-unused-vars, react/prop-types */
import React, { useEffect, useRef, useState } from "react";
//
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { FormattedMessage } from "react-intl";
// leaflet
import { <PERSON><PERSON>, Popup, CircleMarker, Tooltip } from "react-leaflet"; // <PERSON><PERSON> might complain Mark<PERSON> is unused if only CircleMarker is active
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
//
import EasyMapIcon from "./EasyMapIcon.jsx";
import L from "leaflet";

// config
import { circleStyleDef, mapFilterOptions } from "../config";
import { isInHongKong } from "../MapHelper.jsx";

import "../../../styles/myMap.scss";

const fetchPointIcon = () => {
    return EasyMapIcon;
};

const EasyCircleMarker = ({
    clusterIndex,
    cluster,
    latitude,
    longitude,
    //     clusterPoints,
    //     isLocNameDisplay,
    //     paletteStyle,
    onPopupOpen,
    onPopupClose,
    onClick,
    onTooltipOpen,
    showPopup,
    showTooltip,
    //     pointIconClassName,
    popupElement,
    eventList,
    type,
    isShowingHongKongTextBelow
}) => {
    const ref = useRef(null);
    const { location } = cluster.properties;

    function getInfoTypes(data) {
        if (!Array.isArray(data)) return [];
        const infoTypes = new Set();
        data.forEach(item => {
            if (item.infoType) {
                infoTypes.add(item.infoType);
            }
        });
        return Array.from(infoTypes);
    }

    const infoTypeList = getInfoTypes(eventList);

    return (
        <>
            {/* Visible Marker */}
            <CircleMarker
                ref={ref}
                /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
                // @ts-expect-error
                className="pointMarker"
                key={`cluster-${clusterIndex}-${cluster.properties.locId}-visible`}
                center={L.latLng({ lat: latitude, lng: longitude })}
                eventHandlers={{
                    click: e => {
                        if (isFunction(onClick)) {
                            onClick(cluster, ref);
                        }
                    }
                }}
                stroke={circleStyleDef.stroke}
                radius={circleStyleDef.radius}
                color={circleStyleDef.color}
                weight={circleStyleDef.weight}
                fillColor={circleStyleDef.fillColor}
                fillOpacity={circleStyleDef.fillOpacity}
            >
                {showPopup && popupElement
                    ? popupElement
                    : showPopup && (
                        <Popup
                            eventHandlers={{
                                popupopen: e => {
                                    if (isFunction(onPopupOpen)) {
                                        onPopupOpen(cluster, ref);
                                    }
                                },
                                popupclose: e => {
                                    if (isFunction(onPopupClose)) {
                                        onPopupClose(cluster, ref);
                                    }
                                }
                            }}
                        >
                            <Box>
                                <Typography
                                    variant="subtitle1"
                                    sx={{ marginBottom: 1 }}
                                >
                                    {location}
                                </Typography>
                            </Box>
                        </Popup>
                    )}
                {showTooltip && type === "General" && (
                    <Tooltip
                        permanent
                        direction="center"
                        className="custom-tooltip"
                    >
                        <div style={{ position: "absolute" }}>
                            {eventList?.length}
                        </div>
                        <div
                            style={{
                                display: "flex",
                                gap: "2px",
                                marginTop: "3rem"
                            }}
                        >
                            {infoTypeList.map(infoType => (
                                <div
                                    key={infoType}
                                    style={{
                                        width: "6px",
                                        height: "6px",
                                        borderRadius: "50%",
                                        backgroundColor:
                                            mapFilterOptions[infoType] ||
                                            "black"
                                    }}
                                />
                            ))}
                        </div>
                        <div>
                            {isShowingHongKongTextBelow && (
                                <div
                                    style={{
                                        fontSize: "12px",
                                        marginTop: "2px",
                                        color: "#104860",
                                        fontWeight: "bold"
                                    }}
                                >
                                    (
                                    <FormattedMessage
                                        id={"map.hongkong"}
                                        defaultMessage={"Hong Kong"}
                                    />
                                    )
                                </div>
                            )}
                        </div>
                    </Tooltip>
                )}
            </CircleMarker>

            {/* Invisible marker at the same position for the hover tooltip */}
            <CircleMarker
                key={`cluster-${clusterIndex}-${cluster.properties.locId}-hover`} // Added distinct key
                center={L.latLng({ lat: latitude, lng: longitude })}
                radius={circleStyleDef.radius}
                pathOptions={{
                    stroke: false, // No visible border for the hover area
                    fill: true, // Crucial: It must be considered "filled" to capture mouse events
                    fillColor: "transparent" // Use a completely transparent fill color
                }}
                eventHandlers={{
                    click: e => {
                        e.originalEvent.stopPropagation();
                        if (isFunction(onClick)) {
                            onClick(cluster, ref); // ref still refers to the visible marker's ref
                        }
                    },
                    mouseover: e => {
                        if (isFunction(onTooltipOpen)) {
                            onTooltipOpen(cluster);
                        }
                    }
                }}
            >
                <Tooltip className="hover-tooltip" direction="bottom">
                    {location}
                </Tooltip>
            </CircleMarker>
        </>
    );
};

export default EasyCircleMarker;
