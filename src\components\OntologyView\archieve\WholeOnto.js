import React, { useContext, useEffect, createRef, useState } from "react";
import { Container } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import { getWholeOntology } from "../../services/nmtl-api";
import { UserInfoAct } from "../../reducerStore/actionMethods";

import { DEFAULT_PROP_TYPES, drawOntology } from "../ontoUtils/DrawOntology";
import {
    dataToNodesLinks,
    filterDupTriple,
    ontoHandleChange
} from "../ontoUtils/ontoCommon";
import OntoRadioBox from "../subComponents/OntoRadioBox";

const WholeOnto = () => {
    const { backendApi, userInfo, userInfoDispatch } = useContext(StoreContext);
    const [checked, setChecked] = useState("Both");
    const myRef = createRef();

    useEffect(() => {
        if (userInfo.ontology.hasOwnProperty("Whole")) {
            drawOntology(myRef, userInfo.ontology.Whole);
            return;
        }

        if (userInfo.ontology.hasOwnProperty("download")) {
            const download = userInfo.ontology.download;
            const ontos = dataToNodesLinks(download, []);

            drawOntology(myRef, ontos);

            userInfoDispatch({
                type: UserInfoAct.setOntology,
                payload: { Whole: ontos }
            });
            return;
        }

        getWholeOntology(backendApi, (_ontology, error) => {
            // op, domain, range, inverse, type:ObjectProperty/DatatypeProperty, ontology:db/protege
            if (!error) {
                const _filteredOnto = filterDupTriple(_ontology);
                const ontos = dataToNodesLinks(_filteredOnto, []);

                drawOntology(myRef, ontos);

                userInfoDispatch({
                    type: UserInfoAct.setOntology,
                    payload: { Whole: ontos, download: _filteredOnto }
                });
            }
        });
        // eslint-disable-next-line
    }, [backendApi, userInfo, userInfoDispatch]);

    const handleChange = (e, { value }) => {
        setChecked(value);
        ontoHandleChange(myRef, value);
    };

    return (
        <Container textAlign={"center"}>
            <OntoRadioBox checked={checked} handleChange={handleChange} />
            <div ref={myRef} />
        </Container>
    );
};

// export default WholeOnto;
