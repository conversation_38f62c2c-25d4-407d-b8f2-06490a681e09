// firebase
import firebase from "firebase/app";
import "firebase/database";

import RealTimeListener from "./subLayers/RealTimeListener";
// import firebaseConfig from "../../config/config-firebase";

// firebase.initializeApp(firebaseConfig);

const realTimeDb = firebase.database();

let INIT_ONCE = 0;
const FirebaseLayer = props => {
    if (INIT_ONCE === 0) {
        RealTimeListener({ realTimeDb });

        INIT_ONCE = 1;
    }

    // eslint-disable-next-line react/destructuring-assignment
    return props.children || null;
};

export default FirebaseLayer;
