import Act from "../actions";

const initState = {
    personId: "",
    ontologyOneOfThemData: {},
    inverseRelationData: [],
    isLoading: false,
    renderTarget: "",
    renderSignal: "",
    searchInputs: {},
    paginations: {
        globalMaxRows: 10,
        globalActivePage: 1,
        globalTotalPages: 10
    },
    curOpenTab: "person"
};

const informationReducer = (state = initState, action) => {
    switch (action.type) {
        // PersonId
        case Act.INFORMATION_PERSON_ID_SET:
            return { ...state, personId: action.payload };
        case Act.INFORMATION_PERSON_ID_CEL:
            return { ...state, personId: initState.personId };
        // render signal
        case Act.INFORMATION_DATA_RENDER_SIGNAL_SET:
            return {
                ...state,
                renderTarget: action.payload.target,
                renderSignal: action.payload.signal
            };
        case Act.INFORMATION_DATA_IS_LOADING_SET:
            return {
                ...state,
                isLoading: action.payload
            };
        case Act.INFORMATION_DATA_IS_LOADING_CEL:
            return {
                ...state,
                isLoading: initState.isLoading
            };
        case Act.INFORMATION_INVERSE_RELATION_DATA_SET:
            return {
                ...state,
                inverseRelationData: action.payload
            };
        case Act.INFORMATION_ONTOLOGY_ONE_OF_THEM_DATA_SET:
            return {
                ...state,
                ontologyOneOfThemData: action.payload
            };
        case Act.INFORMATION_SEARCH_INPUTS:
            return {
                ...state,
                searchInputs: {
                    ...state.searchInputs,
                    ...action.payload
                }
            };
        case Act.INFORMATION_PAGINATIONS:
            return {
                ...state,
                paginations: {
                    ...state.paginations,
                    ...action.payload
                }
            };
        case Act.INFORMATION_CURRENTLY_OPEN_TAB:
            return {
                ...state,
                curOpenTab: action.payload
            };
        default:
            return state;
    }
};

export default informationReducer;
