import React, { useContext, useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Button, Menu } from "semantic-ui-react";
import { UserAvatar } from "./UserAvatar";
import { FormattedMessage, injectIntl } from "react-intl";
import { StoreContext } from "../store/StoreProvider";
import { isEmpty } from "../common/codes";

//
// type RightMenuProps = {
//     fixed: boolean,
//     permission: number,
//     location: string,
//     onClick: Function
// };

const RightMenu = ({ intl, fixed, isAnonymous, location, onClick, mobile }) => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const [localLocale, setLocalLocale] = useState(locale);

    useEffect(() => {
        setLocalLocale(locale);
    }, [locale]);

    const loginEle =
        location !== `/${localLocale}/login` ? (
            <Menu.Item
                position="right"
                style={{ height: mobile ? "auto" : "100%" }}
            >
                <Button
                    as={Link}
                    name="login"
                    to={`/${localLocale}/login`}
                    inverted={!fixed}
                    onClick={onClick}
                    style={{
                        color: "#104860",
                        padding: "0"
                    }}
                    secondary
                >
                    <FormattedMessage
                        id="button.login"
                        defaultMessage="Sign in"
                    />
                </Button>
            </Menu.Item>
        ) : null;

    const signUpEle = (
        <Menu.Item style={{ height: mobile ? "auto" : "100%" }}>
            {location !== `/${localLocale}/signup` ? (
                <Button
                    as={Link}
                    name="signup"
                    to={`/${localLocale}/signup`}
                    inverted={!fixed}
                    onClick={onClick}
                    secondary
                >
                    <FormattedMessage
                        id="button.register"
                        defaultMessage="Register"
                    />
                </Button>
            ) : null}
        </Menu.Item>
    );

    return !isAnonymous && !isEmpty(user.uid) ? (
        <UserAvatar intl={intl} />
    ) : (
        <>{loginEle}</>
    );
};

export default injectIntl(RightMenu);
