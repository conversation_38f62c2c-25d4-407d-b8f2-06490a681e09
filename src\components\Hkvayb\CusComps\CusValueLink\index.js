import React, { useContext } from "react";

import useStyles from "./style";

import CusInfoMask from "../CusInfoMask";

import { bs64Encode, isEmpty } from "../../../../common/codes";

import { StoreContext } from "../../../../store/StoreProvider";

const CusValueLink = ({ icon, value, type, style = {} }) => {
    if (isEmpty(value)) {
        return "-";
    }

    const classes = useStyles(style);
    const [state] = useContext(StoreContext);
    const [authorArr, setAuthorArr] = React.useState([]);
    const { user } = state;
    const { locale } = user;
    const authors = `${value}`.split(/[、,]+/);

    const handleUrl = (path, name) => {
        return window.open(`/${locale}/${path}/${bs64Encode(name)}`, "_blank");
    };

    const handleOnClick = name => {
        if (name) {
            switch (type.toLowerCase()) {
                case "person":
                    handleUrl("people", name);
                    break;
                case "organization":
                    handleUrl("organization", name);
                    break;
                default:
            }
        }
    };

    React.useEffect(() => {
        setAuthorArr(
            authors.reduce((prev, author, i) => {
                let commaSymbol =
                    i === authors.length - 1 ? null : (
                        <span key={`span-comma-${author}`}>{", "}</span>
                    );
                return [
                    ...prev,
                    <span
                        key={`span-author-${author}`}
                        className={classes.hkvayb_author}
                        title={author}
                    >
                        {author}
                        {/* <CusInfoMask onClick={() => handleOnClick(author)} /> */}
                    </span>,
                    commaSymbol
                ];
            }, [])
        );
    }, [JSON.stringify(value)]);

    return <div className={classes.hkvayb_author_div}>{authorArr}</div>;
};

export default CusValueLink;
