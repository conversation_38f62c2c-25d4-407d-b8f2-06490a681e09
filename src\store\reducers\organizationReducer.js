import Act from "../actions";
import { getValue } from "../../services/common";
import { removePrefix } from "../../services/rdf";

const initState = {
    name: null,
    searchName: null,
    periods: null,
    persons: null,
    organizations: null,
    positions: null,
    societalTypes: null,
    places: null,
    actions: null,
    graphs: null,
    orgTimeline: null,
    orgSNA: null,
    orgEvolution: null,
    orgClass: null
};

const orgReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.ORG_FETCH_INFO:
            return {
                ...state,
                ...action.payload
            };

        case Act.ORG_FETCH_TIMELINE:
            return {
                ...state,
                orgTimeline: action.payload.orgTimeline
            };
        default:
            return state;
    }
};

export default orgReducer;
