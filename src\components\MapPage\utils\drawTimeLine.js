import * as d3 from "d3";

let canvasWidth = 0;
let canvasHeight = 0;
const margin = { top: 10, left: 5, bottom: 5, right: 20 };
const initialStartYear = 1800;

const drawTimeLine = ({
    years,
    anchorYear,
    startYear,
    endYear,
    timeLineStartYear,
    timeLineEndYear,
    onYearClick,
    onYearHover,
    onBarDrag,
    onStartYearChange,
    onEndYearChange,
    onMouseOnBar,
    onMouseOutBar
}) => {
    // Get canvas dimensions
    const width =
        document.getElementById("canvas").clientWidth -
        margin.left -
        margin.right;
    const height =
        document.getElementById("canvas").clientHeight -
        margin.top -
        margin.bottom;
    canvasWidth = width;
    canvasHeight = height;

    // Initialize or clear SVG canvas
    let svgCanvas = d3
        .select("#canvas")
        .select("svg")
        .style("overflow", "initial");
    if (svgCanvas.node() == null) {
        svgCanvas = d3
            .select("#canvas")
            .append("svg")
            .attr("class", "svgCanvas")
            .attr("width", width)
            .attr("height", height);
    } else {
        svgCanvas.selectAll("*").remove();
    }

    // Timeline constants
    // const timelineStartYear = initialStartYear; // 固定為 1800
    const timelineStartYear = timeLineStartYear;
    // const timelineEndYear = new Date().getFullYear(); // 固定為當前年份
    const timelineEndYear = timeLineEndYear;

    // Create scale
    const xScale = d3
        .scaleLinear()
        .domain([timelineStartYear, timelineEndYear])
        .range([0, width]);

    // Create axis
    const xAxis = d3
        .axisBottom(xScale)
        .tickFormat(d3.format("d"))
        .ticks(Math.floor((timelineEndYear - timelineStartYear) / 20))
        .tickSize(0)
        .tickPadding(15);

    // Draw axis
    const xisG = svgCanvas
        .append("g")
        .attr("class", "axis")
        .attr(
            "transform",
            `translate(${margin.left}, ${margin.top + height / 2})`
        )
        .style("font-size", "12px")
        .call(xAxis);

    // Style axis
    xisG.selectAll("path, line")
        .attr("stroke", "#f3f3f3")
        .attr("stroke-width", 4);
    xisG.selectAll("text").attr("fill", "#000000");

    // Helper function to create drag handlers
    const createDragHandler = (yearType, constraintFn, updateFn) => {
        return d3
            .drag()
            .on("start", function () {
                d3.select(this)
                    .raise()
                    .attr("fill", "#384B70");
            })
            .on("drag", function () {
                const { x } = d3.event;
                const newX = x - margin.left;
                const newYear = Math.round(xScale.invert(newX));

                if (constraintFn(newYear)) {
                    d3.select(this).attr("x", xScale(newYear) - 4);

                    // Special case for anchor dragging
                    if (
                        yearType === "anchor" &&
                        typeof updateFn === "function"
                    ) {
                        d3.select(".year-range-background")
                            .attr("x", xScale(startYear))
                            .attr("width", xScale(newYear) - xScale(startYear));
                    }

                    if (typeof updateFn === "function") {
                        updateFn(newYear);
                    }
                    if (
                        (yearType === "start" && newYear >= anchorYear) ||
                        (yearType === "end" && newYear <= anchorYear)
                    ) {
                        d3.select(".anchor-bar").attr("x", xScale(newYear) - 4);
                        onBarDrag(newYear);
                    }
                }
            })
            .on("end", function () {
                if (yearType !== "anchor") {
                    d3.select(this).attr("fill", "#104860");
                }
            });
    };

    // Define drag handlers
    const dragHandlers = {
        start: createDragHandler(
            "start",
            newYear => newYear >= timelineStartYear && newYear < endYear,
            onStartYearChange
        ),
        end: createDragHandler(
            "end",
            newYear => newYear <= timelineEndYear && newYear > startYear,
            onEndYearChange
        ),
        anchor: createDragHandler(
            "anchor",
            newYear => newYear >= startYear && newYear <= endYear,
            onBarDrag
        )
    };

    // UI constants
    const barWidth = 8;
    const barHeight = 8;
    const boxWidth = 40;
    const boxHeight = 40;
    const anchorX = xScale(anchorYear) + margin.left;
    const anchorY = margin.top + height / 2;
    const cornerRadius = 10;

    // Draw timeline background
    svgCanvas
        .append("rect")
        .attr("class", "year-range-background-bottom")
        .attr("x", xScale(startYear))
        .attr("y", -barHeight / 3)
        .attr(
            "transform",
            `translate(${margin.left}, ${margin.top + height / 2})`
        )
        .attr("width", xScale(endYear) - xScale(startYear))
        .attr("height", 5)
        .attr("fill", "#BDBDBD")
        .attr("rx", 5);

    // Draw filled timeline (to anchor)
    if (anchorYear) {
        svgCanvas
            .append("rect")
            .attr("class", "year-range-background")
            .attr("x", xScale(startYear))
            .attr("y", -barHeight / 3)
            .attr(
                "transform",
                `translate(${margin.left}, ${margin.top + height / 2})`
            )
            .attr("width", xScale(anchorYear) - xScale(startYear))
            .attr("height", 5)
            .attr("fill", "#104860")
            .attr("rx", 5);
    }

    // Helper function to create year markers and labels
    const createYearMarker = (year, className, dragHandler) => {
        // Create the marker point
        svgCanvas
            .append("rect")
            .attr("class", `${className}-bar`)
            .attr("x", xScale(year) - 4)
            .attr("y", -barHeight / 2)
            .attr(
                "transform",
                `translate(${margin.left}, ${margin.top + height / 2})`
            )
            .attr("width", barWidth)
            .attr("height", barHeight)
            .attr("fill", "#104860")
            .attr("rx", 5)
            .style("cursor", "ew-resize")
            .call(dragHandler);

        // Create the year display box (if not an anchor point)
        if (className !== "anchor") {
            // Main box
            svgCanvas
                .append("rect")
                .attr("class", `${className}-box`)
                .attr("x", xScale(year) + boxWidth / 2 - 37)
                .attr("y", anchorY - boxHeight / 2 - 15)
                .attr("width", 45)
                .attr("height", 22.5)
                .attr("rx", cornerRadius)
                .attr("ry", cornerRadius)
                .attr("fill", "#104860")
                .attr("stroke", "#104860");

            // Pointer box
            svgCanvas
                .append("rect")
                .attr("class", `${className}-sub-box`)
                .attr("x", xScale(year) + boxWidth / 2 - 17.5)
                .attr("y", anchorY - boxHeight / 2 + 5)
                .attr("width", 5)
                .attr("height", 5)
                .attr("rx", 5)
                .attr("ry", 5)
                .attr("fill", "#104860")
                .attr("stroke", "#104860");

            // Year text
            svgCanvas
                .append("text")
                .attr("class", `${className}-text`)
                .attr("x", xScale(year) + boxWidth / 2 - 14.5)
                .attr("y", anchorY - boxHeight / 2)
                .attr("text-anchor", "middle")
                .attr("font-size", "12px")
                .attr("fill", "#ffffff")
                .text(year);
        }
    };

    // Create anchor year if years array has data
    if (years.length > 0) {
        const createAnchorTooltip = () => {
            // Main box
            svgCanvas
                .append("rect")
                .attr("class", "anchor-year-box")
                .attr("x", anchorX - 22)
                .attr("y", anchorY - boxHeight / 2 - 15)
                .attr("width", 45)
                .attr("height", 22.5)
                .attr("rx", cornerRadius)
                .attr("ry", cornerRadius)
                .attr("fill", "#104860")
                .attr("stroke", "#104860");

            // Pointer box
            svgCanvas
                .append("rect")
                .attr("class", "anchor-year-sub-box")
                .attr("x", anchorX - 3)
                .attr("y", anchorY - boxHeight / 2 + 5)
                .attr("width", 5)
                .attr("height", 5)
                .attr("rx", 5)
                .attr("ry", 5)
                .attr("fill", "#104860")
                .attr("stroke", "#104860");

            // Year text
            svgCanvas
                .append("text")
                .attr("class", "anchor-year-text")
                .attr("x", anchorX)
                .attr("y", anchorY - boxHeight / 2)
                .attr("text-anchor", "middle")
                .attr("font-size", "12px")
                .attr("fill", "#ffffff")
                .text(anchorYear);
        };

        const removeAnchorTooltip = () => {
            svgCanvas.selectAll(".anchor-year").remove();
            svgCanvas.selectAll(".anchor-year-box").remove();
            svgCanvas.selectAll(".anchor-year-sub-box").remove();
            svgCanvas.selectAll(".anchor-year-text").remove();
        };

        svgCanvas
            .append("rect")
            .attr("class", "anchor-bar")
            .attr("x", xScale(anchorYear) - 4)
            .attr("y", -barHeight / 2)
            .attr(
                "transform",
                `translate(${margin.left}, ${margin.top + height / 2})`
            )
            .attr("width", barWidth)
            .attr("height", barHeight)
            .attr("fill", "#104860")
            .attr("rx", 5)
            .style("cursor", "ew-resize")
            .call(dragHandlers.anchor)
            .on("mouseover", function (event) {
                d3.select(this).attr("fill", "#384B70");
                createAnchorTooltip();
                if (typeof onMouseOnBar === "function") {
                    onMouseOnBar(event);
                }
            })
            .on("mouseout", function (event) {
                removeAnchorTooltip();
                if (typeof onMouseOutBar === "function") {
                    onMouseOutBar(event);
                }
            });
    }

    // Create the year markers, make sure the startYear and endYear markers are created after the anchor bar
    createYearMarker(startYear, "start-year", dragHandlers.start);
    createYearMarker(endYear, "end-year", dragHandlers.end);
};

export default drawTimeLine;
