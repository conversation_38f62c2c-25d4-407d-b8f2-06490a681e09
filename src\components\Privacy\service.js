import { Api, readHkbdbData } from "../../api/hkbdb/Api";

const service = {};

service.getWebConfig = () => {
    return new Promise((resolve, reject) => {
        const url = Api.getWebConfig();
        readHkbdbData(url)
            .then(res => {
                resolve(res.data);
            })
            .catch(err => {
                reject(err);
            });
    });
};

export default service;
