import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusMarkdown from "../CusMarkdown";
import CusDualPages from "../CusDualPages";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const CusExhibition = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const defObj = { type: null, value: [] };

    const bilingFunc = bilingual(defObj);

    const [descZh, descEn] = bilingFunc(data, "desc");
    const [labelZh, labelEn] = bilingFunc(data, "hasAcademicDegree");
    const [postTypeZh, postTypeEn] = bilingFunc(data, "postType");
    const [durationZh, durationEn] = bilingFunc(data, "duration");
    const [educatedAtZh, educatedAtEn] = bilingFunc(data, "hasEducatedAt");
    const [academicDisciplineZh, academicDisciplineEn] = bilingFunc(data, "hasAcademicDiscipline");

    return (
        <div className={classes.hkvayb_exhibition}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.education.postType"
                    defaultMessage="Types of Programmes : "
                />
                <CusValue {...postTypeZh} />
                <CusValue prefix="/" defVal="" {...postTypeEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.education.educatedAt"
                    defaultMessage="Institute(s) : "
                />
                <CusValue {...educatedAtZh} />
                <CusValue prefix="/" defVal="" {...educatedAtEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.education.academicDiscipline"
                    defaultMessage="Department : "
                />
                <CusValue {...academicDisciplineZh} />
                <CusValue prefix="/" defVal="" {...academicDisciplineEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.education.duration"
                    defaultMessage="Duration : "
                />
                <CusValue {...durationZh} />
                <CusValue prefix="/" defVal="" {...durationEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.education.desc"
                    defaultMessage="Program Description : "
                />
            </CusPara>
            <CusDualPages>
                <CusMarkdown value={descZh.value} />
                <CusMarkdown value={descEn.value} />
            </CusDualPages>
        </div>
    );
};

export default CusExhibition;
