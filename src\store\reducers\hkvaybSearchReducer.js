import Act from "../actions";

const initState = {
    fields: {
        keyword: "",
        year: "",
        label: "",
        organizer: "",
        mediaType: "",
        collectibleType: "",
        education: "",
        author: "",
        loc: "",
        talkCatType: "",
        awardCatType: "",
        auctioneer: "",
        category: ""
    },
    queryString: "",
    results: [],
    limit: 10,
    offset: 0,
    activePage: 1,
    selectedId: null
};

const searchHkvaybReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_HKVAYB_SEARCHBAR_FIELDS:
            return { ...state, fields: { ...state.fields, ...action.payload } };
        case Act.SET_HKVAYB_SEARCHBAR_FIELDS_RESET:
            return { ...state, fields: { ...initState.fields } };
        case Act.SET_HKVAYB_SEARCHBAR_QUERY_STRING:
            return { ...state, queryString: action.payload };
        case Act.SET_HKVAYB_SEARCHBAR_RESULTS:
            return { ...state, results: [...action.payload] };
        case Act.SET_HKVAYB_SEARCHBAR_LIMIT:
            return { ...state, limit: action.payload };
        case Act.SET_HKVAYB_SEARCHBAR_OFFSET:
            return { ...state, offset: action.payload };
        case Act.SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE:
            return { ...state, activePage: action.payload };
        case Act.SET_HKVAYB_SEARCHBAR_SELECTED_ID:
            return { ...state, selectedId: action.payload };
        default:
            return state;
    }
};

export default searchHkvaybReducer;
