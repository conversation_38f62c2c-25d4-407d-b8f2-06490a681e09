import React, { useContext, useState } from "react";
import IconButton from "@material-ui/core/IconButton";
import DeleteIcon from "@material-ui/icons/Delete";
import { red } from "@material-ui/core/colors";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";

const Clearall = () => {
    const [, dispatch] = useContext(StoreContext);
    const [deleteStyle, setDeleteStyle] = useState({ fontSize: "3rem" });

    const handleOnClick = () => {
        dispatch({
            type: act.SET_ISSEARCH,
            payload: false
        });
        dispatch({
            type: act.SET_KEYWORD,
            payload: ""
        });

        dispatch({
            // 清除pageBar
            type: act.SET_SEARCHDATACOUNT,
            payload: 0
        });
    };
    const handleOnMouseEnter = () => {
        setDeleteStyle({ fontSize: "3rem", color: red[500] });
    };
    const handleOnMouseLeave = () => {
        setDeleteStyle({ fontSize: "3rem" });
    };
    return (
        <IconButton onClick={handleOnClick} style={{ padding: "0" }}>
            <DeleteIcon
                style={deleteStyle}
                onMouseEnter={handleOnMouseEnter}
                onMouseLeave={handleOnMouseLeave}
            />
        </IconButton>
    );
};
export default Clearall;
