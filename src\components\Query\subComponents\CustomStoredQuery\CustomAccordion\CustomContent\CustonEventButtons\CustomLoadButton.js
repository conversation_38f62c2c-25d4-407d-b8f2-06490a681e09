// react
import React, { useContext } from "react";

// ui
import { Button } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
import Act from "../../../../../../../store/actions";
import { injectIntl } from "react-intl";

const CustonLoadButton = ({ docId, authorId, intl }) => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    //
    const handleOnClick = () => {
        if (docId) {
            // save docId
            dispatch({
                type: Act.QUERY_SELECTED_QUERY_ID_SET,
                payload: docId
            });
            // save authorId
            dispatch({
                type: Act.QUERY_SELECTED_AUTHOR_ID_SET,
                payload: authorId
            });
        }
    };
    //
    return (
        <Button
            size="tiny"
            color="teal"
            content={intl.formatMessage({
                id: "custom.loadButton",
                defaultMessage: "Load"
            })}
            onClick={handleOnClick}
        />
    );
};

export default injectIntl(CustonLoadButton);
