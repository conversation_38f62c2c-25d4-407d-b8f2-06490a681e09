import React, { useContext, useEffect, useState } from "react";
import { FormattedMessage, injectIntl } from "react-intl";
import CreateMenuItems from "./CreateMenuItems";
import LanguageSwitcher from "./LanguageSwitcher";
import RightMenu from "./RightMenu";
import { AppTitle } from "./AppTitle";

import {
    Menu,
    Responsive,
    Segment,
    Sidebar,
    Icon,
    Container,
    Button
} from "semantic-ui-react";
import { menus } from "../App-header";
import { StoreContext } from "../store/StoreProvider";
import { isProductionDb } from "../common/codes";
import useBgImgAndStyle from "./useBgImgAndStyle";
import uiConfig from "../config/config-ui";

// type MobileContainerProps = {
//     children: Array<any>,
//     intl: Object,
//     onLocaleChanged: Function
// };
//
// type MobileContainerState = {
//     sidebarOpen: boolean
// };

const MobileContainer = ({
    intl,
    onLocaleChanged,
    user,
    location,
    mobile,
    header,
    webStyle,
    children
}) => {
    const [state] = useContext(StoreContext);
    const { main } = state;
    const { imageToken, database } = main;

    const { permission, isAnonymous } = user;
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [fixedForced, setFixedForced] = useState(false);
    const [activeItem, setActiveItem] = useState(null);
    const [localBgStyle, setLocalBgStyle] = useState("");
    // hook
    const { bgImgUrl, bgStyle, isHomePage } = useBgImgAndStyle({
        imageToken,
        mobile,
        location
    });
    const handlePusherClick = () => {
        if (sidebarOpen) {
            setSidebarOpen(false);
        }
    };
    const handleToggle = () => setSidebarOpen(!sidebarOpen);
    const onMenuItemClick = (e, { name }) => {
        setActiveItem(name);
    };

    let fixed;
    if (isHomePage(location)) {
        fixed = fixedForced;
    } else {
        fixed = true;
    }
    // let segmentHeight = fixed ? 60 : 500;
    let segmentHeight = isHomePage(location) ? "100vh" : "60px";

    useEffect(() => {
        setLocalBgStyle(bgStyle);
        setTimeout(() => {
            ["home-header", "home-header-small"].forEach(name => {
                const selectorName = `.${name}`;
                const ele = document.querySelector(selectorName);
                if (ele) {
                    ele.classList.add(`${name}__show`);
                }
            });
        }, 1000);
    }, [bgImgUrl, bgStyle]);

    const getSegmentClassName = () => {
        if (isHomePage(location)) {
            return "home-header-small";
        }
        return "non-home-header-mobile";
    };

    return (
        <Responsive maxWidth={uiConfig.BP_DESK_MIN_WIDTH - 1}>
            <Sidebar.Pushable>
                <Sidebar
                    style={{ backgroundColor: "white", color: "black" }}
                    as={Menu}
                    animation="push"
                    vertical
                    visible={sidebarOpen}
                    direction={"right"}
                >
                    {/* navbar left items */}
                    <CreateMenuItems
                        intl={intl}
                        permission={permission}
                        activeItem={activeItem}
                        onMenuItemClick={onMenuItemClick}
                        mobile={mobile}
                        webStyle={webStyle}
                        menuItems={menus.menuLeft}
                    />
                    <RightMenu
                        mobile
                        fixed={fixed}
                        isAnonymous={isAnonymous}
                        permission={permission}
                        location={location}
                        onClick={onMenuItemClick}
                        webStyle={webStyle}
                    />
                    <LanguageSwitcher
                        intl={intl}
                        onLocaleChanged={onLocaleChanged}
                        mobile={mobile}
                    />
                </Sidebar>

                <Sidebar.Pusher
                    dimmed={sidebarOpen}
                    onClick={handlePusherClick}
                    style={{ minHeight: "100vh" }}
                >
                    <Segment
                        className={getSegmentClassName()}
                        textAlign="center"
                        style={{
                            overflowY: "scroll",
                            minHeight: segmentHeight,
                            padding: "0em 0em",
                            marginBottom:
                                mobile && isHomePage(location) ? "0" : "2em",
                            border: "none",
                            backgroundColor: "transparent",
                            background: localBgStyle
                        }}
                        vertical
                    >
                        <Menu
                            pointing={!fixed}
                            secondary={!fixed}
                            fixed={fixed ? "top" : null}
                            style={{
                                backgroundColor: "transparent"
                            }}
                            size="large"
                        >
                            {/* logo */}
                            <AppTitle
                                fixed={fixed}
                                onClick={onMenuItemClick}
                                mobile={mobile}
                                webStyle={webStyle}
                            />

                            {/* 是否顯示測試站 */}
                            {!isProductionDb(database) ? (
                                <Menu.Item
                                    position="right"
                                    style={{ alignSelf: "center" }}
                                >
                                    <Button color={"red"} disabled inverted>
                                        <FormattedMessage
                                            id={"menu.isProductionSite"}
                                            defaultMessage={"Testing Site"}
                                        />
                                    </Button>
                                </Menu.Item>
                            ) : (
                                <Menu.Item
                                    position="right"
                                    style={{ alignSelf: "center" }}
                                ></Menu.Item>
                            )}

                            <Menu.Item
                                position="right"
                                style={{ alignSelf: "center" }}
                                onClick={handleToggle}
                            >
                                <Icon name="sidebar" size="large" />
                            </Menu.Item>
                        </Menu>
                        {header && header({ mobile, webStyle })}
                    </Segment>
                    {/* children will placed here */}
                    {children}
                </Sidebar.Pusher>
            </Sidebar.Pushable>
        </Responsive>
    );
};

export default injectIntl(MobileContainer);
