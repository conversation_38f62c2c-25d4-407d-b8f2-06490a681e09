import { makeStyles } from "@material-ui/styles";

const titleStyle = {
    fontFamily: "NotoSansHK",
    fontStretch: "normal",
    fontStyle: "normal",
    textAlign: "left",
    color: "#333"
};

const useStyles = makeStyles({
    root: {},
    hkvayb_div: props => ({
        cursor: "pointer",
        "&:hover": {
            backgroundColor: "rgb(0 0 0 / 5%)"
        },
        ...props.hkvayb_div
    }),
    hkvayb_card: props => ({
        margin: "16px 24px 0 0",
        ...props.hkvayb_card
    }),
    hkvayb_card_box: props => ({
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        alignContent: "center",
        height: "81px",
        ...props.hkvayb_card_box
    }),
    hkvayb_card_title_group: props => ({
        display: "flex",
        flexDirection: "column",
        ...props.hkvayb_card_title_group
    }),
    hkvayb_card_title_zh: props => ({
        ...titleStyle,
        fontSize: "20px",
        fontWeight: "500",
        lineHeight: "1.6",
        letterSpacing: "0.32px",
        ...props.hkvayb_card_title_zh
    }),
    hkvayb_card_title_en: props => ({
        ...titleStyle,
        fontSize: "16px",
        fontWeight: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        ...props.hkvayb_card_title_en
    }),
    hkvayb_card_under_line: props => ({
        width: "100%",
        height: "1px",
        backgroundColor: "#333",
        ...props.hkvayb_card_under_line
    })
});

export default useStyles;
