// firebase
import firebase from "firebase/app";
import "firebase/firestore";
import { isEmpty } from "../../../common/codes";
import axios from "axios";

// sha256 for docId
// import sha256 from "crypto-js/sha256";

const cloudStorage = {};

/**
 *
 * @param uid
 * @returns {list}
 */
cloudStorage.getUserQueries = uid => {
    return firebase
        .firestore()
        .collection("storedQuery")
        .where("author.uid", "==", uid)
        .get()
        .then(snapshots => {
            const docs = [];
            snapshots.forEach(snapshot => {
                const data = snapshot.data();
                // snapshot.data 不包含文件 ID，所以要另外取出並塞回 data
                data.id = snapshot.id;
                docs.push(data);
            });
            return docs;
        })
        .catch(error => {
            console.error("cloudStorage.getUserQueries:error: ", error);
            return [];
        });
};

/**
 *
 * @returns {list}
 */
cloudStorage.getPublicQueries = () => {
    return firebase
        .firestore()
        .collection("storedQuery")
        .where("private", "==", false)
        .get()
        .then(snapshots => {
            console.log("getPublicQueries", snapshots);
            const docs = [];
            snapshots.forEach(snapshot => {
                const data = snapshot.data();
                // snapshot.data 不包含文件 ID，所以要另外取出並塞回 data
                data.id = snapshot.id;
                docs.push(data);
            });
            return docs;
        })
        .catch(error => {
            console.error("cloudStorage.getUserQueries:error: ", error);
            return [];
        });
};

/**
 *
 * @param document
 * @returns {boolean|*}
 */

cloudStorage.insertQuery = async document => {
    try {
        const response = await axios.post(
            process.env.REACT_APP_ADD_USER_SPARQL_IN_FIREBASE_ENDPOINT,
            { query: JSON.stringify(document) }
        );

        const result = await response.json();
        return result.success || false;
    } catch (error) {
        console.error("cloudStorage.insertQuery:error: ", error);
        return false;
    }
};
/**
 *
 * @param document
 * @param id
 * @returns {boolean|*}
 */
cloudStorage.updateQuery = (document, id) => {
    if (id) {
        return firebase
            .firestore()
            .collection("storedQuery")
            .doc(id)
            .update(document)
            .then(() => {
                return true;
            })
            .catch(error => {
                console.error("cloudStorage.updateQuery:error: ", error);
                return false;
            });
    }
    return false;
};

/**
 *
 * @param id
 * @returns {boolean|*}
 */
cloudStorage.deleteQuery = id => {
    return firebase
        .firestore()
        .collection("storedQuery")
        .doc(id)
        .delete()
        .then(() => {
            return true;
        })
        .catch(error => {
            console.error("cloudStorage.deleteQuery:error: ", error);
            return false;
        });
};

export default cloudStorage;
