import React, { useContext, useState, useEffect } from "react";
import {
    FaBriefcase,
    FaBirthdayCake,
    FaGraduationCap,
    FaAward,
    FaBook,
    FaReadme
} from "react-icons/fa";
import { MdDateRange } from "react-icons/md";
import { BsFillPersonFill } from "react-icons/bs";
import { CgMoreO, CgOrganisation } from "react-icons/cg";

import { GiHastyGrave } from "react-icons/gi";
import {
    VerticalTimeline,
    VerticalTimelineElement
} from "react-vertical-timeline-component";
import "react-vertical-timeline-component/style.min.css";

import "./css/style.css";
import { Dimmer, Header, Label, List, Loader } from "semantic-ui-react";
import { cvtDatasetLocale } from "../../../../common/codes";
import PropTypes from "prop-types";
const ignoreCompare = ["graph", "educationEvt", "employment"];

// provided keys in every type of event
const eventProvideKey = {
    basicInfo: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    education: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    employment: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    award: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    organization: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    publication: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    article: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    event: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    persons: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    },
    otherWork: {
        title: "title",
        subtitle: "subTitle",
        subSubtitle: "",
        des1: "description",
        des2: "",
        tag: "g"
    }
};

// set up unique key(required) in every type of event => used for distinguish event type
const eventUniqueKey = {
    basicInfo: [],
    education: [],
    employment: [],
    award: [],
    organization: [],
    publication: [],
    article: [],
    event: [],
    persons: [],
    otherWork: []
};

const eventTypeIndex = {
    basicInfo: "BIC",
    education: "EDE",
    employment: "EME",
    award: "AWE",
    organization: "ORG",
    publication: "PUB",
    article: "ART",
    event: "EVT",
    persons: "PER",
    otherWork: "OTW"
};

const birthOrDeath = event => {
    if (event.subTitle === "出生") {
        return "birth";
    } else if (event.subTitle === "辭世") {
        return "death";
    } else {
        return null;
    }
};

// custom icon in timelineElement
const genIcon = (event, type) => {
    if (type === "basicInfo" && birthOrDeath(event) === "birth")
        return <FaBirthdayCake />;
    if (type === "basicInfo" && birthOrDeath(event) === "death")
        return <GiHastyGrave />;
    if (type === "basicInfo") return <CgOrganisation />;
    if (type === "education") return <FaGraduationCap />;
    if (type === "employment") return <FaBriefcase />;
    if (type === "organization") return <FaBook />;
    if (type === "award") return <FaAward />;
    if (type === "publication") return <FaBook />;
    if (type === "article") return <FaReadme />;
    if (type === "event") return <MdDateRange />;
    if (type === "persons") return <BsFillPersonFill />;
    if (type === "otherWork") return <CgMoreO />;
    return null;
};

const combineTimelineData = bindings => {
    const map = new Map();
    bindings.forEach(bd => {
        const mapItemKey = `${bd.title || ""}-${bd.subTitle ||
            ""}-${bd.eventType || ""}-${bd.hasStartDate || ""}`;
        if (!map.has(mapItemKey)) {
            map.set(mapItemKey, {
                // perId: bd.perId,
                orgId: bd.orgId,
                title: bd.title || "",
                subTitle: bd.subTitle || "",
                g: bd.g ? [bd.g] : [],
                hasStartDate: bd.hasStartDate ? [bd.hasStartDate] : [],
                description: bd.description ? [bd.description] : [],
                eventType: bd.eventType ? [bd.eventType] : []
            });
        } else {
            const bdVal = map.get(mapItemKey);
            if (!bdVal.g.includes(bd.g)) {
                bdVal.g.push(bd.g);
            }
            if (!bdVal.description.includes(bd.description)) {
                bdVal.description.push(bd.description);
            }
            map.set(mapItemKey, bdVal);
        }
    });

    // map.values store to array
    const newBindings = [];
    for (let mapVal of map.values()) {
        let keys = Object.keys(mapVal);
        keys.forEach(k => {
            if (Array.isArray(mapVal[k])) {
                mapVal[k] = mapVal[k].join("/");
            }
        });
        newBindings.push(mapVal);
    }
    return newBindings;
};

const genTimelineEle = (event, type, eventProvideKey, key, globalDataset) => {
    if (!event || !type) return;

    let tagList = [];
    if (type in eventProvideKey && event?.[eventProvideKey[type]?.tag]) {
        event[eventProvideKey[type].tag].split("/").forEach((tag, tagIdx) => {
            // dataset i18n
            const datasetLocale = cvtDatasetLocale(tag, globalDataset);
            tagList.push(
                <List.Item key={`graph-mark-${key}-${tagIdx}`}>
                    <Label color={"orange"}>{datasetLocale}</Label>
                </List.Item>
                // <GraphMark key={`graph-mark-${key}-${tagIdx}`} value={tag} />
            );
        });
    }

    return (
        <VerticalTimelineElement
            className="vertical-timeline-element--work"
            date={`${event.hasStartDate}`}
            iconStyle={{
                background: "rgb(33, 150, 243)",
                color: "#fff"
            }}
            icon={genIcon(event, type)}
            key={key}
        >
            <h3 className="vertical-timeline-element-title">
                {event[eventProvideKey[type].title]}
            </h3>
            <h4 className="vertical-timeline-element-subtitle">
                {event[eventProvideKey[type].subtitle]}
            </h4>
            {/* <h5 className="vertical-timeline-element-subtitle"> */}
            {/*    {event[eventProvideKey[type].subSubtitle]} */}
            {/* </h5> */}
            <p>{event[eventProvideKey[type].des1]}</p>
            {/* <p>{event[eventProvideKey[type].des2]}</p> */}
            <div>
                <List horizontal>{tagList}</List>
            </div>
        </VerticalTimelineElement>
    );
};

// check event type and return event type
const checkEventType = (
    event,
    eventTypeIndex,
    eventUniqueKey = [],
    useUniqKey = false
) => {
    let type = null;

    if (event.eventType !== "") {
        // if event has type key, then use event.type to distinguish
        let evtTypes = Object.keys(eventTypeIndex);
        evtTypes.forEach(et => {
            if (event.eventType === eventTypeIndex[et]) {
                type = et;
            }
        });
    } else {
        if (useUniqKey) {
            let eventObjKey = Object.keys(event);
            let types = Object.keys(eventUniqueKey);
            types.forEach(t => {
                const thisTypeKey = eventUniqueKey[t];
                for (let i = 0; i < eventObjKey.length; i++) {
                    if (thisTypeKey.includes(eventObjKey[i])) {
                        type = t;
                        break;
                    }
                }
            });
        }
    }

    return type;
};

// 利用時間欄位判斷該筆資料是否有時間
const timePredicate = ["hasStartDate"];
// check if event has time key
const ifHasTime = (event, timeKey) => {
    let hasTime = false;
    for (let i = 0; i < timeKey.length; i++) {
        // event has this predicate and not blank
        if (event[timeKey[i]] !== "") {
            hasTime = true;
            break;
        }
    }
    return hasTime;
};

const Timeline = ({ intl, type, name, ReducerContext }) => {
    const [state] = useContext(ReducerContext);
    const { source, personInformation } = state;
    const { timelineData } = personInformation;
    // 資料集中英文名稱
    // [{ dataset: "abcwhkp", label: "《香港古典詩文集經眼錄》", lang: "zh" },{}]
    const { dataset: globalDataset } = source;
    const { user } = state;
    const { locale } = user;

    //
    const [timelineItemSet, setTimelineItemSet] = useState(null);
    const [loading, setLoading] = useState(true);

    // 整理成 timeline 的資料格式
    const pushEvent = () => {
        if (!(timelineData && Array.isArray(timelineData.bindings))) {
            return;
        }
        let timelineItems = [];
        const { bindings } = timelineData;

        // hasStartDate may be like: '1975', '1975.0', '1975-00-00'
        // 2.1 將事件時間資料 format 成可以排序的格式
        const regDate = /[0-9]{2,4}|[0-9]{2,4}(?=\.)|[0-9]{2,4}(?=-)/;
        let bindingsFormat = Object.assign([], bindings).map(b => {
            return {
                ...b,
                hasStartDate: b.hasStartDate && b.hasStartDate.match(regDate)[0]
            };
        });

        // 2.2將事件(education, employment, publication, articles)依時間排序
        bindingsFormat.sort((a, b) => {
            return parseInt(a.hasStartDate) - parseInt(b.hasStartDate);
        });

        // bindingsFormat 資料格式(array)如下：
        // 0: {perId: "PER古蒼梧", subTitle: "出生", g: "auda", hasStartDate: "1945", eventType: "BIC"}
        // 1: {perId: "PER古蒼梧", subTitle: "出生", g: "hkwrpr", hasStartDate: "1945", eventType: "BIC"}

        // 2.3特殊處理：
        // a.subTitle 為"出生"，hasStartDate 相同的資料，合併 g
        // b.if subTitle 不為"出生"，subTitle 及 hasStartDate 相同的資料，合併 description (delimiter: '/')
        bindingsFormat = combineTimelineData(bindingsFormat);

        // 3.塞入 timelineItems
        bindingsFormat.forEach((bind, idx) => {
            // if event do not has time
            if (!ifHasTime(bind, timePredicate)) return;

            let timelineEle = null;
            timelineEle = genTimelineEle(
                bind,
                checkEventType(bind, eventTypeIndex, eventUniqueKey, false),
                eventProvideKey,
                idx,
                globalDataset
            );
            if (!timelineEle) return;
            return timelineItems.push(timelineEle);
        });

        if (timelineData && Array.isArray(timelineData.bindings)) {
            setLoading(false);
        }
        setTimelineItemSet(timelineItems);
    };

    useEffect(() => {
        // 若無 name, 取消 loading
        setLoading(!!name);
    }, [name]);

    useEffect(() => {
        if (name) {
            // 語系變換時, 先 loading
            setLoading(true);
        }
    }, [locale]);

    // name 或 語系變更(globalDataset 會跟著變更) 或 timelineData 變更時, 重新調整 timeline
    useEffect(() => {
        pushEvent();
    }, [state.personInformation.name, globalDataset, timelineData]);

    if (state.personInformation.name === "") {
        return null;
    }

    if (loading)
        return (
            <Dimmer active inverted>
                <Loader inverted content="Loading" />
            </Dimmer>
        );

    // fixme: 若 element 太多會有渲染不即時的問題
    return (
        <VerticalTimeline layout="1-column">
            <Header as={"h2"}>{state.personInformation.name}</Header>
            {/* order: birth -> event between -> death */}
            {/* timelineItem include birthDate, events between, deathDate  */}
            {timelineItemSet}

            {/* end element */}
            <VerticalTimelineElement
                iconStyle={{ background: "rgb(16, 204, 82)", color: "#fff" }}
            />
        </VerticalTimeline>
    );
};

Timeline.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    type: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    ReducerContext: PropTypes.objectOf(PropTypes.any).isRequired
};
export default Timeline;
