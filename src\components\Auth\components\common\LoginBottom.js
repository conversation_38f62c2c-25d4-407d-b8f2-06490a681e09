import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { injectIntl } from "react-intl";
import { useHistory } from "react-router";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Checkbox from "@mui/material/Checkbox";
// component, hooks
import useLocaleRoute from "../../../../hook/useLocaleRoute";
//
import { Api } from "../../../../api/hkbdb/Api";

// store

// config,utils

// css
import "./LoginBottom.scss";
import { StoreContext } from "../../../../store/StoreProvider";
import { convert2HtmlEntities } from "../../../../common/components/Markdown2React/mdUtils";

const LoginBottom = props => {
    // props
    const { intl, agreeChecked, onAgreeCheckChange } = props;
    const { locale, formatMessage } = intl;

    // route,intl
    const history = useHistory();
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { setting } = state;
    const { privacyPolicy, termsOfService } = setting?.signuplConfig;
    // local state
    const [checked, setChecked] = useState(agreeChecked);
    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    const handleCheckboxChange = e => {
        const val = e.target.checked;
        setChecked(val);
        if (onAgreeCheckChange) onAgreeCheckChange(val);
    };

    const agreeTextTemp = formatMessage({
        id: "login.user.agree",
        defaultMessage: `I agree with the above [termsOfService] and [privacyPolicy] of "Hong Kong Writers and Artists Biographical Database".`
    });
    const termsOfServiceTxt = formatMessage({
        id: "login.terms.of.service",
        defaultMessage: `"Clauses of Service"`
    });
    const privacyPolicyTxt = formatMessage({
        id: "login.privacy.policy",
        defaultMessage: ` "privacy policy"`
    });
    const termsOfServiceKey = "[termsOfService]";
    const privacyPolicyKey = "[privacyPolicy]";
    const agreeText = agreeTextTemp
        .replace(termsOfServiceKey, termsOfServiceTxt)
        .replace(privacyPolicyKey, privacyPolicyTxt);

    const getSrcInfoLocale = () => {
        const localeKey = locale.indexOf("zh") >= 0 ? "zh" : "en";
        return (
            <div>
                <div>{convert2HtmlEntities(termsOfService?.[localeKey])}</div>
                <br />
                <div>{convert2HtmlEntities(privacyPolicy?.[localeKey])}</div>
            </div>
        );
    };

    return (
        <Box className={"login-bottom"}>
            <Box className={"login-bottom__privacy-text"}>
                {getSrcInfoLocale()}
            </Box>
            <Box
                className={"login-bottom__agree"}
                display={"flex"}
                alignItems={"flex-start"}
            >
                <Box display={"inline-block"}>
                    <Checkbox
                        checked={checked}
                        onChange={handleCheckboxChange}
                    />
                </Box>
                <Typography textAlign="left">{agreeText}</Typography>
            </Box>
        </Box>
    );
};

LoginBottom.propTypes = {};

LoginBottom.defaultProps = {};

export default injectIntl(LoginBottom);
