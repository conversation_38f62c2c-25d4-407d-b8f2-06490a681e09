import { removePrefix } from "../../../../../../../services/rdf";

// download
const fileDownload = require("react-file-download");
// jszip
const JSZip = require("jszip");

const generateNode = bindings => {
    let IdCount = 10000000;
    const nodeMap = {};
    let foundCat = false;
    bindings.forEach(r => {
        const source = "SOURCE" in r && r.SOURCE ? removePrefix(r.SOURCE) : "";
        const target = "TARGET" in r && r.TARGET ? removePrefix(r.TARGET) : "";
        const scat = "SCAT" in r && r.SCAT ? removePrefix(r.SCAT) : "";
        const tcat = "TCAT" in r && r.TCAT ? removePrefix(r.TCAT) : "";
        if (target === "" || source === "") {
            return;
        }
        if (Object.keys(nodeMap).indexOf(source) < 0) {
            if (scat !== "") {
                foundCat = true;
                nodeMap[source] = { ID: IdCount, WEIGHT: 1, CAT: scat };
            } else {
                nodeMap[source] = { ID: IdCount, WEIGHT: 1 };
            }
            IdCount++;
        } else {
            nodeMap[source].WEIGHT++;
        }
        if (Object.keys(nodeMap).indexOf(target) < 0) {
            if (tcat !== "") {
                foundCat = true;
                nodeMap[target] = { ID: IdCount, WEIGHT: 1, CAT: tcat };
            } else {
                nodeMap[target] = { ID: IdCount, WEIGHT: 1 };
            }
            IdCount++;
        } else {
            nodeMap[target].WEIGHT++;
        }
    });
    let tsvStr = foundCat
        ? "ID\tLABEL\tWEIGHT\tCAT\r\n"
        : "ID\tLABEL\tWEIGHT\r\n";
    Object.keys(nodeMap).forEach(key => {
        const idval = nodeMap[key].ID;
        const wval = nodeMap[key].WEIGHT;
        const cat = foundCat ? nodeMap[key].CAT : "";
        tsvStr += foundCat
            ? `${idval}\t${key}\t${wval}\t${cat}\r\n`
            : `${idval}\t${key}\t${wval}\r\n`;
    });
    return { nodeMap: nodeMap, nodesTsv: tsvStr };
};

const generateEdge = (bindings, nodeMap) => {
    let edgeStr = "SOURCE\tTARGET\tWEIGHT\tTYPE\tLABEL\r\n";
    bindings.forEach(r => {
        const source = "SOURCE" in r ? removePrefix(r.SOURCE) : "";
        const target = "TARGET" in r ? removePrefix(r.TARGET) : "";
        const op = "OP" in r ? removePrefix(r.OP) : "";
        if (target === "" || source === "" || op === "") {
            return;
        }
        const sourceId = nodeMap[source].ID;
        const targetId = nodeMap[target].ID;
        edgeStr += `${sourceId}\t${targetId}\t1\tundirected\t${op}\r\n`;
    });
    return edgeStr;
};

// { source: "", target: "", op: ""}
// preprocess bindings
const processBindings = (bindings, headersMap) => {
    if (!(bindings && Array.isArray(bindings))) return [];

    if (
        !(
            headersMap &&
            headersMap["source"] &&
            headersMap["target"] &&
            headersMap["op"]
        )
    )
        return bindings;
    const bindMap = [];
    bindings.forEach(bind => {
        const eachBind = {};
        eachBind["SOURCE"] = bind[headersMap["source"]] || "";
        eachBind["TARGET"] = bind[headersMap["target"]] || "";
        eachBind["OP"] = bind[headersMap["op"]] || "";
        bindMap.push(eachBind);
    });
    return bindMap;
};

/**
 * 下載 gephi 格式
 *
 * @param bindings => [{poet: {value: ""}, preface: {type: ""},title:{type:""} }]
 * @param headerMap => e.g. {source: "poet", target: "preface", op:"title"}
 */
const downloadGephi = (bindings, headerMap) => {
    if (!(headerMap && headerMap.source && headerMap.target && headerMap.op))
        return;
    const DownloadGephiFileName = "Gephi_generated_by_HKBDB.zip";

    // const headerMap = {
    //     source: "poet",
    //     target: "preface",
    //     op: "title"
    // };
    const psBindings = processBindings(bindings, headerMap);
    const { nodeMap, nodesTsv } = generateNode(psBindings);
    // nodesTsv: ID	LABEL	WEIGHT
    // edgesTsv: SOURCE	TARGET	WEIGHT	TYPE	LABEL
    const edgesTsv = generateEdge(psBindings, nodeMap);
    const zip = new JSZip();
    zip.file("Node_Table.tsv", nodesTsv);
    zip.file("Edge_Table.tsv", edgesTsv);
    zip.generateAsync({ type: "blob" }).then(function(content) {
        fileDownload(content, DownloadGephiFileName);
    });
};

export { processBindings, generateNode, generateEdge, downloadGephi };
