import React, { useContext, useEffect } from "react";
// import { HomePageLayout } from "./HomePageLayout";
// import AccountManage from "./AccountManage";
import { ResponsiveContainer } from "../../layout/Layout";
// store
import { StoreContext } from "../../store/StoreProvider";
import { Container, Divider, Header, Segment } from "semantic-ui-react";
import { useHistory } from "react-router";
import { FormattedMessage } from "react-intl";

const AccountManagePage = ({ ...props }) => {
    const [state] = useContext(StoreContext);
    const { backend } = state;
    const { backendWeb } = backend;

    const history = useHistory();

    // redirect to backend web
    useEffect(() => {
        if (!(backendWeb && backendWeb.url)) return;
        // open external link in current tab
        // window.location = backendWeb.url;

        // open external link with a new tab
        window.open(backendWeb.url, "_blank", "noopener noreferrer");

        // 導向首頁
        history.goBack();
    }, [backendWeb]);

    return (
        <ResponsiveContainer {...props}>
            <Container>
                <Divider hidden />
                <Segment>
                    <Header textAlign="center" as="h3">
                        <FormattedMessage
                            id="AccountManage.redirect"
                            defaultMessage="You are now redirect to backend web."
                        />
                    </Header>
                </Segment>
            </Container>
        </ResponsiveContainer>
    );
};

export default AccountManagePage;
