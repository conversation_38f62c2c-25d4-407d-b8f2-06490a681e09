import React, { createRef, useState, useContext, useEffect } from "react";
import {
    Accordion,
    Grid,
    Header,
    Icon,
    Label,
    List,
    ListItem,
    Table
} from "semantic-ui-react";
import { genRandom<PERSON>ey } from "../../../common/codes";

export const LIST_DIRECTION = {
    horizontal: "horizontal",
    vertical: "vertical"
};

const PropertyList = ({
    propertyRef,
    properties,
    sliceLength,
    listDirection
}) => {
    const [showMore, setShowMore] = useState(false);
    if (!properties || (properties && properties.length === 0))
        return <p>{"目前無資料"}</p>;

    return (
        <List horizontal={listDirection === LIST_DIRECTION.horizontal}>
            {showMore &&
                properties &&
                properties.map(ppt => <List.Item key={ppt}>{ppt}</List.Item>)}
            {!showMore &&
                properties &&
                properties
                    .filter((pp, idx) =>
                        sliceLength ? idx < sliceLength : true
                    )
                    .map(ppt => <List.Item key={ppt}>{ppt}</List.Item>)}
            {sliceLength && properties.length > sliceLength && (
                <List.Item
                    key={"more"}
                    onClick={() => {
                        setShowMore(!showMore);
                    }}
                    as={"a"}
                    style={{ fontSize: "1.2em" }}
                >
                    {showMore ? "...... less" : "...... more"}
                </List.Item>
            )}
        </List>
    );
};

export const CustomAccordion = ({
    data,
    active,
    onClick,
    width,
    useFluid,
    subTitle,
    useSubTitle,
    useIcon: useIcon = true,
    useContent: useContent = true,
    sliceContentLength,
    listDirection: listDirection = LIST_DIRECTION.vertical
}) => {
    const [displayCt, setDisplayCt] = useState(active);

    useEffect(() => {
        setDisplayCt(active);
    }, [active]);

    return (
        data && (
            <Accordion
                fluid={useFluid}
                styled
                style={{ width: width || "default" }}
            >
                <Accordion.Title
                    active={displayCt}
                    onClick={() => {
                        setDisplayCt(!displayCt);
                        if (onClick) onClick();
                    }}
                >
                    {useIcon && <Icon name={"dropdown"} />}
                    {data.title || ""}
                    {useSubTitle && subTitle && (
                        <Label style={{ marginLeft: "10px" }}>{subTitle}</Label>
                    )}
                </Accordion.Title>
                {useContent && (
                    <Accordion.Content active={displayCt}>
                        {data.content &&
                            !Array.isArray(data.content) &&
                            data.content}
                        {data.content && Array.isArray(data.content) && (
                            <PropertyList
                                propertyRef={data.title}
                                properties={data.content}
                                sliceLength={sliceContentLength}
                                listDirection={listDirection}
                            />
                        )}
                    </Accordion.Content>
                )}
            </Accordion>
        )
    );
};

// 顯示所有 className 的 propertyRef
export const PropertyRef = ({ propertyRef }) => {
    const onClickPropRef = pr => {
        if (!pr) return;
        // do something when click accordiion title
        return null;
    };

    return (
        <Grid columns={1}>
            <Grid.Row>
                {propertyRef &&
                    Object.keys(propertyRef).length > 0 &&
                    Object.keys(propertyRef).map(pr => (
                        <Grid.Column key={pr}>
                            <CustomAccordion
                                key={pr}
                                data={{
                                    title: pr,
                                    content: propertyRef[pr].properties
                                }}
                                active
                                useSubTitle
                                subTitle={propertyRef[pr].className}
                                onClick={() => onClickPropRef(pr)}
                                // width={"200px"}
                                useIcon={false}
                                useFluid={true}
                                useContent={true}
                                sliceContentLength={50}
                                listDirection={LIST_DIRECTION.horizontal}
                            />
                        </Grid.Column>
                    ))}
            </Grid.Row>
        </Grid>
    );
};

// 顯示所有 className 的 propertyRef
export const RelationOP = ({ relationOP, headers }) => {
    return (
        <Grid columns={1}>
            <Grid.Row>
                <Table striped celled color={"blue"}>
                    <Table.Header>
                        <Table.Row>
                            {Object.keys(headers).map(
                                hd =>
                                    headers[hd].display && (
                                        <Table.HeaderCell
                                            key={genRandomKey(10)}
                                        >
                                            {headers[hd].name}
                                        </Table.HeaderCell>
                                    )
                            )}
                        </Table.Row>
                    </Table.Header>
                    <Table.Body>
                        {relationOP &&
                            relationOP.map(rop => (
                                <Table.Row key={genRandomKey(10)}>
                                    {Object.keys(headers).map(
                                        hd =>
                                            headers[hd].display && (
                                                <Table.Cell
                                                    key={genRandomKey(10)}
                                                >
                                                    {rop[hd]}
                                                </Table.Cell>
                                            )
                                    )}
                                </Table.Row>
                            ))}
                    </Table.Body>
                </Table>
            </Grid.Row>
        </Grid>
    );
};

// 顯示目前點選的 className 的 property ref (e.g. 'ref-1') 的 property list
export const CurPropList = ({ curProperties, onClickList, style, active }) => {
    return (
        <div style={style}>
            <CustomAccordion
                data={{
                    title: "Property list",
                    content: curProperties ? curProperties.properties : null
                }}
                subTitle={
                    <h4
                        style={{
                            lineHeight: "0.8rem"
                        }}
                    >
                        {(curProperties &&
                            curProperties.propertyRef &&
                            curProperties.propertyRef) ||
                            ""}
                    </h4>
                }
                useSubTitle={true}
                width={"200px"}
                useFluid={false}
                active={active}
                onClick={onClickList}
                useIcon={true}
                useContent={true}
            />
        </div>
    );
};
