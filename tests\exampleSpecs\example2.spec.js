// @ts-check
// const { test, expect } = require("@playwright/test");

const { test: base, expect } = require("@playwright/test");

const test = base.extend({
    page: async ({ page }, use) => {
        page.on("console", msg => {
            if (msg.type() === "error") {
                console.log("Console error suppressed:", msg.text());
            }
        });
        await use(page);
    }
});

test("has h3", async ({ page }) => {
    await page.goto("https://playwright.dev/", { waitUntil: "networkidle" });

    // Expect h3 with 'Any browser • Any platform • One API' to be visible.
    await expect(
        page.getByRole("heading", {
            name: "Any browser • Any platform • One API"
        })
    ).toBeVisible();

    // Expect h3 with 'Resilient • No flaky tests' to be visible.
    await expect(
        page.getByRole("heading", { name: "Resilient • No flaky tests" })
    ).toBeVisible();

    // Expect h3 with 'No trade-offs • No limits' to be visible.
    await expect(
        page.getByRole("heading", { name: "No trade-offs • No limits" })
    ).toBeVisible();

    // Expect h3 with 'Full isolation • Fast execution' to be visible.
    await expect(
        page.getByRole("heading", { name: "Full isolation • Fast execution" })
    ).toBeVisible();

    // Expect h3 with 'Powerful Tooling' to be visible.
    await expect(
        page.getByRole("heading", { name: "Powerful Tooling" })
    ).toBeVisible();
});

// test footer  with 3 columns
test("footer with 3 columns", async ({ page }) => {
    await page.goto("https://playwright.dev/", { waitUntil: "networkidle" });

    // Expect 3 .footer__col to be visible
    // Locate all footer column elements
    const footerColumns = page.locator(".footer__col");

    // Assert the count of footer column elements
    await expect(footerColumns).toHaveCount(3);
});

const footerColumnTitles = ["Learn", "Community", "More"];

// test 3 column's title and content in footer
test("3 column's title and content in footer", async ({ page }) => {
    await page.goto("https://playwright.dev/", { waitUntil: "networkidle" });

    // Select .footer__col
    const footerCols = page.locator(".footer__col");
    // Expect 3 .footer__col to be visible
    await expect(footerCols).toHaveCount(3);

    // interate over each .footer__col
    for (let i = 0; i < 3; i++) {
        // Select .footer__col:nth-child(i) .footer__title
        const footerTitle = page.locator(
            `.footer__col:nth-child(${i + 1}) .footer__title`
        );
        // Expect .footer__col:nth-child(i) .footer__title to be visible
        await expect(footerTitle).toBeVisible();
        // Expect .footer__col:nth-child(i) .footer__title to have textContent as expected
        await expect(footerTitle).toHaveText(footerColumnTitles[i]);

        // Select .footer__col:nth-child(i) .footer__items
        const footerContent = page.locator(
            `.footer__col:nth-child(${i + 1}) .footer__items`
        );
        // Expect .footer__col:nth-child(i) .footer__content to be visible
        await expect(footerContent).toBeVisible();
    }
});

// test footer with copy right
test("footer with copy right", async ({ page }) => {
    await page.goto("https://playwright.dev/", { waitUntil: "networkidle" });

    await expect(page.locator(".footer__copyright")).toHaveText(
        "Copyright © 2024 Microsoft"
    );
});
