import React, { useEffect } from "react";
// ui
import { Button, Modal, Image } from "semantic-ui-react";

const AvatarModal = ({ _open, imgUrl, personName, handleClose }) => {
    const [open, setOpen] = React.useState(false);

    useEffect(() => {
        setOpen(_open);
    }, [_open]);

    const onClose = () => {
        setOpen(false);
        if (handleClose) handleClose();
    };

    const onOpen = () => {
        setOpen(true);
    };

    return (
        <Modal onClose={onClose} onOpen={onOpen} open={open} size="small">
            <Modal.Header>{personName}</Modal.Header>
            <Modal.Content
                image
                style={{ display: "flex", justifyContent: "center" }}
            >
                <Image size="medium" src={imgUrl} wrapped />
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClose}>Close</Button>
            </Modal.Actions>
        </Modal>
    );
};

export default AvatarModal;
