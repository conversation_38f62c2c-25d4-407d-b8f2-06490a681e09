import React from "react";
import { Container } from "semantic-ui-react";
import { ResponsiveContainer } from "./Layout";
// import { signOut } from "../services/authentication";

export default function ErrorPage(props) {
    const code = props.match.params.code;
    let message;
    switch (code) {
        case "400":
            // signOut();
            message = "Bad Request";
            break;
        case "401":
            message = "Unauthorized";
            break;
        case "403":
            message = "Forbidden";
            break;
        default:
            message = `Not found (${code})`;
    }
    return (
        <ResponsiveContainer {...props}>
            <div>
                <Container text>
                    <h1>{message}</h1>
                </Container>
            </div>
        </ResponsiveContainer>
    );
}
