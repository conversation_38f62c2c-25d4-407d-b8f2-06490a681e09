import { useEffect, useContext } from "react";
import { FETCH_FIREBASE_DATA_SUCCESS } from "../firebaseAction";
import { rtReset, FBWordCloud, updateAll } from "../realtimeDatabase";
import { StoreContext } from "../../../store/StoreProvider/index";

const rtCounter = process.env.RT_COUNTER;

const RealTimeListener = ({ realTimeDb }) => {
    // const dispatch = useDispatch();
    const [, dispatch] = useContext(StoreContext);
    useEffect(() => {
        if (!realTimeDb) {
            return;
        }
        realTimeDb
            .ref(`counter/${rtCounter}`)
            .once("value")
            .then(snapshot => {
                const fbData = snapshot.val();
                // 重設所有值
                let resetToDefault = false;
                if (!fbData) {
                    resetToDefault = true;
                } else if (
                    !Object.prototype.hasOwnProperty.call(fbData, rtReset)
                ) {
                    resetToDefault = true;
                } else if (fbData[rtReset] === true) {
                    resetToDefault = true;
                }

                if (resetToDefault) {
                    const toRtDb = { [FBWordCloud]: {} };
                    // 預設值
                    toRtDb[FBWordCloud]["PER%E9%87%91%E5%BA%B8"] = {
                        id: "PER%E9%87%91%E5%BA%B8",
                        type: "Person",
                        count: 1,
                        keyword: "金庸"
                    };
                    dispatch({
                        type: FETCH_FIREBASE_DATA_SUCCESS,
                        payload: toRtDb
                    });
                    updateAll(toRtDb);
                } else {
                    // visits: { today: 1, all: 10, modified: '2021-10-22' } for <Footer />
                    dispatch({
                        type: FETCH_FIREBASE_DATA_SUCCESS,
                        payload: fbData
                    });
                }
            });
    }, [realTimeDb]);

    return null;
};

export default RealTimeListener;
