import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { Container, Typography } from "@material-ui/core";
import { styled } from "@material-ui/styles";

import { ResponsiveContainer } from "../../layout/Layout";
import Footer from "../../layout/Footer";
import InfoModal from "./infoModal/InfoModal";
import CustomPagination from "../../common/components/CustomPagination";

import collectionConfig from "./collectionConfig";

import "./Collection.scss";
import ImageCard from "./imageCard/imageCard";
import useWindowSize from "../../hook/useWindowSize";
import Box from "@mui/material/Box";
import { createTheme } from "@mui/material/styles";
import { Api, readHkbdbData } from "../../api/hkbdb/Api";

const pageSize = 16;

const theme = createTheme({
    breakpoints: {
        values: {
            xs: 576,
            sm: 768,
            md: 992,
            lg: 1200,
            xl: 1366,
            xxl: 1536
        }
    }
});

const StyledGrid = styled(Box)(({ theme }) => ({
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(20%, 0fr))",
    gridGap: "1rem 2.5rem",
    justifyContent: "center",
    margin: "2rem auto",
    [theme.breakpoints.up("xxl")]: {
        gridGap: "2rem 2.5rem",
        gridTemplateColumns: "repeat(auto-fill, minmax(20%, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("xxl")]: {
        gridGap: "2rem 2.5rem",
        gridTemplateColumns: "repeat(auto-fill, minmax(20%, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("xl")]: {
        gridGap: "1.5rem 2.5rem",
        gridTemplateColumns: "repeat(auto-fill, minmax(21%, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("lg")]: {
        gridTemplateColumns: "repeat(auto-fill, minmax(18rem, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("md")]: {
        gap: "2rem 6rem",
        gridTemplateColumns: "repeat(auto-fill, minmax(30%, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("sm")]: {
        marginTop: "3rem",
        gridGap: "1.5rem",
        gridTemplateColumns: "repeat(auto-fill, minmax(40%, 0fr))",
        justifyContent: "center"
    },
    [theme.breakpoints.down("xs")]: {
        marginTop: "3rem",
        gridTemplateColumns: "repeat(auto-fit, minmax(25%, 148px))",
        justifyContent: "center",
        alignItems: "center"
    }
}));

const CollectionPage = props => {
    const [isLoading, setIsLoading] = useState(false);
    const [focusItem, setFocusItem] = useState();
    const [data, setData] = useState([]);
    const [curPage, setCurPage] = useState(1);
    const [totalPage, setTotalPage] = useState(0);

    const size = useWindowSize();
    const isMobile = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= 568;
    };

    const { colType } = useParams();

    useEffect(() => {
        setTotalPage(data?.length ? Math.ceil(data?.length / pageSize) : 0);
    }, [data, pageSize]);

    useEffect(() => {
        const getData = async () => {
            const size = "400x400";
            const apiStr =
                colType === "featuredPub"
                    ? Api.getBookcoverList(size)
                    : Api.getManuscriptList(size);
            const { data } = await readHkbdbData(apiStr);
            const transformedData = data.map(item => {
                item.imageURL = [item.imageURL];
                return item;
            });
            setData(transformedData);
        };
        getData();
    }, []);

    return (
        <ResponsiveContainer
            header={() => (
                <div className={"collection"}>
                    {!isLoading && (
                        <Container style={{ paddingLeft: 0, paddingRight: 0 }}>
                            <Typography
                                variant={"h2"}
                                className={"collection__header"}
                            >
                                {collectionConfig[colType].title}
                            </Typography>
                            {colType ===
                                collectionConfig.manuScript.pathName && (
                                <Typography className={"collection__content"}>
                                    {collectionConfig.manuScript.desc}
                                </Typography>
                            )}
                            <StyledGrid theme={theme}>
                                {data.length > 0 &&
                                    data
                                        .slice(
                                            (curPage - 1) * pageSize,
                                            curPage * pageSize
                                        )
                                        .map(imgObj => (
                                            <Box
                                                key={imgObj.id}
                                                className={"collection__box"}
                                            >
                                                <ImageCard
                                                    size={isMobile() ? 90 : 160}
                                                    colType={colType}
                                                    imgObj={imgObj}
                                                    setFocusItem={setFocusItem}
                                                />
                                            </Box>
                                        ))}
                            </StyledGrid>
                            {data?.length > 0 && (
                                <CustomPagination
                                    totalPage={totalPage}
                                    page={curPage}
                                    curPage={curPage}
                                    setCurPage={setCurPage}
                                />
                            )}
                        </Container>
                    )}
                    <InfoModal
                        type={colType}
                        modalInfo={focusItem}
                        data={data}
                        setFocusItem={setFocusItem}
                        onClose={() => {
                            setFocusItem();
                        }}
                    />
                </div>
            )}
            withFooter={true}
            footer={Footer}
            {...props}
        ></ResponsiveContainer>
    );
};

export default CollectionPage;
