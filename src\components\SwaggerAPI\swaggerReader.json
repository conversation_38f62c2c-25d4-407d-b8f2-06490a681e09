{"swagger": "2.0", "info": {"description": "HKBDB API API (swagger version 2.0)", "version": "2.0", "title": "HKBDB API API", "termsOfService": "", "contact": {}, "license": {"name": "", "url": ""}}, "host": "hkbdb-api.daoyidh.com", "basePath": "/api", "consumes": ["application/json"], "produces": ["application/json"], "tags": [], "schemes": ["https", "http"], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}}, "parameters": {}, "paths": {}, "definitions": {}, "externalDocs": {}}