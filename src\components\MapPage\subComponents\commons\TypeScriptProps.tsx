import { type Feature, type Point, type LineString } from "geojson";

export interface IBasicLoc {
  lat: string | number;
  long: string | number;
  locId: string;
  location: string;
  // locations?: string;
}

export interface IGeoJsonProperties {
  id: string;
  locId: string;
  location: string;
  lngLat: Array<[number, number]>;
  intensity: number;
  upstreamCount?: number;
  handlerCount?: number;
  isFetching?: boolean; // 是否正在 fetch handlers 及 upstream 資料
  alreadyFetch?: boolean; // 是否已經 fetch handlers 及 upstream 資料
}

export interface IPoint extends Feature<Point, IGeoJsonProperties> {
  id: string;
}
export interface ICluster extends Feature<Point, IGeoJsonProperties> {
  id: string;
}
export interface ILine extends Feature<LineString, IGeoJsonProperties> {
  id: string;
}
