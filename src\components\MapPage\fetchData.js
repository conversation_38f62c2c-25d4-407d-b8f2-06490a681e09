import { Api, readHkbdbData } from "../../api/hkbdb/Api";
import { bs64Encode } from "../../common/codes";

/**
 * example of IMapPerson:
 * {
 *      "perId": "PER%E9%AE%91%E6%85%A7%E6%99%B6",
 *      "bestKnownName": "鮑慧晶",
 *      "placeCount": 1
 * }
 * @typedef {{
 *   perId: string,
 *   bestKnownName: string,
 *   placeCount: number
 *   }} IMapPerson
 */

/**
 * 透過關鍵字搜尋人物
 * @param keyword {string}
 * @returns {Promise<{error:boolean;data?:IMapPerson[]}>}
 */
export const searchMapPerson = async () => {
    const url = Api.getMapSearchPersonList(bs64Encode(), 20, 0);
    const response = await readHkbdbData(
        url,
        undefined,
        false,
        false,
        undefined,
        false
    );
    return response;
};

/**
 * example 1 of IMapPersonPlace:
 *    {
 *       "perId": "PER%E9%87%91%E5%BA%B8",
 *       "person": "金庸",
 *       "placeId": "PLA%E6%B5%99%E6%B1%9F%E7%9C%81%E6%B5%B7%E5%AF%A7%E7%B8%A3%E8%A2%81%E8%8A%B1%E9%8E%AE",
 *       "lat": "30.411076",
 *       "long": "120.781114",
 *       "place": "浙江省海寧縣袁花鎮",
 *       "placeKey": "出生地點",
 *       "dateKey": "出生日期",
 *       "year": "1924",
 *       "month": "0",
 *       "day": "0",
 *       "infoId": "PER%E9%87%91%E5%BA%B8",
 *       "infoType": "hasPlaceOfBirth"
 *     },
 * example 2 of IMapPersonPlace:
 *    {
 *       "perId": "PER%E9%87%91%E5%BA%B8",
 *       "person": "金庸",
 *       "placeId": "PLA%E8%8B%B1%E5%9C%8B",
 *       "lat": "55.378051",
 *       "long": "-3.435973",
 *       "place": "英國",
 *       "placeKey": "教育單位地點",
 *       "dateKey": "就讀日期",
 *       "year": "2010",
 *       "month": "0",
 *       "day": "0",
 *       "infoId": "EDUEVT__01111ca2-6926-442d-bc8e-82df7df0c695",
 *       "infoType": "EducationEvent",
 *       "infoDescKey": "教育單位",
 *       "infoDesc": "劍橋大學"
 *     }
 *
 * @typedef {{
 *    perId: string,
 *    person: string,
 *    placeId: string,
 *    lat: string,
 *    long: string,
 *    place: string,
 *    placeKey: string,
 *    dateKey: string,
 *    year: string,
 *    month: string,
 *    day: string,
 *    infoId: string,
 *    infoType: string,
 *    infoDescKey?: string,
 *    infoDesc?: string
 *    }} IMapPersonPlace
 *
 */

/**
 * 透過人物ID列表, 取得人物的相關地點資訊
 * @param ids {string[]}
 * @returns {Promise<{data?: IMapPersonPlace[], error?:string; durationSS?:number}>}
 */
export const fetchMapPersonPlaceList = async (ids, locale) => {
    const url = Api.getMapPersonsPlaceList(bs64Encode("\n" + ids.join("\n")));
    const response = await readHkbdbData(
        url,
        undefined,
        false,
        false,
        locale,
        false
    );
    return response;
};

// todo:取得 point 的詳細資訊
export const fetchPointData = async () => {
    return new Promise((resolve, reject) => {
        //
        resolve({});
    });
};

/**
 *
 * @type {{name:string;id:string}[]}
 */
const fakePersons = [
    {
        bestKnownName: "張三",
        perId: "1"
    },
    {
        bestKnownName: "李四",
        perId: "2"
    },
    {
        bestKnownName: "王五",
        perId: "3"
    },
    {
        bestKnownName: "趙六",
        perId: "4"
    }
];

export const searchFakePerson = async keyword => {
    return new Promise((resolve, reject) => {
        if (!keyword) return resolve(fakePersons.slice(0, 10));
        const result = fakePersons.filter(person =>
            person.name.includes(keyword)
        );
        return resolve(result);
    });
};

/**
 * 找gisEnable的人物，返回出生、死亡、教育、工作、獲獎、事件資料
 * @param ids {string[]}
 * @returns {Promise<{data?: IMapPersonPlace[], error?:string; durationSS?:number}>}
 */
export const fetchMapSearchInfomation = async locale => {
    const url = Api.getMapSearchInfomation();
    const response = await readHkbdbData(
        url,
        undefined,
        false,
        false,
        locale,
        false
    );
    return response;
};

/**
 * 行跡圖timeline預設起起、結束年份
 * @param ids {string[]}
 * @returns {Promise<{data?: IMapPersonPlace[], error?:string; durationSS?:number}>}
 */
export const fetchMapTimelineRange = async () => {
    const url = Api.getMapTimelineRange();
    const response = await readHkbdbData(
        url,
        undefined,
        false,
        false,
        undefined,
        false
    );
    return response;
};

/**
 * 地圖使用說明
 * @param ids {string[]}
 * @returns {Promise<{data?: IMapPersonPlace[], error?:string; durationSS?:number}>}
 */
export const fetchMapGuideContent = async () => {
    const url = Api.getMapGuideContent();
    const response = await readHkbdbData(
        url,
        undefined,
        false,
        false,
        undefined,
        false
    );
    return response;
};
