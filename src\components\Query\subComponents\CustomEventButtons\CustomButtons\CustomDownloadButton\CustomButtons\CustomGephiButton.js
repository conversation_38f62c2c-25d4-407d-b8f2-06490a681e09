// react
import React, { useContext, useState } from "react";
// ui
import { Button } from "semantic-ui-react";
// api
import { Api, queryHkbdbData } from "../../../../../../../api/hkbdb/Api";
// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
// common
import { isEmpty } from "../../../../../../../common/codes";
// services
import { removePrefix } from "../../../../../../../services/rdf";
// download
const fileDownload = require("react-file-download");
// jszip
const JSZip = require("jszip");

const CustomGephiButton = ({ open, setOpen }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryString } = state.query;
    //
    const [isLoad, setIsLoad] = useState(false);
    //
    const generateNode = bindings => {
        let IdCount = 10000000;
        const nodeMap = {};
        let foundCat = false;
        bindings.forEach(r => {
            const source = "SOURCE" in r ? removePrefix(r.SOURCE.value) : "";
            const target = "TARGET" in r ? removePrefix(r.TARGET.value) : "";
            const scat = "SCAT" in r ? removePrefix(r.SCAT.value) : "";
            const tcat = "TCAT" in r ? removePrefix(r.TCAT.value) : "";
            if (target === "" || source === "") {
                return;
            }
            if (Object.keys(nodeMap).indexOf(source) < 0) {
                if (scat !== "") {
                    foundCat = true;
                    nodeMap[source] = { ID: IdCount, WEIGHT: 1, CAT: scat };
                } else {
                    nodeMap[source] = { ID: IdCount, WEIGHT: 1 };
                }
                IdCount++;
            } else {
                nodeMap[source].WEIGHT++;
            }
            if (Object.keys(nodeMap).indexOf(target) < 0) {
                if (tcat !== "") {
                    foundCat = true;
                    nodeMap[target] = { ID: IdCount, WEIGHT: 1, CAT: tcat };
                } else {
                    nodeMap[target] = { ID: IdCount, WEIGHT: 1 };
                }
                IdCount++;
            } else {
                nodeMap[target].WEIGHT++;
            }
        });
        let tsvStr = foundCat
            ? "ID\tLABEL\tWEIGHT\tCAT\r\n"
            : "ID\tLABEL\tWEIGHT\r\n";
        Object.keys(nodeMap).forEach(key => {
            const idval = nodeMap[key].ID;
            const wval = nodeMap[key].WEIGHT;
            const cat = foundCat ? nodeMap[key].CAT : "";
            tsvStr += foundCat
                ? `${idval}\t${key}\t${wval}\t${cat}\r\n`
                : `${idval}\t${key}\t${wval}\r\n`;
        });
        return { nodeMap: nodeMap, nodesTsv: tsvStr };
    };
    const generateEdge = (bindings, nodeMap) => {
        let edgeStr = "SOURCE\tTARGET\tWEIGHT\tTYPE\tOPCategory\r\n";
        bindings.forEach(r => {
            const source = "SOURCE" in r ? removePrefix(r.SOURCE.value) : "";
            const target = "TARGET" in r ? removePrefix(r.TARGET.value) : "";
            const op = "OP" in r ? removePrefix(r.OP.value) : "";
            if (target === "" || source === "" || op === "") {
                return;
            }
            const sourceId = nodeMap[source].ID;
            const targetId = nodeMap[target].ID;
            edgeStr += `${sourceId}\t${targetId}\t1\tundirected\t${op}\r\n`;
        });
        return edgeStr;
    };
    const downloadGephi = bindings => {
        const DownloadGephiFileName = "Gephi_generated_by_HKBDB.zip";
        const { nodeMap, nodesTsv } = generateNode(bindings);
        const edgesTsv = generateEdge(bindings, nodeMap);
        const zip = new JSZip();
        zip.file("Node_Table.tsv", nodesTsv);
        zip.file("Edge_Table.tsv", edgesTsv);
        zip.generateAsync({ type: "blob" }).then(function(content) {
            fileDownload(content, DownloadGephiFileName);
        });
    };
    //
    const handleOnClick = async () => {
        console.log("I am Gephi.");
        if (queryString) {
            const api = Api.getQueryAndCount();
            const result = await queryHkbdbData(api, queryString, -1, 0);
            if (!isEmpty(result)) {
                const { head, data } = result;
                downloadGephi(data);
            }
        }
        setIsLoad(true);
        setOpen(false);
    };
    //
    return (
        <Button color="green" onClick={handleOnClick} loading={isLoad}>
            Gephi
        </Button>
    );
};

export default CustomGephiButton;
