import React, { useEffect, useState, useMemo } from "react";
// mui
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Alert from "@mui/material/Alert";
import Snackbar from "@mui/material/Snackbar";

import Slider from "react-slick";
import { FormattedMessage } from "react-intl";

// style
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "../../../styles/slider.scss";

// utils
import { searchMapPerson } from "../../../fetchData";
import debounce from "../../../utils/debounce";
import PropTypes from "prop-types";

// components
import SliderNextBtn from "./slider/SliderNextBtn";
import SliderPrevBtn from "./slider/SliderPrevBtn";

const config = {
    // 最多選擇的人數
    maxSelectionsCount: 3
};

// React-Slick settings
const sliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    swipeToSlide: true,
    centerMode: false,
    variableWidth: true,
    nextArrow: <SliderNextBtn />,
    prevArrow: <SliderPrevBtn />,
    responsive: [
        {
            breakpoint: 1024,
            settings: {
                slidesToShow: 4,
                slidesToScroll: 2
            }
        },
        {
            breakpoint: 600,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1
            }
        },
        {
            breakpoint: 480,
            settings: {
                slidesToShow: 2,
                slidesToScroll: 1
            }
        }
    ]
};

const slickSlideStyle = {
    margin: "0 8px",
    padding: "5px"
};

/**
 * @typedef {Object} IPersonOption
 * @property {string} perId
 * @property {number} bestKnownName
 * @property {string} label
 * @property {string} value
 */

/**
 *
 * @param {Object} props
 * @param {Function} props.onPersonSelectedChange - Callback when selection changes
 * @param {IPersonOption[]} props.personSelected - Selected persons from parent component
 * @param {React.RefObject} props.carouselRef - Reference to carousel element
 * @param {React.RefObject} props.alertRef - Reference to alert element
 * @returns {React.JSX.Element}
 */
const TraceMapSearch = props => {
    const {
        onPersonSelectedChange,
        carouselRef,
        alertRef,
        personSelected = []
    } = props;

    const [localPersonSelected, setLocalPersonSelected] = useState(
        personSelected
    );
    // Available persons for selection
    const [persons, setPersons] = useState([]);
    const [isError, setIsError] = useState(false);

    useEffect(() => {
        setLocalPersonSelected(personSelected);
    }, [personSelected]);

    // Fetch available persons once on component mount
    useEffect(() => {
        searchMapPerson().then(res => {
            if (Array.isArray(res.data)) {
                setPersons(
                    res.data.map(r => ({
                        ...r,
                        label: r.bestKnownName,
                        value: r.perId
                    }))
                );
            } else {
                setPersons([]);
            }
        });
    }, []);

    useEffect(() => {
        if (isError) {
            const timeoutId = setTimeout(() => {
                setIsError(false);
            }, 5000);
            return () => clearTimeout(timeoutId);
        }
    }, [isError]);

    const handleItemClick = person => {
        const isSelected = personSelected.some(p => p.perId === person.perId);

        if (isSelected) {
            const updatedSelection = localPersonSelected.filter(
                p => p.perId !== person.perId
            );
            setLocalPersonSelected(updatedSelection);
            setIsError(false);
        } else {
            // If not selected and under limit, add to selection
            if (localPersonSelected.length < config.maxSelectionsCount) {
                setLocalPersonSelected(prev => [...prev, person]);
                setIsError(false);
            }
            if (localPersonSelected.length === config.maxSelectionsCount) {
                setIsError(true);
            }
        }
    };

    const debouncedOnPersonSelectedChange = useMemo(
        () => debounce(onPersonSelectedChange, 600),
        []
    );

    // debounce for onPersonSelectedChange, only trigger when localPersonSelected changes
    useEffect(() => {
        debouncedOnPersonSelectedChange(localPersonSelected);
    }, [localPersonSelected]);

    // Check if item is selected
    const isItemSelected = person => {
        return localPersonSelected.some(p => p.perId === person.perId);
    };

    return (
        <>
            <Box ref={carouselRef}>
                {/* React-Slick Carousel */}
                {persons.length > 0 && (
                    <Box sx={{ marginBottom: "16px", marginTop: "16px" }}>
                        <Slider
                            {...sliderSettings}
                            style={{ overflow: "initial" }}
                        >
                            {persons
                                .sort((a, b) =>
                                    a.bestKnownName.localeCompare(
                                        b.bestKnownName
                                    )
                                )
                                .map(person => {
                                    const selected = isItemSelected(person);
                                    return (
                                        <div
                                            key={person.perId}
                                            style={slickSlideStyle}
                                        >
                                            <Box
                                                sx={{
                                                    padding: "16px",
                                                    height: "31px",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    cursor: "pointer",
                                                    backgroundColor: selected
                                                        ? "#104860"
                                                        : "#F3F3F3",
                                                    color: selected
                                                        ? "#FFFFFF"
                                                        : "#104860",
                                                    transition:
                                                        "background-color 0.1s, color 0.1s",
                                                    borderRadius: "4px",
                                                    "&:hover": {
                                                        backgroundColor: selected
                                                            ? "#043348"
                                                            : "#DCDCDC",
                                                        color: selected
                                                            ? "#FFFFFF"
                                                            : "#104860"
                                                    }
                                                }}
                                                onClick={() =>
                                                    handleItemClick(person)
                                                }
                                            >
                                                <Typography
                                                    variant="body1"
                                                    sx={{ textAlign: "center" }}
                                                >
                                                    {person.label}
                                                </Typography>
                                            </Box>
                                        </div>
                                    );
                                })}
                        </Slider>
                    </Box>
                )}
            </Box>
            <Box
                sx={{
                    minHeight: "15px",
                    display: "flex",
                    alignItems: "flex-start",
                    justifyContent: "center"
                }}
            >
                <Snackbar
                    open={isError}
                    autoHideDuration={3000}
                    // onClose={handleClose}
                    anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
                >
                    <Alert
                        severity="error"
                        ref={alertRef}
                        sx={{
                            width: "100%",
                            // maxWidth: "600px",
                            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                            borderRadius: "4px"
                        }}
                    >
                        <FormattedMessage
                            id="map.max.select.person.warning"
                            defaultMessage="最多可同時選擇三位人物。"
                        />
                    </Alert>
                </Snackbar>
            </Box>
        </>
    );
};

TraceMapSearch.propTypes = {
    onPersonSelectedChange: PropTypes.func.isRequired,
    personSelected: PropTypes.array,
    carouselRef: PropTypes.object,
    alertRef: PropTypes.object
};

export default TraceMapSearch;
