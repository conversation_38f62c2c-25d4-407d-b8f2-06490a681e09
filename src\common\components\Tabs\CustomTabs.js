import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import React from "react";

const CustomTabs = ({
    TABS,
    tabIdx,
    handleTabChange,
    activeBackgroundColor = "#104860",
    inactiveBackgroundColor = "#F3F3F3",
    activeTextColor = "#ffffff",
    inactiveTextColor = "#104860"
}) => (
    <Tabs
        value={tabIdx}
        onChange={handleTabChange}
        aria-label="custom tabs"
        sx={{
            "& .MuiTabs-indicator": {
                display: "none"
            },
            "& .MuiTab-root": {
                color: inactiveTextColor,
                backgroundColor: inactiveBackgroundColor,
                // minWidth: "auto",
                width: "160px",
                borderRadius: "4px",
                padding: "12px 40px",
                fontSize: "14px",
                "&:hover": {
                    opacity: 0.8
                },
                "&.Mui-selected": {
                    // 將 Mui-selected 移到 MuiTab-root 內部
                    backgroundColor: `${activeBackgroundColor} !important`,
                    color: `${activeTextColor} !important`
                }
            }
        }}
    >
        {TABS.map((tab, index) => (
            <Tab key={index} label={tab.label} disabled={tab.disabled} />
        ))}
    </Tabs>
);

export default CustomTabs;
