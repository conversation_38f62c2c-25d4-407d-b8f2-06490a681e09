import { Api, readHkbdbData } from "../../../api/hkbdb/Api";

export const getNextNameId = async classType => {
    if (!(classType && ["Person", "Organization"].includes(classType)))
        return null;
    const apiStr = Api.getNextNameId().replace("{class}", classType);

    try {
        const res = await readHkbdbData(apiStr, 120 * 1000);
        if (res) {
            const { data } = res;
            if (data[0]) {
                return data[0].maxNum;
            }
        }
    } catch (err) {
        // print
        console.error("getNextNameId func:err:", err.message);
        return null;
    }
};

export const fetchOptionList = async (
    type,
    value,
    limit = 10,
    timeout = 1000
) => {
    try {
        let apiStr;
        //
        if ((type || "").toLowerCase() === "person") {
            // api
            apiStr = Api.getClassPersonList()
                .replaceAll("{limit}", limit.toString())
                // 必須使用除了 {name}之外的 parameter, 避免空白字元被換成底線,
                .replace("{keyword}", value);
        } else if ((type || "").toLowerCase() === "organization") {
            // api
            apiStr = Api.getClassOrgList()
                .replaceAll("{limit}", limit.toString())
                // 必須使用除了 {name}之外的 parameter, 避免空白字元被換成底線,
                .replace("{keyword}", value);
        }
        // 24.3.1 原著或版本資料的下拉選單內容更改為顯示Publication
        else if ((type || "").toLowerCase() === "writtenwork") {
            apiStr = Api.getClassList()
                .replace("{limit}", limit.toString())
                .replace("{class}", "Publication")
                .replace("{name}", value);
        } else {
            apiStr = Api.getClassList()
                .replace("{limit}", limit.toString())
                .replace("{class}", type)
                .replace("{name}", value);
        }
        // { data: [], error: err.message, durationSS?: };
        const res = await readHkbdbData(apiStr, timeout.toString());
        // safe data: 一定要有 value & label
        const safeData = Array.isArray(res?.data)
            ? res.data.filter(item => item.value && item.label)
            : [];

        return {
            ...(res || {}),
            data: safeData
        };
    } catch (e) {
        return {
            data: [],
            error: e.message
        };
    }
};

export const convertToStringArray = data => {
    // Input 的 value 必需存字串或字串陣列
    return data.map(d => {
        // {label, value}
        if (Object.keys(d).indexOf("label") > -1) {
            return d.label;
        }
        // 字串
        return d;
    });
};

export const isStringInArray = data => {
    // ["this is a value"]: return true
    // [{label, value}]: return false
    // FIXME: 也許有更好的處理方式
    return !!(
        data &&
        Array.isArray(data) &&
        data.length === 1 &&
        typeof data[0] === "string"
    );
};

export const _reformatData = (data, ontologyType) => {
    const { srcId, eventId, propertyBindRangeStr, values, graph } = data;

    // values: [{ value: "" }, { label: "" }]
    const newValues = values.map(obj => {
        if (!obj) return;
        return obj.value || obj.label || obj;
    });

    return {
        graph,
        srcId: eventId || srcId,
        classType: ontologyType,
        value: { [propertyBindRangeStr]: newValues || [] }
    };
};
