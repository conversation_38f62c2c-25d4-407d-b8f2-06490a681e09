/* eslint-disable */

import React from 'react';
import { IconButton, Grid, Box, Fade } from '@material-ui/core';
import NavigateBeforeIcon from '@material-ui/icons/NavigateBefore';
import NavigateNextIcon from '@material-ui/icons/NavigateNext';
// import { useIntl } from 'react-intl';
import Carousel from 'react-material-ui-carousel';
// import NoData from '../../../images/noData.webp';
import './lightBox.scss';
import collectionConfig from "../collectionConfig";
import {organizationUrl, peopleUrl} from "../../../services/common";
import {CLASS_PREFIX} from "../../../config/config-ontology";
import queryString from "query-string";
import {bs64Encode} from "../../../common/codes";
import {removeStartPrefix} from "../../Hkbdb/EntityComponent/helper";

const DEFAULT_ZOOM_STEP = 0.3;
const DEFAULT_LARGE_ZOOM = 4;
function getXY(e) {
    let x = 0;
    let y = 0;
    if (e.touches && e.touches.length) {
        x = e.touches[0].pageX;
        y = e.touches[0].pageY;
    } else {
        x = e.pageX;
        y = e.pageY;
    }
    return { x, y };
}
function Cond(props) {
    if (!props.condition) return null;
    return <React.Fragment>{props.children}</React.Fragment>;
}

function withUseIntl(Component) {
    return function WrappedComponent(props) {
        const useIntlHook = useIntl();
        return <Component {...props} useIntlHook={useIntlHook} />;
    };
}

class Lightbox extends React.Component {
    initX = 0;
    initY = 0;
    lastX = 0;
    lastY = 0;
    _cont = React.createRef();
    state = {
        x: 0,
        y: 0,
        zoom: 1,
        rotate: 0,
        loading: true,
        moving: false,
        current: this.props?.startIndex ?? 0,
        multi: this.props?.images?.length > 1 ? true : false,
        isImageFailed: false,
        ...(this.props?.carouselOn
            ? { carouselOn: true, carouselLoading: true, currentCarousel: 0 }
            : { carouselOn: false, carouselLoading: false }),
    };
    createTransform = (x, y, zoom, rotate) => `translate3d(${x}px,${y}px,0px) scale(${zoom}) rotate(${rotate}deg)`;
    stopSideEffect = e => e.stopPropagation();
    getCurrentImage = (s, p) => {
        if (!s.multi) return (s.carouselOn ? p.images[0] : p.image) ?? 'none';
        const index = s.carouselOn ? s.currentCarousel : s.current;
        return p.images[index]?.url ?? p.images?.[index] ?? 'none';
    };
    getCurrentTitle = (s, p) => {
        if (!s.multi) return p.title ?? '';
        return p.images?.[s.current]?.title ?? p.title ?? '';
    };
    resetZoom = () => this.setState({ x: 0, y: 0, zoom: 1, rotate: 0, ...(this.props.carouselOn && { carouselLoading: true }) });
    shockZoom = e => {
        let { zoomStep = DEFAULT_ZOOM_STEP, allowZoom = true, doubleClickZoom = DEFAULT_LARGE_ZOOM } = this.props;
        if (!allowZoom || !doubleClickZoom) return false;
        this.stopSideEffect(e);
        if (this.state.zoom > 1) return this.resetZoom();
        const _z = (zoomStep < 1 ? Math.ceil(doubleClickZoom / zoomStep) : zoomStep) * zoomStep;
        const _xy = getXY(e);
        const _cbr = this._cont.current?.getBoundingClientRect?.();
        const _ccx = _cbr.x + _cbr.width / 2;
        const _ccy = _cbr.y + _cbr.height / 2;
        const x = (_xy.x - _ccx) * -1 * _z;
        const y = (_xy.y - _ccy) * -1 * _z;
        this.setState({ x, y, zoom: _z });
    };
    navigateImage = (direction, e) => {
        this.stopSideEffect(e);
        let current = 0;
        switch (direction) {
            case 'next':
                current = this.state.current + 1;
                break;
            case 'prev':
                current = this.state.current - 1;
                break;
        }
        if (current >= this.props.images.length) current = 0;
        else if (current < 0) current = this.props.images.length - 1;
        this.setState({ current, x: 0, y: 0, zoom: 1, rotate: 0, loading: true });
        if (typeof this.props.onNavigateImage === 'function') {
            this.props.onNavigateImage(current);
        }
    };
    navigateCarouselImage = (direction, e) => {
        this.stopSideEffect(e);
        let current = 0;
        switch (direction) {
            case 'next':
                current = this.state.currentCarousel + 1;
                break;
            case 'prev':
                current = this.state.currentCarousel - 1;
        }
        if (current >= this.props.images.length) current = 0;
        else if (current < 0) current = this.props.images.length - 1;

        this.setState({
            currentCarousel: current,
            carouselLoading: true,
            x: 0,
            y: 0,
            zoom: 1,
            rotate: 0,
        });
        if (typeof this.props.onNavigateImage === 'function') {
            this.props.onNavigateImage(current);
        }
    };
    startMove = e => {
        if (this.state.zoom <= 1) return false;
        this.setState({ moving: true });
        let xy = getXY(e);
        this.initX = xy.x - this.lastX;
        this.initY = xy.y - this.lastY;
    };
    duringMove = e => {
        if (!this.state.moving) return false;
        let xy = getXY(e);
        this.lastX = xy.x - this.initX;
        this.lastY = xy.y - this.initY;
        this.setState({
            x: xy.x - this.initX,
            y: xy.y - this.initY,
        });
    };
    endMove = e => this.setState({ moving: false });
    resetProps = () => {
        this.initX = 0;
        this.initY = 0;
        this.lastX = 0;
        this.lastY = 0;
    };
    applyZoom = type => {
        let { zoomStep = DEFAULT_ZOOM_STEP, carouselOn, images = [] } = this.props;
        switch (type) {
            case 'in':
                this.setState({ zoom: this.state.zoom + zoomStep });
                break;
            case 'out':
                let newZoom = this.state.zoom - zoomStep;
                if (newZoom < 1) break;
                else if (newZoom === 1) {
                    this.setState({ x: 0, y: 0, zoom: 1, rotate: 0, ...(carouselOn && images.length && { carouselLoading: true }) });
                    this.resetProps();
                } else this.setState({ zoom: newZoom });
                break;
            case 'reset':
                this.resetZoom();
                break;
        }
    };
    applyRotate = type => {
        switch (type) {
            case 'cw':
                this.setState({ rotate: this.state.rotate + 90 });
                break;
            case 'acw':
                this.setState({ rotate: this.state.rotate - 90 });
                break;
        }
    };
    reset = e => {
        const { carouselOn, images = [] } = this.props;
        this.stopSideEffect(e);
        this.setState({
            x: 0,
            y: 0,
            zoom: 1,
            rotate: 0,
            ...(carouselOn && images.length && { carouselLoading: true }),
        });
        this.resetProps();
    };
    exit = e => {
        if (typeof this.props.onClose === 'function') return this.props.onClose(e);
        console.error('No Exit function passed on prop: onClose. Clicking the close button will do nothing');
    };
    shouldShowReset = () => this.state.x || this.state.y || this.state.zoom !== 1 || this.state.rotate !== 0;
    canvasClick = e => {
        let { clickOutsideToExit = true } = this.props;
        if (clickOutsideToExit && this.state.zoom <= 1) return this.exit(e);
    };
    keyboardNavigation = e => {
        let { allowZoom = true, allowReset = true, carouselOn = false } = this.props;
        let { multi, x, y, zoom } = this.state;
        if (e.ctrlKey) {
            switch (e.key) {
                case 'ArrowLeft':
                    this.props.onLeftClick();
                    break;
                case 'ArrowRight':
                    this.props.onRightClick();
                    break;
            }
        } else {
            switch (e.key) {
                case 'ArrowLeft':
                    if (multi && zoom === 1) carouselOn ? this.navigateCarouselImage('prev', e) : this.navigateImage('prev', e);
                    else if (zoom > 1) this.setState({ x: x - 20 });
                    break;
                case 'ArrowRight':
                    if (multi && zoom === 1) carouselOn ? this.navigateCarouselImage('next', e) : this.navigateImage('next', e);
                    else if (zoom > 1) this.setState({ x: x + 20 });
                    break;
                case 'ArrowUp':
                    if (zoom > 1) this.setState({ y: y + 20 });
                    break;
                case 'ArrowDown':
                    if (zoom > 1) this.setState({ y: y - 20 });
                    break;
                case '+':
                    if (allowZoom) this.applyZoom('in');
                    break;
                case '-':
                    if (allowZoom) this.applyZoom('out');
                    break;
                case 'Escape':
                    if (allowReset && this.shouldShowReset()) this.reset(e);
                    else this.exit(e);
                    break;
            }
        }
    };
    componentDidMount() {
        document.body.classList.add('lb-open-lightbox');
        let { keyboardInteraction = true } = this.props;
        if (keyboardInteraction) document.addEventListener('keyup', this.keyboardNavigation);
    }
    componentWillUnmount() {
        document.body.classList.remove('lb-open-lightbox');
        let { keyboardInteraction = true } = this.props;
        if (keyboardInteraction) document.removeEventListener('keyup', this.keyboardNavigation);
    }

    dealContent(dataObj, type) {
        switch (type) {
            case collectionConfig.manuScript.pathName:
                // const { name1 = '', date1 = '', type1 = '', creator1 = '', unit1 = '', other1 = '', detail1 = '', url1 = '' } = dataObj || {};
                const tmpText = `${dataObj.authorDisplay}`;
                return tmpText;
            case collectionConfig.featuredPub.pathName:
                // const { name = '', date = '', type = '', creator = '', unit = '', other = '', detail = '', url = '' } = dataObj || {};
                const tmpText2 = `${dataObj.authorDisplay}`;
                return tmpText2;

        }
    }
    render() {
        // const { formatMessage } = this.props.useIntlHook;
        let image = this.getCurrentImage(this.state, this.props);
        let title = this.getCurrentTitle(this.state, this.props);
        let {
            allowZoom = true,
            allowRotate = true,
            buttonAlign = 'flex-end',
            showTitle = true,
            allowReset = true,
            imgInfo,
            type,
            onLeftClick,
            onRightClick,
            images = [],
            curData,
            colType,
            isLoading
        } = this.props;
        let { x, y, zoom, rotate, multi, loading, carouselLoading, moving, carouselOn = false, isImageFailed = false } = this.state;
        let _reset = allowReset && this.shouldShowReset();
        const infos = this.dealContent(imgInfo, type);

        const authorUrl = curData.map(b => {
            let url = "";
                url = peopleUrl(
                    removeStartPrefix(b.hasRelatedPerson || b.hasAuthor
                        , CLASS_PREFIX.Person),
                    queryString.stringify({
                        name: bs64Encode(b.bestKnownName || "")
                    })
                );
            return url

        });

        const publisherUrl = curData.map(b => {
            let url = "";
            url = organizationUrl(
                removeStartPrefix(b.hasPublisher
                    , CLASS_PREFIX.Organization),
                queryString.stringify({
                    name: bs64Encode(b.publisher || "")
                })
            );
            return url
        });

        return (
            <div className="lb-container">
                <div className="lb-header" style={{ justifyContent: buttonAlign }}>
                    <Cond condition={showTitle && title}>
                        <div
                            className="lb-title"
                            style={{
                                display: buttonAlign === 'center' ? 'none' : 'flex',
                                order: buttonAlign === 'flex-start' ? '2' : 'unset',
                            }}
                        >
							<span title={title} style={{ textAlign: buttonAlign === 'flex-start' ? 'right' : 'left' }}>
								{title}
							</span>

                        </div>
                    </Cond>
                    <Cond condition={buttonAlign === 'center' || _reset}>
                        <div
                            title="Reset"
                            style={{ order: buttonAlign === 'flex-start' ? '1' : 'unset' }}
                            className={`lb-button lb-icon-reset lb-hide-mobile reload ${_reset ? '' : 'lb-disabled'}`}
                            onClick={this.reset}
                        />
                    </Cond>
                    <Cond condition={multi && !carouselOn}>
                        <div
                            title="Previous"
                            className="lb-button lb-icon-arrow prev lb-hide-mobile"
                            onClick={e => this.navigateImage('prev', e)}
                        />
                        <div
                            title="Next"
                            className="lb-button lb-icon-arrow next lb-hide-mobile"
                            onClick={e => this.navigateImage('next', e)}
                        />
                    </Cond>
                    <Cond condition={allowZoom}>
                        <div title="Zoom In" className="lb-button lb-icon-zoomin zoomin" onClick={() => this.applyZoom('in')} />
                        <div
                            title="Zoom Out"
                            className={`lb-button lb-icon-zoomout zoomout ${zoom <= 1 ? 'lb-disabled' : ''}`}
                            onClick={() => this.applyZoom('out')}
                        />
                    </Cond>
                    <Cond condition={allowRotate}>
                        <div title="Rotate left" className="lb-button lb-icon-rotate rotatel" onClick={() => this.applyRotate('acw')} />
                        <div title="Rotate right" className="lb-button lb-icon-rotate rotater" onClick={() => this.applyRotate('cw')} />
                    </Cond>
                    <div
                        title="Close"
                        className="lb-button lb-icon-close close"
                        style={{ order: buttonAlign === 'flex-start' ? '-1' : 'unset' }}
                        onClick={e => this.exit(e)}
                    />
                </div>
                <div className={`canvas-flex${carouselLoading ? ' lb-loading' : ''}`}>
                    <IconButton onClick={onLeftClick} disabled={!onLeftClick}>
                        <NavigateBeforeIcon style={{ color: onLeftClick ? '#fff' : 'rgba(255, 255, 255, 0.2)' }} />
                    </IconButton>
                    <div
                        className={`lb-canvas${loading || carouselLoading ? ' lb-loading' : ''}`}
                        ref={this._cont}
                        onClick={e => this.canvasClick(e)}
                    >
                        <Fade in={!carouselLoading} timeout={{ appear: 0, enter: 1000, exit: 100 }}>
                            {!isLoading ?
                            <div className="lb-canvas">
                                {/* display none to fix text flash when loading image */}
                                {colType==='featuredPub'?
                                    <>
                                        {curData && curData.map((i)=>{
                                            return (
                                                <div className="img-info" key={i.desc}>
                                                    <div className='img-info-ctxBox'>
                                                        <div className='img-info-titlediv'>
                                                            <a
                                                                href={authorUrl}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                style={{
                                                                    textDecoration: "none",
                                                                    color: "inherit",
                                                                    borderBottom:'1px solid',cursor:'pointer',display:'inline-block'
                                                                }}
                                                            >
                                                                {i.authorDisplay}
                                                            </a>
                                                            <span> {i.label?.includes("《")?i.label:`《${i.label}》`}</span>
                                                        </div>
                                                        <div className='img-info-contentdiv'>
                                                            <span>{i.hasPlaceOfPublication &&`${i.hasPlaceOfPublication}：`}</span>
                                                            {i.publisher &&
                                                                <>
                                                                    <a
                                                                        href={publisherUrl}
                                                                        target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        style={{
                                                                            textDecoration: "none",
                                                                            color: "inherit",
                                                                            borderBottom:'1px solid',cursor:'pointer',display:'inline-block'
                                                                        }}
                                                                    >
                                                                        {i.publisher&&`${i.publisher}`}
                                                                    </a>
                                                                    <span>，</span>
                                                                </>
                                                            }

                                                            <span>{i.inceptionDisplayDate}。</span>
                                                        </div>
                                                        <div className='img-info-contentdiv'>
                                                            {i.desc?.split('\n').map(i=><p key={i}>{i}</p>)}
                                                        </div>
                                                    </div>
                                                    <div className='img-info-ctxBox'>
                                                        <div className='img-info-subtitlediv'>
                                                            <span>{i.authorDisplayEn&&`${i.authorDisplayEn}. `}</span>
                                                            <span style={{fontStyle:'italic'}}>{i.publicationDisplayEn&&`${i.publicationDisplayEn.replace(/’/g, "'")}.`}</span>
                                                        </div>
                                                        <div className='img-info-contentdiv'>
                                                            <span>{i.placeOfPublicationDisplayEn&&`${i.placeOfPublicationDisplayEn} : `}</span>
                                                            <span>{i.publisherDisplayEn&&`${i.publisherDisplayEn}, `}</span>
                                                            <span>{i.inceptionDisplayDateEn}.</span>
                                                        </div>
                                                    </div>
                                            </div>)
                                        })}
                                    </>
                                    :
                                    <>
                                        {curData && curData.map((i)=>{
                                            return (
                                                <div className="img-info" key={i.label}>
                                                    <div className='img-info-ctxBox'>
                                                        <div className='img-info-titlediv'>
                                                            <a
                                                                href={authorUrl}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                style={{
                                                                    textDecoration: "none",
                                                                    color: "inherit",
                                                                    borderBottom:'1px solid',cursor:'pointer',display:'inline-block'
                                                                }}
                                                            >
                                                                {i.authorDisplay}
                                                            </a>
                                                            <span> {i.label&&`${i.label}`}</span>
                                                        </div>
                                                        {i.collectIn &&
                                                            <div className='img-info-contentdiv'>
                                                                <span>收錄於 </span>
                                                                <a
                                                                    href={i.externalLinks}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    style={{
                                                                        textDecoration: "none",
                                                                        color: "inherit",
                                                                        borderBottom:'1px solid',cursor:'pointer',display:'inline-block'
                                                                    }}
                                                                >
                                                                    {i.collectIn}
                                                                </a>
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                            )
                                    })}
                                    </>
                                }

                                {carouselOn ? (
                                    <Box style={{ minWidth: '15rem', color: 'white' }}>
                                        {zoom > 1 || rotate !== 0 || images.length === 0 ? (
                                            <img
                                                draggable="false"
                                                style={{
                                                    transform: this.createTransform(x, y, zoom, rotate),
                                                    cursor: zoom > 1 ? 'grab' : 'unset',
                                                    transition: moving ? 'none' : 'all 0.1s',
                                                }}
                                                onMouseDown={e => this.startMove(e)}
                                                onTouchStart={e => this.startMove(e)}
                                                onMouseMove={e => this.duringMove(e)}
                                                onTouchMove={e => this.duringMove(e)}
                                                onMouseUp={e => this.endMove(e)}
                                                onMouseLeave={e => this.endMove(e)}
                                                onTouchEnd={e => this.endMove(e)}
                                                onClick={e => this.stopSideEffect(e)}
                                                onDoubleClick={e => this.shockZoom(e)}
                                                onLoad={e => this.setState({ carouselLoading: false, loading: false })}
                                                onError={e =>
                                                    this.setState({ carouselLoading: false, loading: false, isImageFailed: true })
                                                }
                                                className={`lb-img${carouselLoading ? ' lb-loading' : ''}`}
                                                title={title}
                                                src={image}
                                                alt={title}
                                            />
                                        ) : (
                                            <Grid container direction="column" justifyContent="center" alignContent="center" spacing={1}>
                                                <Grid item container justifyContent="center">
                                                    <Box>
                                                        <Carousel
                                                            index={this.state.currentCarousel}
                                                            navButtonsAlwaysInvisible
                                                            indicators={false}
                                                            fullHeightHover={false}
                                                            animation="slide"
                                                            autoPlay={false}
                                                            swipe={false}
                                                            changeOnFirstRender
                                                        >
                                                            {images.map(image => (
                                                                <img
                                                                    key={image.split('/').pop()}
                                                                    draggable="false"
                                                                    onLoad={e => this.setState({ carouselLoading: false, loading: false })}
                                                                    className={`lb-img`}
                                                                    title={title}
                                                                    src={image}
                                                                    alt={title}
                                                                    style={{ cursor: 'auto' }}
                                                                    onError={e =>
                                                                        this.setState({
                                                                            carouselLoading: false,
                                                                            loading: false,
                                                                            isImageFailed: true,
                                                                        })
                                                                    }
                                                                />
                                                            ))}
                                                        </Carousel>
                                                    </Box>
                                                </Grid>
                                                {/* display none to fix text flash when loading image */}
                                                <Grid container item style={{ display: carouselLoading ? 'none' : '' }}>
                                                    <Grid container item justifyContent="flex-end" xs={4}>
                                                        <IconButton
                                                            style={{
                                                                width: '16px',
                                                                height: '16px',
                                                                margin: '4px 12px 4px 0',
                                                                objectFit: 'contain',
                                                                ...(!multi && { opacity: 0.2 }),
                                                            }}
                                                            disabled={!multi}
                                                            onClick={e => this.navigateCarouselImage('prev', e)}
                                                        >
                                                            <NavigateBeforeIcon style={{ color: '#fff' }} />
                                                        </IconButton>
                                                    </Grid>
                                                    <Grid container item justifyContent="center" xs={4}>
                                                        <Box className={`lb-box-carousel-index`}>{`${this.state.currentCarousel + 1} / ${
                                                            this.props.images?.length
                                                        }`}</Box>
                                                    </Grid>
                                                    <Grid container item justifyContent="flex-start" xs>
                                                        <IconButton
                                                            style={{
                                                                width: '16px',
                                                                height: '16px',
                                                                margin: '4px 0 4px 12px',
                                                                objectFit: 'contain',
                                                                ...(!multi && { opacity: 0.2 }),
                                                            }}
                                                            disabled={!multi}
                                                            onClick={e => this.navigateCarouselImage('next', e)}
                                                        >
                                                            <NavigateNextIcon style={{ color: '#fff' }} />
                                                        </IconButton>
                                                    </Grid>
                                                </Grid>
                                            </Grid>
                                        )}
                                    </Box>
                                ) : (
                                    <img
                                        draggable="false"
                                        style={{
                                            transform: this.createTransform(x, y, zoom, rotate),
                                            cursor: zoom > 1 ? 'grab' : 'unset',
                                            transition: moving ? 'none' : 'all 0.1s',
                                        }}
                                        onMouseDown={e => this.startMove(e)}
                                        onTouchStart={e => this.startMove(e)}
                                        onMouseMove={e => this.duringMove(e)}
                                        onTouchMove={e => this.duringMove(e)}
                                        onMouseUp={e => this.endMove(e)}
                                        onMouseLeave={e => this.endMove(e)}
                                        onTouchEnd={e => this.endMove(e)}
                                        onClick={e => this.stopSideEffect(e)}
                                        onDoubleClick={e => this.shockZoom(e)}
                                        onLoad={e => this.setState({ loading: false })}
                                        className={`lb-img${loading ? ' lb-loading' : ''}`}
                                        title={title}
                                        src={image}
                                        alt={title}
                                        onError={e => this.setState({ loading: false, isImageFailed: true })}
                                    />
                                )}
                                {carouselOn ? (
                                    _reset ? (
                                        <div className="mobile-controls lb-show-mobile">
                                            <div title="Reset" className="lb-button lb-icon-reset reload" onClick={this.reset} />
                                        </div>
                                    ) : null
                                ) : (
                                    <div className="mobile-controls lb-show-mobile">
                                        {multi ? (
                                            <div
                                                title="Previous"
                                                className="lb-button lb-icon-arrow prev"
                                                onClick={e => this.navigateImage('prev', e)}
                                            />
                                        ) : null}
                                        {_reset ? (
                                            <div title="Reset" className="lb-button lb-icon-reset reload" onClick={this.reset} />
                                        ) : null}
                                        {multi ? (
                                            <div
                                                title="Next"
                                                className="lb-button lb-icon-arrow next"
                                                onClick={e => this.navigateImage('next', e)}
                                            />
                                        ) : null}
                                    </div>
                                )}
                            </div>:<div>Loading...</div>
                            }
                        </Fade>
                    </div>
                    <IconButton onClick={onRightClick} disabled={!onRightClick}>
                        <NavigateNextIcon style={{ color: onRightClick ? '#fff' : 'rgba(255, 255, 255, 0.2)' }} />
                    </IconButton>
                </div>
            </div>
        );
    }
}
export default Lightbox;
