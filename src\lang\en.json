{"about.about-title": "About HKBDB", "about.about-content": "HKBDB is a graph database combining six datasets amounting to approximately 19,000 personal entries. It is envisioned as an open platform for researchers to use and contribute to with their own data sets, offering a number of analytical instruments which can be freely adapted to support investigations at the intersection of history, sociology, literary history, and digital humanities.", "about.tool-title": "HKBDB as a Tool", "about.tool-content": "HKBDB is a tool for the prosopographical study of social groups in Taiwan. By allowing users to systematically correlate commonalities in the lives of individuals with each other, such as place of birth, education, occupation, marriage, family background, and social status, HKBDB reveals sociologically relevant patterns which only become manifest from a birds-eye perspective.", "about.functions-title": "HKBDB and its Functions", "about.functions-content": "HKBDB has a range of functions. It can display all information about any person, organization, or place gleaned from different sources which are contained in the database. Furthermore, users can make detailed semantic queries across existing data sets, as they are stored as linked data. Users are welcome to either display or download the results of their queries for further analysis.", "about.funding-title": "Funding", "about.funding-content": "HKBDB is being developed as part of a bilateral project funded by the Czech Grant Agency and the Ministry of Science and Technology, Taiwan (registration number: 17-03529J), called “Concepts in Contexts: A Corpus-based Approach to the Literary Field in Early Post-war Taiwan and Its Application to the Sociology of Literature.” The project is complemented by the Taiwan Early Post-war Corpus which serves to (1) identify salient political concepts through keyword analysis, and (2) align conceptual distinctions with publication patterns. In combination with the Taiwan Early Post-war Corpus, HKBDB will facilitate the study of underlying correlations between the social backgrounds of actors in the literary field and a variety of linguistic features in order to create a richer account of post-war literary dynamics.", "password": "Password", "login.password-reset-sent": "Password reset email has been sent, please check your mail box.", "login.heading": "Login to HKBDB", "button.login": "Log in", "login.userLogin.title": "User Login", "login.userLogin.text1": "Registered users please login with your email.", "login.userSignup.title": "Account Registration", "login.userSignup.text1": "Note: \nCurrently we only open to members of **tertiary institutions in Hong Kong** using **institutional email** for registration (Please enter required information according to instruction);", "login.userSignup.text2": "Other researchers please email the following information to <a href=\"mailto:[email]\">[email]</a> for registration:", "login.userSignup.info.name": "Last & First Name (Chinese / English)", "login.userSignup.info.institution": "Institution (if applicable)", "login.userSignup.info.post": "Post (if applicable)", "login.userSignup.info.email": "Email", "login.userSignup.info.researchPurpose": "Research purpose", "login.comment": "註: 暫只開放予**香港大專院校**人士使用**院校電郵**註冊 (請選擇”Sign in with email” 然後按指示輸入所需資料)；其他研究人員請電郵至<a href=\"mailto:[email]\">[email]</a>申請註冊。", "login.send-reset-email": "Send Password Reset Email", "browse.clickClass": "Select any class on the left to display individuals it contains.", "browse.individuals": "Individuals", "browse.loadingIndividuals": "Loading individuals...", "editbuttons.add": "Add", "editbuttons.edit": "Edit", "editbuttons.done": "Done", "editbuttons.cancel": "Cancel", "general.noChange": "Nothing changed.", "general.created": "Successfully created.", "general.selectOpt": "Select an option", "general.dataset": "Source Dataset", "general.datasetTitle": "Dataset*", "general.datasetEmpty": "Dataset is empty!", "general.btnDup": "Duplicate", "general.btnCancel": "Cancel", "general.btnSave": "Save", "general.btnEdit": "Edit", "general.btnDel": "Del", "basic.title": "Basic Information", "basic.editTitle": "Edit Basic Information", "basic.addTitle": "Add new Basic Information", "basic.rowTitle": "Title", "basic.value": "Value", "basic.name": "Name", "basic.pinyin": "<PERSON><PERSON><PERSON>", "basic.othername": "Other names", "basic.penname": "Pen name", "basic.gender": "Gender", "basic.nthchild": "Nth Child", "basic.birth": "Birth (DD/MM/YYYY)", "basic.death": "Death (DD/MM/YYYY)", "basic.choronym": "Choronym", "basic.nameError": "The value of Name is incorrect. Example: 王大明@zh", "basic.otherNameError": "The value of Other Names is incorrect. Example: 王大明@zh", "basic.penNameError": "The value of Pen name is incorrect. Example: 王大明@zh", "basic.pinyinError": "The value of Pinyin is incorrect. Example: WangDaMing@en", "basic.genderError": "The value of Gender is incorrect. Example: Male/Female", "basic.nthChildError": "The value of Nth Child is incorrect. Example: FirstSon/FirstDaughter ...", "basic.birthError": "The value of Birth is incorrect. Example: 20/10/1945", "basic.deathError": "The value of Death is incorrect. Example: 20/10/1945", "people.Information": "Information", "people.Relation": "Relation", "people.timeline": "Timeline", "people.sna": "Social Network", "people.familyTree": "FamilyTree", "people.Information.basic": "Basic", "people.Information.nameNode": "NamesInfo", "people.Information.nameNode_org": "Organization Names", "people.Information.imprisonment": "Imprisonment", "people.Information.family": "Family", "people.Information.education": "Education", "people.Information.employment": "Employment", "people.Information.membership": "Membership", "people.Information.otherwork": "Other Work", "people.Information.publication": "Publication", "people.Information.article": "Article", "people.Information.event": "Event", "people.Information.award": "Award", "people.Information.status": "Status", "people.Information.comment": "Comment", "people.Information.source": "Source", "people.sna.target": "Target：", "people.sna.people": "People：", "people.sna.organization": "Organization：", "people.sna.member": "Member：", "people.sna.linkLabel": "Link label：", "newentity.person": "Person", "newentity.name": "Name", "newentity.title": "New entity", "newentity.add": "Add", "personDelete.delete": "Delete", "personDelete.no": "No", "personDelete.yes": "Yes", "personDelete.name": "Delete {name}", "personDelete.remind": "Are you sure you want to delete {name}?", "personNew.new": "New", "personNew.no": "No", "personNew.yes": "Yes", "personNew.title": "Create Person", "personNew.person": "Person*", "selectGraph.dataset": "Dataset*", "selectGraph.placeholder": "Select an option", "selectOption.placeholder": "Input the new name", "org.information.administrativeLevel": "AdministrativeLevel", "administrativeLevel.editTitle": "Edit administrativeLevel Information", "administrativeLevel.addTitle": "Add new administrativeLevel Information", "administrativeLevel.rowTitle": "Type", "administrativeLevel.value": "Organization", "administrativeLevel.central": "Central", "administrativeLevel.provincial": "Provincial", "administrativeLevel.local": "Local", "basic.organization": "Organization", "basic.occursInPlace": "Location", "basic.occursInTime": "Period", "basic.founder": "Founder", "basic.money": "Money", "basic.description": "Description", "basic.founderError": "The value of Founder is incorrect. Example: 王大明@zh", "basic.startDateError": "The value of start date is incorrect. Example: 20/10/1945", "basic.endDateError": "The value of end date is incorrect. Example: 20/10/1945", "general.filed.dataset": "Source Dataset*", "org.information.economicEvent": "EconomicEvent", "economicEvent.editTitle": "Edit Economic Event", "economicEvent.addTitle": "Add new Economic Event", "economicEvent.mustDifferent": "Organization A and B must different!", "economicEvent.orgARequired": "Organization A is required", "economicEvent.orgBRequired": "Organization B is required", "economicEvent.actionRequired": "Action is required", "economicEvent.mustHaveTargetOrg": "Either organization A or organization B must be the target organization", "economicEvent.field.orgA": "Organization A*", "economicEvent.field.action": "Action*", "economicEvent.field.orgB": "Organization B*", "economicEvent.field.time": "Time", "economicEvent.orgA": "Organization A", "economicEvent.action": "Action", "economicEvent.orgB": "Organization B", "economicEvent.occursInTime": "OccursInTime", "orgDelete.delete": "Delete", "orgDelete.no": "No", "orgDelete.yes": "Yes", "orgDelete.name": "Delete {name}", "orgDelete.remind": "Are you sure you want to delete {name}?", "orgNew.new": "New", "orgNew.no": "No", "orgNew.yes": "Yes", "orgNew.title": "Create Organization", "orgNew.organization": "Organization*", "member.editTitle": "Edit Member", "member.addTitle": "Add new Member", "member.personRequired": "Person is required", "member.positionRequired": "Position is required", "member.filed.person": "Person*", "member.filed.position": "Position", "member.person": "Person", "member.position": "Position", "societalSector.title": "SocietalSector", "societalSector.editTitle": "Edit SocietalSector", "societalSector.addTitle": "Add new SocietalSector", "societalSector.filed.societal": "Societal*", "societalSector.societal": "Societal", "org.timeline": "Timeline", "timeline.editTitle": "Edit Timeline Information", "timeline.addTitle": "Add new Timeline Information", "timeline.rowTitle": "Type", "timeline.value": "Organization", "timeline.organization": "Organization", "timeline.predecessor": "Predecessor", "timeline.successor": "Successor", "timeline.superordinate": "Superordinate", "timeline.subordinate": "Subordinate", "timeline.dataCantbeTargetOrg": "Data can't be the target organization", "timeline.dataMustDifferent": "Each data must be different value", "org.Information": "Information", "org.Information.basic": "Basic", "org.member": "Member", "org.Information.administrativeLevel": "AdministrativeLevel", "org.Information.societalSector": "SocietalSector", "org.Information.economicEvent": "EconomicEvent", "org.SNA": "Social Network", "org.Evolution": "Evolution", "org.Class": "Hierarchy", "general.translation": "Translation", "general.author": "Author", "storedQueries.myQueries": "My queries", "storedQueries.publicQueries": "Public queries", "button.delete": "Delete", "button.edit": "Edit", "sources.intro-title": "Data sources", "sources.intro-content": "Data sources description", "sources.auda-title": "Author Data", "sources.auda-content": "database description", "sources.hklit-title": "HKLit", "sources.hklit-content": "database description", "sources.hkwpr-title": "HK Writers Profile", "sources.hkwpr-content": "database description", "sources.abcwhkp-title": "An Annotated Bibliography of the Classical Writings of Hong Kong Poets", "sources.abcwhkp-content": "database description", "browse.inputForSearching": "Input for searching", "browse.description": "Description", "query.query": "Advanced Search", "query.description": "Advanced Search allows SPARQL Query for searching. Users can write their own SPARQL queries, choose the Public Query from the right menu as default use or customized for data retrieval. Paste the SPARAL query to the big box below and click “Send” to search. Result entries will be shown below. Registered users can store personal SPARAL queries to “My Query” on the right and download the result dataset.", "query.search": "Search...", "custom.updateButton": "Update", "custom.cancelButton": "Cancel", "custom.saveButton": "Save", "custom.sendButton": "Send", "custom.downloadButton": "Download", "query.limit": "limit", "query.page": "page", "query.myQueries": "My Queries", "query.publicQueries": "Public Queries", "custom.loadButton": "Load", "custom.deleteButton": "Delete", "custom.author": "Author", "custom.translation": "Translation", "custom.email": "Email", "query.boxPrompt": "Type SPARQL query, e.g. SELECT * WHERE \\{ ?s ?p ?o \\}", "map.tab.general": "Map Mode", "map.tab.trace": "Trajectory Map Mode", "map.note": "*Please enlarge the map to view more detailed locations", "map.speed": "Speed", "map.end.time": "End Date", "map.start.time": "Start Date", "map.reset": "Reset", "map.info": "Description", "map.legend": "Trajectory Legend", "map.sidebar.no.info": "No information is currently available.", "map.sidebar.search.hint": "Please search for a person and select a location on the map.", "map.mobile.hint.header": "GIS Available on Desktop Only", "map.mobile.hint.content": "To provide you with the best experience, GIS functionality is only supported on the desktop version.", "map.mobile.hint.sub.content": "Please access this page from a computer for full functionality.", "map.filter.heaeder": "Filters", "map.filter.context": "Please select at least one.", "map.max.select.person.warning": "You can select up to three characters at a time.", "map.current.page": "Go to Page:", "map.total.page": "of {totalPages}", "map.filter.sidebar.birth.place": "Birthplace", "map.filter.sidebar.educational.institution": "Educational Institution", "map.filter.sidebar.work.place": "Workplace", "map.filter.sidebar.award.location": "Award Location", "map.filter.sidebar.event.location": "Event Location", "map.filter.sidebar.death.place": "Place of Death", "map.sidebar.duplicate.note": "Please click on the author’s name to view the sources for different dates", "map.hongkong": "Hong Kong"}