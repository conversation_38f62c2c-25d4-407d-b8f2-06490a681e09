import React, { useContext } from "react";
import {
    Con<PERSON>er,
    Divider,
    <PERSON>rid,
    Header,
    Segment,
    List
} from "semantic-ui-react";

// firebase
import firebase from "firebase/app";
import "firebase/auth";
import StyledFirebaseAuth from "react-firebaseui/StyledFirebaseAuth";

// config
import { Redirect } from "react-router";
import { FormattedMessage, injectIntl } from "react-intl";
import firebaseUiConfig from "../../../config/config-firebase-ui";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

// commons code
import { isEmpty, getFormatUser } from "../../../common/codes";
import { ResponsiveContainer } from "../../../layout/Layout";
import role from "../../../App-role";
import { convert2HtmlEntities } from "../../../common/components/Markdown2React/mdUtils";
import "./signin.scss";
import emailConfig from "../../../config/config-email";

const CONTACT_MAIL_TO = emailConfig.cuhk;

const SignIn = props => {
    const { intl } = props;
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const locale = user && user.locale ? user.locale : "";

    firebaseUiConfig.callbacks = {
        // eslint-disable-next-line no-unused-vars
        signInSuccessWithAuthResult: (authResult, redirectUrl) => {
            const userInfo = getFormatUser(authResult.user);
            // console.log("userInfo", userInfo);
            const { uid, displayName, email } = userInfo;
            if (uid && (displayName || email)) {
                dispatch({
                    type: Act.FIREBASE_LOGIN_USER,
                    payload: userInfo
                });
                // sign in 後不用 refresh anonymousToken
                dispatch({
                    type: Act.REFRESH_ANONYMOUS_TOKEN,
                    payload: false
                });
            }
            return false;
        }
    };

    const textAlign = "left";

    const signupText = [
        {
            id: "login.userSignup.text1",
            defaultMessage:
                "Note: \nCurrently we only open to members of **tertiary institutions in Hong Kong** using **institutional email** for registration (Please enter required information according to instruction);"
        },
        {
            id: "login.userSignup.text2",
            defaultMessage: `Other researchers please email the following information to <a href="mailto:[email]">[email]</a> for registration:`
        }
    ];
    const signupInfo = [
        {
            id: "login.userSignup.info.name",
            defaultMessage: "Last & First Name (Chinese / English)"
        },
        {
            id: "login.userSignup.info.institution",
            defaultMessage: "Institution (if applicable)"
        },
        {
            id: "login.userSignup.info.post",
            defaultMessage: "Post (if applicable)"
        },
        {
            id: "login.userSignup.info.email",
            defaultMessage: "Email"
        },
        {
            id: "login.userSignup.info.researchPurpose",
            defaultMessage: "Research purpose"
        }
    ];

    return user.isAnonymous || user.role === role.anonymous ? (
        // eslint-disable-next-line react/jsx-filename-extension
        <ResponsiveContainer {...props}>
            <Container style={{ padding: "40px", height: "calc(100vh)" }}>
                <Grid verticalAlign="middle" stackable>
                    <Grid.Row>
                        <Grid.Column>
                            <Segment
                                raised
                                padded="very"
                                className={"signin__segment"}
                            >
                                <Grid centered>
                                    <Grid.Row columns={2} centered>
                                        <Grid.Column width={6}>
                                            <div
                                                className={"signin__textBlock1"}
                                            >
                                                <Header
                                                    as="h1"
                                                    color="teal"
                                                    textAlign={textAlign}
                                                    className={
                                                        "signin__textBlock1--title"
                                                    }
                                                >
                                                    <FormattedMessage
                                                        id="login.userLogin.title"
                                                        defaultMessage="User Login"
                                                    />
                                                </Header>
                                                <Header
                                                    as="h3"
                                                    textAlign={textAlign}
                                                    className={
                                                        "signin__textBlock1--subtitle"
                                                    }
                                                >
                                                    <FormattedMessage
                                                        id="login.userLogin.text1"
                                                        defaultMessage="Registered users please login with your email."
                                                    />
                                                </Header>
                                            </div>
                                        </Grid.Column>
                                        <Grid.Column width={6}>
                                            <div
                                                className={"signin__textBlock2"}
                                            >
                                                <Header
                                                    as="h1"
                                                    color="teal"
                                                    textAlign={textAlign}
                                                    className={
                                                        "signin__textBlock2--title"
                                                    }
                                                >
                                                    <FormattedMessage
                                                        id="login.userSignup.title"
                                                        defaultMessage="Account Registration"
                                                    />
                                                </Header>
                                                {signupText.map((opt, idx) => {
                                                    const text = intl
                                                        .formatMessage({
                                                            id: opt.id,
                                                            defaultMessage:
                                                                opt.defaultMessage
                                                        })
                                                        .replaceAll(
                                                            "[email]",
                                                            CONTACT_MAIL_TO
                                                        );
                                                    return (
                                                        <div
                                                            key={idx.toString()}
                                                            className={
                                                                "signin__textBlock2--subtitle"
                                                            }
                                                        >
                                                            {convert2HtmlEntities(
                                                                text
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                                <List
                                                    className={
                                                        "signin__textBlock2--list"
                                                    }
                                                    bulleted
                                                >
                                                    {signupInfo.map(
                                                        (opt, idx) => {
                                                            const text = intl.formatMessage(
                                                                {
                                                                    id: opt.id,
                                                                    defaultMessage:
                                                                        opt.defaultMessage
                                                                }
                                                            );
                                                            return (
                                                                <List.Item
                                                                    key={idx.toString()}
                                                                    className={
                                                                        "signin__textBlock2--list-item"
                                                                    }
                                                                >
                                                                    {convert2HtmlEntities(
                                                                        text
                                                                    )}
                                                                </List.Item>
                                                            );
                                                        }
                                                    )}
                                                </List>
                                            </div>
                                        </Grid.Column>
                                    </Grid.Row>

                                    <Grid.Row columns={1} centered>
                                        <Grid.Column width={6}>
                                            <StyledFirebaseAuth
                                                uiConfig={firebaseUiConfig}
                                                firebaseAuth={firebase.auth()}
                                            />
                                        </Grid.Column>
                                    </Grid.Row>
                                </Grid>
                            </Segment>
                        </Grid.Column>
                    </Grid.Row>
                </Grid>
            </Container>
        </ResponsiveContainer>
    ) : (
        <Redirect to={`/${locale}`} />
    );
};

export default injectIntl(SignIn);
