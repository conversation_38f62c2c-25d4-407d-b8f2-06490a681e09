/* eslint-disable no-param-reassign */

import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    message: {
        open: false,
        content: ""
    },
    loader: {
        open: false
        // value: 20, // loader 顯示的百分比
    },
    dialogContext: {
        openSignal: false,
        title: "",
        contentText: "",
        yesText: "yes",
        noText: "no",
        onYes: () => null,
        onNo: () => null,
        onClose: () => null
    }
};

const MESSAGE_TYPE = {
    success: "success",
    error: "error",
    info: "info",
    warning: "warning"
};

const commonSlice = createSlice({
    name: "common",
    initialState,
    reducers: {
        setMessage: (state, action) => {
            state.message = { ...action.payload };
        },
        setSuccessMessage: (state, action) => {
            state.message = { ...action.payload, type: MESSAGE_TYPE.success };
        },
        setInfoMessage: (state, action) => {
            state.message = { ...action.payload, type: MESSAGE_TYPE.info };
        },
        setWarningMessage: (state, action) => {
            state.message = { ...action.payload, type: MESSAGE_TYPE.warning };
        },
        setErrorMessage: (state, action) => {
            state.message = { ...action.payload, type: MESSAGE_TYPE.error };
        },
        clearMessage: (state, action) => {
            state.message = { ...initialState.message };
        },
        setLoader: (state, action) => {
            state.loader = action.payload;
        },
        setCommonDialogContext: (state, action) => {
            state.dialogContext = { ...action.payload };
        }
    }
});

export const {
    setMessage,
    setSuccessMessage,
    setInfoMessage,
    setWarningMessage,
    setErrorMessage,
    setLoader,
    clearMessage,
    setCommonDialogContext
} = commonSlice.actions;

export default commonSlice.reducer;
