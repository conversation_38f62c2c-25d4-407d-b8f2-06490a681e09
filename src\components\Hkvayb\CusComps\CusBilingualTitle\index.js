import React from "react";

import useStyles from "./style";

const CusBilingualTitle = ({ titleZh, titleEn, style = {} }) => {
    const classes = useStyles(style);
    if (`${titleZh}` === `${titleEn}`) {
        return (
            <div>
                <div className={classes.hkvaby_bilingual_title}>{titleZh}</div>
            </div>
        );
    }
    return (
        <div>
            <div className={classes.hkvaby_bilingual_title}>{titleZh}</div>
            <div className={classes.hkvaby_bilingual_title}>{titleEn}</div>
        </div>
    );
};

export default CusBilingualTitle;
