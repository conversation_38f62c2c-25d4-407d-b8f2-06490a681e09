{"parser": "babel-es<PERSON>", "extends": ["standard", "plugin:react/recommended", "prettier", "prettier/react", "prettier/standard", "plugin:jest/recommended"], "plugins": ["react", "prettier", "standard", "jest"], "parserOptions": {"sourceType": "module", "ecmaFeatures": {"modules": true, "jsx": true}}, "env": {"es6": true, "node": true, "browser": true}, "rules": {"prettier/prettier": ["error", {"tabWidth": 4}], "no-unused-vars": "off", "import/no-unresolved": 0, "react/no-unused-prop-types": 0, "linebreak-style": "off", "import/no-named-as-default": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/no-access-key": 0, "jsx-a11y/anchor-is-valid": "off", "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/prop-types": 0, "indent": "off", "template-curly-spacing": "off"}, "settings": {"react": {"version": "detect"}}}