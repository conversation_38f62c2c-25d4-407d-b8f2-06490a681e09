import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { useHistory } from "react-router";
import { injectIntl } from "react-intl";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { styled } from "@material-ui/styles";
// component, hooks
import useLocaleRoute from "../../../../hook/useLocaleRoute";
import LoginTop from "../common/LoginTop";
import LoginBottom from "../common/LoginBottom";
import EmailSignUp from "./EmailSignUp";
// store
// config,utils

//
import { Api } from "../../../../api/hkbdb/Api";
import { getPathById, ROUTE_ID } from "../../../../App-route";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// style
const StyledLinkBlue = styled(
    "span",
    {}
)(({ theme }) => ({
    color: "#1e70bf",
    cursor: "pointer"
}));

const CustomSignUpUI = props => {
    // props
    const { intl } = props;
    // route,intl
    const history = useHistory();
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { account } = state;
    const { form } = account;
    // local state
    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    const handleGoLogIn = () => {
        const url = handleLocaleRoute(getPathById(ROUTE_ID.Login));
        history.push(url);
    };

    const hintForSignin = (
        <Box>
            <Typography textAlign="center">
                <span>
                    {intl.formatMessage({
                        id: "login.already.has.account.question",
                        defaultMessage: "Already have login and password?"
                    })}
                </span>
                <StyledLinkBlue
                    onClick={handleGoLogIn}
                    style={{ paddingLeft: "8px" }}
                >
                    {intl.formatMessage({
                        id: "login.already.has.account.login",
                        defaultMessage: "Sing in"
                    })}
                </StyledLinkBlue>
            </Typography>
        </Box>
    );

    const onAgreeCheckChange = checked => {
        dispatch({
            type: Act.ACCOUNT_SIGNUP_FORM_USER_AGREE,
            payload: checked
        });
    };

    React.useEffect(() => {
        return () => {
            // clear store state
            dispatch({
                type: Act.ACCOUNT_SIGNUP_FORM_CLEAR
            });
        };
    }, []);

    return (
        <Box
            width="368px"
            display="flex"
            flexDirection="column"
            alignItems="center"
            mb={8}
        >
            <Box
                width="100%"
                mb={5}
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent={"center"}
            >
                {/* <LoginTop /> */}

                <Typography
                    variant={"h4"}
                    sx={{
                        marginBottom: 4,
                        fontSize: "34px",
                        fontWeight: "bold"
                    }}
                >
                    {intl.formatMessage({
                        id: "signup.page.title",
                        defaultMessage: "Register"
                    })}
                </Typography>

                {/* email login */}
                <EmailSignUp />
            </Box>

            {/* <Box width="100%"> */}
            {/*    <LoginBottom */}
            {/*        agreeChecked={form?.userAgree} */}
            {/*        onAgreeCheckChange={onAgreeCheckChange} */}
            {/*    /> */}
            {/* </Box> */}
        </Box>
    );
};

CustomSignUpUI.propTypes = {};

CustomSignUpUI.defaultProps = {};

export default injectIntl(CustomSignUpUI);
