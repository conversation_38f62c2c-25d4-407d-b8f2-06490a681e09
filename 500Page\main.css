* {
    box-sizing: border-box;
}

body{
    background-color:#6C5151;
    min-height:100vh;
    display: flex;
    justify-content: center;
    align-items:center;
}

main {
    width: 100%;
    height: 100vh;
    background: url("https://fs-root.daoyidh.com/read/hkbdb/images/500-bg.jpg");
    overflow-y: hidden;
}

.loading-box{
    position: relative;
    margin: 20px auto;
    display:flex;
    justify-content: center;
    align-items: center;
}

.loading{
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
}

.l1 {
    width: 90px;
    height: 90px;
    border: 1px dashed #ffffff;
    border-radius:50%;
    -webkit-animation: loading3D 3s linear 0s infinite;
}

.l2 {
    width: 70px;
    height: 70px;
    border: 1px dashed #ffffff;
    border-radius:50%;
    -webkit-animation: loading3D 2s linear 0s infinite;
}

.earth{
    width: 50px;
    height: 50px;
    border: 3px solid #88DAFF;
    border-radius: 50%;
    -webkit-animation: loading3D 6s linear 0s infinite;
}

.e1 {
    transform: rotate3D(90deg);
}

.e2 {
    transform: rotate3D(1, 3, .5, 180deg);
}

.e3 {
    transform: rotate3D(.5, 1, 3, 360deg);
}

footer {
    position: absolute;
    width: 100vw;
    bottom: 0;
    padding: 20px  10px;
    background-color: #6c5151;
}
@keyframes loading3D {
    from {
        transform: rotate3d(3,3,3, 360deg);
    }
    to{
        transform: rotate3d(0deg);
    }
}