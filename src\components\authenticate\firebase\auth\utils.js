import firebase from "firebase/app";

import firebaseUiConfig from "../../../../config/config-firebase-ui";

import role from "../../../../App-role";

const fireAuth = {};

const getAuth = () => firebase.auth();

fireAuth.isLoggedIn = user => {
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));
    if (isLogin) return true;
    const keyRequired = ["uid", "email", "currentUser"];
    // eslint-disable-next-line no-underscore-dangle
    let _isLoggedIn = false;
    if (!user) return _isLoggedIn;
    keyRequired.forEach(key => {
        if (Object.keys(user).indexOf(key) >= 0) _isLoggedIn = true;
    });
    return _isLoggedIn;
};

fireAuth.isAdmin = user => {
    const { role: userRole } = user;
    const isLoggedIn = fireAuth.isLoggedIn(user);
    // todo:更嚴謹的驗證方式,檢查 token
    return isLoggedIn && role && userRole === role.admin;
};

fireAuth.getFormatUser = user => {
    // uid:
    // The user's ID, unique to the Firebase project. Do NOT use
    // this value to authenticate with your backend server, if
    // you have one. Use User.getToken() instead.
    const {
        uid,
        displayName,
        email,
        emailVerified,
        isAnonymous,
        providerData,
        metadata
    } = user;

    const safeProviderId =
        providerData && providerData[0]
            ? providerData && providerData[0].providerId
            : "unKnown";

    // creationTime 或 lastSignInTime 可能為 undefined
    const { creationTime, lastSignInTime } = metadata;

    // make data flat
    return {
        // ...user,
        uid,
        displayName,
        email,
        emailVerified,
        isAnonymous,
        providerId: safeProviderId,
        creationTime,
        lastSignInTime
    };
};

fireAuth.createUser = ({
    email,
    password,
    displayName,
    onSuccess,
    onError
}) => {
    const auth = getAuth();

    auth.createUserWithEmailAndPassword(email, password)
        .then(userCredential => {
            // 註冊後即進入登入狀態
            // Signed in
            const { user } = userCredential;
            // ...
            user.updateProfile({
                displayName
            })
                .then(() => {
                    // Profile updated!
                    // ...
                    if (onSuccess) onSuccess({ user });
                })
                .catch(error => {
                    // Handle Errors here.
                    const errorCode = error.code;
                    const errorMessage = error.message;
                    if (onError) onError({ errorCode, errorMessage });
                });
        })
        .catch(error => {
            // Handle Errors here.
            const errorCode = error.code;
            const errorMessage = error.message;
            // An error happened.
            if (onError) onError({ errorCode, errorMessage });
        });
};

fireAuth.signIn = ({ email, password, onSuccess, onError }) => {
    const auth = getAuth();

    auth.signInWithEmailAndPassword(email, password)
        .then(userCredential => {
            // Signed in
            const { user } = userCredential;
            if (onSuccess) onSuccess({ user });
        })
        .catch(error => {
            // Handle Errors here.
            const errorCode = error.code;
            const errorMessage = error.message;
            // An error happened.
            if (onError) onError({ errorCode, errorMessage });
        });
};

fireAuth.signOut = ({ onSuccess, onError }) => {
    const auth = getAuth();

    auth.signOut()
        .then(() => {
            // Sign-out successful.
            if (onSuccess) onSuccess();
        })
        .catch(error => {
            // Handle Errors here.
            const errorCode = error.code;
            const errorMessage = error.message;
            // An error happened.
            if (onError) onError({ errorCode, errorMessage });
        });
};

const provider = new firebase.auth.GoogleAuthProvider();

fireAuth.signInGoogle = ({ onSuccess, onError }) => {
    const auth = getAuth();

    auth.signInWithPopup(provider)
        .then(result => {
            // This gives you a Google Access Token.
            const token = result.credential.accessToken;
            // The signed-in user info.
            const user = result.user;
            // Sign-out successful.
            if (onSuccess) onSuccess({ user, token });
        })
        .catch(error => {
            // Handle Errors here.
            const errorCode = error.code;
            const errorMessage = error.message;
            // The email of the user's account used.
            const { email } = error;
            // The AuthCredential type that was used.
            // todo: 帶修改成 old version
            // const credential = GoogleAuthProvider.credentialFromError(error);
            // An error happened.
            // if (onError)
            //     onError({ errorCode, errorMessage, email, credential });
        });
};

// Send a user a verification email
fireAuth.sendEmailVerification = ({ onSuccess, onError }) => {
    const auth = getAuth();

    auth.currentUser
        .sendEmailVerification()
        .then(() => {
            // eslint-disable-next-line no-console
            console.log("Email verification sent!");
            // Email verification sent!
            // ...
            if (onSuccess) onSuccess();
        })
        .catch(error => {
            const errorCode = error.code;
            const errorMessage = error.message;
            // An error happened.
            if (onError) onError({ errorCode, errorMessage });
        });
};

// Send a password reset email
fireAuth.sendPasswordResetEmail = ({ email, onSuccess, onError }) => {
    const auth = getAuth();

    auth.sendPasswordResetEmail(email)
        .then(() => {
            // Password reset email sent!
            // ..
            if (onSuccess) onSuccess();
        })
        .catch(error => {
            const errorCode = error.code;
            const errorMessage = error.message;
            // An error happened.
            if (onError) onError({ errorCode, errorMessage });
        });
};

// 取得 firebase auth 物件
fireAuth.getAuth = getAuth;

// 取得設定檔
fireAuth.getAuthUiConfig = () => ({
    ...firebaseUiConfig,
    // 這邊不設定 signInSuccessUrl, 讓使用者可以彈性地回到前一個頁面
    signInSuccessUrl: null,
    callbacks: {
        signInSuccessWithAuthResult: (authResult, redirectUrl) => true,
        signInFailure: error => {
            console.log("error", error);
        }
    }
});

export default fireAuth;
