import React, { useState, useContext } from "react";

import TablePagination from "@material-ui/core/TablePagination";

import { StoreContext } from "../../../../../store/StoreProvider";

import act from "../../../../../store/actions";

const CusPagination = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { count, currentPage, rowsPerPage } = state.searchPage2;

    const handleChangePage = (event, newPage) => {
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT_PAGE,
            payload: newPage
        });
    };

    const handleChangeRowsPerPage = event => {
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT_PAGE,
            payload: 0
        });
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT_ROWS_PAGE,
            payload: parseInt(event.target.value, 10)
        });
    };

    // todo onChangePage or onPageChange ? material has version bug
    // todo onChangeRowsPerPage or onRowsPerPageChange ? material has version bug
    return (
        <TablePagination
            component="div"
            count={count}
            page={currentPage}
            rowsPerPage={rowsPerPage}
            onChangePage={handleChangePage}
            onChangeRowsPerPage={handleChangeRowsPerPage}
            // onPageChange={handleChangePage}
            // onRowsPerPageChange={handleChangeRowsPerPage}
        />
    );
};

export default CusPagination;
