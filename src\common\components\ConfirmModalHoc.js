import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { useDispatch, useSelector } from "react-redux";

//
// component, hooks
import ConfirmModal from "./dialog/ConfirmModal";
// store
// config,utils

// eslint-disable-next-line import/named
import { setCommonDialogContext } from "../../reduxStore/commonSlice";

// style

const ConfirmModalHoc = () => {
    // props

    // route,intl
    // store
    const authState = useSelector(_state => _state.common);
    const { dialogContext } = authState;
    const reduxDispatch = useDispatch();

    // local state
    // hooks

    const handleClose = () => {
        reduxDispatch(
            setCommonDialogContext({
                title: "",
                contentText: ""
            })
        );
    };

    return <ConfirmModal {...dialogContext} onClose={handleClose} />;
};

ConfirmModalHoc.propTypes = {};

ConfirmModalHoc.defaultProps = {};

export default ConfirmModalHoc;
