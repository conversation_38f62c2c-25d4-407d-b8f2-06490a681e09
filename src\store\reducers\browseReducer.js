import Act from "../actions";

const initState = {
    curGraph: "", // 目前選取的資料集
    filterGraphOn: true // 是否要過濾 graph
};

const browseReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_CUR_GRAPH:
            return {
                ...state,
                curGraph: action.payload
            };
        case Act.SET_FILTER_GRAPH_ON:
            return {
                ...state,
                filterGraphOn: action.payload
            };
        default:
            return state;
    }
};

export default browseReducer;
