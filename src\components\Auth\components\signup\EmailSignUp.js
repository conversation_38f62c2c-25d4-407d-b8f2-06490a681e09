import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { useHistory } from "react-router";
import { injectIntl } from "react-intl";
import { useDispatch } from "react-redux";
//
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import { styled } from "@material-ui/styles";
// component, hooks
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Typography from "@mui/material/Typography";
import { useLocation } from "react-router-dom";
import useLocaleRoute from "../../../../hook/useLocaleRoute";
// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
// config,utils
import InputPlusIcon from "../../../../common/components/input/InputPlusIcon";
import { validateEmail } from "../../../../common/codes/validator";
import fireAuth from "../../../authenticate/firebase/auth/utils";
import { setCommonDialogContext } from "../../../../reduxStore/commonSlice";
import { Api } from "../../../../api/hkbdb/Api";
import { getPathById, ROUTE_ID } from "../../../../App-route";
import { getFormatUser, isEmpty } from "../../../../common/codes";
import {
    getUser,
    setUser,
    updateUser
} from "../../../authenticate/firebase/realtimeDatabase";
import role from "../../../../App-role";
import { convert2HtmlEntities } from "../../../../common/components/Markdown2React/mdUtils";
import Button from "@mui/material/Button";
import {
    fieldsCfg,
    fieldsDef,
    fieldName,
    findFieldByName,
    findHintByName,
    findErrHintByName,
    getPrivacyLinkByLocale
} from "./option";
import { mailUserSignup } from "../../email.api";
import LoginBottom from "../common/LoginBottom";

// style
const StyledHint = styled(
    "span",
    {}
)(({ theme }) => ({
    color: "#ff5252"
}));

const EmailSignUp = props => {
    // props
    const { intl } = props;

    // route,intl
    const history = useHistory();
    const location = useLocation();
    // store
    const reduxDispatch = useDispatch();
    const [state, dispatch] = useContext(StoreContext);
    const { user, setting, account } = state;
    const { locale } = user;
    const { emailConfig, signuplConfig } = setting; // {email-domain:{allowable:[]}}
    const { form } = account;
    const allowDomain = signuplConfig?.["email-domain"]?.["allowable"] || [];
    const fromEmail = emailConfig?.daoyidhNoReply?.email;
    const toEmail = emailConfig?.cuhk?.email;
    // local state
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = useState(false);

    const isUserAgree = form?.userAgree; // 使用者是否同意
    // 儲存各 input 的 value 及 error
    // {username:{value: "", error: ""},institution:{value: "", error: ""}}
    const [fields, setFields] = useState(fieldsDef);
    const emailValidate = validateEmail(fields.email.value);
    const usernameValidate = !!fields.username.value;
    const passwordTheSame =
        fields.password.value === fields.passwordConfirm.value;
    const passwordValidate = !!fields.password.value && passwordTheSame;

    // 是否為港中允許的 email domain(若為 vip, 可省略部分資訊填寫)
    const isVip = !!allowDomain.find(
        dm => (fields?.email?.value || "").indexOf(dm) >= 0
    );
    const otherInfoValidate = isVip
        ? true
        : ![
              fieldName.institution,
              fieldName.position,
              fieldName.researchPurpose
          ].find(n => !fields?.[n]?.value);

    const inputAllValidated =
        emailValidate &&
        passwordValidate &&
        usernameValidate &&
        isUserAgree &&
        otherInfoValidate;

    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    const [activeStep, setActiveStep] = useState(0);
    const steps = [
        intl.formatMessage({
            id: "signup.step.create.account",
            defaultMessage: "create an account"
        }),
        intl.formatMessage({
            id: "signup.step.personal.information",
            defaultMessage: "personal information"
        }),
        intl.formatMessage({
            id: "signup.step.application.completed",
            defaultMessage: "application completed"
        })
    ];

    const handleInputChange = name => e => {
        const inputVal = (e.target.value || "").trim();
        if (name in fields) {
            setFields(prev => ({
                ...prev,
                [name]: {
                    ...prev[name],
                    value: inputVal,
                    startEdit: true, // 此欄位狀態改為 true
                    error:
                        typeof findErrHintByName(name) === "function" &&
                        findErrHintByName(name)(
                            getCtx(findFieldByName(name), inputVal)
                        )
                }
            }));
        }
    };

    const signupSubTitle = [
        {
            id: "signup.comment.text1",
            defaultMessage:
                "Currently members of **tertiary institutions in Hong Kong** could register automatically with **institutional email**; other researchers would be manually approved by Administrator.",
            step: [0]
        }
    ];
    const signupDesc = [
        {
            id: "signup.comment.desc",
            defaultMessage:
                "Please enter the following information in completing your registration:",
            step: [1]
        }
    ];

    const signupFinishText = [
        {
            id: "signup.finish.message",
            defaultMessage:
                "Thank you for your registration and we will process it as soon as possible. Please <NAME_EMAIL> if you have any enquiries."
        }
    ];

    // 寄發驗證信成功
    const verifyEmailSuccessMsg = loginUser => {
        const { displayName: userName } = loginUser;
        // 彈跳訊息
        reduxDispatch(
            setCommonDialogContext({
                openSignal: Date.now(),
                title: intl.formatMessage(
                    {
                        id: "signup.success.message",
                        defaultMessage:
                            "{name}, please go to the mailbox to verify."
                    },
                    {
                        name: userName
                    }
                ),
                contentText: [``],
                // contentText: [`重新登入後即可使用本網站功能`],
                yesText: intl.formatMessage({
                    id: "signup.yes",
                    defaultMessage: "OK"
                }),
                withFinishIcon: true,
                onYes: () => {
                    dispatch({
                        type: Act.FIREBASE_LOGIN_USER,
                        payload: {
                            displayName: userName
                        }
                    });
                }
            })
        );
    };

    // 寄發驗證信失敗
    const verifyEmailFailMsg = ({ errorCode, errorMessage }) => {
        reduxDispatch(
            setCommonDialogContext({
                openSignal: Date.now(),
                title: intl.formatMessage({
                    id: "signup.sendEmail.verifyError",
                    defaultMessage: "Failed to send verification email"
                }),
                contentText: [errorMessage],
                yesText: intl.formatMessage({
                    id: "signup.yes",
                    defaultMessage: "OK"
                })
            })
        );
    };

    // 註冊失敗訊息
    const signupFailMsg = () => {
        reduxDispatch(
            setCommonDialogContext({
                openSignal: Date.now(),
                title: intl.formatMessage({
                    id: "signup.failure.message",
                    defaultMessage:
                        "Registration failed, please check the filled information"
                }),
                // contentText: [],
                yesText: intl.formatMessage({
                    id: "signup.yes",
                    defaultMessage: "OK"
                })
            })
        );
    };

    // 送出註冊申請
    const handleSubmit = ({ onFinish }) => {
        if (!inputAllValidated) return;
        // authentication create user success
        const createSuccess = ({ user: loginUser }) => {
            // send verify email success
            const verifySuccess = async () => {
                try {
                    setLoading(false);
                    const { displayName: userName } = loginUser;

                    if (!loginUser) return;
                    // 寄發驗證信成功
                    verifyEmailSuccessMsg(loginUser);

                    // 儲存至 realtime database
                    const userInfo = getFormatUser(loginUser);
                    const { uid, displayName, email } = userInfo;
                    // 若 user 已登入, check realtime DB 是否該使用者的資料, 若沒有, 則將 user 資料寫入
                    const data = await getUser(uid);
                    // 依據 realtime db 是否已有使用者資料,決定寫入方式
                    const updateAct = isEmpty(data) ? setUser : updateUser;
                    if (!loginUser.isAnonymous) {
                        const moreInfo = Object.entries(fields).reduce(
                            (acc, [key, obj]) => {
                                const writeDb = fieldsCfg.find(
                                    o => o.name === key
                                )?.writeRtDb;
                                if (writeDb) {
                                    acc[[key]] = obj.value;
                                }
                                return acc;
                            },
                            {}
                        );

                        // 新增 role key
                        const _userInfo = {
                            ...userInfo,
                            role: role.anonymous,
                            displayName:
                                userInfo.displayName || fields.username.value, // 如果是 email 註冊,不會有 displayName
                            ...moreInfo
                        };
                        await updateAct(uid, _userInfo);

                        // send email to manager
                        const emilInfo = {
                            ...user,
                            displayName: fields.username.value,
                            email: fields.email.value,
                            ...Object.entries(fields).reduce(
                                (acc, [key, obj]) => {
                                    acc[key] = obj.value;
                                    return acc;
                                },
                                {}
                            )
                        };
                        const mailRes = await mailUserSignup(
                            emilInfo,
                            fromEmail,
                            toEmail
                        );
                        if (!mailRes?.state || mailRes?.error) {
                            console.error("send mail fail");
                        }

                        //
                        if (typeof onFinish === "function") {
                            onFinish();
                        }
                    }
                } catch (e) {
                    //
                }
            };
            // send verify email fail
            const verifyError = ({ errorCode, errorMessage }) => {
                setLoading(false);
                // 寄發驗證信失敗
                verifyEmailFailMsg({ errorCode, errorMessage });
            };
            // 寄發驗證信
            fireAuth.sendEmailVerification({
                onSuccess: verifySuccess,
                onError: verifyError
            });
        };
        // authentication create user fail
        const createError = ({ errorCode, errorMessage }) => {
            if (errorCode === "auth/email-already-in-use") {
                setFields(prev => ({
                    ...prev,
                    [fieldName.email]: {
                        ...(prev?.[fieldName.email] || {}),
                        error: intl.formatMessage({
                            id: "signup.createUser.fail.email-already-in-use",
                            defaultMessage:
                                "Someone has already registered this email"
                        })
                    }
                }));
            }
            if (errorCode === "auth/invalid-email") {
                setFields(prev => ({
                    ...prev,
                    [fieldName.email]: {
                        ...(prev?.[fieldName.email] || {}),
                        error: intl.formatMessage({
                            id: "signup.createUser.fail.invalid-email",
                            defaultMessage:
                                "email does not meet the requirements"
                        })
                    }
                }));
            }
            if (errorCode === "auth/weak-password") {
                setFields(prev => ({
                    ...prev,
                    [fieldName.password]: {
                        ...(prev?.[fieldName.password] || {}),
                        error: intl.formatMessage({
                            id: "signup.createUser.fail.weak-password",
                            defaultMessage: "Insufficient password strength"
                        })
                    }
                }));
            }

            setLoading(false);
            // 回到步驟0
            setActiveStep(0);

            // 註冊失敗訊息
            signupFailMsg();
        };
        setLoading(true);
        fireAuth.createUser({
            email: fields.email.value,
            password: fields.password.value,
            displayName: fields.username.value,
            onSuccess: createSuccess,
            onError: createError
        });
    };

    // 是否顯示 password callback
    const handleDisplayPassword = e => {
        const checkedVal = e.target.checked;
        setShowPassword(checkedVal);
    };

    // 整合需要送進 config 的 function 的 context
    const getCtx = (field, value) => ({
        intl,
        isVip,
        value: value || fields?.[field.name]?.value,
        startEdit: fields?.[field.name]?.startEdit
    });

    const safeRequired = (required, field) =>
        typeof required === "function" ? required(getCtx(field)) : required;

    // 步驟0 的 next 是否disabled:有 error 或 user not agree
    const disabledStep0Next =
        !!fieldsCfg
            .filter(o => o.step === 0 && safeRequired(o.required, o))
            .find(o => o.error(getCtx(o))) ||
        !isUserAgree ||
        !emailValidate ||
        !passwordValidate;
    // 步驟1 的 next 是否disabled: 有 error 或 input not validated
    const disabledStep1Next =
        !!fieldsCfg
            .filter(o => o.step === 1 && safeRequired(o.required, o))
            .find(o => o.error(getCtx(o))) || !inputAllValidated;

    // button setting
    const btnList = [
        {
            name: "back",
            className: "signup__btn--back",
            focusEle: ".signup__btn--back",
            text: intl.formatMessage({
                id: "signup.btn.back",
                defaultMessage: "Back"
            }),
            disabled: false,
            step: [1], // 出現在哪幾個步驟
            onClick: () => setActiveStep(prev => prev - 1)
        },
        {
            name: "next",
            className: "signup__btn--next",
            focusEle: ".signup__btn--next",
            text: intl.formatMessage({
                id: "signup.btn.next",
                defaultMessage: "Next Step"
            }),
            disabled: activeStep === 0 ? disabledStep0Next : disabledStep1Next,
            step: [0, 1],
            onClick: () => {
                if (activeStep === 0) {
                    // todo: 優化=>先查詢 email 是否有人註冊過,
                    setActiveStep(1);
                } else {
                    handleSubmit({ onFinish: () => setActiveStep(2) });
                }
            }
        },
        {
            name: "backToHome",
            className: "signup__btn--backToHome",
            focusEle: ".signup__btn--backToHome",
            text: intl.formatMessage({
                id: "signup.btn.backToHome",
                defaultMessage: "Back To Home"
            }),
            disabled: false,
            step: [2],
            onClick: () => {
                const url = handleLocaleRoute(getPathById(ROUTE_ID.Home));
                history.push(url);
            }
        }
    ];

    // 儲存目前畫面中 elements 清單(包含 active 狀態)
    const [elesFocus, setElesFocus] = useState(
        fieldsCfg.concat(btnList).map(ele => ({
            ...ele,
            active: false
        }))
    );

    // step 變更時,更新 elesFocus
    useEffect(() => {
        const cBtnList = btnList.filter(btn => btn.step.includes(activeStep));
        const cInputs = fieldsCfg.filter(o => o.step === activeStep);
        setElesFocus(
            cInputs.concat(cBtnList).map(ele => ({
                ...ele,
                active: false
            }))
        );
    }, [activeStep]);

    // focus 事件
    const onFocus = eleName => e => {
        const thisIdx = elesFocus.findIndex(el => el.name === eleName);
        setElesFocus(list =>
            list.map((l, idx) => {
                return { ...l, active: idx === thisIdx };
            })
        );
    };

    // keyup 事件: 使用者enter可跳至下一個element
    const onKeyUp = eleName => e => {
        if (e.key === "Enter" || e.keyCode === 13) {
            const thisIdx = elesFocus.findIndex(el => el.name === eleName);
            const nextIdx = thisIdx >= elesFocus.length - 1 ? 0 : thisIdx + 1;
            const ele = document.querySelector(
                `${elesFocus[nextIdx].focusEle}`
            );
            if (ele?.focus) {
                ele.focus();
            }
        }
    };

    // 是否顯示密碼
    const showOrNotPassword = activeStep === 0 && (
        <Box
            mb={2}
            display="flex"
            flexDirection="row"
            justifyContent="space-between"
        >
            <Box>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={showPassword}
                                onChange={handleDisplayPassword}
                            />
                        }
                        label={intl.formatMessage({
                            id: "signup.display.password",
                            defaultMessage: "Display password"
                        })}
                    />
                </FormGroup>
            </Box>
        </Box>
    );

    // 成功註冊後的提示訊息
    const singUpFinishTxt = activeStep === 2 && (
        <Box mb={5} className={"email-signup__subtitle"}>
            {signupFinishText.map((opt, idx) => {
                const text = intl.formatMessage({
                    id: opt.id,
                    defaultMessage: opt.defaultMessage
                });
                return (
                    <div
                        key={idx.toString()}
                        className={"signin__textBlock2--subtitle"}
                    >
                        {convert2HtmlEntities(text)}
                    </div>
                );
            })}
        </Box>
    );

    const onAgreeCheckChange = checked => {
        dispatch({
            type: Act.ACCOUNT_SIGNUP_FORM_USER_AGREE,
            payload: checked
        });
    };

    const privacyInfoText = intl
        .formatMessage({
            id: "signup.privacy.agree.text",
            defaultMessage:
                "Fields marked with an asterisk (*) are required. Information and personal data entered in this form is voluntary and will be processed according to the CUHK **Protection of Personal Data (Privacy)** Policy ([link])."
        })
        .replace("[link]", getPrivacyLinkByLocale(locale));

    const privacyAgreeInfo = activeStep === 0 && (
        <Box mt={1} className={"email-signup__privacyAgreeInfo"}>
            {convert2HtmlEntities(privacyInfoText)}
        </Box>
    );

    // const privacyAgreeInfo = (
    //     <Box width="100%">
    //         {activeStep === 0 && (
    //             <LoginBottom
    //                 agreeChecked={form?.userAgree}
    //                 onAgreeCheckChange={onAgreeCheckChange}
    //             />
    //         )}
    //     </Box>
    // );

    useEffect(() => {
        // 語系變更時, 必須變更 error 顯示文字...
        setFields(prev =>
            Object.entries(prev).reduce((acc, [name, obj], idx) => {
                acc[name] = {
                    ...obj,
                    error:
                        typeof findErrHintByName(name) === "function" &&
                        findErrHintByName(name)(getCtx(findFieldByName(name)))
                };
                return acc;
            }, {})
        );
    }, [intl]);

    //
    return (
        <Box className={"email-signup"}>
            <Box mb={5} className={"email-signup__subtitle"}>
                {/* 副標題 */}
                {signupSubTitle
                    .filter(o => o.step.includes(activeStep))
                    .map((opt, idx) => {
                        const text = intl.formatMessage({
                            id: opt.id,
                            defaultMessage: opt.defaultMessage
                        });
                        return (
                            <div
                                key={idx.toString()}
                                className={"signin__textBlock2--subtitle"}
                            >
                                {convert2HtmlEntities(text)}
                            </div>
                        );
                    })}
            </Box>
            {/* 步驟1=>2=>3 */}
            <Box width={"100%"} mb={5} className={"email-signup__stepper"}>
                <Stepper activeStep={activeStep} alternativeLabel>
                    {steps.map((label, index) => {
                        const stepProps = {};
                        const labelProps = {};
                        return (
                            <Step key={label} {...stepProps}>
                                <StepLabel {...labelProps}>{label}</StepLabel>
                            </Step>
                        );
                    })}
                </Stepper>
            </Box>
            {/* signup 說明 */}
            <Box mb={5} className={"email-signup__subtitle"}>
                {signupDesc
                    .filter(o => o.step.includes(activeStep))
                    .map((opt, idx) => {
                        const text = intl.formatMessage({
                            id: opt.id,
                            defaultMessage: opt.defaultMessage
                        });
                        return (
                            <div
                                key={idx.toString()}
                                className={"signin__textBlock2--subtitle"}
                            >
                                {convert2HtmlEntities(text)}
                            </div>
                        );
                    })}
            </Box>
            {/* 輸入欄位 */}
            <Box>
                {fieldsCfg
                    .filter(o => o.display)
                    .filter(o => activeStep === o.step)
                    .map((item, idx) => (
                        <Box
                            mb={2}
                            key={item.name}
                            className={"email-signup__form"}
                        >
                            <InputPlusIcon
                                type={
                                    typeof item.inputType === "function"
                                        ? item.inputType({ showPassword })
                                        : item.inputType
                                }
                                defaultValue={fields?.[item.name]?.value}
                                placeholder={item.placeholder(getCtx(item))}
                                className={item.className}
                                onChange={handleInputChange(item.name)}
                                error={!!item.error(getCtx(item))}
                                onKeyUp={onKeyUp(item.name)}
                                onFocus={onFocus(item.name)}
                            />
                            {typeof findHintByName(item?.name) ===
                                "function" && (
                                <Box mt={1}>
                                    <Typography variant="body2">
                                        <span>
                                            {findHintByName(item?.name)(
                                                getCtx(item)
                                            )}
                                        </span>
                                    </Typography>
                                </Box>
                            )}
                            {item?.name in fields && fields[item.name]?.error && (
                                <Box mt={1}>
                                    <Typography variant="body2">
                                        <StyledHint>
                                            {fields[item.name].error}
                                        </StyledHint>
                                    </Typography>
                                </Box>
                            )}
                        </Box>
                    ))}
            </Box>
            {/* 是否顯示密碼 */}
            {showOrNotPassword}
            {/* 成功註冊訊息 */}
            {singUpFinishTxt}
            {/* 隱私權政策提醒 */}
            {privacyAgreeInfo}
            {/* button 區:下一步,註冊,返回首頁 */}
            <Box
                mt={5}
                mb={5}
                columnGap={1}
                display={"flex"}
                justifyContent={"space-between"}
                className={"email-signup__buttons"}
            >
                {btnList
                    .filter(btn => btn.step.includes(activeStep))
                    .map((btn, idx) => (
                        <Button
                            key={idx.toString()}
                            variant={"contained"}
                            className={btn.className}
                            fullWidth
                            disabled={btn.disabled}
                            onClick={btn.onClick}
                            onKeyUp={onKeyUp(btn.className)}
                            onFocus={onFocus(btn.className)}
                        >
                            {btn.text}
                        </Button>
                    ))}
            </Box>
        </Box>
    );
};

EmailSignUp.propTypes = {};

EmailSignUp.defaultProps = {};

export default injectIntl(EmailSignUp);
