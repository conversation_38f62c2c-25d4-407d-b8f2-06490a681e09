import React, { useState, useEffect } from "react";

const CustomDebounce = (value, delay) => {
    // State and setters for debounced value
    const [debouncedValue, setDebouncedValue] = useState(value);
    //
    useEffect(
        () => {
            // Set debouncedValue to value (passed in) after the specified delay
            const handler = setTimeout(() => {
                setDebouncedValue(value);
            }, delay);
            return () => {
                clearTimeout(handler);
            };
        },
        // Only re-call effect if value changes
        [value]
    );
    return debouncedValue;
};

export default CustomDebounce;
