// firebase
import firebase from "firebase/app";
import "firebase/database";
import { isEmpty } from "../../../common/codes";

// !!Don't change me!!
const rtDir = "counter";

const FBWordCloud = "wordCloud";
const rtCounter = process.env.RT_COUNTER;
const rtReset = "reset";

// 只存 100 個以免爆炸
export const MAX_WORD_CLOUD = 100;

/*
    component: accountManagement
    desc: get all user
    path: nmtl-web/users
 */
const getUsers = () =>
    firebase
        .database()
        .ref("/users")
        .once("value")
        .then(snapshot => Object.values(snapshot.val()).map(item => item));

/*
    component: accountManagement
    desc: findUser
    path: nmtl-web/users
 */
const getUser = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`/users/${safeUid}`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

const updateUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}`)
        .update(data);
};

const deleteUser = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}`)
        .remove();
};

/*
    component: SignIn
    desc: save user detail
    path: nmtl-web/user/{uid}
 */
const saveUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    firebase
        .database()
        .ref(`users/${safeUid}`)
        .update(data);
};

const SETTINGS_PATH = {
    webStyle: "webStyle",
    image: "image",
    production: {
        name: "production",
        webStyle: "webStyle",
        image: "image/production"
    },
    development: {
        name: "development",
        webStyle: "webStyle",
        image: "image/develop"
    }
};

// 依 mode(production, development) 取得 firebase realtime db 的 path
const getSettingPath = (mode, section) => {
    if (!section) return "";
    if (mode && mode in SETTINGS_PATH) {
        return SETTINGS_PATH[mode][section];
    }
    return SETTINGS_PATH[section];
};

const getWebStyle = path => {
    return firebase
        .database()
        .ref(`/settings/${path}`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return Promise.resolve(data);
        })
        .catch(err => {
            return Promise.reject(err);
        });
};

const setWebStyle = (uid, path, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`settings/${path}`)
        .update(data);
};

const updateWebStyle = (uid, path, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`settings/${path}`)
        .update(data);
};

/**
 *
 * @param uid
 * @param settings
 * @returns {Promise<any>}
 */
const updateUserSettings = (uid, settings) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}/settings/tableSortedRecords`)
        .update(settings)
        .then(res => true)
        .catch(err => {
            console.error(err.message);
            return false;
        });
};

const getUserSettings = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`/users/${safeUid}/settings`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

const getDefaultSettings = () => {
    // make sure uid is correct
    return firebase
        .database()
        .ref(`settings/tableSortedRecords`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

const getFieldSetting = () => {
    //
    const env = process.env.REACT_APP_MODE || "development";
    // make sure uid is correct
    return firebase
        .database()
        .ref(`settings/fieldSetting/${env}`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

const getSwgJSON = role => {
    return firebase
        .database()
        .ref(`settings/swgJSON/${role}`)
        .once("value")
        .then(snapshot => {
            // console.log(snapshot.val());
            if (snapshot.val()) {
                return Promise.resolve(snapshot.val());
            }
            return Promise.resolve(null);
        });
};

/**
 * desc: update organization/keyword/visits list
 * */
const updateAll = data => {
    const updates = {};
    updates[`${rtDir}/${rtCounter}/${FBWordCloud}`] = data[FBWordCloud];
    // updates[`${rtDir}/${rtCounter}/${rtVisits}`] = data.visits;
    updates[`${rtDir}/${rtCounter}/${rtReset}`] = false;
    firebase
        .database()
        .ref()
        .update(updates);
};

/**
 * desc: update organization/keyword list
 * */
const updateHotKey = (id, data) => {
    const updates = {};
    updates[`${rtDir}/${rtCounter}/${FBWordCloud}/${id}`] = data;
    updates[`${rtDir}/${rtCounter}/${rtReset}`] = false;
    firebase
        .database()
        .ref()
        .update(updates);
};

/**
 * desc: update value
 * */
const atomicInc = valPath => {
    const updates = {};
    updates[
        `${rtDir}/${rtCounter}/${valPath}`
    ] = firebase.database.ServerValue.increment(1);
    firebase
        .database()
        .ref()
        .update(updates);
};

export {
    getUser,
    getUsers,
    updateUser,
    deleteUser,
    saveUser,
    getWebStyle,
    setWebStyle,
    updateWebStyle,
    updateUserSettings,
    getUserSettings,
    getDefaultSettings,
    getFieldSetting,
    SETTINGS_PATH,
    getSettingPath,
    getSwgJSON,
    updateHotKey,
    atomicInc,
    FBWordCloud,
    rtReset,
    updateAll
};
