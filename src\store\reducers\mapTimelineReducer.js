import Act from "../actions";

const initState = {
    /** for TimeLine component */
    transMapData: [],

    /** for SearchInput */
    transSearchData: ""
};

const mapTimelineReducer = (state = initState, action) => {
    switch (action.type) {
        /** for TimeLine component */
        case Act.TRANSFORM_MAP_DATA:
            // console.log(action.transMapData);
            return {
                ...state,
                transMapData: action.transMapData
            };
        case Act.TRANSFORM_SEARCH_DATA:
            // console.log(action.transSearchData);
            return {
                ...state,
                transSearchData: action.transSearchData
            };
        default:
            return state;
    }
};

export default mapTimelineReducer;
