import { FormattedMessage } from "react-intl";
import React from "react";
const link = (
    <a
        href="https://archives.lib.cuhk.edu.hk/"
        target="_blank"
        rel="noopener noreferrer"
        style={{
            textDecoration: "none",
            borderBottom: "1px solid",
            color: "inherit"
        }}
    >
        檔案文獻
    </a>
);
const collectionConfig = {
    manuScript: {
        pathName: "manuScript",
        title: (
            <FormattedMessage
                id="menu.manuScript"
                defaultMessage="Writers' Manuscripts"
            />
        ),
        desc: (
            <span>
                香港中文大學圖書館收藏不少香港作家手稿，手稿主要蒙作家本人、作家親友及有心人慷慨捐贈，為研究作家及作品的重要原始材料。詳情可瀏覽香港中文大學圖書館
                {link}。以下為本館珍藏的部分手稿及書信：
            </span>
        ),
        bgColor: "#FEFAF6"
    },
    featuredPub: {
        pathName: "featuredPub",
        title: (
            <FormattedMessage
                id="menu.featuredPub"
                defaultMessage="Featured Publications"
            />
        ),
        bgColor: "#F3F3F3"
    }
};

export default collectionConfig;
