import React, { useEffect, useContext, useState } from "react";
import {
    withStyles,
    createMuiTheme,
    ThemeProvider
} from "@material-ui/core/styles";
import Divider from "@material-ui/core/Divider";
import Badge from "@material-ui/core/Badge";
import IconButton from "@material-ui/core/IconButton";

// import BookmarksIcon from "@material-ui/icons/Bookmarks";
import FilterListIcon from "@material-ui/icons/FilterList";
import Menu from "@material-ui/core/Menu";
import MenuItem from "@material-ui/core/MenuItem";
import FormLabel from "@material-ui/core/FormLabel";
import FormControl from "@material-ui/core/FormControl";
import FormGroup from "@material-ui/core/FormGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import Checkbox from "@material-ui/core/Checkbox";
import CheckBoxOutlineBlankIcon from "@material-ui/icons/CheckBoxOutlineBlank";
import CheckBoxIcon from "@material-ui/icons/CheckBox";
import AddCircleIcon from "@material-ui/icons/AddCircle";
import RemoveCircleIcon from "@material-ui/icons/RemoveCircle";
import { teal, red } from "@material-ui/core/colors";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";

const StyledMenu = withStyles({
    paper: {
        border: "1px solid #d3d4d5"
    }
})(props => (
    <Menu
        elevation={0}
        getContentAnchorEl={null}
        anchorOrigin={{
            vertical: "top",
            horizontal: "right"
        }}
        transformOrigin={{
            vertical: "top",
            horizontal: "left"
        }}
        {...props}
    />
));
const StyledMenuItem = withStyles(theme => ({
    root: {
        padding: "0px 16px",
        "&:focus": {
            "& .MuiListItemIcon-root, & .MuiListItemText-primary": {
                color: theme.palette.common.white
            }
        }
    }
}))(MenuItem);
const theme = createMuiTheme({
    palette: {
        primary: teal,
        secondary: { main: red[500] }
    }
});

const Filter = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { label, count, checkSelectAll, labelTranslate } = state.searchPage;
    const [buttonStyle, setButtonStyle] = useState({ fontSize: "3rem" });
    const [anchorEl, setAnchorEl] = useState(null);

    useEffect(() => {
        if (
            Object.values(label).filter(item => {
                return item === true;
            }).length === 9
        ) {
            // 全部filter手動勾選，select all也自動勾選
            dispatch({
                type: act.SET_CHECKSELECTALL,
                payload: true
            });
        }
    }, [label]);

    const handleClick = event => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
        dispatch({
            type: act.SET_COUNT,
            payload: Object.values(label).filter(item => {
                return item === true;
            }).length
        });
    };
    const handleChange = event => {
        if (event.target.name === "selectAll" && !checkSelectAll) {
            // 選擇所有選項
            let tmpLabel = {};
            Object.keys(label).forEach(element => {
                tmpLabel[element] = true;
            });
            dispatch({
                type: act.SET_LABEL,
                payload: tmpLabel
            });
            dispatch({
                type: act.SET_CHECKSELECTALL,
                payload: true
            });
            // setCheck(true);
        } else if (event.target.name === "selectAll" && checkSelectAll) {
            // 取消所有選項
            let tmpLabel = {};
            Object.keys(label).forEach(element => {
                tmpLabel[element] = false;
            });
            dispatch({
                type: act.SET_LABEL,
                payload: Object.assign({}, tmpLabel)
            });
            dispatch({
                type: act.SET_CHECKSELECTALL,
                payload: false
            });
        } else {
            dispatch({
                type: act.SET_LABEL,
                payload: { ...label, [event.target.name]: event.target.checked }
            });
            dispatch({
                type: act.SET_CHECKSELECTALL,
                payload: false
            });
        }
    };

    const handleOnMouseEnter = () => {
        setButtonStyle({ fontSize: "3rem", color: teal[500] });
    };
    const handleOnMouseLeave = () => {
        setButtonStyle({ fontSize: "3rem" });
    };
    let labelValues = Object.values(label); // 儲存label key的array
    return (
        <ThemeProvider theme={theme}>
            <Badge color="primary" badgeContent={count}>
                <IconButton
                    aria-controls="customized-menu"
                    aria-haspopup="true"
                    onClick={handleClick}
                    style={{ padding: "0" }}
                >
                    <FilterListIcon
                        style={buttonStyle}
                        onMouseEnter={handleOnMouseEnter}
                        onMouseLeave={handleOnMouseLeave}
                    />
                </IconButton>
            </Badge>
            <StyledMenu
                id="customized-menu"
                anchorEl={anchorEl}
                keepMounted
                open={Boolean(anchorEl)}
                onClose={handleClose}
            >
                <FormControl style={{ display: "flex", alignItems: "center" }}>
                    <FormLabel style={{ margin: "10px", textAlign: "center" }}>
                        <span style={{ fontSize: "1.5rem" }}>FILTER</span>
                    </FormLabel>
                    <Divider style={{ width: "90%" }} />
                    <FormGroup>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={checkSelectAll}
                                        icon={
                                            <CheckBoxOutlineBlankIcon fontSize="small" />
                                        }
                                        checkedIcon={
                                            <CheckBoxIcon fontSize="small" />
                                        }
                                        onChange={handleChange}
                                        name="selectAll"
                                    />
                                }
                                label="全部選擇"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.essay}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="essay"
                                    />
                                }
                                label="專題論述"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.publicIssue}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="publicIssue"
                                    />
                                }
                                label="公眾議題"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.awardEvent}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="awardEvent"
                                    />
                                }
                                label="藝術獎項"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.educationEvent}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="educationEvent"
                                    />
                                }
                                label="藝術教育"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.publication}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="publication"
                                    />
                                }
                                label="藝術論著"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.auction}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="auction"
                                    />
                                }
                                label="藝術拍賣"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.talksSymposium}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="talksSymposium"
                                    />
                                }
                                label="藝術講座/研討會"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.exhibition}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="exhibition"
                                    />
                                }
                                label="藝術展覽"
                            />
                        </StyledMenuItem>
                        <StyledMenuItem>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={label.venueInHK}
                                        icon={<AddCircleIcon color="primary" />}
                                        checkedIcon={
                                            <RemoveCircleIcon color="secondary" />
                                        }
                                        onChange={handleChange}
                                        name="venueInHK"
                                    />
                                }
                                label="香港展覽場地"
                            />
                        </StyledMenuItem>
                    </FormGroup>
                </FormControl>
            </StyledMenu>
        </ThemeProvider>
    );
};
export default Filter;
