import React, { useContext } from "react";

import { Grid, Box } from "@mui/material";
//
// component
import Custom<PERSON>oginUI from "./components/signin/CustomLoginUI";
import ConfirmModalHoc from "../../common/components/ConfirmModalHoc";

// config
import { Redirect } from "react-router";
import { injectIntl } from "react-intl";
import { StoreContext } from "../../store/StoreProvider";

// commons code
import { ResponsiveContainer } from "../../layout/Layout";
import role from "../../App-role";
import "./SignInLayout.scss";

const SignInLayout = props => {
    const { intl } = props;
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const locale = user && user.locale ? user.locale : "";

    return user.isAnonymous || user.role === role.anonymous ? (
        // eslint-disable-next-line react/jsx-filename-extension
        <ResponsiveContainer {...props}>
            <Grid container justifyContent="center">
                <Grid item>
                    <Box my={1}>
                        <CustomLoginUI />
                        <ConfirmModalHoc />
                    </Box>
                </Grid>
            </Grid>
        </ResponsiveContainer>
    ) : (
        <Redirect to={`/${locale}`} />
    );
};

export default injectIntl(SignInLayout);
