import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { FormattedMessage, injectIntl } from "react-intl";
import Box from "@mui/material/Box";
// component, hooks
import Typography from "@mui/material/Typography";
import { AppTitle } from "../../../../layout/AppTitle";
// store
// config,utils

// style

const LoginTop = props => {
    // props
    const { intl } = props;
    const { locale } = intl;
    // route,intl
    // store
    // local state
    // hooks

    return (
        <Box>
            <Box display="flex" justifyContent="center" mb={4}>
                <AppTitle />
            </Box>
            <Box mb={6}>
                <Typography textAlign="center" variant="h6" fontWeight={700}>
                    <FormattedMessage
                        id="login.welcome.title"
                        defaultMessage={`Welcome to "Hong Kong Writers and Artists Biographical Database"`}
                    />
                </Typography>
            </Box>
        </Box>
    );
};

LoginTop.propTypes = {};

LoginTop.defaultProps = {};

export default injectIntl(LoginTop);
