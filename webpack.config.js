const path = require("path");
const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer")
    .BundleAnalyzerPlugin;
const Dotenv = require("dotenv-webpack");
const FaviconsWebpackPlugin = require("favicons-webpack-plugin");

const PATH = {
    BUILD: path.join(__dirname, "build"),
    ASSETS: process.env.ASSET_PATH || "/"
};

const config = {
    entry: {
        main: "./src/index.js",
        react: ["react", "react-dom"]
    },
    output: {
        path: PATH.BUILD,
        publicPath: PATH.ASSETS,
        // filename: "[name].js",
        // cache chunk hash
        filename: "js/[name].[hash].].bundle.js",
        // async chunk
        chunkFilename: "js/[name]-[id].[hash].bundle.js"
    },
    optimization: {
        splitChunks: {
            chunks: "all"
        }
    },
    module: {
        rules: [
            {
                test: /\.(js|jsx)$/,
                // exclude: /node_modules/,
                include: [
                    path.resolve(__dirname, "src"),
                    // include leaflet
                    path.resolve(__dirname, "node_modules/react-leaflet"),
                    path.resolve(__dirname, "node_modules/@react-leaflet"),
                    // leaflet plugins
                    path.resolve(
                        __dirname,
                        "node_modules/react-leaflet-arrowheads"
                    ),
                    path.resolve(__dirname, "node_modules/leaflet-arrowheads")
                ],
                loader: "babel-loader",
                options: {
                    /* 被轉換的結果將會被暫存起來。當Webpack再次編譯時，將會首先嘗試從暫存中讀取 */
                    cacheDirectory: true
                }
            },
            {
                test: /\.html$/,
                use: [
                    {
                        // inject html to index.html
                        loader: "html-loader"
                    }
                ]
            },
            {
                test: /\.(scss|css)$/,
                use: [
                    {
                        // inject css to html
                        loader: "style-loader",
                        options: { sourceMap: true }
                    },
                    // css-loader for css
                    { loader: "css-loader", options: { sourceMap: true } },
                    {
                        // postcss-loader for autoprefixer
                        loader: "postcss-loader",
                        options: { sourceMap: true }
                    },
                    {
                        loader: "sass-loader",
                        // using dart-sass for compiling scss
                        options: { implementation: require("sass") }
                    }
                ]
            },
            {
                test: /\.less$/,
                use: [
                    { loader: "css-loader", options: { sourceMap: true } },
                    {
                        loader: "sass-loader",
                        options: { implementation: require("sass") }
                    },
                    {
                        loader: "less-loader",
                        options: {
                            sourceMap: true
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpg|jpeg|woff|woff2|eot|ttf|svg|gif)$/,
                loader: "url-loader?limit=100000"
            }
        ]
    },
    resolve: {
        extensions: ["*", ".js", ".jsx"]
    },
    plugins: [
        new webpack.HotModuleReplacementPlugin(),
        new HtmlWebpackPlugin({
            template: "public/index.html",
            filename: "index.html"
        }),
        new CopyWebpackPlugin([
            { from: "public/img", to: "img" },
            { from: "public/403.html", to: "403.html" }
        ]),
        new FaviconsWebpackPlugin(
            "./public/img/img_202102/HKBDB_icon_mobile.jpg"
        )
    ],
    devServer: {
        contentBase: PATH.BUILD,
        clientLogLevel: "error",
        port: 3000,
        hot: true,
        publicPath: "/",
        historyApiFallback: {
            // disableDotRule: true =>
            // 處理 url with dot(e.g. /:locale/people/abc.def) 對應不到 Route 的問題
            disableDotRule: true
        }
    }
};

function loadDotenv(mode) {
    const dotenv = new Dotenv({
        path: `./.env.${mode}`
    });
    config.plugins.push(dotenv);
}

module.exports = (env, argv) => {
    argv && loadDotenv(argv.mode);
    if (argv && argv.mode === "development") {
        config.devtool = "eval-source-map";
    }
    if (env && env.analyze) {
        config.plugins.push(new BundleAnalyzerPlugin());
    }
    return config;
};
