{"swagger": "2.0", "info": {"description": "HKBDB API API (swagger version 2.0)", "version": "2.0", "title": "HKBDB API API", "termsOfService": "", "contact": {}, "license": {"name": "", "url": ""}}, "host": "hkbdb-api.daoyidh.com", "basePath": "/api", "consumes": ["application/json"], "produces": ["application/json"], "tags": [{"name": "search", "description": "", "externalDocs": {"description": "", "url": ""}}, {"name": "organization", "description": "", "externalDocs": {"description": "", "url": ""}}, {"name": "person", "description": "", "externalDocs": {"description": "", "url": ""}}, {"name": "ontology", "description": "", "externalDocs": {"description": "", "url": ""}}, {"name": "property", "description": "", "externalDocs": {"description": "", "url": ""}}], "schemes": ["https", "http"], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}}, "parameters": {"limitParam": {"in": "query", "name": "limit", "description": "limit for results (0 ~ 100)", "required": true, "type": "integer", "minimum": -1, "maximum": 100}, "offsetParam": {"in": "query", "name": "offset", "description": "offset for results (0 ~ )", "required": true, "type": "integer", "minimum": 0}, "localeParam": {"in": "path", "name": "locale", "description": "en, zh", "required": true, "type": "string", "enum": ["en", "zh"]}, "keywordParam": {"in": "query", "name": "keyword", "description": "keyword for search, e.g. 古", "required": true, "type": "string"}, "personKeywordParam": {"in": "query", "name": "keyword", "description": "keyword for search organization, e.g. 金", "required": true, "type": "string"}, "orgKeywordParam": {"in": "query", "name": "keyword", "description": "keyword for search person, e.g. 香港", "required": true, "type": "string"}, "personNameParam": {"in": "query", "name": "name", "description": "person name for search, e.g. 金庸", "required": true, "type": "string"}, "orgNameParam": {"in": "query", "name": "name", "description": "organization name for search, e.g. 香港中文大學", "required": true, "type": "string"}, "graphParam": {"in": "query", "name": "graph", "description": "filter by graph, e.g. hklit", "required": true, "type": "string", "enum": ["abcwhkp", "hkwrpr", "hklit", "auda_經眼錄", "auda_hkcan", "auda_spc", "auda_hklit", "auda_rsdi", "auda_spcn", "auda_lwhklit", "auda_ccp", "auda_viaf"]}}, "paths": {"/{locale}/search/organization/2.0": {"get": {"tags": ["search"], "summary": "Get search organization 2.0", "description": "Search organization by keyword.", "operationId": "GetSearchOrganization20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/orgKeywordParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SearchOrganization"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/search/person/2.0": {"get": {"tags": ["search"], "summary": "Get search person 2.0", "description": "Search person by keyword.", "operationId": "GetSearchPerson20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personKeywordParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/SearchPerson"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/list/oneGraph/2.0": {"get": {"tags": ["organization"], "summary": "Get organization list onegraph 2.0", "description": "Get organization list (one graph) .", "operationId": "GetOrganizationListOnegraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/graphParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/list/allGraph/2.0": {"get": {"tags": ["organization"], "summary": "Get organization list allgraph 2.0", "description": "Get organization list (all graphs) .", "operationId": "GetOrganizationListAllgraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/count/oneGraph/2.0": {"get": {"tags": ["organization"], "summary": "Get organization count onegraph 2.0", "description": "Get organization count (one graph)", "operationId": "GetOrganizationCountOnegraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/graphParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationCount"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/count/allGraph/2.0": {"get": {"tags": ["organization"], "summary": "Get organization count allgraph 2.0", "description": "Get organization count (all graphs)", "operationId": "GetOrganizationCountAllgraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationCount"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/information/2.0": {"get": {"tags": ["organization"], "summary": "Get organization information 2.0", "description": "Get organization basic information.", "operationId": "GetOrganizationInformation20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/orgNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationInformation"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/members/2.0": {"get": {"tags": ["organization"], "summary": "Get organization members 2.0", "description": "Get persons related to organization.", "operationId": "GetOrganizationMembers20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/orgNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationMembers"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/organization/timeline/2.0": {"get": {"tags": ["organization"], "summary": "Get organization timeline 2.0", "description": "Get organization timeline information .", "operationId": "GetOrganizationTimeline20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/orgNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OrganizationTimeline"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/list/oneGraph/2.0": {"get": {"tags": ["person"], "summary": "Get person list onegraph 2.0", "description": "Get person list (one graph).", "operationId": "GetPersonListOnegraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/graphParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/list/allGraph/2.0": {"get": {"tags": ["person"], "summary": "Get person list allgraph 2.0", "description": "Get person list (all graphs).", "operationId": "GetPersonListAllgraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/count/oneGraph/2.0": {"get": {"tags": ["person"], "summary": "Get person count onegraph 2.0", "description": "Get person count (one graph) .", "operationId": "GetPersonCountOnegraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/graphParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonCount"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/count/allGraph/2.0": {"get": {"tags": ["person"], "summary": "Get person count allgraph 2.0", "description": "Get person count (all graphs) .", "operationId": "GetPersonCountAllgraph20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonCount"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/information/2.0": {"get": {"tags": ["person"], "summary": "Get person information 2.0", "description": "Get person's basic information .", "operationId": "GetPersonInformation20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonInformation"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/education/2.0": {"get": {"tags": ["person"], "summary": "Get person education 2.0", "description": "Get person's information of education .", "operationId": "GetPersonEducation20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonEducation"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/employment/2.0": {"get": {"tags": ["person"], "summary": "Get person employment 2.0", "description": "Get person's information of employment .", "operationId": "GetPersonEmployment20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonEmployment"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/publication/2.0": {"get": {"tags": ["person"], "summary": "Get person publication 2.0", "description": "Get person's information of publication.", "operationId": "GetPersonPublication20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonPublication"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/article/2.0": {"get": {"tags": ["person"], "summary": "Get person article 2.0", "description": "Get person articles .", "operationId": "GetPersonArticle20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonArticle"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/otherwork/2.0": {"get": {"tags": ["person"], "summary": "Get person otherwork 2.0", "description": "Get person's other work.", "operationId": "GetPersonOtherwork20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonOtherwork"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/organization/2.0": {"get": {"tags": ["person"], "summary": "Get person organization 2.0", "description": "Get organization related to person.", "operationId": "GetPersonOrganization20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonOrganization"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/relation/2.0": {"get": {"tags": ["person"], "summary": "Get person relation 2.0", "description": "Get person's relation.", "operationId": "GetPersonRelation20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonRelation"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/event/2.0": {"get": {"tags": ["person"], "summary": "Get person event 2.0", "description": "Get person's information of event.", "operationId": "GetPersonEvent20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonEvent"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/award/2.0": {"get": {"tags": ["person"], "summary": "Get person award 2.0", "description": "Get person's awards .", "operationId": "GetPersonAward20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonAward"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/person/timeline/2.0": {"get": {"tags": ["person"], "summary": "Get person timeline 2.0", "description": "Get person's timeline information.", "operationId": "GetPersonTimeline20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}, {"$ref": "#/parameters/personNameParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PersonTimeline"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/ontology/property/2.0": {"get": {"tags": ["ontology"], "summary": "Get ontology property 2.0", "description": "Get ontology property.", "operationId": "GetOntologyProperty20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OntologyProperty"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/ontology/protege/list/2.0": {"get": {"tags": ["ontology"], "summary": "Get ontology protege list 2.0", "description": "Get ontology property with locale.", "operationId": "GetOntologyProtegeList20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/OntologyProtegeList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}, "/{locale}/property/list/2.0": {"get": {"tags": ["property"], "summary": "Get property list 2.0", "description": "Get property list.", "operationId": "GetPropertyList20", "consumes": [], "produces": [], "parameters": [{"$ref": "#/parameters/limitParam"}, {"$ref": "#/parameters/offsetParam"}, {"$ref": "#/parameters/localeParam"}], "deprecated": false, "responses": {"200": {"description": "successful operation", "schema": {"$ref": "#/definitions/PropertyList"}}, "400": {"description": "Invalid status value", "schema": {"$ref": "#/definitions/Error"}}}, "security": [{"BearerAuth": []}]}}}, "definitions": {"PersonList": {"type": "object", "properties": {"data": {"type": "array", "description": "person list", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "properties": {"personName": {"type": "string", "description": "person name"}, "g": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonList"}}, "PersonEducation": {"type": "object", "properties": {"data": {"type": "array", "description": "person education", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["graph"], "properties": {"hasPlace": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "period": {"type": "string", "description": "period"}, "hasEducatedAt": {"type": "string", "description": "hasEducatedAt"}, "hasStartDate": {"type": "string", "description": "hasStartDate"}, "hasEndDate": {"type": "string", "description": "hasEndDate"}, "hasAcademicDiscipline": {"type": "string", "description": "hasAcademicDiscipline"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonEducation"}}, "PersonGenealogy": {"type": "object", "properties": {"data": {"type": "array", "description": "person genealogy", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["relationOP", "t<PERSON><PERSON>", "s<PERSON><PERSON>"], "properties": {"relationOP": {"type": "string", "description": "relationOP"}, "tPerson": {"type": "string", "description": "t<PERSON><PERSON>"}, "sPerson": {"type": "string", "description": "s<PERSON><PERSON>"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonGenealogy"}}, "PersonCount": {"type": "object", "properties": {"data": {"type": "array", "description": "person count", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["count"], "properties": {"count": {"type": "string", "description": "person count"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonCount"}}, "PersonEvent": {"type": "object", "properties": {"data": {"type": "array", "description": "person event", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["_eventId", "graph"], "properties": {"hasRelatedPerson": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hasEventPlace": {"type": "string", "description": "hasEventPlace"}, "period": {"type": "string", "description": "period"}, "graph": {"type": "string", "description": "graph"}, "_eventId": {"type": "string", "description": "_eventId"}, "eventType": {"type": "string", "description": "eventType"}, "comment": {"type": "string", "description": "comment"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonEvent"}}, "PersonPublication": {"type": "object", "properties": {"data": {"type": "array", "description": "person publication", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["_pubId", "graph"], "properties": {"_pubId": {"type": "string", "description": "_pubId"}, "period": {"type": "string", "description": "period"}, "hasGenre": {"type": "string", "description": "hasGenre"}, "name": {"type": "string", "description": "name"}, "penName": {"type": "string", "description": "penName"}, "hasAuthor": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "hasPublisher": {"type": "string", "description": "hasPublisher"}, "hasInceptionDate": {"type": "string", "description": "hasInceptionDate"}, "hasTranslator": {"type": "string", "description": "hasTranslator"}, "hasEditor": {"type": "string", "description": "hasEditor"}, "hasDescribedTarget": {"type": "string", "description": "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonPublication"}}, "PersonTimeline": {"type": "object", "properties": {"data": {"type": "array", "description": "person timeline", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["perId", "g"], "properties": {"perId": {"type": "string", "description": "perId"}, "eventType": {"type": "string", "description": "eventType"}, "title": {"type": "string", "description": "title"}, "subTitle": {"type": "string", "description": "subTitle"}, "description": {"type": "string", "description": "description"}, "hasStartDate": {"type": "string", "description": "hasStartDate"}, "g": {"type": "string", "description": "g"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonTimeline"}}, "PersonAward": {"type": "object", "properties": {"data": {"type": "array", "description": "person award", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["hasStartDate", "awardTitle", "graph"], "properties": {"hasStartDate": {"type": "string", "description": "hasStartDate"}, "awardTitle": {"type": "string", "description": "awardTitle"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonAward"}}, "PersonRelation": {"type": "object", "properties": {"data": {"type": "array", "description": "person relation", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["relationData", "relationProperty", "graph"], "properties": {"relationData": {"type": "string", "description": "relationData"}, "relationProperty": {"type": "string", "description": "relationProperty"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonRelation"}}, "PersonOtherwork": {"type": "object", "properties": {"data": {"type": "array", "description": "person otherwork", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["name", "graph"], "properties": {"name": {"type": "string", "description": "name"}, "comment": {"type": "string", "description": "comment"}, "graph": {"type": "string", "description": "graph"}, "hasStartDate": {"type": "string", "description": "hasStartDate"}, "displayStartDate": {"type": "string", "description": "displayStartDate"}, "otherWorkType": {"type": "string", "description": "displayStartDate"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonOtherwork"}}, "PersonEmployment": {"type": "object", "properties": {"data": {"type": "array", "description": "person employment", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["_eventId", "graph"], "properties": {"employmentType": {"type": "string", "description": "employmentType"}, "hasEmployedAt": {"type": "string", "description": "hasEmployedAt"}, "jobTitle": {"type": "string", "description": "jobTitle"}, "_eventId": {"type": "string", "description": "_eventId"}, "hasPlace": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "hasGenre": {"type": "string", "description": "hasGenre"}, "column": {"type": "string", "description": "column"}, "workingArea": {"type": "string", "description": "workingArea"}, "hasAlumni": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "hasColumnist": {"type": "string", "description": "hasColumnist"}, "penName": {"type": "string", "description": "penName"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonEmployment"}}, "PersonArticle": {"type": "object", "properties": {"data": {"type": "array", "description": "person article", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["name", "graph"], "properties": {"hasInceptionDate": {"type": "string", "description": "hasInceptionDate"}, "hasPublishedIn": {"type": "string", "description": "hasPublishedIn"}, "publishYear": {"type": "string", "description": "publishYear"}, "displayDate": {"type": "string", "description": "displayDate"}, "name": {"type": "string", "description": "name"}, "articleId": {"type": "string", "description": "articleId"}, "language": {"type": "string", "description": "language"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonArticle"}}, "PersonInformation": {"type": "object", "properties": {"data": {"type": "array", "description": "person information", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["graph", "basicProperty", "basicData"], "properties": {"graph": {"type": "string", "description": "graph"}, "basicProperty": {"type": "string", "description": "basicProperty"}, "basicData": {"type": "string", "description": "basicData"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonInformation"}}, "PersonOrganization": {"type": "object", "properties": {"data": {"type": "array", "description": "person organization", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["_eventId", "graph"], "properties": {"_eventId": {"type": "string", "description": "_eventId"}, "hasStartDate": {"type": "string", "description": "hasStartDate"}, "hasParticipant": {"type": "string", "description": "hasParticipant"}, "hasFounded": {"type": "string", "description": "hasFounded"}, "period": {"type": "string", "description": "period"}, "jobTitle": {"type": "string", "description": "jobTitle"}, "hasPlace": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "hasGenre": {"type": "string", "description": "hasGenre"}, "activity": {"type": "string", "description": "activity"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PersonOrganization"}}, "SearchPerson": {"type": "object", "properties": {"data": {"type": "array", "description": "search person", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["personName"], "properties": {"personName": {"type": "string", "description": "personName"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "<PERSON><PERSON><PERSON>"}}, "OrganizationCount": {"type": "object", "properties": {"data": {"type": "array", "description": "Organization count", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["count"], "properties": {"count": {"type": "string", "description": "count"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OrganizationCount"}}, "SearchOrganization": {"type": "object", "properties": {"data": {"type": "array", "description": "search organization", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["organizationName"], "properties": {"organizationName": {"type": "string", "description": "organizationName"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "SearchOrganization"}}, "OrganizationTimeline": {"type": "object", "properties": {"data": {"type": "array", "description": "Organization timeline", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["orgId", "eventType", "title", "hasStartDate"], "properties": {"orgId": {"type": "string", "description": "perId"}, "eventType": {"type": "string", "description": "eventType"}, "title": {"type": "string", "description": "title"}, "subTitle": {"type": "string", "description": "subTitle"}, "description": {"type": "string", "description": "description"}, "hasStartDate": {"type": "string", "description": "hasStartDate"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OrganizationTimeline"}}, "OrganizationInformation": {"type": "object", "properties": {"data": {"type": "array", "description": "Organization information", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["graph", "property", "data"], "properties": {"graph": {"type": "string", "description": "graph"}, "property": {"type": "string", "description": "property"}, "data": {"type": "string", "description": "data"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OrganizationInformation"}}, "OrganizationMembers": {"type": "object", "properties": {"data": {"type": "array", "description": "Organization members", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["hasStartDate", "type", "bestKnownName", "graph"], "properties": {"hasStartDate": {"type": "string", "description": "hasStartDate"}, "hasEndDate": {"type": "string", "description": "hasEndDate"}, "orgType": {"type": "string", "description": "orgType"}, "bestKnownName": {"type": "string", "description": "bestKnownName"}, "graph": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OrganizationMembers"}}, "OrganizationList": {"type": "object", "properties": {"data": {"type": "array", "description": "Organization list", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["organizationName", "g"], "properties": {"organizationName": {"type": "string", "description": "organizationName"}, "g": {"type": "string", "description": "graph"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OrganizationList"}}, "OntologyProperty": {"type": "object", "properties": {"data": {"type": "array", "description": "ontology property", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["property", "label"], "properties": {"property": {"type": "string", "description": "property"}, "label": {"type": "string", "description": "label"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OntologyProperty"}}, "PropertyList": {"type": "object", "properties": {"data": {"type": "array", "description": "property list", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["property", "type", "domain", "range"], "properties": {"property": {"type": "string", "description": "property"}, "type": {"type": "string", "description": "type"}, "domain": {"type": "string", "description": "domain"}, "range": {"type": "string", "description": "range"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "PropertyList"}}, "OntologyProtegeList": {"type": "object", "properties": {"data": {"type": "array", "description": "ontology protege list", "xml": {"name": "data", "wrapped": true}, "items": {"type": "object", "required": ["op", "range", "domain"], "properties": {"domain": {"type": "string", "description": "domain"}, "op": {"type": "string", "description": "op"}, "range": {"type": "string", "description": "range"}, "inverse": {"type": "string", "description": "inverse"}, "type": {"type": "string", "description": "type"}, "ontology": {"type": "string", "description": "ontology"}, "comment": {"type": "string", "description": "comment"}}}}, "total": {"type": "string", "description": "count of data"}}, "xml": {"name": "OntologyProtegeList"}}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "error message"}}, "xml": {"name": "Error"}}}, "externalDocs": {}}