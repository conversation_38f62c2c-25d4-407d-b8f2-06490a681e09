import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusAlbum from "../CusAlbum";
import CusBilingualTitle from "../CusBilingualTitle";
import CusPublicationTable from "../CusPublicationTable";

import { bilingual } from "../../util";

import { safeGet } from "../../../../common/codes";

const CusPublication = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };
    const authorKey = ["hasAuthor", "hasAuthorTmp"];

    const eventId = safeGet(data, ["eventId"], []);

    const bilingFunc = bilingual(defObj);

    const [authorZh, authorEn] = bilingFunc(data, authorKey);

    const [pageZh, pageEn] = bilingFunc(data, "page");
    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [isbnZh, isbnEn] = bilingFunc(data, "isbn");
    const [priceZh, priceEn] = bilingFunc(data, "price");
    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [editorZh, editorEn] = bilingFunc(data, "hasEditor");
    const [designZh, designEn] = bilingFunc(data, "comment");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [publisherZh, publisherEn] = bilingFunc(data, "hasPublisher");
    const [collectibleZh, collectibleEn] = bilingFunc(data, "collectibleType");
    const [inceptionDateZh, inceptionDateEn] = bilingFunc(data, "displayInceptionDate");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_essay}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`publications/${year}`}
                    backupPath={"publications"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.publisher"
                    defaultMessage="Publisher : "
                />
                <CusValue {...publisherZh} />
                <CusValue prefix="/" defVal="" {...publisherEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.collectible"
                    defaultMessage="Type : "
                />
                <CusValue {...collectibleZh} />
                <CusValue prefix="/" defVal="" {...collectibleEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.editor"
                    defaultMessage="Editor : "
                />
                <CusValue {...editorZh} />
                <CusValue prefix="/" defVal="" {...editorEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.author"
                    defaultMessage="Author : "
                />
                <CusValue {...authorZh} />
                <CusValue prefix="/" defVal="" {...authorEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.design"
                    defaultMessage="Design : "
                />
                <CusValue {...designZh} />
                <CusValue prefix="/" defVal="" {...designEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.page"
                    defaultMessage="Page : "
                />
                <CusValue {...pageZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.date"
                    defaultMessage="Date : "
                />
                <CusValue {...inceptionDateZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.isbn"
                    defaultMessage="ISBN : "
                />
                <CusValue {...isbnZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.publication.price"
                    defaultMessage="Price : "
                />
                <CusValue prefix="HK$" {...priceZh} />
            </CusPara>
            <CusPublicationTable eventId={eventId} />
        </div>
    );
};

export default CusPublication;
