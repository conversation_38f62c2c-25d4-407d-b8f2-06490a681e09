.leaflet-container {
    height: 100%;

    // background-color 是對應到地圖圖層的海洋顏色
    //background-color: #adcceb;
    background-color: #d5dadc;

    /* left zoom in and zoom out control */
    //.leaflet-control-container {
    //  .leaflet-top.leaflet-left {
    //    display: none;
    //  }
    //  .leaflet-bottom.leaflet-right {
    //    .leaflet-control {
    //      display: none;
    //    }
    //  }
    //}

    /* point without cluster marker */
    .leaflet-div-icon {
        background: none !important;
        border: none !important;
    }

    /* cluster marker icon */
    .cluster-marker-icon-wrapper {
        position: relative;

        .cluster-marker-icon {
            color: #241c01;
            background: #eed817;
            border-radius: 50%;
            border: 3px solid #b56557;
            padding: 10px;
            width: 1px;
            height: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0.8;
            position: relative;

            &:hover {
                box-shadow: 0 0 0 2px #b56557;
            }
        }

        .cluster-marker-icon-light {
            border: 3px solid #ed745e;

            &:hover {
                box-shadow: 0 0 0 2px #ed745e;
            }
        }

        .cluster-marker-icon-dark {
            border: 3px solid #b56557;

            &:hover {
                box-shadow: 0 0 0 2px #b56557;
            }
        }
    }

    /* point marker icon */
    .point-marker-icon-wrapper {
        .point-marker-icon {
            font-weight: bold;
            color: #241c01;
            background: #c0fa6d;
            border-radius: 50%;
            border: 2px solid #a6a6a6;
            padding: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0.8;
        }

        .point-marker-icon-light {
            border: 2px solid #a6a6a6;
        }

        .point-marker-icon-dark {
            border: 2px solid #a6a6a6;
        }
    }
}

@media only screen and (max-width: 600px) {
    .leaflet-container {
        /* left zoom in and zoom out control */
        //.leaflet-control-container {
        //  .leaflet-top.leaflet-left {
        //    display: none;
        //  }
        //
        //  .leaflet-bottom.leaflet-right {
        //    display: none;
        //  }
        //}
    }
}

// 地圖點的背景
.custom-tooltip {
    color: white !important;
    border: none !important;
    box-shadow: none !important;
    font-size: 12px;
    border-radius: 24px;
    text-align: center;
    white-space: nowrap;
    background-color: rgba(242, 113, 28, 0.3) !important;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

// 地圖點hover底下的tooltip
.hover-tooltip {
    background-color: #3d3d3d !important;
    color: white !important;
    border-radius: 2px !important;
    padding: 2px 6px !important;
    font-size: 12px !important;
    border: none !important;
    box-shadow: none !important;
    text-align: center;
    white-space: nowrap;
    margin-top: 1.7rem;
}

// 地圖點hover底下tooltip跟circlemarker連結的箭頭
.leaflet-tooltip-top.hover-tooltip::before {
    border-top-color: #3d3d3d !important;
}

.leaflet-tooltip-bottom.hover-tooltip::before {
    border-bottom-color: #3d3d3d !important;
}

.leaflet-tooltip-left.hover-tooltip::before {
    border-left-color: #3d3d3d !important;
}

.leaflet-tooltip-right.hover-tooltip::before {
    border-right-color: #3d3d3d !important;
}

// 隱藏點擊後地圖的箭頭標點
//.gis-map-container {
//    .leaflet-marker-pane {
//        display: none;
//    }
//.leaflet-marker-pane {
//  display: none;
//}
// 隱藏點擊後地圖的箭頭標點
.easyMapIcon {
    display: none;
}

.leaflet-control-zoom {
    border: 1px solid #89acbb !important;
}

.leaflet-control-layers {
    border: 1px solid #89acbb !important;
}

.gis-map-container {
    padding: 0 72px 40px 72px;

    @media screen and (max-width: 992px) {
        padding: 0 24px 40px 24px;
    }

    &-header {
        display: flex;
        // width: 100%;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin-bottom: 32px;
        padding: 10px 0;

        &-tab {
            // flex-grow: 1;
            // justify-content: center;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            width: auto;
        }

        &-guide-box {
            margin-left: auto;
            visibility: visible;
        }

        &::before {
            content: "";
            display: block;
            height: 40px;
            visibility: hidden;
        }
    }

    &-mobile {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        &-title {
            font-size: 20px;
            font-weight: 600;
            color: #104860;
            margin-bottom: 16px;
        }

        &-content {
            font-size: 14px;
            color: #000000;
        }
    }
}


/* L.divIcon 中設定的 className */
.leaflet-arrow-icon {
    /* 移除 Leaflet 可能添加的預設背景 */
    background: none !important;
    /* 移除 Leaflet 可能添加的預設邊框 */
    border: none !important;
    /* 確保 SVG 在 div 中正確顯示 (通常不需要，但以防萬一) */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 讓 SVG 內的 path 繼承顏色 (如果 SVG 沒寫死 fill) */
/* .leaflet-arrow-icon svg path { */
/* fill: currentColor; */
/* } */