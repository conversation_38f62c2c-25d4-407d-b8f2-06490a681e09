<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>地圖右上_線條圖例_內容 (西西)</name>
   <tag></tag>
   <elementGuidId>6e45c3f4-6593-42d8-91d9-6688a83f045c</elementGuidId>
   <selectorCollection>
      <entry>
         <key>CSS</key>
         <value>p.MuiTypography-root.MuiTypography-body2.css-g418i2</value>
      </entry>
      <entry>
         <key>XPATH</key>
         <value>//div[@id='root']/div/div/div[2]/div[2]/div/div[3]/div/div/div/div/div/p</value>
      </entry>
   </selectorCollection>
   <selectorMethod>BASIC</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>internal:text=&quot;西西&quot;i >> nth=1</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>true</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>p</value>
      <webElementGuid>fa8c23f2-2cca-4338-af5f-f22af47d6628</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>MuiTypography-root MuiTypography-body2 css-g418i2</value>
      <webElementGuid>02fd521d-a8b7-45b7-9a21-bee80d8ba2a5</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>text</name>
      <type>Main</type>
      <value>西西</value>
      <webElementGuid>9c32e044-497d-4b42-81ac-44815418604e</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;root&quot;)/div[1]/div[1]/div[@class=&quot;gis-map-container MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-8atqhb&quot;]/div[@class=&quot;MuiBox-root css-196jqjz&quot;]/div[@class=&quot;MuiBox-root css-1w884lv&quot;]/div[@class=&quot;MuiBox-root css-8atqhb&quot;]/div[@class=&quot;MuiBox-root css-0&quot;]/div[@class=&quot;MuiGrid-root MuiGrid-container css-8p7p4d&quot;]/div[@class=&quot;MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 css-1s50f5r&quot;]/p[@class=&quot;MuiTypography-root MuiTypography-body2 css-g418i2&quot;]</value>
      <webElementGuid>fafed70b-93f8-4a97-af89-1f0f422535b4</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='root']/div/div/div[2]/div[2]/div/div[3]/div/div/div/div/div/p</value>
      <webElementGuid>3d986d90-2844-402b-9cb9-5722be41c450</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='線條圖例'])[1]/following::p[1]</value>
      <webElementGuid>310d2341-07fd-4a07-b517-108922a629c7</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='使用說明'])[1]/following::p[17]</value>
      <webElementGuid>27d06205-42ad-4229-b409-674664eb6718</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='(香港)'])[1]/preceding::p[3]</value>
      <webElementGuid>1738cc50-37f8-41df-a289-3dcdb47c2e8e</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='Stadia.AlidadeSmooth'])[1]/preceding::p[3]</value>
      <webElementGuid>d5474085-b2c8-4764-9c53-4307e6fe4f4a</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//div[3]/div/div/div/div/div/p</value>
      <webElementGuid>84222ccf-174a-4638-93d0-9d3e00faf20c</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//p[(text() = '西西' or . = '西西')]</value>
      <webElementGuid>6ea83f8e-6342-4a9e-a632-2f0d462b1401</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
