import React from "react";
import { Dimmer, Loader, Segment } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";

const MainLoader = () => {
    return (
        <Segment style={{ minHeight: "550px", paddingBottom: "100px" }}>
            <Dimmer active inverted>
                <Loader
                    inverted
                    content={
                        <FormattedMessage
                            id={"people.Information.loading.content"}
                            defaultMessage={"Loading..."}
                        />
                    }
                />
            </Dimmer>
        </Segment>
    );
};

export { MainLoader };
