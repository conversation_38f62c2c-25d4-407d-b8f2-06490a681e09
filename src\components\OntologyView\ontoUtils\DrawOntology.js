import * as d3 from "d3";

const drag = simulation => {
    function dragstarted(d) {
        if (!d3.event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    function dragged(d) {
        d.fx = d3.event.x;
        d.fy = d3.event.y;
    }

    // function dragended(d) {
    //     if (!d3.event.active) simulation.alphaTarget(0);
    //     d.fx = null;
    //     d.fy = null;
    // }

    return d3
        .drag()
        .on("start", dragstarted)
        .on("drag", dragged);
    // .on("end", dragended);
};

const calculateCurve = (source, target, curveLen) => {
    const mx = (target.x + source.x) * 0.5;
    const my = (target.y + source.y) * 0.5;

    const tsx = target.x - source.x;
    const tsy = target.y - source.y;

    const L = Math.pow(Math.pow(tsx, 2) + Math.pow(tsy, 2), 0.5);

    const qx = mx - (tsy * curveLen) / L;
    const qy = my + (tsx * curveLen) / L;

    return { qx, qy };
};

const getLineCurve = (source, target, curveLen) => {
    const { qx, qy } = calculateCurve(source, target, curveLen);

    if (isNaN(qx)) {
        return `M${source.x}, ${source.y} Q0 0 ${target.x} ${target.y}`;
    }
    return `M${source.x}, ${source.y} Q${qx} ${qy} ${target.x} ${target.y}`;
};

const DEFAULT_PROP_TYPES = ["ObjectProperty", "DatatypeProperty"];

const drawOntology = (
    myRef,
    data,
    types = DEFAULT_PROP_TYPES,
    actions = {}
) => {
    const { onEdgeLabelClick, edgeLabelCursor, mouseOverColorChange } = actions;

    const width = 1000;
    const height = 700;
    const nodeLabelSize = "1rem";
    const edgeLabelSize = "16";
    const lineRaduis = 30;
    const nodeStroke = 1.5;
    const nodeRadius = 10 - nodeStroke;
    const arrowPercent = 0.8;
    const arrowViewBox = `-0 -${nodeRadius} ${nodeRadius * 2} ${nodeRadius *
        2}`;
    const arrowPath = `M 0,-${nodeRadius * arrowPercent} L ${nodeRadius *
        arrowPercent *
        2} ,0 L 0,${nodeRadius * arrowPercent}`;

    // const color = d3.scaleOrdinal(d3.schemeCategory10);
    const color = d3
        .scaleOrdinal()
        .domain(types)
        .range(["#ff7f0e", "#2ca02c", "#0e72ff", "#ff0e3a"]);

    if (
        !(
            data &&
            data.links &&
            Array.isArray(data.links) &&
            data.nodes &&
            Array.isArray(data.nodes)
        )
    )
        return;
    const links = data.links.map(d => Object.create(d));
    const nodes = data.nodes.map(d => Object.create(d));

    const calcLinkDist = nodes => {
        if (nodes.length < 5) return 70;
        return ((nodes.length - 5) / 5) * 40 + 70;
    };

    const calcCollideR = nodes => {
        return (Math.PI * 2 * calcLinkDist(nodes)) / nodes.length;
    };

    const simulation = d3
        .forceSimulation(nodes)
        /** link: link 的拉力 */
        .force(
            "link",
            d3
                .forceLink(links)
                .id(d => d.id)
                .distance(calcLinkDist(nodes))
                .strength(1)
        )
        /** charge: node 與 node 的引力 */
        .force("charge", d3.forceManyBody())
        /** center: 重力中心 */
        .force("center", d3.forceCenter(width / 2, height / 2))
        /** 碰撞(collide)偵測 */
        .force(
            "collide",
            d3
                .forceCollide()
                .radius(calcCollideR(nodes))
                .iterations(5)
        );

    d3.select(myRef.current)
        .select("svg")
        .remove();

    const zoom = d3
        .zoom()
        .scaleExtent([-40, 40])
        // eslint-disable-next-line no-use-before-define
        .on("zoom", zoomed);

    function zoomed() {
        d3.select(".view").attr("transform", d3.event.transform);
    }

    const svg = d3
        .select(myRef.current)
        .append("svg")
        .attr("class", "svg")
        .attr("width", width)
        .attr("height", height)
        .attr("viewBox", [0, 0, width, height])
        .call(zoom);

    const view = svg
        .append("g")
        .attr("class", "view")
        .attr("x", 0.5)
        .attr("y", 0.5)
        .attr("width", width - 1)
        .attr("height", height - 1);

    svg.append("defs")
        .append("marker")
        .attr("id", "arrowhead")
        .attr("viewBox", arrowViewBox)
        .attr("refX", nodeRadius * 2 + nodeRadius * arrowPercent)
        .attr("refY", 0)
        .attr("orient", "auto")
        .attr("markerWidth", nodeRadius * (1 + arrowPercent))
        .attr("markerHeight", nodeRadius * (1 + arrowPercent))
        .attr("xoverflow", "visible")
        .append("svg:path")
        .attr("d", arrowPath)
        .attr("fill", "#999")
        .style("stroke", "none");

    const link = view
        .append("g")
        .attr("stroke", "#999")
        .attr("stroke-opacity", 0.6)
        .selectAll("line")
        .data(links)
        .join("path")
        .attr("fill", "none")
        .attr("stroke-dasharray", function(d) {
            return d.ontology === "protege" ? "2,5,3" : "";
        })
        .attr("key", function(d) {
            return d.type;
        })
        .attr("marker-end", "url(#arrowhead)");

    const node = view
        .append("g")
        .attr("stroke", "#fff")
        .attr("stroke-width", nodeStroke)
        .selectAll("circle")
        .data(nodes)
        .join("circle")
        .attr("r", nodeRadius)
        .attr("fill", function(d) {
            return color(d.type);
        })
        .attr("key", function(d) {
            return d.type;
        })
        .call(drag(simulation));

    node.append("title").text(d => d.id);

    const nodeLabel = view
        .append("g")
        .selectAll("text")
        .data(nodes)
        .join("text")
        .attr("fill", "#000")
        .attr("text-anchor", "middle")
        .attr("key", function(d) {
            return d.type;
        })
        .style("font-size", nodeLabelSize)
        .text(function(d) {
            return d.id;
        });

    const edgepaths = view
        .selectAll(".edgepath")
        .data(links)
        .enter()
        .append("path")
        .attr("class", "edgepath")
        .attr("fill-opacity", 0)
        .attr("stroke-opacity", 0)
        .attr("id", function(d, i) {
            return "edgepath" + i;
        })
        .style("pointer-events", "none");

    const edgelabels = view
        .selectAll(".edgelabel")
        .data(links)
        .enter()
        .append("text")
        // .style("pointer-events", "none")
        .attr("class", "edgelabel")
        .attr("font-size", edgeLabelSize)
        .attr("fill", "#aaa")
        .attr("visibility", "visible")
        .attr("key", function(d) {
            return d.type;
        })
        .attr("id", function(d, i) {
            return "edgelabel" + i;
        });

    let activePropRef = "";
    const setActivePropRef = ref => {
        activePropRef = ref;
    };

    edgelabels
        .append("textPath")
        .attr("xlink:href", function(d, i) {
            return "#edgepath" + i;
        })
        .style("text-anchor", "middle")
        // .style("pointer-events", "none")
        .attr("startOffset", "50%")
        .call(textWrap)
        .style("fill", "#555");

    simulation.on("tick", () => {
        link.attr("d", d => {
            return getLineCurve(d.source, d.target, lineRaduis);
        });

        node.attr("cx", d => d.x).attr("cy", d => d.y);

        nodeLabel.attr("x", d => d.x).attr("y", d => d.y - nodeRadius);

        edgepaths.attr("d", function(d) {
            return getLineCurve(d.source, d.target, lineRaduis);
        });

        // FIXME: to rotate the text to readable position.
        edgelabels.attr("transform", function(d) {
            if (d.target.x < d.source.x) {
                const bbox = this.getBBox();

                const rx = bbox.x + bbox.width / 2;
                const ry = bbox.y + bbox.height / 2;
                return "rotate(180 " + rx + " " + ry + ")";
            } else {
                return "rotate(0)";
            }
        });
    });

    function textWrap(texts) {
        texts.each(function(d, i) {
            const text = d3.select(this);
            const tspan = text.append("tspan");
            const properties = d.property.split("\n");
            if (properties.length >= 2) {
                tspan
                    .append("tspan")
                    // .text(properties[0] + " ......")
                    .text(d.propertyRef)
                    .attr("x", 0)
                    // .attr("dy", edgeLabelSize)
                    .attr("fill", "blue")
                    .attr("font-size", edgeLabelSize * 1.2)
                    .style("cursor", edgeLabelCursor || "default")
                    .on("click", function(d, i, g) {
                        setActivePropRef(d.propertyRef);
                        if (onEdgeLabelClick) {
                            onEdgeLabelClick({
                                propertyRef: d.propertyRef,
                                properties,
                                item: d3.select(this),
                                group: g,
                                texts: texts
                            });
                        }
                    })
                    .on("mouseover", function(d, i) {
                        d3.select(this).attr(
                            "fill",
                            mouseOverColorChange ? "red" : "blue"
                        );
                    })
                    .on("mouseout", function(d, i) {
                        d3.select(this).attr("fill", "blue");
                    });
            } else {
                properties.forEach((p, i) => {
                    tspan
                        .append("tspan")
                        .text(p)
                        .attr("x", 0);
                    // .attr("dy", edgeLabelSize);
                });
            }
        });
    }
};

export { DEFAULT_PROP_TYPES, drawOntology };
