import React, { createContext, useReducer } from "react";

import translations from "./translations";

import { safeGet } from "../common/codes";

// This function will be used to create `translate` function for the context
const getTranslate = langCode => key =>
    safeGet(translations, [langCode, key]) || key;

const defaultLangCode = "en";

// get langCode if it had been exist
const recordedLangCode = localStorage.getItem("langCode");

const currentLangCode = recordedLangCode || defaultLangCode;

/* We will have two things in our context state,
langCode will be the current language of the page
and translate will be the method to translate keys
into meaningful texts. Default language will be English */
const initialState = {
    langCode: currentLangCode,
    translate: getTranslate(currentLangCode)
};

export const I18nContext = createContext(initialState);

export const I18nContextProvider = ({ children }) => {
    /* This is where magic starts to happen. We're creating
    a reducer to manage the global state which will sit in
    I18nContext. For now, the only action we will have
    is setting language */
    const reducer = (state, action) => {
        switch (action.type) {
            case "setLanguage":
                return {
                    langCode: action.payload,
                    translate: getTranslate(action.payload)
                };
            default:
                return { ...initialState };
        }
    };

    /* useReducer hook receives a reducer and an initialState to
    return the current state object with a dispatch method to
    dispatch actions. */
    const [state, dispatch] = useReducer(reducer, initialState);

    /* We're Providing state object (langCode and translate method
    in this case) and also the dispatch for the children components */
    return (
        <I18nContext.Provider value={{ ...state, dispatch }}>
            {children}
        </I18nContext.Provider>
    );
};
