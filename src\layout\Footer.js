import React from "react";
//
import { FormattedMessage } from "react-intl";
import { Container, Header } from "semantic-ui-react";
import "../style/App.scss";
import footerLogoText from "../images/hkbdb-logo/footer_logo/footer_logo_text.png";
import Box from "@material-ui/core/Box";

const Logo = ({ mobile }) => (
    <Box display={"flex"} alignItems={"center"} mr={mobile ? 1 : 2}>
        <img
            src={footerLogoText}
            alt={"footer logo"}
            height={mobile ? 25 : 40}
        />
    </Box>
);

const Footer = ({ webStyle, mobile }) => {
    return (
        <React.Fragment>
            <div
                // fluid
                // textAlign={"center"}
                className={"home-footer"}
                style={{
                    width: "100%",
                    height: mobile ? "50px" : "50px",
                    backgroundColor: webStyle.footer.bg.color,
                    color: webStyle.footer.font.color,
                    textAlign: "center",
                    position: "absolute",
                    bottom: "0",
                    paddingTop: "5px",
                    paddingBottom: "5px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                }}
            >
                <Header
                    as={mobile ? "h6" : "h3"}
                    style={{
                        color: webStyle.footer.font.color
                        // lineHeight: mobile ? "30px" : "70px"
                    }}
                >
                    <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        flexDirection={mobile ? "column" : "row"}
                    >
                        {/* <Logo mobile={mobile} /> */}
                        <Box textAlign="center" ml={2} display={"flex"}>
                            <span
                                style={{
                                    marginRight: "10px",
                                    fontSize: "16px"
                                }}
                            >
                                <FormattedMessage
                                    id={"common.footer.copyright"}
                                    defaultMessage={
                                        "© 2024 All Rights Reserved. The Chinese University of Hong Kong Library"
                                    }
                                />
                            </span>
                        </Box>
                    </Box>
                </Header>
            </div>
        </React.Fragment>
    );
};

export default Footer;
