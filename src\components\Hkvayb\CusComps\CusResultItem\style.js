import { makeStyles } from "@material-ui/styles";

const fontStyle = {
    fontFamily: "NotoSansHK",
    fontStretch: "normal",
    fontStyle: "normal",
    textAlign: "left"
};

const useStyles = makeStyles({
    root: {},
    hkvayb_default: props => ({
        ...fontStyle,
        margin: "8px 0 0 ",
        fontSize: "16px",
        fontWeight: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        color: "#333",
        ...props.hkvayb_default
    }),
    hkvayb_primary: props => ({
        ...fontStyle,
        margin: "0 0 4px 0",
        fontSize: "14px",
        lineHeight: "2",
        letterSpacing: "0.22px",
        color: "#b79d79",
        ...props.hkvayb_primary
    }),
    hkvayb_secondary: props => ({
        ...fontStyle,
        margin: "4px 0 0",
        fontSize: "20px",
        fontWeight: "500",
        lineHeight: "1.6",
        letterSpacing: "0.32px",
        color: "#333",
        ...props.hkvayb_secondary
    })
});

export default useStyles;
