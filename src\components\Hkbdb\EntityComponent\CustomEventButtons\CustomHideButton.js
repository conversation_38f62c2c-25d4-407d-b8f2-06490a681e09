import React, { useContext, useEffect, useState } from "react";

// intl
import { FormattedMessage } from "react-intl";

// ui
import { <PERSON><PERSON>, Popup } from "semantic-ui-react";

// api
import { Api, updateHkbdbData } from "../../../../api/hkbdb/Api";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// global vars
import { GV } from "../../../../common/codes/globalVars";
import config from "../../../../config/config";

// common
import {
    isNotEmpty,
    isTrue,
    isHide,
    convertHideValue
} from "../../../../common/codes";

// custom
import CustomDebounce from "../TableComp/CustomDeBounce";

// auth
import authority from "../../../../App-authority";

// helper
import { displayInstanceName } from "../helper";
import { queryPersonInfo } from "../../People/action";
import { queryOrgInfo } from "../../Organization/action";
import { decodeURIComponentSafe } from "../../../../common/codes/jenaHelper";

const DEFAULT_VALUE = "person";
const CustomHideButton = ({ id, name, type }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation: info, information } = state;

    // PER/ORG 統一放在 "personId" property
    const { personId } = information;
    const { user } = state;
    const { uid, role } = user;

    // personInfo or OrganizationInfo
    const targetInfo = info[type.toLowerCase() || DEFAULT_VALUE];
    const [hideValue, setHideValue] = useState(isHide(targetInfo));

    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debHideValue = CustomDebounce(hideValue, 500);
    const oriHideValue = isHide(targetInfo);

    useEffect(() => {
        setHideValue(isHide(targetInfo));
    }, [targetInfo]);

    const handleOnClick = () => {
        setHideValue(!hideValue);
    };

    const _reformatHideData = hide => {
        const { property, graph } = GV.defaultHideObj;
        return {
            graph,
            srcId: personId,
            classType: type,
            value: { [property]: convertHideValue(hide) }
        };
    };

    useEffect(() => {
        if (oriHideValue === debHideValue) {
            // no change
            return;
        }
        async function updateData() {
            const entrySrc = _reformatHideData(oriHideValue);
            const entryDst = _reformatHideData(debHideValue);
            //
            const result = await updateHkbdbData(
                Api.restfulHKBDB(),
                entrySrc,
                entryDst
            );
            //
            if (result.state) {
                // 重新抓更新後的info
                if (type && type === "Person") {
                    await queryPersonInfo(name, dispatch);
                } else {
                    await queryOrgInfo(name, dispatch);
                }
                // close
                dispatch({
                    type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                    payload: {
                        target: config.entity[type].reloadInfo,
                        signal: `created-${new Date().getTime()}`
                    }
                });
            }
        }
        updateData();
    }, [debHideValue]);
    //
    const customButtonStyle = {
        marginLeft: ".8em",
        marginTop: "1em",
        marginBottom: "1em",
        padding: ".3em"
    };
    //
    if (
        !isTrue(process.env.FEATURE_HIDE_INSTANCE) ||
        !isNotEmpty(uid) ||
        !authority.People_Information.includes(role)
    ) {
        return null;
    }

    return (
        <div>
            <Popup
                content={
                    hideValue ? (
                        <FormattedMessage
                            id={"people.Information.show.person"}
                            defaultMessage={`Hide ｢{name}」`}
                            values={{
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                )
                            }}
                        />
                    ) : (
                        <FormattedMessage
                            id={"people.Information.hide.person"}
                            defaultMessage={`Hide ｢{name}」`}
                            values={{
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                )
                            }}
                        />
                    )
                }
                key={"hide"}
                trigger={
                    <Button
                        size="mini"
                        color={hideValue ? "grey" : "green"}
                        icon="hide"
                        style={customButtonStyle}
                        onClick={handleOnClick}
                    />
                }
            />
        </div>
    );
};

export default CustomHideButton;
