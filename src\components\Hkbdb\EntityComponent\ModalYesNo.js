import React from "react";
import { Mo<PERSON>, But<PERSON> } from "semantic-ui-react";
import { displayInstanceName } from "./helper";
import { Api } from "../../../api/hkbdb/Api";
import { FormattedMessage } from "react-intl";

const ModalYesNo = ({ open, setOpen, setYesNo, modalHeader }) => {
    const handleOnClickNo = () => {
        setOpen(false);
        setYesNo(false);
    };
    const handleOnClickYes = () => {
        setOpen(false);
        setYesNo(true);
    };

    return (
        <Modal
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            open={open}
        >
            <Modal.Header>{modalHeader}</Modal.Header>
            <Modal.Content>
                <Modal.Description>
                    <p>
                        <FormattedMessage
                            id={"information.ModalYesNo.description"}
                            defaultMessage={`Ready for {name}...`}
                            values={{
                                name: modalHeader && modalHeader.toLowerCase()
                            }}
                        />
                    </p>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button color="black" onClick={handleOnClickNo}>
                    <FormattedMessage
                        id={"information.ModalYesNo.cancel"}
                        defaultMessage={`Cancel`}
                    />
                </Button>
                <Button onClick={handleOnClickYes} positive>
                    <FormattedMessage
                        id={"information.ModalYesNo.confirm"}
                        defaultMessage={`Confirm`}
                    />
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default ModalYesNo;
