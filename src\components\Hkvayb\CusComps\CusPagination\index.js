import React from "react";

import Pagination from "@mui/material/Pagination";

import CusDropdown from "../CusDropdown";

import useStyles from "./style";

const coverDropdownStyle = {
    hkvayb_dropdown: {
        width: "unset"
    }
};

const CusPagination = ({
    limit,
    activePage,
    pages,
    onPageChange = () => {},
    onPageSelect = () => {},
    style = {}
}) => {
    const classes = useStyles(style);
    const pageCount = Math.ceil(pages / limit || 1);
    const pageOptions = Array.from(Array(pageCount + 1).keys())
        .filter(i => i !== 0)
        .map(i => ({ label: `${i}`, value: `${i}` }));
    return (
        <div className={classes.hkvayb_pagination_group}>
            <Pagination
                page={activePage}
                className={classes.hkvayb_pagination}
                count={pageCount}
                variant="outlined"
                shape="rounded"
                onChange={onPageChange}
            />
            <div className={classes.hkvayb_pagination_dropdown_group}>
                <div>目前位於</div>
                <CusDropdown
                    options={pageOptions.filter(
                        item => item.label !== `${activePage}`
                    )}
                    style={coverDropdownStyle}
                    placeholder={`${activePage}`}
                    onChange={onPageSelect}
                />
                <div>{`頁，共${pageCount}頁`}</div>
            </div>
        </div>
    );
};

export default CusPagination;
