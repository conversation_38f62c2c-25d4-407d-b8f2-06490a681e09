import React from "react";

import { useTheme } from "@mui/material/styles";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";

import useStyles from "./style";

const CusDropdown = ({
    value = "",
    onChange = () => {},
    options = [],
    placeholder = "Please select...",
    style = {},
    ...rest
}) => {
    const classes = useStyles(style);
    const theme = useTheme();
    const [selectedValues, setSelectedValues] = React.useState([]);
    const ITEM_HEIGHT = 48;
    const ITEM_PADDING_TOP = 8;
    const MenuProps = {
        PaperProps: {
            style: {
                maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
                width: 250
            }
        }
    };

    const handleChange = event => {
        const {
            target: { value }
        } = event;
        const tmpValue = typeof value === "string" ? value.split(",") : value;
        setSelectedValues(tmpValue);
        onChange(event);
    };
    const getStyles = (_value, _selectedValue, theme) => {
        return {
            fontWeight:
                _selectedValue.indexOf(_value) === -1
                    ? theme.typography.fontWeightRegular
                    : theme.typography.fontWeightMedium
        };
    };

    React.useEffect(() => {
        if (value) {
            setSelectedValues(
                options.filter(option => value.indexOf(option.label) !== -1)
            );
        }
        return () => setSelectedValues([]);
    }, []);

    return (
        <Select
            className={classes.hkvayb_dropdown}
            multiple
            displayEmpty
            value={selectedValues}
            onChange={handleChange}
            // input={<OutlinedInput />}
            renderValue={selected => {
                if (selected.length === 0) {
                    return <em>{placeholder}</em>;
                }
                if (!value) {
                    setSelectedValues([]);
                    return <em>{placeholder}</em>;
                }
                // return selected.map(item => item.label).join(", ");
                return value;
            }}
            MenuProps={MenuProps}
            inputProps={{ "aria-label": "Without label" }}
            {...rest}
        >
            <MenuItem disabled value="">
                <em>{placeholder}</em>
            </MenuItem>
            {options.map(item => (
                <MenuItem
                    key={item.label}
                    value={item}
                    style={getStyles(item.label, selectedValues, theme)}
                >
                    {item.label}
                </MenuItem>
            ))}
        </Select>
    );
};

export default CusDropdown;
