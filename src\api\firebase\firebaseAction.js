import {
    MAX_WORD_CLOUD,
    FBWordCloud,
    atomicInc,
    updateHotKey
} from "./realtimeDatabase";

export const FETCH_FIREBASE_DATA_SUCCESS = "FETCH_FIREBASE_DATA_SUCCESS";

export const incValueCount = ({ keyword, fbReducer }) => {
    // keyword: {name: '賴和', id: 'PER250'}
    // keyword: {perId: 'PER%E8%A5%BF%E8%A5%BF%E5%88%A9%E5%A9%AD', srcName: '西西利婭',
    // name: '西西利婭', url: '/zh-hans/people/JUU4JUE1JUJGJUU4JUE1JUJGJUU1JTg4JUE5JUU1JUE5JUFE?name=6KW_6KW_5Yip5amt'}
    const { perId: id, name } = keyword;

    if ((id && id.length > 0) || (name && name.length > 0)) {
        const { wordCloud } = fbReducer;

        if (!wordCloud) {
            return;
        }

        // if keyword id matches
        if (Object.prototype.hasOwnProperty.call(wordCloud, id)) {
            const keyCount = `${FBWordCloud}/${id}/count`;
            atomicInc(keyCount);
            return;
        }

        // if keyword name matches
        const keyFound = Object.keys(wordCloud).find(
            bid => wordCloud[bid].keyword === name
        );
        if (keyFound && keyFound.length > 0) {
            const keyCount = `${FBWordCloud}/${keyFound}/count`;
            atomicInc(keyCount);
        }

        // if the new key, 目前只處理 Person 和 Organization
        if (!id.startsWith("PER") && !id.startsWith("ORG")) {
            return;
        }

        // 以免 database 爆炸
        if (Object.keys(wordCloud).length > MAX_WORD_CLOUD) {
            return;
        }

        // {"count":"90","id":"PER87","type":"Person","keyword":"施梅樵"}
        const newWordCloud = {
            id,
            keyword: name,
            count: 1,
            type: id.startsWith("PER") ? "Person" : "Organization"
        };
        wordCloud[id] = newWordCloud;
        updateHotKey(id, newWordCloud);
    }
};

export const test = () => {};
