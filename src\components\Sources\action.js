import { Api, readHkbdbData } from "../../api/hkbdb/Api";
import Act from "../../store/actions";

// 將 property 依語系處理(必須 配合 API => 取得所有語系的 property): 方便切換語系時, 可直接渲染
const processDataset = data => {
    // data like this:
    // [{
    //   "label": "香港古典詩文集經眼錄",
    //   "lang": "zh",
    //   "dataset": "abcwhkp"
    // },
    // {
    //   "label": "An Annotated Bibliography of the Classical Writings of Hong Kong Poets",
    //   "lang": "en",
    //   "dataset": "abcwhkp"
    // },...]
    const _data = data.map(d => {
        if (Object.keys(d).includes("lang")) {
            if (d["lang"] === "en") {
                return {
                    ...d,
                    en: { ...d }
                };
            } else {
                return {
                    ...d,
                    zh: { ...d }
                };
            }
        }
        return { ...d };
    });
};

export const getDataset = (dispatch, locale, setIsLoading) => {
    const api = Api.getDataset().replace("{locale}", locale);
    setIsLoading(true);
    return readHkbdbData(api)
        .then(response => {
            const { data } = response;
            // console.log("api get graph: ", response.data);
            dispatch({
                type: Act.SET_DATASET,
                payload: Object.assign([], response.data)
            });
        })
        .catch(() => {
            dispatch({
                type: Act.SET_DATASET,
                payload: []
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};
