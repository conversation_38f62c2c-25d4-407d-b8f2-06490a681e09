import React, { useContext, useEffect, createRef, useState } from "react";
import { Api, readHkbdbData } from "../../../api/hkbdb/Api";
import Act from "../../../store/actions";
import { StoreContext } from "../../../store/StoreProvider";
import { DEFAULT_PROP_TYPES, drawOntology } from "../ontoUtils/DrawOntology";
import {
    dataToNodesLinks,
    ontoHandleChange,
    filterDupTriple,
    addPropertyRef,
    filterLinksPropRef,
    ONTOLOGY
} from "../ontoUtils/ontoCommon";
import OntoRadioBox from "./OntoRadioBox";
import PropTypes from "prop-types";
import { ontoGraphStyle } from "../config/ontoConfig";

export const filterToOnto = (_ontology, className) => {
    const classOntology = _ontology.filter(o => {
        return o.domain === className || o.range === className;
    });

    return dataToNodesLinks(classOntology, [className]);
};

const CommonOnto = ({ className, myRef }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { property } = state;
    const { ontology, ontoClassNames, drawOntologyData } = property;
    const [checked, setChecked] = useState("Both");

    // 放進 d3 中, Event listener callback
    const graphAct = {
        edgeLabelCursor: "default", // e.g. "pointer", "default"...
        mouseOverColorChange: false, // 滑鼠移到 edgeLabel 上時, 是否要改顏色
        onEdgeLabelClick: data => {
            if (data && data.properties && data.propertyRef) {
                dispatch({
                    type: Act.ONTO_CUR_PROPERTIES,
                    payload: Object.assign({}, data)
                });
                // set active prop ref
                dispatch({
                    type: Act.ONTO_ACTIVE_PROP_REF,
                    payload: data.propertyRef
                });
            }
        }
    };

    const handleChange = (e, { value }) => {
        setChecked(value);
        ontoHandleChange(myRef, value);
    };

    useEffect(() => {
        // 當 className 變更時, 清除 curProperties
        dispatch({
            type: Act.ONTO_CUR_PROPERTIES,
            payload: []
        });
        // clear active prop ref
        dispatch({
            type: Act.ONTO_ACTIVE_PROP_REF,
            payload: ""
        });
    }, [className]);

    useEffect(() => {
        if (!ontoClassNames || ontoClassNames.length === 0) return;

        if (ontology.hasOwnProperty(className)) {
            return;
        }

        if (ontology.hasOwnProperty("download")) {
            return;
        }

        /**
         * API response data:
         * [{op, domain, range, inverse, type:ObjectProperty/DatatypeProperty, ontology:db/protege},
         * {...},...]
         * 當下載 ontology 後, 一併處理 ontology, 轉成我們需要的資料
         */
        readHkbdbData(Api.getOntologyProtege())
            .then(_ontology => {
                const _filteredOnto = filterDupTriple(_ontology.data);
                const classOnto = {};
                classOnto["download"] = _filteredOnto;
                let initPropRefNum = 1;

                // 將各類別的 data 集合在一起 ['className': {links:[], nodes:[]}, ...]
                const ontologySet = {};

                // 將各類別的 propRef 集合在一起 {'ref-1':[], 'ref-2':[],...}
                let propRefs = {};

                const classNamesAll = ontoClassNames.map(ocn => ocn.className);
                // 清除重複的類別
                const classNames = [...new Set(classNamesAll)];
                classNames.forEach(cn => {
                    classOnto[cn] = filterToOnto(classOnto["download"], cn);
                    // 各類別的 property ref 編碼統一由 1 續編
                    // add properties ref
                    const { ontologyClass, propertyNum } = addPropertyRef(
                        classOnto[cn],
                        ONTOLOGY.propRefPrefix,
                        initPropRefNum
                    );
                    // 合併至 ontologySet
                    if (!ontologySet.hasOwnProperty(cn)) {
                        ontologySet[cn] = { links: [], nodes: [] };
                    }

                    ontologySet[cn].links = ontologyClass.links
                        ? ontologySet[cn].links.concat(ontologyClass.links)
                        : ontologySet[cn].links;

                    ontologySet[cn].nodes = ontologyClass.nodes
                        ? ontologySet[cn].nodes.concat(ontologyClass.nodes)
                        : ontologySet[cn].nodes;

                    //
                    const filterPropRef = filterLinksPropRef(ontologyClass, cn);
                    propRefs = Object.assign(propRefs, filterPropRef);

                    // 下一個類別的 propRef 編號接續
                    initPropRefNum = propertyNum;
                });

                dispatch({
                    type: Act.DRAW_ONTO_DATA,
                    payload: ontologySet
                });

                dispatch({
                    type: Act.PROTEGE_GET_DATA,
                    payload: classOnto
                });

                if (propRefs) {
                    dispatch({
                        type: Act.ONTO_PROPERTY_REF,
                        payload: Object.assign({}, propRefs)
                    });
                } else {
                    dispatch({
                        type: Act.ONTO_PROPERTY_REF,
                        payload: null
                    });
                }
            })
            .catch(err => {
                console.log(err);
            });
    }, [ontoClassNames]);

    useEffect(() => {
        if (!(ontology && drawOntologyData)) return;
        if (ontology.hasOwnProperty(className)) {
            // 繪製 ontology
            drawOntology(
                myRef,
                drawOntologyData[className],
                DEFAULT_PROP_TYPES,
                graphAct
            );
        }
    }, [className, ontology, drawOntologyData]);

    return (
        <React.Fragment>
            {/* todo: 待完成過濾 object property & data property 再開啟 OntoRadioBox */}
            {/* <OntoRadioBox checked={checked} handleChange={handleChange} /> */}
            <div ref={myRef} className={"common-onto"} style={ontoGraphStyle} />
        </React.Fragment>
    );
};

CommonOnto.propTypes = {
    className: PropTypes.string.isRequired
};

export default CommonOnto;
