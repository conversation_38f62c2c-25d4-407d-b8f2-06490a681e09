import React, { useState, forwardRef, useEffect, createRef } from "react";
import { Button } from "semantic-ui-react";
import {
    exportImage<PERSON><PERSON><PERSON>,
    EXPORT_FILE_NAME
} from "../../ontoUtils/ontoCommon";
import { FormattedMessage } from "react-intl";
import { scrollTop, genOptions } from "./utils";
import OntologyComp from "../../OntologyComp";

// eslint-disable-next-line react/display-name
const DrawDisplay = forwardRef((props, ref) => {
    const myRef = createRef();
    // props
    const { found, onExportImgClick, onFinishExportImg } = props;
    const [exportImgOpt, setExportImgOpt] = useState({
        resolution: 5
    });

    // local state
    const [isImgExporting, setIsImgExporting] = useState(false);

    useEffect(() => {
        if (ref.current) {
            setExportImgOpt(genOptions(ref.current, exportImgOpt));
        }
    }, [ref]);

    return (
        <React.Fragment>
            {/* fixme: download image ,image和所見有差距 */}
            <div
                style={{
                    display: "flex",
                    // display: "none", // 暫時隱藏
                    justifyContent: "flex-end",
                    // height: "0px"
                    marginBottom: "20px"
                }}
            >
                <Button
                    loading={isImgExporting || undefined}
                    onClick={() => {
                        // 必須先 scroll top 再匯出, 才不會被截圖
                        scrollTop();
                        setIsImgExporting(true);
                        if (onExportImgClick) onExportImgClick();
                        setTimeout(() => {
                            exportImageHandler(
                                ref.current,
                                EXPORT_FILE_NAME.ontology,
                                exportImgOpt
                            )
                                .then(res => {
                                    console.log(res);
                                })
                                .catch(err => {
                                    console.log(err);
                                })
                                .finally(() => {
                                    setIsImgExporting(false);
                                    if (onFinishExportImg) onFinishExportImg();
                                });
                        }, 300);
                    }}
                    color={"blue"}
                >
                    <FormattedMessage
                        id={"ontology.exportImage"}
                        defaultMessage={"Export Image"}
                    />
                </Button>
            </div>
            <div
                ref={ref}
                style={{
                    marginTop: "0px",
                    maxHeight: "700px",
                    minWidth: "1000px",
                    maxWidth: "1000px",
                    padding: "5px"
                }}
            >
                {found && <OntologyComp className={found} myRef={myRef} />}
            </div>
        </React.Fragment>
    );
});

export default DrawDisplay;
