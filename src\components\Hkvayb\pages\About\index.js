import React, { useContext } from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import config from "../../config";

import CusBr from "../../CusComps/CusBr";
import CusDiv from "../../CusComps/CusDiv";
import CusCard from "../../CusComps/CusCard";
import CusFooter from "../../CusComps/CusFooter";
import CusMarkdown from "../../CusComps/CusMarkdown";
import CusCardGroup from "../../CusComps/CusCardGroup";
import CusSecondaryHeader from "../../CusComps/CusSecondaryHeader";

import { safeGet } from "../../../../common/codes";

import { StoreContext } from "../../../../store/StoreProvider";

const coverCardStyle = {
    hkvayb_card: {
        float: "left",
        margin: `0 0 63.5px 0`
    },
    hkvayb_card_title_zh: {
        fontSize: "16px"
    },
    hkvayb_card_box: {
        height: "55.5px"
    },
    hkvayb_desc: {
        "@media screen and (max-width: 600px)": {
            marginLeft: "0",
            marginTop: "24px"
        }
    }
};

const coverDivStyle = {
    hkvayb_div: {
        display: "flex",
        justifyContent: "space-between",
        flexWrap: "wrap",
        maxWidth: "1500px",
        "&:after": {
            content: "close-quote",
            width: "222px"
        }
    }
};

const coverCardGroupStyle = {
    hkvayb_card_group: {
        justifyContent: "flex-end",
        textAlign: "justify",
        "@media screen and (max-width: 600px)": {
            padding: "0px 40px"
        }
    }
};

const About = () => {
    const classes = useStyles();
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const [width, height] = [222, 276];
    // const [options, setOptions] = React.useState(["最新", "時間"]);
    const yearBookContent = safeGet(config.yearBookContent, [locale], []);
    const yearBookDesc = safeGet(config.yearBookDesc, [locale], []);

    return (
        <div>
            <CusSecondaryHeader
                label={
                    <FormattedMessage
                        id="hkvayb.search.header.about"
                        defaultMessage="About"
                    />
                }
            />
            <div className={classes.hkvayb_about_grid}>
                <CusBr />
                <CusCardGroup style={coverCardGroupStyle}>
                    <CusMarkdown value={yearBookDesc} />
                </CusCardGroup>
                <CusBr />
                <CusCardGroup style={coverCardGroupStyle}></CusCardGroup>
                <CusCardGroup>
                    <CusDiv style={coverDivStyle}>
                        {yearBookContent.map(item => (
                            <CusCard
                                lineArrow
                                underLine
                                key={item.primary}
                                src={item.src}
                                primary={item.primary}
                                content={item.content}
                                width={width}
                                height={height}
                                style={coverCardStyle}
                            />
                        ))}
                    </CusDiv>
                </CusCardGroup>
            </div>
            <div className={classes.hkvayb_about_grid}>
                <div className={classes.hkvayb_about_declare}>
                    <FormattedMessage
                        id="hkvayb.search.disclaimer"
                        defaultMessage="Hong Kong Arts Development Council fully supports freedom of artistic expression. The views and opinions expressed in this project do not represent the stand of the Council."
                    />
                </div>
            </div>
            <CusFooter />
        </div>
    );
};

export default About;
