import L from "leaflet";
//
import EasyMapIcon from "./mapCore/EasyMapIcon.jsx";
//
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
// eslint-disable-next-line import/no-extraneous-dependencies
import isString from "lodash/isString";

// config
import { linePallet } from "./config";

// get the map center [lat, long]
// 若經緯度相同，權重只算一次
export const getCenter = latLngList => {
    const center = { lat: 0.0, long: 0.0 };
    const latLongList = [];
    let uniqPlaceCount = 0;
    const safeCount = count => (count === 0 ? 1 : count);
    if (!Array.isArray(latLngList)) return [center.long, center.lat];
    latLngList.forEach(item => {
        // 	coordinates: [long, lat],

        const long = item[0];
        const lat = item[1];
        const latLongStr = `${lat}_${long}`;
        if (!latLongList.includes(latLongStr)) {
            latLongList.push(latLongStr);
            center.lat += lat;
            center.long += long;
            uniqPlaceCount += 1;
        }
    });
    // return [long, lat]
    return [
        center.long / safeCount(uniqPlaceCount),
        center.lat / safeCount(uniqPlaceCount)
    ];
};

/**
 * // generate points for cluster
 * @param srcData
 * @param latKey
 * @param longKey
 * @param locationIdKey
 * @param locationNameKey
 * @param propertiesKey
 * @returns {(*&{properties: *, geometry: *&{coordinates: [number,number]|*}})[]}
 */
export const generatePointsForCluster = ({
    srcData,
    latKey = "lat",
    longKey = "long",
    locationIdKey = "locId",
    locationNameKey = "location",
    propertiesKey = []
}) => {
    //
    const tmpPoints = [];
    //
    srcData.forEach(item => {
        const latFloat =
            typeof item[latKey] === "string"
                ? parseFloat(item[latKey])
                : item[latKey];
        const longFloat =
            typeof item[longKey] === "string"
                ? parseFloat(item[longKey])
                : item[longKey];
        // const found = tmpPoints.find(info => info.geometry?.coordinates?.[1] === lat && info.geometry?.coordinates?.[0] === long);
        const found = tmpPoints.find(
            info => info.properties?.location === item[locationNameKey]
        );
        // console.log("[step1]found", found);
        if (!found) {
            const eventList = found?.properties?.eventList ?? [];
            eventList.push({
                ...propertiesKey.reduce(
                    (acc, key) => ({
                        ...acc,
                        [key]: item[key]
                    }),
                    {}
                )
            });
            tmpPoints.push({
                type: "Feature",
                id: item[locationIdKey],
                properties: {
                    id: item[locationIdKey],
                    // Leaflet uses lat-lng (or northing-easting) whereas GeoJSON uses lng-lat (or easting-northing).
                    lngLat: [[longFloat, latFloat]],
                    locId: item[locationIdKey],
                    location: item[locationNameKey],
                    intensity: 1,
                    ...propertiesKey.reduce(
                        (acc, key) => ({
                            ...acc,
                            [key]: item[key]
                        }),
                        {}
                    ),
                    eventList: eventList
                },
                geometry: {
                    type: "Point", // 必須為 Point, useSupercluster 才可以整合
                    // 更詳細資訊請參考: https://www.npmjs.com/package/supercluster
                    coordinates: [longFloat, latFloat]
                }
            });
        } else {
            // console.log("[step2]found", found);
            found.properties.intensity += 1;
            found.properties.lngLat.concat([[longFloat, latFloat]]);
            const eventList = found?.properties?.eventList ?? [];
            eventList.push({
                ...propertiesKey.reduce(
                    (acc, key) => ({
                        ...acc,
                        [key]: item[key]
                    }),
                    {}
                )
            });
            found.properties.eventList = eventList;
        }
    });

    // return tmpPoints;
    return tmpPoints.map(o => ({
        ...o,
        properties: {
            ...o.properties
        },
        geometry: {
            ...o.geometry,
            // 重新計算 每個 location 的 lat, lng
            coordinates: getCenter(o?.properties?.lngLat || [])
        }
    }));
};

export const generateGroupData = ({ srcData, groupKey }) => {
    if (!Array.isArray(srcData) || srcData.length === 0) {
        return {};
    }
    if (!groupKey) {
        return {};
    }
    const group = srcData.reduce((acc, item) => {
        const grpId = item[groupKey] || "";
        if (!acc[grpId]) {
            acc[grpId] = [];
        }
        acc[grpId].push(item);
        return acc;
    }, {});
    return group;
};

/**
 * // get years from srcData
 *
 * @typedef {{year:number; month:number; day:number}} srcDataItem
 *
 * @typedef {srcDataItem[]} SrcData
 *
 * @param srcData {SrcData}
 * @returns {number[]}
 */
export const generateYears = srcData => {
    const years = [];
    srcData.forEach(item => {
        if (item.year && !years.includes(item.year)) {
            years.push(Number(item.year));
        }
    });
    return years.sort();
};

/**
 * // get lines{{srcPoint:any; dstPoint:any}[]} from srcData
 * // step1: sort by year, month, day
 * // step2: group data by perId => {[perId-1]: any[]; [perId-2]: any[]}
 * // step3: generate lines for each item in group
 *
 * @typedef {{srcPoint:any; dstPoint:any; srcYear: number; dstYear: number}} Line
 *
 * @param srcData
 * @returns {Line[]}
 */
export const generateLines = ({ srcData, locationNameKey = "location" }) => {
    // 對原始數據進行排序 - 保持不變
    const sortedData = srcData.sort((a, b) => {
        const yearA = a.year || 0;
        const yearB = b.year || 0;
        const monthA = a.month || 0;
        const monthB = b.month || 0;
        const dayA = a.day || 0;
        const dayB = b.day || 0;
        if (yearA === yearB) {
            if (monthA === monthB) {
                return dayA - dayB;
            }
            return monthA - monthB;
        }
        return yearA - yearB;
    });

    // 按人物ID分組
    const groupData = sortedData.reduce((acc, item) => {
        const perId = item.perId || "";
        if (!acc[perId]) {
            acc[perId] = [];
        }
        acc[perId].push(item);
        return acc;
    }, {});

    // 線條樣式分配
    let styleIndex = 0;
    const getStyle = () => {
        if (styleIndex >= linePallet.length) {
            styleIndex = 0;
        }
        const style = linePallet[styleIndex];
        styleIndex += 1;
        return style;
    };

    const groupPallet = [];
    const lines = [];
    const optimizedLines = []; // 用於存儲優化後的線段

    Object.keys(groupData).forEach(perId => {
        const group = groupData[perId];
        const groupLength = group.length;
        const style = getStyle();
        const person = group[0].person || "";

        groupPallet.push({ perId, group, style, person });

        for (let i = 0; i < group.length - 1; i += 1) {
            // 跳過相同位置
            if (group[i][locationNameKey] === group[i + 1][locationNameKey]) {
                continue;
            }

            // 創建原始線段對象（用於 groupPallet 的參考）
            const originalLine = {
                id: `${perId}-${i}-${group[i][locationNameKey]}-${group[i + 1][locationNameKey]
                    }`,
                srcPoint: group[i],
                srcYear: group[i].year,
                srcLocation: group[i][locationNameKey],
                dstPoint: group[i + 1],
                dstYear: group[i + 1].year,
                dstLocation: group[i + 1][locationNameKey],
                style: style,
                addArrowHead: groupLength >= 2
            };

            lines.push(originalLine);

            // 預處理經緯度數據
            const srcLat = parseFloat(group[i].lat.replace("e0", ""));
            const srcLong = parseFloat(group[i].long.replace("e0", ""));
            const dstLat = parseFloat(group[i + 1].lat.replace("e0", ""));
            const dstLong = parseFloat(group[i + 1].long.replace("e0", ""));

            // 預計算邊界
            const bounds = [
                Math.min(srcLong, dstLong), // minLng
                Math.min(srcLat, dstLat), // minLat
                Math.max(srcLong, dstLong), // maxLng
                Math.max(srcLat, dstLat) // maxLat
            ];

            // 創建優化的線段對象
            optimizedLines.push({
                id: originalLine.id,
                bounds: bounds, // 存儲預計算的邊界
                srcPoint: {
                    ...group[i],
                    lat_num: srcLat, // 添加數值類型的經緯度
                    long_num: srcLong // 但保留原始字符串格式
                },
                dstPoint: {
                    ...group[i + 1],
                    lat_num: dstLat,
                    long_num: dstLong
                },
                srcYear: originalLine.srcYear,
                srcLocation: originalLine.srcLocation,
                dstYear: originalLine.dstYear,
                dstLocation: originalLine.dstLocation,
                style: originalLine.style,
                addArrowHead: originalLine.addArrowHead
            });
        }
    });

    return {
        lines: optimizedLines, // 返回優化線段
        // lines: lines,       // 返回原始線段
        groupPallet
    };
};

// interface ClusterMapProps {
//     intensity: number;
//     data: any[];
//     publications: any[];
//     location: string;
// }
//
// interface ClusterMap extends ClusterMapProps {
//     count: number;
// }

export const clusterMap = _props => ({
    intensity: _props.intensity,
    count: 1,
    data: _props.data,
    publications: _props.publications,
    location: _props.location
});

export const clusterReduce = (acc, _props) => {
    acc.count += 1;
    acc.intensity += _props.intensity;
    acc.data = [...(acc.data || []), ...(_props.data || [])];
    acc.publications = [
        ...(acc.publications || []),
        ...(_props.publications || [])
    ];
    if (_props.location) {
        acc.location +=
            isString(acc?.location) && acc.location.length > 0
                ? `,${_props.location}`
                : _props.location;
    }
    return acc;
};

// interface UpdateMapActions {
//     setBounds: (latLng: BBox) => void;
//     setZoom: (zoom: number) => void;
// }
// // 更新地圖 state
export const updateMap = (map, actions) => () => {
    const { setBounds, setZoom } = actions;
    if (isFunction(setBounds)) {
        const b = map.getBounds();

        setBounds([
            b.getSouthWest().lng,
            b.getSouthWest().lat,
            b.getNorthEast().lng,
            b.getNorthEast().lat
        ]);
    }
    if (isFunction(setZoom)) {
        setZoom(map.getZoom());
    }
};

// 自地圖上建立 Marker
export const createMarker = (map, [log, lat]) => {
    // more info about default zIndex: https://leafletjs.com/reference.html#map-pane
    if (!map) return null;

    // 驗證座標是否有效
    if (log == null || lat == null || isNaN(log) || isNaN(lat)) {
        console.warn('Invalid coordinates for marker:', { log, lat });
        return null;
    }

    const options = {
        draggable: true,
        zIndexOffset: 610, // always on top of other Marker
        icon: EasyMapIcon
    };
    return L.marker([lat, log], options).addTo(map);
};

// function to concat string: year, month, day
// for example: year: 2021, month: 1, day: 1 => 2021-01-01
// for example: year: 2021, month: 1 => 2021-01
// for example: year: 2021=> 2021
export const concatDate = (year, month, day) => {
    const list = [];
    const isYearValidate = year !== "" && year != null;
    const isMonthValidate =
        month !== "" &&
        month != null &&
        String(month) !== "0" &&
        String(month) !== "00";
    const isDayValidate =
        day !== "" &&
        day != null &&
        String(day) !== "0" &&
        String(day) !== "00";
    if (isYearValidate) {
        list.push(year);
        if (isMonthValidate) {
            list.push(month);
            if (isDayValidate) {
                list.push(day);
            }
        }
    }
    return list.map(o => String(o || "").padStart(2, "0")).join("-");
};

export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Combines map points with the same coordinates
 * @param {Array} points - Array of map point objects
 * @returns {Array} - Combined array where points with same coordinates are merged
 */
export const combinePointsByCoordinates = points => {
    if (!Array.isArray(points) || points.length === 0) {
        return [];
    }

    // Create a map to group points by their coordinates
    const coordMap = {};

    // First pass: group points by coordinates
    points.forEach(point => {
        const coords = point.geometry.coordinates;
        const coordKey = `${coords[0]}、${coords[1]}`;

        if (!coordMap[coordKey]) {
            coordMap[coordKey] = [];
        }

        coordMap[coordKey].push(point);
    });

    // Second pass: combine points with the same coordinates
    const combinedPoints = Object.values(coordMap).map(pointGroup => {
        if (pointGroup.length === 1) {
            return pointGroup[0]; // No need to combine if there's only one point
        }

        // Use the first point as a base
        const basePoint = JSON.parse(JSON.stringify(pointGroup[0]));

        // Track unique locations
        const uniqueLocations = new Set([basePoint.properties.location]);

        // Only combine eventList, intensity, and location
        pointGroup.slice(1).forEach(point => {
            // Combine eventList
            if (
                point.properties.eventList &&
                Array.isArray(point.properties.eventList)
            ) {
                basePoint.properties.eventList = [
                    ...basePoint.properties.eventList,
                    ...point.properties.eventList
                ];
            }

            // Sum up intensity
            if (typeof point.properties.intensity === "number") {
                basePoint.properties.intensity += point.properties.intensity;
            }

            // Add unique locations
            if (
                point.properties.location &&
                !uniqueLocations.has(point.properties.location)
            ) {
                uniqueLocations.add(point.properties.location);
            }
        });

        // Join unique locations with comma
        basePoint.properties.location = Array.from(uniqueLocations).join(",");

        return basePoint;
    });

    return combinedPoints;
};

/**
 * 判斷坐標是否在香港範圍內
 * @param {number} lat - 緯度
 * @param {number} lng - 經度
 * @returns {boolean} - 是否在香港範圍內
 */
export const isInHongKong = (lat, lng) => {
    // 香港的大致地理範圍
    const hongKongBounds = {
        north: 22.6, // 北緯
        south: 22.1, // 南緯
        east: 114.4, // 東經
        west: 113.8 // 西經
    };

    return (
        lat >= hongKongBounds.south &&
        lat <= hongKongBounds.north &&
        lng >= hongKongBounds.west &&
        lng <= hongKongBounds.east
    );
};

/**
 * 計算兩點之間的距離（公里）
 * @param {number} lat1 - 第一點緯度
 * @param {number} lon1 - 第一點經度
 * @param {number} lat2 - 第二點緯度
 * @param {number} lon2 - 第二點經度
 * @returns {number} - 距離（公里）
 */
const getDistanceFromLatLonInKm = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // 地球半徑（公里）
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};

/**
 * 判斷坐標是否在香港附近（50公里範圍內）
 * @param {number} lat - 緯度
 * @param {number} lng - 經度
 * @returns {boolean} - 是否在香港附近
 */
export const isNearHongKong = (lat, lng, mapLatLongDefault) => {
    const hongKongLat = mapLatLongDefault.hongKong[0];
    const hongKongLng = mapLatLongDefault.hongKong[1];
    const distance = getDistanceFromLatLonInKm(
        lat,
        lng,
        hongKongLat,
        hongKongLng
    );
    return distance <= 50; // 50公里範圍內視為香港附近
};

/**
 * 篩選重複的資料，只保留先出現的那一筆
 * 只處理 infoType 不是 "hasPlaceOfDeath" 和 "hasPlaceOfBirth" 的資料
 * 只有當指定的關鍵屬性都一樣時才過濾掉，如果有任何一個值不同就保留
 * @param {Array} points - 地圖點位陣列
 * @param {Array} keyProperties - 需要比較的關鍵屬性列表，如果為空則比較所有屬性
 * @param {Array} skipProperties - 需要跳過比較的屬性列表
 * @returns {Array} - 篩選後的地圖點位陣列
 */
export const filterDuplicatePoints = (
    points,
    keyProperties = [],
    skipProperties = ["place", "placeId"]
) => {
    if (!Array.isArray(points) || points.length === 0) {
        return [];
    }

    // 需要特殊處理的 infoType
    const specialInfoTypes = ["hasPlaceOfDeath", "hasPlaceOfBirth"];

    // 用於追蹤已處理的 infoId 及其對應的事件數據
    const processedInfoData = {};

    // 篩選後的結果
    return points
        .map(point => {
            // 如果沒有 eventList 或 eventList 不是陣列，直接保留
            if (
                !point.properties.eventList ||
                !Array.isArray(point.properties.eventList)
            ) {
                return point;
            }

            // 篩選 eventList
            const filteredEventList = point.properties.eventList.filter(
                event => {
                    // 如果是特殊類型，直接保留
                    if (specialInfoTypes.includes(event.infoType)) {
                        return true;
                    }

                    // 如果沒有 infoId，直接保留
                    if (!event.infoId) {
                        return true;
                    }

                    // 檢查是否已經處理過相同的 infoId
                    if (processedInfoData[event.infoId]) {
                        // 比較當前事件與已處理事件的關鍵屬性
                        const previousEvent = processedInfoData[event.infoId];

                        // 如果提供了關鍵屬性列表，則只比較這些屬性
                        // 否則比較所有屬性（除了需要跳過的屬性）
                        let propertiesToCompare =
                            keyProperties.length > 0
                                ? keyProperties
                                : Object.keys(event);

                        // 從比較列表中移除需要跳過的屬性
                        propertiesToCompare = propertiesToCompare.filter(
                            key => !skipProperties.includes(key)
                        );

                        // 檢查指定的屬性是否完全相同
                        const isIdentical = propertiesToCompare.every(key => {
                            // 如果屬性不存在於事件中，則跳過
                            if (!(key in event) || !(key in previousEvent))
                                return true;

                            return (
                                JSON.stringify(event[key]) ===
                                JSON.stringify(previousEvent[key])
                            );
                        });

                        // 如果完全相同，則過濾掉
                        // 如果有任何不同，則保留並更新已處理的數據
                        if (!isIdentical) {
                            processedInfoData[event.infoId] = { ...event };
                            return true;
                        }

                        return false;
                    }

                    // 第一次遇到這個 infoId，保留並記錄
                    processedInfoData[event.infoId] = { ...event };
                    return true;
                }
            );

            // 更新 point 的 eventList
            const updatedPoint = { ...point };
            updatedPoint.properties = {
                ...point.properties,
                eventList: filteredEventList
            };

            return updatedPoint;
        })
        .filter(point => {
            // 過濾掉 eventList 為空的點
            return (
                point.properties.eventList &&
                point.properties.eventList.length > 0
            );
        });
};
