import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusButton from "../CusButton";

import { url } from "../../../../api/hkvayb";

const CusPdfLink = ({
    value,
    path,
    label = "閱讀全文",
    style = {},
    ...rest
}) => {
    const classes = useStyles(style);

    const handlePdfPath = p => {
        return `${url.hkvayb.EXTERNAL_CONNECTION}/${path}/${p}`;
    };

    const handlePdf = url => {
        window.open(url, "_blank");
    };

    return (
        <div className={classes.hkvayb_div}>
            {value.map((p, index) => {
                const serialNum = index === 0 ? "" : index + 1;
                return (
                    <div key={p} title={handlePdfPath(p)}>
                        <CusButton
                            {...rest}
                            style={style}
                            borderInvert
                            label={`${label} ${serialNum}`}
                            onClick={() => handlePdf(handlePdfPath(p))}
                        />
                    </div>
                );
            })}
        </div>
    );
};

export default CusPdfLink;
