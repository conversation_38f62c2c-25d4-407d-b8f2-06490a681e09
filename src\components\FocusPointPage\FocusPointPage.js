import React, { useEffect, useState } from "react";
import axios from "axios";
import queryString from "query-string";
import { FormattedMessage } from "react-intl";
import { ResponsiveContainer } from "../../layout/Layout";
import Slider from "react-slick";
import { Header } from "semantic-ui-react";

import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./focusPoint.scss";

import Footer from "../../layout/Footer";
import writerImg from "../../images/defaultWriterImg.png";
import { Api } from "../../api/hkbdb/Api";
import { bs64Encode } from "../../common/codes";
import { peopleUrl } from "../../services/common";
import { CLASS_PREFIX } from "../../config/config-ontology";

const removeStartPrefix = (id, prefix) => {
    if (id && id.startsWith(prefix)) {
        return id.replace(prefix, "");
    }
    return id;
};

const FocusPointPage = props => {
    const [nav1, setNav1] = useState();
    const [nav2, setNav2] = useState();
    const [displayNum, setDisplayNum] = useState();
    const [tmpData, setTmpData] = useState();
    const [isLoading, setIsLoading] = useState(false);
    const [list, setList] = useState([]);

    const isMobile = window.innerWidth <= 576;
    const currentMonth = new Date().getMonth() + 1;
    const isEven = number => {
        return number % 2 === 0;
    };
    const NumberToChinese = number => {
        const convertToChinese = num => {
            switch (num) {
                case 1:
                    return "一";
                case 2:
                    return "二";
                case 3:
                    return "三";
                case 4:
                    return "四";
                case 5:
                    return "五";
                case 6:
                    return "六";
                case 7:
                    return "七";
                case 8:
                    return "八";
                case 9:
                    return "九";
                case 10:
                    return "十";
                case 11:
                    return "十一";
                case 12:
                    return "十二";
                default:
                    return "";
            }
        };
        return convertToChinese(number);
    };

    useEffect(() => {
        if (!currentMonth) return;
        const api = Api.getFocusPointData()
            .replace("{size}", bs64Encode("600x600"))
            .replace("{month}", bs64Encode(`${currentMonth}`));
        axios.get(api).then(res => {
            setIsLoading(true);
            setTmpData(
                res?.data?.data.filter(i => i?.personPhotoEnable === "1")
            );
            setIsLoading(false);
        });
    }, [currentMonth]);

    useEffect(() => {
        let tmpDisplayNum = 5;

        if (isMobile) {
            tmpDisplayNum = 3;
        }

        if (list.length < tmpDisplayNum) {
            setDisplayNum(
                isEven(list.length)
                    ? list.length === 2
                        ? list.length
                        : list.length - 1
                    : list.length
            );
        } else {
            setDisplayNum(tmpDisplayNum);
        }
    }, [list]);

    useEffect(() => {
        if (Array.isArray(tmpData)) {
            const tmpList = tmpData.map(b => {
                // 不管是 Person 或 Organization,
                // 第二次 fetch 詳細資料的 response.data 的 key 都是 perId, srcName
                let url = "";
                if ((b.classType || "").toLowerCase() === "person") {
                    url = peopleUrl(
                        removeStartPrefix(b.srcId, CLASS_PREFIX.Person),
                        queryString.stringify({
                            name: bs64Encode(b.bestKnownName || "")
                        })
                    );
                }
                return {
                    ...(b || {}),
                    url: url
                };
            });
            setList(tmpList);
        }
    }, [tmpData]);
    //
    const onItemClick = item => e => {
        e.preventDefault();

        if (item?.url) {
            window.open(item.url, "_blank", "noopener,noreferrer");
        }
    };
    const messages = {
        MESSAGE: {
            id: "menu.focusPoint",
            defaultMessage: isMobile
                ? '"Writers and Artists" {br} in Focus'
                : '"Writers and Artists" in Focus'
        }
    };

    const onItemNameClick = item => e => {
        e.preventDefault();

        const isCentered =
            nav2 && nav2.innerSlider.state.currentSlide === list.indexOf(item);
        if (isCentered) {
            if (item?.url) {
                window.open(item.url, "_blank", "noopener,noreferrer");
            }
        }
    };

    return (
        <ResponsiveContainer
            header={() => (
                <div
                    style={{
                        marginTop: `calc(50vh - ${
                            isMobile ? 150 + 130 : 150 + 180
                        }px)`,
                        textAlign: "center",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center"
                    }}
                >
                    <div className="focuspoint__box">
                        {!isLoading && (
                            <>
                                <Header
                                    as="h2"
                                    textAlign="center"
                                    className={"focuspoint__header"}
                                >
                                    <FormattedMessage
                                        {...messages.MESSAGE}
                                        values={{
                                            p: (...chunks) => <p>{chunks}</p>,
                                            br: <br />
                                        }}
                                    />
                                </Header>
                                <Header.Subheader
                                    textAlign="center"
                                    className={"focuspoint__subheader"}
                                >
                                    <span>
                                        {NumberToChinese(currentMonth)}月出生
                                    </span>
                                </Header.Subheader>
                                <div className="focuspoint__firstSwiper">
                                    <Slider
                                        asNavFor={nav2}
                                        ref={slider1 => setNav1(slider1)}
                                    >
                                        {list &&
                                            list?.map(i => {
                                                return (
                                                    <div
                                                        key={i.srcId}
                                                        style={{
                                                            width: "240px",
                                                            height: "240px"
                                                        }}
                                                    >
                                                        <img
                                                            src={
                                                                i.personPhotoEnable
                                                                    ? i.personPhotoUrl
                                                                        ? i.personPhotoUrl
                                                                        : writerImg
                                                                    : writerImg
                                                            }
                                                            alt="person photo"
                                                            style={{
                                                                display:
                                                                    "block",
                                                                maxWidth:
                                                                    "100%",
                                                                objectFit:
                                                                    "cover",
                                                                width: "240px",
                                                                height: "240px"
                                                            }}
                                                            onClick={onItemClick(
                                                                i
                                                            )}
                                                        />
                                                    </div>
                                                );
                                            })}
                                    </Slider>
                                </div>

                                <div className="focuspoint__secondSwiper">
                                    <Slider
                                        asNavFor={nav1}
                                        ref={slider2 => setNav2(slider2)}
                                        slidesToShow={displayNum}
                                        swipeToSlide={true}
                                        focusOnSelect={true}
                                        centerMode={true}
                                        autoplay={false}
                                        autoplaySpeed={5000}
                                        // centerPadding={"60px"}
                                        className="swiper__inside--div"
                                    >
                                        {list &&
                                            list?.map(i => {
                                                return (
                                                    <div
                                                        key={i.srcId}
                                                        className="focuspoint__secondSwiper__container"
                                                    >
                                                        <h1
                                                            onClick={onItemNameClick(
                                                                i
                                                            )}
                                                        >
                                                            {i.bestKnownName}
                                                        </h1>
                                                    </div>
                                                );
                                            })}
                                    </Slider>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            )}
            withFooter={true}
            footer={Footer}
            {...props}
        ></ResponsiveContainer>
    );
};

export default FocusPointPage;
