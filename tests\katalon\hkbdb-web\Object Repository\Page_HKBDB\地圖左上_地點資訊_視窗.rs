<?xml version="1.0" encoding="UTF-8"?>
<WebElementEntity>
   <description></description>
   <name>地圖左上_地點資訊_視窗</name>
   <tag></tag>
   <elementGuidId>aeebb98a-eaf5-4fa3-8829-e8c6f43e9892</elementGuidId>
   <imagePath></imagePath>
   <selectorCollection>
      <entry>
         <key>XPATH</key>
         <value>//div[@id='root']/div/div/div[2]/div[2]/div/div[4]/div[2]/div</value>
      </entry>
      <entry>
         <key>IMAGE</key>
         <value></value>
      </entry>
      <entry>
         <key>CSS</key>
         <value>div.MuiBox-root.css-1hi4lfo</value>
      </entry>
   </selectorCollection>
   <selectorMethod>CSS</selectorMethod>
   <smartLocatorCollection>
      <entry>
         <key>SMART_LOCATOR</key>
         <value>div >> internal:has-text=/^目前沒有資訊可以顯示請搜尋人物並選取地圖中的地點$/ >> nth=1</value>
      </entry>
   </smartLocatorCollection>
   <smartLocatorEnabled>false</smartLocatorEnabled>
   <useRalativeImagePath>true</useRalativeImagePath>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>tag</name>
      <type>Main</type>
      <value>div</value>
      <webElementGuid>f3f19f0c-9b69-4918-8107-e24ef232b3b4</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>class</name>
      <type>Main</type>
      <value>MuiBox-root css-1hi4lfo</value>
      <webElementGuid>1c1de6c8-203b-453d-9c31-5c0d3254e433</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>text</name>
      <type>Main</type>
      <value>目前沒有資訊可以顯示請搜尋人物並選取地圖中的地點</value>
      <webElementGuid>cd43b33d-7e09-4802-9dcf-71ff741d4d6f</webElementGuid>
   </webElementProperties>
   <webElementProperties>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath</name>
      <type>Main</type>
      <value>id(&quot;root&quot;)/div[1]/div[1]/div[@class=&quot;gis-map-container MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-0&quot;]/div[@class=&quot;MuiBox-root css-8atqhb&quot;]/div[@class=&quot;MuiBox-root css-i5qpif&quot;]/div[@class=&quot;MuiBox-root css-196jqjz&quot;]/div[@class=&quot;MuiBox-root css-1hi4lfo&quot;]</value>
      <webElementGuid>91807b63-0837-4b77-9aab-069b251173db</webElementGuid>
   </webElementProperties>
   <webElementXpaths>
      <isSelected>true</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:idRelative</name>
      <type>Main</type>
      <value>//div[@id='root']/div/div/div[2]/div[2]/div/div[4]/div[2]/div</value>
      <webElementGuid>779c1546-**************-be975d6258e5</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='OpenStreetMap'])[1]/following::div[2]</value>
      <webElementGuid>fa090f0f-3ee4-41db-aa6b-a69e46986ccc</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:neighbor</name>
      <type>Main</type>
      <value>(.//*[normalize-space(text()) and normalize-space(.)='Leaflet'])[1]/following::div[2]</value>
      <webElementGuid>5e75ade5-d9af-497d-9e87-d00db7d9be10</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:position</name>
      <type>Main</type>
      <value>//div[4]/div[2]/div</value>
      <webElementGuid>fd5417ce-7c52-43bc-bbaa-9d5fb5c5cedf</webElementGuid>
   </webElementXpaths>
   <webElementXpaths>
      <isSelected>false</isSelected>
      <matchCondition>equals</matchCondition>
      <name>xpath:customAttributes</name>
      <type>Main</type>
      <value>//div[(text() = '目前沒有資訊可以顯示請搜尋人物並選取地圖中的地點' or . = '目前沒有資訊可以顯示請搜尋人物並選取地圖中的地點')]</value>
      <webElementGuid>2ef262b4-fac3-47af-9dc3-060af3fd7b40</webElementGuid>
   </webElementXpaths>
</WebElementEntity>
