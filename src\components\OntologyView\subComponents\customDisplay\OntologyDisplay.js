import React from "react";
import { But<PERSON>, Divider } from "semantic-ui-react";
import * as _ from "lodash";
import { exportPdfHelper } from "../../../../common/codes";
import { FormattedMessage } from "react-intl";
import OntoTable from "../OntoTable";

const OntologyDisplay = ({ ontoTableRef }) => {
    return (
        <React.Fragment>
            <div
                style={{
                    display: "flex",
                    justifyContent: "flex-end"
                }}
            >
                <Button
                    onClick={() => {
                        if (!_.isElement(ontoTableRef.current)) return;
                        exportPdfHelper(ontoTableRef.current, "HKBDB ontology");
                    }}
                    color={"blue"}
                >
                    <FormattedMessage
                        id={"ontology.exportPdf"}
                        defaultMessage={"  Export ontology pdf"}
                    />
                </Button>
            </div>
            <Divider hidden />
            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    maxWidth: "555px"
                }}
            >
                <OntoTable myRef={ontoTableRef} ontoTableData={[]} />
            </div>
        </React.Fragment>
    );
};

export default OntologyDisplay;
