import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

WebUI.openBrowser('')

WebUI.navigateToUrl('https://hkbdb2.daoyidh.com/zh-hans/map')

WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_按鈕'))

WebUI.waitForElementNotVisible(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_標題'), 10)

WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_按鈕'))

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_標題'), 10)

WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖左上_地點資訊_按鈕'))

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖左上_地點資訊_標題 (無資訊)'), 10)

WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖左上_地點資訊_按鈕'))

WebUI.waitForElementNotVisible(findTestObject('Object Repository/Page_HKBDB/開始時間_input'), 10)

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/結束時間_input'), 10)

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/速度_input'), 10)

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/開始時間_拖曳點_文字'), 10)

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/結束時間_拖曳點_文字'), 10)

WebUI.closeBrowser()