import React, { useContext, useEffect, useRef, useState } from "react";
// mui
import Box from "@mui/material/Box";

// components
import GeneralMap from "./GeneralMap/GenaralMap";
import MapTimeLine from "./commons/timeline/MapTimeLine.jsx";
import MapSidebar from "./commons/MapSidebar";
import { FormattedMessage } from "react-intl";

// utils
import { fetchMapSearchInfomation } from "../fetchData";
import {
    generatePointsForCluster,
    concatDate,
    generateYears,
    generateLines,
    generateGroupData,
    combinePointsByCoordinates,
    filterDuplicatePoints
} from "./commons/MapHelper";
import Typography from "@mui/material/Typography";
import FilterSidebar from "./GeneralMap/subComponents/FilterSidebar";
import { StoreContext } from "../../../store/StoreProvider";
import { isObjEqual } from "../../../services/common.js";

const GeneralMapIndex = () => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const { mapFilterOptions } = state.map;
    const [rawData, setRawData] = useState([]);
    /**
     * @type {[IPersonOption[], React.Dispatch<React.SetStateAction<IPersonOption[]>>]}
     */
    const [years, setYears] = useState([]);
    const [points, setPoints] = useState([]);
    const [lines, setLines] = useState([]);
    const [groupPallet, setGroupPallet] = useState([]); // 過濾的顏色對照表(用於建立 map line color)
    const [allGroupPallet, setAllGroupPallet] = useState([]); // 所有的顏色對照表(用於建立 map legend color)
    const [pointSelected, setPointSelected] = useState(null); // 目前選取的點
    const [refreshDataTs, setRefreshDataTs] = useState(Date.now()); // 更新資料時間
    const [personGroupData, setPersonGroupData] = useState([]); // 人物群組資料
    const [isClickMapPoint, setIsClickMapPoint] = useState(false);
    //
    const [anchorYear, setAnchorYear] = useState(null); // 目前年份錨點
    const [startYear, setStartYear] = useState(null); // 起始年份
    const [endYear, setEndYear] = useState(null); // 結束年份
    const [isPlaying, setIsPlaying] = useState(false); // 是否正在播放
    //
    // create ref for map
    const mapRef = useRef(null);
    // create ref for timeline
    const timelineRef = useRef(null);
    /**
     * 地圖上的點不需要依據 startYear, endYear, anchorYear 來過濾
     */
    useEffect(() => {
        const safeNumber = (num, replaceNum = 0) => {
            if (num == null || num === "") {
                return replaceNum;
            }
            return Number(String(num));
        };

        fetchMapSearchInfomation(locale).then(res => {
            const { data } = res;
            const mapData = data.map(d => ({
                ...d,
                year: d.year,
                date: concatDate(d.year, d.month, d.day),
                endDate: concatDate(d.endYear, d.endMonth, d.endDay)
            }));
            // sort by year, month, day
            const newMapData = mapData
                .sort((a, b) => {
                    if (safeNumber(a.year) === safeNumber(b.year)) {
                        if (safeNumber(a.month) === safeNumber(b.month)) {
                            return safeNumber(a.day) - safeNumber(b.day);
                        }
                        return safeNumber(a.month) - safeNumber(b.month);
                    }
                    return a.year - b.year;
                })
                .filter(event => mapFilterOptions[event?.infoType]["status"]);

            setRawData(newMapData);
            setStartYear(null);
            setEndYear(null);
            setRefreshDataTs(Date.now());
            // setAnchorYear(null);
            if (newMapData.length > 0) {
                setAnchorYear(newMapData[0].year);
            }

            //
            const tmpYears = generateYears(newMapData);
            setYears(tmpYears);

            //
            const {
                lines: tmpLines,
                groupPallet: tmpGroupPallet
            } = generateLines({
                srcData: newMapData,
                locationNameKey: "place"
            });

            setAllGroupPallet(tmpGroupPallet);

            const tmpPersonGroupData = generateGroupData({
                srcData: newMapData,
                groupKey: "perId"
            });
            setPersonGroupData(tmpPersonGroupData);
        });
    }, [mapFilterOptions, locale]);

    useEffect(() => {
        if (Array.isArray(rawData)) {
            const rawDataFiltered = rawData.filter(d => {
                let present = true;
                // 教育事件如無開始年份，則使用結束年份
                if (d.infoType === "EducationEvent") {
                    const targetYear = d.year ?? d.endYear; // 優先使用 d.year，若為 null 則使用 d.endYear
                    present =
                        present &&
                        targetYear >= startYear &&
                        targetYear <= anchorYear;
                } else {
                    if (startYear) present = present && d.year >= startYear;
                    if (endYear) present = present && d.year <= endYear;
                    if (anchorYear) present = present && d.year <= anchorYear;
                }

                return present;
            });

            const points = generatePointsForCluster({
                srcData: rawDataFiltered,
                latKey: "lat",
                longKey: "long",
                locationIdKey: "placeId",
                locationNameKey: "place",
                propertiesKey: [
                    "perId",
                    "person",
                    "date",
                    "year",
                    "month",
                    "day",
                    "dateKey",
                    "placeId",
                    "placeKey",
                    "place",
                    "infoId",
                    "infoType",
                    "infoDescKey",
                    "infoDesc",
                    "addInfoDescKey",
                    "addInfoDesc",
                    "endYear",
                    "endMonth",
                    "endDay",
                    "endDateKey",
                    "endDate"
                ]
            });

            // 先過濾相同event並且只有place、placeId不一樣的，只保留先出現的那一筆，再合併相同座標的點
            setPoints(
                combinePointsByCoordinates(filterDuplicatePoints(points))
            );
            // setPoints(combinePointsByCoordinates(points));
            // setPoints(points);

            const personGroupData = generateGroupData({
                srcData: rawDataFiltered,
                groupKey: "perId"
            });

            //
            const {
                lines: tmpLines,
                groupPallet: tmpGroupPallet
            } = generateLines({
                srcData: rawDataFiltered,
                locationNameKey: "place"
            });

            setLines(tmpLines);
            setGroupPallet(tmpGroupPallet);
        } else {
            const points = [];
            setPoints(points);
        }
    }, [rawData, startYear, endYear, anchorYear]);

    return (
        <Box sx={{ width: "100%" }}>
            <Box sx={{ width: 0 }}>
                <FilterSidebar mapRef={mapRef} groupPallet={allGroupPallet} />
            </Box>
            <Box
                sx={{
                    width: "100%",
                    minHeight: "400px",
                    height: "400px",
                    display: "flex",
                    alignItems: "center"
                }}
            >
                <Box
                    ref={mapRef}
                    sx={{
                        width: "100%",
                        minHeight: "400px",
                        height: "400px"
                    }}
                >
                    <GeneralMap
                        points={points}
                        onPointChange={point => {
                            setPointSelected(point);
                        }}
                        refreshDataTs={refreshDataTs}
                        onClickMapPointChange={() => {
                            setIsClickMapPoint(true);
                        }}
                    />
                </Box>
                <Box sx={{ width: 0 }}>
                    <MapSidebar
                        mapRef={mapRef}
                        timelineRef={timelineRef}
                        pointSelected={pointSelected}
                        isClickMapPoint={isClickMapPoint}
                        onClickMapPointChange={() => {
                            setIsClickMapPoint(false);
                        }}
                        variant="general"
                    />
                </Box>
            </Box>
            <Box>
                <Typography
                    sx={{
                        color: "#7C7C7C",
                        fontSize: "12px",
                        textAlign: "end"
                    }}
                >
                    <FormattedMessage
                        id="map.note"
                        defaultMessage="*此地圖採用新制地圖，古地名可能無法顯示。標示位置僅供參考，可能與歷史實際位置有所差異。"
                    />
                </Typography>
            </Box>
            <Box
                sx={{
                    width: "100%",
                    height: "120px",
                    marginTop: "16px"
                }}
                ref={timelineRef}
            >
                <MapTimeLine
                    dataYears={years}
                    onStartYearChange={year => {
                        setStartYear(year);
                    }}
                    onAnchorYearChange={year => {
                        setAnchorYear(year);
                    }}
                    onEndYearChange={year => {
                        setEndYear(year);
                    }}
                    onPlayStatusChange={status => {
                        setIsPlaying(status);
                    }}
                    onRestart={() => {
                        setRefreshDataTs(Date.now());
                    }}
                    variant="general"
                />
            </Box>
        </Box>
    );
};

export default GeneralMapIndex;
