import Act from "../actions";

const initState = {
    changedRole: {},
    willRemoveUser: "",
    renderSignal: "",
    // message
    message: { title: "", type: "", success: 0, error: 0, renderSignal: "" },
    form: {
        userAgree: true // 使用者是否同意(取消checkbox,所以default為true)
    }
};

const accountReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.FIREBASE_USER_ROLE_CHANGED:
            return { ...state, changedRole: action.payload };
        case Act.FIREBASE_USER_ROLE_CHANGED_CLEAN:
            return { ...state, changedRole: {} };
        case Act.FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL:
            return { ...state, renderSignal: action.payload };
        case Act.FIREBASE_USER_REMOVE:
            return { ...state, willRemoveUser: action.payload };
        // show result when updated or created or deleted or read
        case Act.DATA_MESSAGE:
            return {
                ...state,
                message: Object.assign(state.message, action.payload)
            };
        // clean message
        case Act.DATA_MESSAGE_CLEAN:
            return {
                ...state,
                message: {
                    title: "",
                    type: "",
                    success: 0,
                    error: 0,
                    renderSignal: state.message.renderSignal
                }
            };
        case Act.ACCOUNT_SIGNUP_FORM_USER_AGREE:
            return {
                ...state,
                form: { ...state.form, userAgree: action.payload }
            };
        case Act.ACCOUNT_SIGNUP_FORM_CLEAR:
            return {
                ...state,
                form: initState.form
            };
        default:
            return state;
    }
};

export default accountReducer;
