import React, { Fragment, useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomMenu from "./CustomFormFlexEdit";
import CustomAlertMessage from "./CustomAlertMessage";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import { isNotEmpty, safeGet } from "../../../../common/codes";

// api
import {
    Api,
    createHkbdbData,
    readHkbdbData,
    updateHkbdbData
} from "../../../../api/hkbdb/Api";
import { bs64DecodeId } from "../../../../common/codes/jenaHelper";
import { EVT_PREFIX } from "../../../../config/config-ontology";
import base64url from "base64url";
import axios from "axios";
import { isEmpty } from "lodash";
import { HAS_COORDS_TYPE } from "../constants";

const CustomEditModalFlex = ({
    open,
    setOpen,
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation, information, user } = state;
    const { paginations } = information;
    const { localActivePage } = safeGet(paginations, [ontologyType], {});
    const infoReloadFunc = safeGet(
        personInformation,
        ["infoReloadFunc", ontologyType],
        () => {}
    );
    const infoSearchFunc = safeGet(
        personInformation,
        ["infoSearchFunc", ontologyType],
        () => {}
    );
    //
    const [isLoading, setIsLoading] = useState(() => false);
    //
    const [updateResults, setUpdateResults] = useState(() => ({
        updatedRowIds: [],
        success: 0,
        failed: 0
    }));
    //
    const [createResults, setCreateResults] = useState(() => ({
        createdRowIds: [],
        success: 0,
        failed: 0
    }));
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));
    // 把 multiple graph 拆開成單一個，以便 edit 及 delete
    const sgRawData = [];
    if (editData && editData.rowData) {
        let rowId = 0;
        editData.rowData.forEach(r => {
            r.graph.forEach(g => {
                sgRawData.push({ ...r, graph: [g], rowId });
                rowId += 1;
            });
        });
        editData.rowData = sgRawData;
    }
    //
    const handleInitChangedAndCreatedData = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            changedData: {},
            createdData: {},
            selectedProperties: []
        }));
    };
    const handleInitIsUpdated = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            isUpdated: false,
            isCreated: false,
            updatedRowIds: [],
            createdRowIds: []
        }));
    };
    const handleReLoadResult = () => {
        if (updateResults.success || createResults.success) {
            // 通知 API 重新去讀取，由於資料更動兩個都要重新讀取資料
            // console.log(localActivePage);
            infoReloadFunc(localActivePage);
            infoSearchFunc();
        }
    };
    //
    const handleClose = () => {
        // init status
        handleInitChangedAndCreatedData();
        handleInitIsUpdated();
        handleReLoadResult();
        dispatch({
            type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
            payload: {
                target: null,
                signal: `update-${new Date().getTime()}`
            }
        });
        // close modal
        setOpen(false);
    };
    const handleOpen = () => {
        // open modal
        setOpen(true);
    };

    const reformatData = async data => {
        const { srcId, propertyBindRangeStr, values, graph } = data;
        const [propName, propRange] = propertyBindRangeStr.split("__");

        let newValues = [...values.map(obj => safeGet(obj, ["value"], ""))];
        //
        // 查詢所有內容的 ID
        if (
            // label property pass
            !`${propertyBindRangeStr}`.startsWith("label_") &&
            // date property pass
            propRange !== "DateEvent"
        ) {
            //
            const promises = newValues.map(value => {
                const apiStr = Api.findIdByValue()
                    .replace("{class}", propRange)
                    .replace("{keyword}", value);
                return readHkbdbData(apiStr);
            });
            //
            const results = await Promise.allSettled(promises).then(
                results => results
            );
            //
            results.forEach((item, idx) => {
                const { status, value } = item;
                if (status === "fulfilled") {
                    const objId = safeGet(value, ["data", 0, "value"], "");
                    if (isNotEmpty(objId)) {
                        newValues[idx] = objId;
                    }
                }
            });
        }

        // Vincent, 如果值有帶語系 @zh/@en，斷行符號 \n 可以斷開兩個值。
        // FIXME: 目前假設語系皆為兩個字元
        if (
            newValues.length === 1 &&
            newValues[0].indexOf("\n") > -1 &&
            newValues[0].charAt(newValues[0].length - 3) === "@"
        ) {
            newValues = newValues[0].split("\n");
        }
        // API 只接受 graph 為 string
        const graphStr = Array.isArray(graph) ? graph[0] : graph;

        //
        let entry = {
            graph: graphStr,
            srcId,
            classType: ontologyType,
            value: { [propertyBindRangeStr]: newValues }
        };
        //

        if (ontologyType === "member") {
            const getEvent = bs64DecodeId(srcId).split("__")[0];
            if (getEvent) {
                const typeObj = EVT_PREFIX.find(
                    item => item.prefix === getEvent
                );
                const type = typeObj?.eventType;

                entry = {
                    ...entry,
                    classType: type
                };
            }

            entry = {
                ...entry,
                dstId: srcId
            };
        }
        //
        return entry;
    };

    const createLocationLabel = async (data, locType) => {
        if (isEmpty(data) || !locType)
            return { state: false, error: "缺少資料" };

        try {
            const { graph, values } = data;
            const graphStr = Array.isArray(graph) ? graph[0] : graph;
            const encodedGraph = base64url.encode(graphStr);

            const graphLocations = await axios.get(
                locType === "Place"
                    ? Api.getGraphPlace(encodedGraph)
                    : Api.getGraphOrganization(encodedGraph)
            );

            const locationList = graphLocations.data.data;
            const locationMap = new Map(
                locationList.map(item => [item.id, item.label])
            );

            const createResults = [];

            for (const { label, value } of values) {
                const hasId = locationMap.has(value);
                const labelValue = locationMap.get(value);
                const needToCreate =
                    !hasId || !labelValue || !labelValue.trim();

                if (needToCreate) {
                    const location = value.replace(/^(ORG|PLA)/, "");
                    const srcId = `${
                        locType === "Place" ? "PLA" : "ORG"
                    }${base64url.encode(location)}`;

                    const createEntry = {
                        graph,
                        srcId,
                        classType: locType,
                        value: {
                            [locType === "Place"
                                ? "label_Place"
                                : "bestKnownName"]: [label]
                        }
                    };

                    const res = await createHkbdbData(
                        Api.restfulHKBDB(),
                        createEntry
                    );

                    createResults.push(res?.state === true);
                }
            }

            return { state: createResults.every(Boolean) };
        } catch (e) {
            console.log("createLocationLabel error:: ", e);
            return { state: false, error: e.message };
        }
    };

    const handleUpdate = async () => {
        setIsLoading(true);

        if (isNotEmpty(editData.changedData)) {
            //
            const editDataRowIds = Object.keys(
                editData.changedData
            ).map(rowIdx => parseInt(rowIdx, 10));
            //
            const tasks = [];

            for (const rowId of editDataRowIds) {
                // origin Data
                const entrySrc = await reformatData(
                    editData.rowData.find(item => item.rowId === rowId)
                );
                // new Data
                const entryDst = await reformatData(
                    editData.changedData[rowId]
                );

                // Place 或 Organization 有新增，另外建label
                const [, propRange] = editData.changedData[
                    rowId
                ].propertyBindRangeStr.split("__");

                if (HAS_COORDS_TYPE.includes(propRange)) {
                    tasks.push(
                        createLocationLabel(
                            editData.changedData[rowId],
                            propRange
                        )
                    );
                }

                tasks.push(
                    updateHkbdbData(Api.restfulHKBDB(), entrySrc, entryDst)
                );
            }

            const results = await Promise.allSettled(tasks);

            results.forEach((res, idx) => {
                //
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的
                    updateResults.updatedRowIds.push(editDataRowIds[idx]);
                    // 紀錄成功資料筆數
                    updateResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    updateResults.failed++;
                }
            });
        }
        if (isNotEmpty(editData.createdData)) {
            const editDataRowIds = Object.keys(
                editData.createdData
            ).map(rowIdx => parseInt(rowIdx, 10));
            const promises = editDataRowIds.map(async rowId => {
                //
                const entryDst = await reformatData(
                    editData.createdData[rowId]
                );
                // 由於不存在原始資料，所以只好從 entryDst 複製資料過來，但是不包含 value 的內容
                const entrySrc = { ...entryDst, value: {} };
                return updateHkbdbData(Api.restfulHKBDB(), entrySrc, entryDst);
            });
            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);
            //
            results.forEach((res, idx) => {
                //
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的
                    createResults.createdRowIds.push(editDataRowIds[idx]);
                    // 紀錄成功資料筆數
                    createResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    createResults.failed++;
                }
            });
        }
        //
        const successTotal = updateResults.success + createResults.success;
        const failedTotal = updateResults.failed + createResults.failed;
        //
        setAlertMsg(prevMsg => ({
            ...prevMsg,
            title: "更新",
            type:
                updateResults.failed || createResults.failed
                    ? "error"
                    : "success",
            content: `已更新: ${successTotal}, 已失敗: ${failedTotal}`,
            renderSignal: `update-${new Date().getTime()}` // 時間只是方便用來觸發更新而已
        }));
        // 用來在 ui 顯示那些資料更新成功
        setEditData(prevEditData => ({
            ...prevEditData,
            isUpdated: true,
            isCreated: true,
            updatedRowIds: [...updateResults.updatedRowIds],
            createdRowIds: [...createResults.createdRowIds]
        }));
        //
        setIsLoading(false);
    };
    const handleCancel = () => {
        // init status
        handleInitChangedAndCreatedData();
        // open modal
        setOpen(false);
    };
    //
    const SwitchButton = () => {
        if (editData.isUpdated || editData.isCreated) {
            return (
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            return (
                <Fragment>
                    <Button
                        loading={isLoading}
                        disabled={editData.isUpdated || editData.isCreated}
                        onClick={handleUpdate}
                        color="green"
                    >
                        <FormattedMessage
                            id={"people.Information.button.update"}
                            defaultMessage={"Update"}
                        />
                    </Button>
                    <Button onClick={handleCancel} color="red">
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };
    //
    const modalContentStyle = {
        width: "100%"
    };
    //
    return (
        <Modal open={open} onClose={handleClose} onOpen={handleOpen}>
            {/* <pre>{JSON.stringify(state.information, null, 2)}</pre> */}
            {/* <pre>{JSON.stringify(editData.changedData, null, 2)}</pre> */}
            {/* <pre>{JSON.stringify(editData.createdData, null, 2)}</pre> */}
            <Modal.Header>
                <FormattedMessage
                    id={"people.Information.header.edit.content"}
                    defaultMessage={"Edit Content"}
                />
            </Modal.Header>
            <Modal.Content image>
                <Modal.Description style={modalContentStyle}>
                    {/* alert */}
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    {/* content */}
                    <CustomMenu
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <SwitchButton />
            </Modal.Actions>
        </Modal>
    );
};

export default CustomEditModalFlex;
