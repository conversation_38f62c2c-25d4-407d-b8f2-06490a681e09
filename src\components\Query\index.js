import React, { useState } from "react";

// ui
import { Container, Grid, Segment, Divider } from "semantic-ui-react";

// top header bar
import { ResponsiveContainer } from "../../layout/Layout";

// custom
import CustomQueryEditor from "./subComponents/CustomQueryEditor";
import CustomQueryResult from "./subComponents/CustomQueryResult";
import CustomStoredQuery from "./subComponents/CustomStoredQuery";
import CustonEventButtons from "./subComponents/CustomEventButtons";
import CustomAlertMessage from "./subComponents/CustomAlertMessage";
import { FormattedMessage } from "react-intl";

const index = props => {
    const [textareaValue, setTextareaValue] = useState("");
    const customDividerStyle = {
        margin: ".5rem 0"
    };
    return (
        <ResponsiveContainer {...props}>
            <Container textAlign="justified">
                <Segment basic compact>
                    <h2>
                        <FormattedMessage
                            id="query.query"
                            defaultMessage="Query"
                        />
                    </h2>
                    <FormattedMessage
                        id="query.description"
                        defaultMessage="Advanced Search allows SPARQL Query for searching. Users can write their own SPARQL queries, choose the Public Query from the right menu as default use or customized for data retrieval.
Paste the SPARAL query to the big box below and click “Send” to search. Result entries will be shown below.
Registered users can store personal SPARAL queries to “My Query” on the right and download the result dataset."
                    />
                </Segment>

                <Divider />

                <Grid celled="internally" stackable>
                    <Grid.Column width={10}>
                        {/* Message */}
                        <CustomAlertMessage />
                        {/* QueryEditor */}
                        <CustomQueryEditor
                            setTextareaValue={setTextareaValue}
                            textareaValue={textareaValue}
                        />
                        <Divider hidden style={customDividerStyle} />
                        {/* EventButtons */}
                        <CustonEventButtons />
                        <Divider hidden style={customDividerStyle} />
                        {/* QueryResult */}
                        <CustomQueryResult />
                    </Grid.Column>
                    <Grid.Column width={6}>
                        {/* QueryStored */}
                        <CustomStoredQuery
                            setTextareaValue={setTextareaValue}
                        />
                    </Grid.Column>
                </Grid>
                <br />
            </Container>
        </ResponsiveContainer>
    );
};

export default index;
