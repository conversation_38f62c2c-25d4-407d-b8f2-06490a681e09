import React, { useContext, useEffect, useRef, useState } from "react";
import anime from "animejs";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";

import { StoreContext } from "../../../store/StoreProvider";
import useWindowSize from "../../../hook/useWindowSize";
import { isEmpty } from "../../../common/codes";

import focusPointImage from "../../../images/homepage_background_carousel/focuspoint.png";
import hotSearchImage from "../../../images/homepage_background_carousel/hotSearch.png";

const HomePageBgCard = imgArr => {
    const topLeftRef = useRef(null);
    const topRightRef = useRef(null);
    const leftBottomRef = useRef(null);
    const rightBottomRef = useRef(null);
    const [state, dispatch] = useContext(StoreContext);
    const [tmpMsImg, setTmpMsImg] = useState();
    const [tmpBcImg, setTmpBcImg] = useState();
    const [isLoading, setIsLoading] = useState(true);

    const { user } = state;
    const { locale } = user;
    const size = useWindowSize();
    const screenWidth = window.innerWidth;

    const [divSize, setDivSize] = useState({ width: 254, height: 254 });
    const [localLocale, setLocalLocale] = useState(locale);

    useEffect(() => {
        setLocalLocale(locale);
    }, [locale]);

    useEffect(() => {
        if (isEmpty(imgArr?.imgArr[0]) || isEmpty(imgArr?.imgArr[0])) return;
        setTmpMsImg(imgArr?.imgArr[0]);
        setTmpBcImg(imgArr?.imgArr[1]);
        setIsLoading(false);
    }, [imgArr]);

    useEffect(() => {
        const initializeAnimation = (ref, direction, offsetX, duration) => {
            const box = ref.current;
            if (!box) return;
            const boxWidth = box.offsetWidth;
            if (direction === "leftToRight") {
                // 起始位置設置在視窗外面 `-${boxWidth + offsetX}px`
                box.style.left = `${offsetX}px`;

                anime({
                    targets: box,
                    translateX: [
                        0,
                        offsetX === 700
                            ? screenWidth - boxWidth - offsetX
                            : screenWidth -
                              boxWidth -
                              (screenWidth - 3 * divSize.width)
                    ],
                    duration: duration,
                    loop: true,
                    easing: "linear",
                    // direction: "linear",
                    // rotateY: [30, -30],
                    // rotateZ: [15, -15],
                    // rotateX: [7, -7],
                    direction: "alternate"
                });
            } else {
                // 起始位置設置在視窗外面 `-${boxWidth + offsetX}px`
                box.style.right = `${offsetX}px`;

                anime({
                    targets: box,
                    translateX: [
                        0,
                        offsetX === 700
                            ? -screenWidth + boxWidth + offsetX
                            : -screenWidth +
                              boxWidth +
                              screenWidth -
                              3 * divSize.width
                    ],
                    duration: duration,
                    loop: true,
                    easing: "linear",
                    // direction: "linear",
                    direction: "alternate",
                    // rotateY: [-30, 30],
                    // rotateX: [-7, 7],
                    translate3D: true
                });
            }
        };

        setTimeout(
            () => initializeAnimation(topLeftRef, "leftToRight", 0, 50000),
            100
        );
        setTimeout(
            () =>
                initializeAnimation(
                    topRightRef,
                    "leftToRight",
                    screenWidth - 3 * divSize.width,
                    50000
                ),
            100
        );
        setTimeout(
            () => initializeAnimation(leftBottomRef, "rightToLeft", 0, 50000),
            100
        );
        setTimeout(
            () =>
                initializeAnimation(
                    rightBottomRef,
                    "rightToLeft",
                    screenWidth - 3 * divSize.width,
                    50000
                ),
            100
        );
    }, [imgArr]);

    useEffect(() => {
        const handleResize = () => {
            const windowHeight = window.innerHeight;
            // const newWidth = Math.min(254, windowHeight / 4); // 寬度小於254
            // const newHeight = newWidth;

            const newWidth = (1080 - windowHeight) / 8;
            const newSize = 254 - newWidth;
            setDivSize({ width: newSize, height: newSize });
        };

        window.addEventListener("resize", handleResize);

        handleResize();

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    const CardTemplateProducer = (
        tmpRef,
        direction,
        tmpImg,
        isLinkTo,
        tmpLink
    ) => {
        const {
            leftDirection,
            rightDirection,
            topDirection,
            bottomDirection
        } = direction;
        // 判斷是否有連結
        const cardTemplate = isLinkTo ? (
            <div
                ref={tmpRef}
                style={{
                    width: divSize.width,
                    height: divSize.height,
                    position: "absolute",
                    bottom: bottomDirection || null,
                    right: rightDirection || null,
                    left: leftDirection || null,
                    top: topDirection || null,
                    overflow: "invisible",
                    transformStyle: "preserve-3d",
                    boxShadow: "rgba(149, 157, 165, 0.2) 0px 8px 24px"
                }}
                className="homepage__background__box"
            >
                <Link
                    to={{ pathname: tmpLink }}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <img src={tmpImg} alt="small card background" />
                </Link>
            </div>
        ) : (
            <div
                ref={tmpRef}
                style={{
                    width: divSize.width,
                    height: divSize.height,
                    position: "absolute",
                    bottom: bottomDirection || null,
                    right: rightDirection || null,
                    left: leftDirection || null,
                    top: topDirection || null,
                    overflow: "invisible",
                    transformStyle: "preserve-3d",
                    boxShadow: "rgba(149, 157, 165, 0.2) 0px 8px 24px"
                }}
                className="homepage__background__normalbox"
            >
                <img src={tmpImg} alt="aaa" />
            </div>
        );

        return cardTemplate;
    };

    const calcStartYAxisPos = (tmpPos, select) => {
        const windowHeight = size[1];
        // 電腦版
        if (select) {
            const YPos = tmpPos + (1080 + windowHeight) / 20;
            return YPos;
        } else {
            // 手機版
            const YPos = tmpPos + (1080 + windowHeight) / 8;
            return YPos;
        }
    };

    return (
        <>
            {!isLoading && (
                <>
                    {CardTemplateProducer(
                        topLeftRef,
                        {
                            topDirection:
                                size[1] < 705
                                    ? calcStartYAxisPos(-75, false)
                                    : calcStartYAxisPos(60, true),
                            leftDirection: 0
                        },
                        hotSearchImage,
                        true,
                        `/${localLocale}/hotSearch`
                    )}
                    {CardTemplateProducer(
                        topRightRef,
                        {
                            topDirection:
                                size[1] < 705
                                    ? calcStartYAxisPos(-75, false)
                                    : calcStartYAxisPos(60, true),
                            rightDirection: 0
                        },
                        tmpMsImg,
                        true,
                        `/${localLocale}/collection/manuScript`
                    )}
                    {CardTemplateProducer(
                        leftBottomRef,
                        {
                            leftDirection: 0,
                            bottomDirection:
                                size[1] < 705
                                    ? calcStartYAxisPos(-500, false)
                                    : calcStartYAxisPos(-620, true)
                        },
                        tmpBcImg,
                        true,
                        `/${localLocale}/collection/featuredPub`
                    )}
                    {CardTemplateProducer(
                        rightBottomRef,
                        {
                            rightDirection: 0,
                            bottomDirection:
                                size[1] < 705
                                    ? calcStartYAxisPos(-500, false)
                                    : calcStartYAxisPos(-620, true)
                        },
                        focusPointImage,
                        true,
                        `/${localLocale}/focusPoint`
                    )}
                </>
            )}
        </>
    );
};

HomePageBgCard.propTypes = {
    imgArr: PropTypes.array
};

export default HomePageBgCard;
