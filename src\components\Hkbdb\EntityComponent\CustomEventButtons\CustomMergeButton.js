import React, { useContext, useEffect, useState } from "react";
import ReactSelect, { createFilter } from "react-select";

// ui
import { Button, Modal, Label, Input, Popup, Select } from "semantic-ui-react";

// lang
import { FormattedMessage, injectIntl } from "react-intl";

// api
import { Api, mergeHkbdbData } from "../../../../api/hkbdb/Api";
import { fetchOptionList } from "../commonAction";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import MenuList from "../TableComp/MenuList";
import {
    bs64Encode,
    handleNameSpellCheck,
    isEmpty,
    isNotEmpty,
    isTrue
} from "../../../../common/codes";

// custom
import CustomAlertMessage from "../TableComp/CustomAlertMessage";
import ModalYesNo from "../ModalYesNo";
import CustomDebounce from "../../../../common/components/CustomDeBounce";

// auth
import authority from "../../../../App-authority";

// helper
import { displayInstanceName, removeStartPrefix } from "../helper";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import queryString from "query-string";
import { options, rootClasses } from "../../../BrowsePage/browseConfig";
import config from "../../../../config/config";
import {
    bs64DecodeId,
    bs64EncodeId,
    decodeURIComponentSafe,
    uriEncIdToNonEncId
} from "../../../../common/codes/jenaHelper";

const CUSTOM_MERGE = "Merge";
const initialState = {
    isLoading: false,
    options: [],
    value: []
};

const CustomMergeButton = ({ type, id, name, intl }) => {
    const [state] = useContext(StoreContext);
    const { information } = state;
    const { personId } = information;
    const { uid, role } = state.user;
    //
    const [searchValue, setSearchValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSearchValue = CustomDebounce(searchValue, 500);
    //
    const [className, setClassName] = useState(type);
    const [typeOption, setTypeOption] = useState(options);
    const [open, setOpen] = useState(false);
    const [openYesNo, setOpenYesNo] = useState(false);
    // Yes: true, No: false
    const [valYesNo, setValYesNo] = useState(false);
    //
    const [mergeState, setMergeState] = useState(initialState);
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 2 * 1000
    }));
    //
    useEffect(() => {
        // 未選擇人物
        if (isEmpty(mergeState.value)) {
            return;
        }
        // 選擇否
        if (valYesNo === false) {
            return;
        }

        const handleMerge = async () => {
            setMergeState(prevData => ({
                ...prevData,
                isLoading: true
            }));
            // 根據目標合併
            const entitySetting = config.entity[className];
            const mergedToTargetName = mergeState.value.label;
            const mergedToTarget = mergeState.value.value;
            const gotoQueryStrName = mergeState.value._label;
            const nameValue = bs64DecodeId(personId);

            if (!isEmpty(mergedToTarget) && !isEmpty(nameValue)) {
                const checkName = handleNameSpellCheck(nameValue);
                const entrySrc = {
                    srcId: bs64EncodeId(checkName)
                };
                const entryDst = {
                    srcId: bs64EncodeId(mergedToTarget)
                };

                const result = await mergeHkbdbData(
                    Api.mergeInstance(),
                    entrySrc,
                    entryDst,
                    // typeSrc
                    type,
                    // typeDst
                    className
                );

                // 顯示訊息
                if (result.state) {
                    //
                    setAlertMsg(prevMsg => ({
                        ...prevMsg,
                        title: intl.formatMessage(
                            {
                                id: "people.alertMessage.title.merge.success",
                                defaultMessage: `Merge ｢{name}」successfully ...`
                            },
                            {
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                ),
                                mergedToTargetName: mergedToTargetName
                            }
                        ),
                        type: "success",
                        content: intl.formatMessage(
                            {
                                id: "people.alertMessage.content.merge.success",
                                defaultMessage: `Waiting to update ...`
                            },
                            {
                                name: mergedToTargetName
                            }
                        ),
                        // 時間只是方便用來觸發更新而已
                        renderSignal: new Date().getTime(),
                        ttl: 20 * 1000
                    }));
                    //
                    handleInitState();
                    // 跳轉到合併的人物
                    if (!isEmpty(mergedToTarget)) {
                        setTimeout(() => {
                            document.location.href = entitySetting.href(
                                removeStartPrefix(
                                    mergedToTarget,
                                    CLASS_PREFIX[className]
                                ),
                                queryString.stringify({
                                    name: bs64Encode(gotoQueryStrName || "")
                                })
                            );
                        }, alertMsg.ttl);
                    } else {
                        setOpen(false);
                        setValYesNo(false);
                    }
                } else {
                    setAlertMsg(prevMsg => ({
                        ...prevMsg,
                        title: intl.formatMessage(
                            {
                                id: "people.alertMessage.title.merge.error",
                                defaultMessage: ` ｢{name}」`
                            },
                            {
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                )
                            }
                        ),
                        type: "error",
                        content: intl.formatMessage(
                            {
                                id: "people.alertMessage.content.merge.error",
                                defaultMessage: `Merge ｢{name}」error ...`
                            },
                            {
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                ),
                                mergedToTargetName: mergedToTargetName
                            }
                        ),
                        // 時間只是方便用來觸發更新而已
                        renderSignal: new Date().getTime(),
                        ttl: 20 * 1000
                    }));
                }
                setValYesNo(false);
            }
            setMergeState(prevData => ({
                ...prevData,
                isLoading: false
            }));
        };
        handleMerge();
    }, [valYesNo]);
    //
    useEffect(() => {
        const _selectionOpt = options.map(ot => ({
            key: ot.key,
            text: intl.formatMessage({
                id:
                    ot.key === rootClasses[0].name
                        ? "browse.classPerson"
                        : "browse.classOrganization",
                defaultMessage: ot.text
            }),
            value: ot.value,
            disabled: ot.disabled
        }));
        setTypeOption(_selectionOpt);
    }, [intl]);
    //
    const handleInitState = () => {
        setMergeState(initialState);
    };
    //
    const handleOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        handleInitState();
    };
    const handleCancel = () => {
        //
        handleInitState();
        //
        setOpen(false);
    };
    //
    const handleChange = async options => {
        // e.g. options = [ {label: "三水", value: "PLA三水"}, ... ]
        options = isEmpty(options) ? [] : options;
        // update dropdown value
        setMergeState(prevState => ({ ...prevState, value: options }));
    };

    //
    useEffect(() => {
        if (isEmpty(type) || isEmpty(debSearchValue)) {
            return;
        }

        // open dropdown Loading
        setMergeState(prevState => ({
            ...prevState,
            isLoading: true
        }));

        fetchOptionList(className, debSearchValue, 100, 30 * 1000)
            .then(res => {
                // api傳回來的資料格式
                // [{
                //     subLabels: "黃健民",
                //     propertyLabel: "常見名稱",
                //     label: "黃健民",
                //     value: "PER黃健民",
                // },
                // {
                //     subLabels: "Wong, Jupiter@splitWong, Kin-man@splitMuxing@split木星@splitHuang, Jianmin@splitJupiter",
                //     propertyLabel: "筆名",
                //     label: "黃健民",
                //     value: "PER黃健民",
                // }]
                // 相同value,label,只留一筆
                const uniqPersons = Array.from(
                    new Set(
                        res.data
                            .map(o => ({
                                _label: o.label,
                                value: o.value,
                                label: `${uriEncIdToNonEncId(
                                    removeStartPrefix(
                                        o.value,
                                        CLASS_PREFIX[className]
                                    )
                                )} (常見名稱:${o.label})`
                            }))
                            .map(o => JSON.stringify(o))
                    )
                ).map(o => JSON.parse(o));

                setMergeState(prevState => ({
                    ...prevState,
                    options: uniqPersons.filter(
                        o => o.value !== `${CLASS_PREFIX[className]}${id}`
                    ),
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setMergeState(prevState => ({
                    ...prevState,
                    options: [],
                    isLoading: false
                }));
            });
    }, [debSearchValue, type]);
    //
    const handleInputChange = value => {
        setSearchValue(value);
    };
    //
    const handleOpenYesNo = () => {
        setOpenYesNo(true);
    };
    //
    const onResultSelect = (event, data) => {
        setClassName(data.value);
    };
    //
    const customButtonStyle = {
        marginLeft: ".8em",
        marginTop: "1em",
        marginBottom: "1em",
        padding: ".3em"
    };
    //
    const customSelectStyle = {
        minWidth: "8rem",
        maxWidth: "10rem",
        marginRight: "1rem"
    };
    //
    const customStyles = {
        container: styles => ({
            ...styles,
            width: "100%"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderTopLeftRadius: "0",
            borderBottomLeftRadius: "0",
            borderLeftColor: "transparent",
            border: "1px solid rgba(34,36,38,.15)"
        })
    };
    //
    const customPlacement = rowId => (rowId >= 4 ? "top" : "bottom");
    //
    if (
        !isTrue(process.env.REACT_APP_CRUD_NODE) ||
        !isNotEmpty(uid) ||
        !authority.People_Information.includes(role)
    ) {
        return null;
    }

    return (
        <div>
            <Modal
                size="small"
                open={open}
                onOpen={handleOpen}
                onClose={handleClose}
                dimmer="inverted"
            >
                <Modal.Header>
                    <FormattedMessage
                        id={"people.Information.merge.person"}
                        defaultMessage={`Merge ｢{name}」to ...`}
                        values={{
                            name: displayInstanceName(
                                decodeURIComponentSafe(id),
                                name,
                                Api.getLocale()
                            )
                        }}
                    />
                </Modal.Header>
                <Modal.Content image>
                    <Modal.Description style={{ width: "100%" }}>
                        <CustomAlertMessage
                            alertMsg={alertMsg}
                            setAlertMsg={setAlertMsg}
                        />
                        <Input
                            fluid
                            labelPosition="left"
                            type="text"
                            loading={mergeState.isLoading}
                        >
                            <Select
                                style={customSelectStyle}
                                fluid
                                options={typeOption}
                                defaultValue={className}
                                onChange={onResultSelect}
                            />
                            <Label>
                                <FormattedMessage
                                    id={"information.merge.label"}
                                    defaultMessage={`Name`}
                                />
                            </Label>
                            <ReactSelect
                                isClearable
                                styles={customStyles}
                                isLoading={mergeState.isLoading}
                                options={mergeState.options}
                                value={mergeState.value}
                                onChange={handleChange}
                                onInputChange={handleInputChange}
                                components={{ MenuList }}
                                menuPlacement={customPlacement()}
                                getOptionLabel={option => {
                                    const { label } = option || {};
                                    return label.length > 130
                                        ? label.slice(0, 130) + "..."
                                        : label;
                                }}
                                placeholder={
                                    <FormattedMessage
                                        id={"information.merge.to.placeholder"}
                                        defaultMessage={
                                            "Please type the target name..."
                                        }
                                    />
                                }
                                filterOption={createFilter({
                                    ignoreAccents: false
                                })}
                            />
                        </Input>
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    <Button
                        disabled={isEmpty(mergeState.value)}
                        loading={mergeState.isLoading}
                        onClick={handleOpenYesNo}
                        color="green"
                    >
                        <FormattedMessage
                            id={"people.Information.button.merge"}
                            defaultMessage={`Merge`}
                        />
                    </Button>
                    <Button color="red" onClick={handleCancel}>
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={`Cancel`}
                        />
                    </Button>
                </Modal.Actions>
            </Modal>
            <ModalYesNo
                open={openYesNo}
                setOpen={setOpenYesNo}
                setYesNo={setValYesNo}
                modalHeader={intl.formatMessage(
                    {
                        id: `information.ModalYesNo.header.merge.${type.toLowerCase()}`,
                        defaultMessage: `{name} {type}`
                    },
                    {
                        name: CUSTOM_MERGE,
                        type: type.toLowerCase()
                    }
                )}
            />
            <Popup
                content={
                    <FormattedMessage
                        id={"information.merge.person"}
                        defaultMessage={`Merge ｢{name}」to ...`}
                        values={{
                            name: displayInstanceName(
                                decodeURIComponentSafe(id),
                                name,
                                Api.getLocale()
                            )
                        }}
                    />
                }
                key={"merge"}
                trigger={
                    <Button
                        size="mini"
                        color="orange"
                        icon="sign-in"
                        style={customButtonStyle}
                        onClick={handleOpen}
                    />
                }
            />
        </div>
    );
};

export default injectIntl(CustomMergeButton);
