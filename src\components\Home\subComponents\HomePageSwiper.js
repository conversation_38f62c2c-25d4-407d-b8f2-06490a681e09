import React, { useState } from "react";

import PropTypes from "prop-types";
import classNames from "classnames";
import { Avatar } from "@material-ui/core";
import KeyboardArrowRightIcon from "@material-ui/icons/KeyboardArrowRight";
import KeyboardArrowLeftIcon from "@material-ui/icons/KeyboardArrowLeft";

// import Swiper JS
// core version + navigation, CustomPagination modules:
import Swiper from "swiper";
// import { Navigation, Pagination } from "swiper/modules";
//
// import Swiper and modules styles
// import 'swiper/swiper.scss';
import "swiper/swiper-bundle.css";

// import 'swiper/css/navigation';
// import 'swiper/css/CustomPagination';
// import 'swiper/css/grid';
// import 'swiper/css/free-mode';
//

import "./homePageSwiper.scss";

// eslint-disable-next-line no-unused-vars
let swiper;

const isMobile = window.innerWidth <= 576;

const swiperOpt = {
    // speed: 400,
    // configure Swiper to use modules
    modules: [],
    // Optional parameters
    // direction: "vertical",
    // // If we need CustomPagination
    pagination: {
        el: ".swiper-CustomPagination"
    },
    // // Navigation arrows
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev"
    },
    // // And if we need scrollbar
    // scrollbar: {
    // 	el: '.swiper-scrollbar',
    // },
    centeredSlides: isMobile, // active slide 是否置中
    // slidesPerView: 1,
    slidesPerView: "auto", // 每頁可以有幾個 slide {number|"auto"}
    spaceBetween: 80, // 卡片的間距
    loop: true, // 無限循環
    // freeMode: false,
    autoplay: true
};

const HomePageSwiper = props => {
    const { listData, onClick, img } = props;
    // hook

    const [, setSwiper] = useState(null);

    React.useEffect(() => {
        const tmpSwiper = new Swiper(".swiper", {
            ...swiperOpt
        });
        setSwiper(tmpSwiper);
    }, []);

    return (
        <div className="swiper">
            <div className="swiper-wrapper">
                {listData.map((data, idx) => {
                    const {
                        imgSrc,
                        imgUrl,
                        title,
                        subtitle,
                        url,
                        derivateWorkType
                    } = data;

                    const template = (
                        <div
                            className={classNames(
                                "swiper-slide",
                                "swiper-card"
                            )}
                            key={idx.toString()}
                            style={{
                                backgroundImage: `url("${imgSrc ||
                                    imgUrl ||
                                    url ||
                                    img}")`
                            }}
                            onClick={() => {
                                if (typeof onClick === "function")
                                    onClick(data);
                            }}
                        >
                            <div className="swiper-card__text">
                                <div className="swiper-card__text--title">
                                    {title || derivateWorkType}
                                </div>
                                <div className="swiper-card__text--subtitle">
                                    {subtitle || ""}
                                </div>
                            </div>
                        </div>
                    );

                    return template;
                })}
            </div>
            {/* <div className="swiper-CustomPagination" /> */}

            <div className="swiper-button-prev">
                <Avatar className="swiper-button-pre__avatar">
                    <KeyboardArrowLeftIcon className="swiper-button-pre__avatar--icon" />
                </Avatar>
            </div>
            <div className="swiper-button-next">
                <Avatar>
                    <KeyboardArrowRightIcon />
                </Avatar>
            </div>
            {/* <div className="swiper-scrollbar" /> */}
        </div>
    );
};

HomePageSwiper.defaultProps = {
    listData: [],
    onClick: () => null,
    img: null
    // type: swiperType.tl,
};

HomePageSwiper.propTypes = {
    listData: PropTypes.arrayOf(PropTypes.any),
    onClick: PropTypes.func,
    img: PropTypes.string
    // type: PropTypes.oneOf(['tl', 'ml', 'rl', 'vr']),
};

export default HomePageSwiper;
