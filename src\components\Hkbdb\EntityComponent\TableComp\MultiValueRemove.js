import React from "react";

// ui
import { Icon, Popup } from "semantic-ui-react";

// component
import { components } from "react-select";

const MultiValueRemove = props => {
    return (
        <div>
            <components.MultiValueRemove {...props}>
                <div>
                    <Popup
                        content="Add this property to Form Table."
                        trigger={
                            <Icon name="triangle up" size="mini" color="grey" />
                        }
                    />
                </div>
            </components.MultiValueRemove>
        </div>
    );
};

export default MultiValueRemove;
