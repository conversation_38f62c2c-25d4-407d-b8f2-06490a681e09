import React, { useContext } from "react";

import useStyles from "./style";

import config from "../../config";

// custom
import CusTour from "../../CusComps/CusTour";
import CusFooter from "../../CusComps/CusFooter";
import CusSearchBox from "../../CusComps/CusSearchBox";
import CusAboutYearbook from "../../CusComps/CusAboutYearbook";
import CusPrimaryHeader from "../../CusComps/CusPrimaryHeader";

import { StoreContext } from "../../../../store/StoreProvider";

import { safeGet } from "../../../../common/codes";

const Search = ({ style }) => {
    const classes = useStyles(style);
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const yearBookDesc = safeGet(config.yearBookDesc, [locale], "");
    const categoryImage = safeGet(config.categoryImage, [locale], "");
    return (
        <div>
            <div className={classes.hkvayb_search_background}>
                <div className={classes.hkvayb_search_grid}>
                    <CusPrimaryHeader />
                    <CusSearchBox />
                </div>
            </div>
            <div className={classes.hkvayb_search_grid}>
                <CusAboutYearbook data={yearBookDesc} />
                <CusTour data={categoryImage} />
            </div>
            <CusFooter />
        </div>
    );
};

export default Search;
