import React from "react";
import { Checkbox } from "semantic-ui-react";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

const OntoRadioBox = ({ checked, handleChange }) => {
    const checkboxStyle = {
        marginRight: "20px"
    };
    return (
        <React.Fragment>
            <Checkbox
                radio
                label={
                    <FormattedMessage
                        id={"ontology.both"}
                        defaultMessage={"Both"}
                    />
                }
                name="checkboxRadioGroup"
                value="Both"
                checked={checked === "Both"}
                onChange={handleChange}
                style={checkboxStyle}
            />
            <Checkbox
                radio
                label={
                    <FormattedMessage
                        id={"ontology.object"}
                        defaultMessage={"Object"}
                    />
                }
                name="checkboxRadioGroup"
                value="ObjectProperty"
                checked={checked === "ObjectProperty"}
                onChange={handleChange}
                style={checkboxStyle}
            />
            <Checkbox
                radio
                label={
                    <FormattedMessage
                        id={"ontology.data"}
                        defaultMessage={"Data"}
                    />
                }
                name="checkboxRadioGroup"
                value="DatatypeProperty"
                checked={checked === "DatatypeProperty"}
                onChange={handleChange}
                style={checkboxStyle}
            />
        </React.Fragment>
    );
};

OntoRadioBox.propTypes = {
    checked: PropTypes.string.isRequired,
    handleChange: PropTypes.func.isRequired
};

export default OntoRadioBox;
