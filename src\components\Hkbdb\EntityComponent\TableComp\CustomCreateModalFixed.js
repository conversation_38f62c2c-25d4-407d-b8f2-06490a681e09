import React, { Fragment, useContext, useEffect, useState } from "react";

// ui
import { <PERSON><PERSON>, Di<PERSON><PERSON>, Modal, Grid, Segment } from "semantic-ui-react";

// custom
import CustomModalContent from "./CustomModalContentFixed";
import CustomAlertMessage from "./CustomAlertMessage";

// lang
import { FormattedMessage } from "react-intl";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import { safeGet, isEmpty, isNotEmpty, isTrue } from "../../../../common/codes";

// api
import { Api, createHkbdbData, doRestCreate } from "../../../../api/hkbdb/Api";

// auth
import authority from "../../../../App-authority";
import allRoles from "../../../../App-role";
import base64url from "base64url";
import axios from "axios";
import { HAS_COORDS_TYPE } from "../constants";

const CustomCreateModalFixed = ({ ontologyDomain, ontologyType }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { ontologyDefined } = state.property;
    const { dataset } = state.source;
    const { personId } = state.information;
    const { user } = state;
    const { uid, role } = user;
    // modal 開啟的狀態統一註冊,避免開啟modal後無預警關閉
    const { modalLockSec } = state.modal;
    //
    // const [open, setOpen] = useState(false);
    //
    const [isLoading, setIsLoading] = useState(() => false);
    //
    const [createData, setCreateData] = useState(() => {
        //
        const graphs = dataset.map(item => item.dataset);
        //
        const ontology = {};
        //
        return {
            graphs,
            selectedProperties: [],
            willCreatedData: {},
            ontology,
            ontologyType,
            ontologyDefined,
            isCreated: false
        };
    });
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const createLocationLabel = async (graph, locType, locId) => {
        const encodedGraph = base64url.encode(graph);

        const graphLocations = await axios.get(
            locType === "Place"
                ? Api.getGraphPlace(encodedGraph)
                : Api.getGraphOrganization(encodedGraph)
        );

        const locationList = graphLocations.data.data;

        const locationMap = new Map(
            locationList.map(item => [item.id, item.label])
        );

        const hasId = locationMap.has(locId);
        const label = locationMap.get(locId);
        const needToCreate = !hasId || !label || !label.trim();

        if (needToCreate) {
            const location = locId.replace(/^(ORG|PLA)/, "");
            const locLabel = decodeURIComponent(location);
            const srcId = `${
                locType === "Place" ? "PLA" : "ORG"
            }${base64url.encode(location)}`;

            const createEntry = {
                graph,
                srcId,
                classType: locType,
                value: {
                    [locType === "Place" ? "label_Place" : "bestKnownName"]: [
                        locLabel
                    ]
                }
            };

            await createHkbdbData(Api.restfulHKBDB(), createEntry);
        }
    };
    //
    const handleCreate = async () => {
        //
        setIsLoading(true);
        //
        if (!isEmpty(personId)) {
            const entry = {
                ...createData.willCreatedData,
                srcId: personId
            };

            const { graph, value } = createData.willCreatedData;
            const graphStr = Array.isArray(graph) ? graph[0] : graph;

            for (const key of Object.keys(value)) {
                const [, propRange] = key.split("__");

                if (HAS_COORDS_TYPE.includes(propRange)) {
                    for (const locId of value[key]) {
                        await createLocationLabel(graphStr, propRange, locId);
                    }
                }
            }

            const result = await doRestCreate(user, entry);
            if (!isEmpty(result)) {
                //
                dispatch({
                    type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                    payload: {
                        target: ontologyType,
                        signal: `created-${new Date().getTime()}`
                    }
                });
                //
                setCreateData(prevData => ({
                    ...prevData,
                    isCreated: true
                }));
                //
                setAlertMsg(prevMsg => ({
                    ...prevMsg,
                    title: "創建新的 property",
                    type: "success",
                    content: `已成功創建新的 property。`,
                    renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
                }));
                // 通知編輯後是否顯示 isLoading(這是區域重新載入，不是整個頁面，e.g. information's publication)
                dispatch({
                    type: Act.INFORMATION_DATA_IS_LOADING_SET,
                    payload: true
                });
            }
        }
        //
        setIsLoading(false);
        // close
        // setOpen(false)
    };
    const handleClose = () => {
        setCreateData(prevData => ({
            ...prevData,
            selectedProperties: [],
            willCreatedData: {},
            isCreated: false
        }));
        // close
        dispatch({
            type: Act.MODAL_LOCK_CLEAR
        });
        // setOpen(false);
    };
    const handleCancel = () => {
        handleClose();
    };

    //
    const SwitchButton = () => {
        //
        if (createData.isCreated) {
            return (
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            //
            const {
                srcId,
                graph,
                ...restWillCreatedData
            } = createData.willCreatedData;
            //
            const isNotEmptyData = isNotEmpty(restWillCreatedData);
            //
            const requiredArr = createData.selectedProperties.reduce(
                (prevObj, item) => {
                    if (item.hasOwnProperty("required")) {
                        return [...prevObj, item.value];
                    }
                    return prevObj;
                },
                []
            );
            //
            const selectedGraph = safeGet(
                createData.willCreatedData,
                ["graph"],
                ""
            );
            //
            const isSelectedGraph = isNotEmpty(selectedGraph);
            //
            const entryValue = safeGet(
                createData.willCreatedData,
                ["value"],
                {}
            );
            //
            const isDoneRequired =
                requiredArr
                    .map(prop => {
                        if (isNotEmpty(safeGet(entryValue, [prop], []))) {
                            return prop;
                        }
                    })
                    .filter(item => item !== undefined).length ===
                requiredArr.length;
            //
            // button's disabled 為 true 才能 disabled，因此所有項目都需要反轉
            const isDisabled =
                requiredArr.length >= 1
                    ? !isDoneRequired || !isSelectedGraph
                    : !isNotEmptyData || !isSelectedGraph;
            //
            return (
                <Fragment>
                    <Button
                        loading={isLoading}
                        disabled={isDisabled}
                        color="green"
                        onClick={handleCreate}
                    >
                        <FormattedMessage
                            id={"people.Information.button.confirm"}
                            defaultMessage={"Confirm"}
                        />
                    </Button>
                    <Button color="red" onClick={handleCancel}>
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };
    //
    useEffect(() => {
        setCreateData(prevState => ({
            ...prevState,
            ontology: ontologyDefined[ontologyType]
        }));
    }, [JSON.stringify(ontologyDefined)]);

    //
    if (
        isTrue(process.env.REACT_APP_CRUD_NODE) &&
        isNotEmpty(uid) &&
        authority.People_Information.includes(role)
    ) {
        return (
            <Segment basic textAlign="center">
                <Grid>
                    <Grid.Column width={5}>
                        <Divider />
                    </Grid.Column>
                    <Grid.Column width={6}>
                        <Modal
                            // open={open}
                            // modal 開啟的狀態統一註冊,避免開啟modal後無預警關閉
                            open={!!modalLockSec}
                            onClose={handleClose}
                            onOpen={() => {
                                // setOpen(true);
                                dispatch({
                                    type: Act.MODAL_LOCK_SET,
                                    payload: `${ontologyDomain}-${ontologyType}`
                                });
                            }}
                            trigger={
                                <Button
                                    basic
                                    size="small"
                                    // content="Create New Property"
                                    content={
                                        <FormattedMessage
                                            id="people.Information.createProperty"
                                            defaultMessage="Create new Property"
                                        />
                                    }
                                    icon="add"
                                    labelPosition="left"
                                />
                            }
                        >
                            {/* <pre>{JSON.stringify(perInfo[infoType], null, 2)}</pre> */}
                            {/* <pre>{JSON.stringify(createData.willCreatedData, null, 2)}</pre> */}
                            <Modal.Header>
                                {/* <FormattedMessage */}
                                {/*    id="people.Information.createProperty" */}
                                {/*    defaultMessage={"Create new Property"} */}
                                {/* /> */}
                                <FormattedMessage
                                    id="people.Information.createProperty"
                                    defaultMessage="Create new Property"
                                />
                            </Modal.Header>
                            <Modal.Content image scrolling>
                                <Modal.Description style={{ width: "100%" }}>
                                    <CustomAlertMessage
                                        alertMsg={alertMsg}
                                        setAlertMsg={setAlertMsg}
                                    />
                                    {/* show content */}
                                    <CustomModalContent
                                        createData={createData}
                                        setCreateData={setCreateData}
                                        ontologyDomain={ontologyDomain}
                                        ontologyType={ontologyType}
                                    />
                                </Modal.Description>
                            </Modal.Content>
                            <Modal.Actions>
                                <SwitchButton />
                            </Modal.Actions>
                        </Modal>
                    </Grid.Column>
                    <Grid.Column width={5}>
                        <Divider />
                    </Grid.Column>
                </Grid>
            </Segment>
        );
    }
    return null;
};

export default CustomCreateModalFixed;
