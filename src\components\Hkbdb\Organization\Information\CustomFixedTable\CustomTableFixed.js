import React, { useContext, useState, useEffect } from "react";

// ui
import { Table } from "semantic-ui-react";

// sorting chinese words
import { sortedByStroke } from "twchar";

// custom
import CustomContentMenu from "../../../EntityComponent/TableComp/CustomContextMenuFixed";
import CustomImageModal from "../../../EntityComponent/TableComp/CustomImageModalDimmer";
import CustomTableLabel from "../../../EntityComponent/TableComp/CustomTableLabel";
import CustomIsLoading from "../../../EntityComponent/TableComp/CustomIsLoading";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// common
import {
    isEmpty,
    getProperty,
    cvtDatasetLocale,
    convertDateEventToFormalDate,
    safeGet,
    isNotEmpty
} from "../../../../../common/codes";
//
import config from "../../../../../config/config";
//
import {
    arrayMergeData,
    mergeArray
} from "../../../../../common/codes/mergeArray";
import { doUriEncode } from "../../../../../common/codes/jenaHelper";
import { GV } from "../../../../../common/codes/globalVars";
import ROLE from "../../../../../App-role";

//
const customDivStyle = {
    maxHeight: "350px",
    overflow: "auto",
    borderTop: "1px solid rgba(34,36,38,.1)",
    borderBottom: "1px solid rgba(34,36,38,.1)",
    borderRadius: ".28571429rem .28571429rem .28571429rem .28571429rem"
};
const customEditMenuStyle = {
    width: "48px"
};
const fixTableHeaderStyle = {
    position: "sticky",
    top: "0",
    zIndex: "998"
};
const tableStyle = {
    borderTop: "unset",
    borderBottom: "unset"
};

const PROPERTY = "property";
const DATA = "data";
const GRAPH = "graph";

const CustomTableFixed = ({
    data,
    typeConfig,
    ontologyDomain,
    ontologyType
}) => {
    //
    const [state] = useContext(StoreContext);
    const {
        user,
        setting,
        property,
        source,
        information,
        personInformation
    } = state;

    const { headers: head } = personInformation;
    const { ontologyDefined } = property;
    // 系統預設的設定
    // state -> setting -> tableSortedRecords -> information
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    //
    const { inverseRelationData, isLoading } = information;
    //
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldAttrRecordsNN = safeGet(
        fieldAttr,
        [ontologyDomain, config.DEF_NNI_TYPE],
        []
    );
    //
    const headers =
        typeConfig[ontologyType] && Object.values(typeConfig[ontologyType]);
    //
    const [mergedData, setMergedData] = useState([]);
    // 使用者的設定
    const { role } = user;
    // 資料集中英文名稱
    // [{ dataset: "abcwhkp", label: "《香港古典詩文集經眼錄》", lang: "zh" },{}]
    const { dataset: globalDataset } = source;
    //
    const nameNode = data.filter(d => {
        const { basicProperty } = d;
        return config.nameNodeProp.indexOf(basicProperty) > -1;
    });
    // const unNameNode = data.filter(d => {
    //     const { basicProperty } = d;
    //     return config.nameNodeProp.indexOf(basicProperty) < 0;
    // });

    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(property?.propertyObj)
            ? _property
            : getProperty(_property, property.propertyObj);
    };

    const handleMerge = _data => {
        // get key to merge graph
        const graphFormatKay =
            head &&
            head[ontologyType] &&
            head[ontologyType]
                .filter(key => key !== "graph" && key !== "basicRange")
                .map(key => {
                    if (key === typeConfig[ontologyType].property) {
                        return PROPERTY;
                    }

                    if (key === typeConfig[ontologyType].data) {
                        return DATA;
                    }

                    return key;
                });

        // get key to merge data
        const dtFormatKey = [
            ...graphFormatKay.filter(key => key !== DATA),
            GRAPH
        ];

        // 除了 graph 之外其他 property 都相同 -> graph merge
        const mergedGraph = arrayMergeData(
            _data,
            graphFormatKay,
            [GRAPH],
            "graphList"
        );
        //
        const formatMergedGraph = mergedGraph.map(prop => ({
            ...prop,
            graph: prop.graphList.map(item => item.graph).join()
        }));

        // 除了 data 之外其他 property 都相同 -> data merge
        const mergedDt = arrayMergeData(
            formatMergedGraph,
            dtFormatKey,
            [DATA],
            "mergeList"
        );

        // create keyName for table
        const keyMergedData = mergedDt
            .map(item => {
                const {
                    mergeList,
                    range,
                    graphList
                    // basicProperty
                } = item;

                const dataset = graphList.map(item => item.graph);
                const values = mergeList.map(item => {
                    const dataString = item.data;
                    if (doUriEncode(range)) {
                        const [label, value] = dataString.split("___");
                        return {
                            label: label || dataString,
                            value: value || dataString
                        };
                    }
                    return { label: dataString, value: dataString };
                });
                return {
                    ...item,
                    range: item["basicRange"],
                    dataset,
                    values
                };
            })
            // 除去系統的 property
            .filter(row => GV.SYSTEM_HIDE_PROPERTY.indexOf(row.property) < 0);

        // property sort
        const sortedData = keyMergedData.slice(0).sort((a, b) => {
            // sort by sorted list
            const order = fieldPropSortedRecords.map(item =>
                (item?.split("__") || [])[0].toUpperCase()
            );

            const nameA = a[PROPERTY].toUpperCase();
            const nameB = b[PROPERTY].toUpperCase();
            if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
            if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
            return 0;
        });

        return sortedData;
    };

    useEffect(() => {
        //
        let tmpNameNodeArr = [];
        // if (!config.getNNIPermission(role)) {
        if (role === ROLE.reader || role === ROLE.anonymous) {
            // 使用後台的設定
            const allowNameNodeProps = Object.keys(fieldAttrRecordsNN)
                .filter(key => {
                    return safeGet(
                        fieldAttrRecordsNN,
                        [key, "roles"],
                        []
                    ).includes(role);
                })
                .map(key => key.split("__")[0]);

            tmpNameNodeArr = nameNode.filter(
                nniItem =>
                    allowNameNodeProps.indexOf(
                        safeGet(nniItem, [PROPERTY], "")
                    ) !== -1
            );
        } else {
            tmpNameNodeArr = [];
        }
        // const sortedMergedData = mergeArray(unNameNode, fieldPropSortedRecords);
        // const sortedMergedNameNodeIdsData =
        //     ontologyType === config.DEF_ORG_DOMAIN
        //         ? mergeArray(tmpNameNodeArr, fieldPropSortedRecords)
        //         : [];
        const sortedMergedData = handleMerge(data);
        const sortedMergedNameNodeIdsData =
            ontologyType === config.DEF_ORG_DOMAIN
                ? handleMerge(tmpNameNodeArr)
                : [];
        //
        const splitRangeFieldAttrRecords = Object.keys(fieldAttrRecords).reduce(
            (prevObj, key) => {
                const oriPropName = key.split("__")[0];
                return { ...prevObj, [oriPropName]: fieldAttrRecords[key] };
            },
            {}
        );
        // 過濾角色
        const filterRoleMergedData = sortedMergedData.filter(item => {
            //
            const propTitle = item[PROPERTY];
            // graph 就不用過濾
            if (propTitle === GRAPH) return true;
            // relationEvent(invert)
            const foundInverse = inverseRelationData.find(
                item =>
                    `${item.invert}`.toLowerCase() ===
                    `${propTitle}`.toLowerCase()
            );
            //
            if (isNotEmpty(foundInverse)) {
                return true;
            }
            //
            const _roles = safeGet(
                splitRangeFieldAttrRecords,
                [propTitle, "roles"],
                []
            );
            return _roles.includes(role);
        });
        //
        setMergedData([
            ...filterRoleMergedData,
            ...sortedMergedNameNodeIdsData
        ]);
        //
    }, [JSON.stringify(data), role, JSON.stringify(fieldAttrRecords)]);
    //
    const Value2CompByTitle = (title, values) => {
        switch (title) {
            case "IMGportrait":
                return values.map((value, idx) => (
                    <CustomImageModal
                        key={`image-label-${value}-${idx}`}
                        imgUrl={value}
                    />
                ));
            default:
                return sortedByStroke(
                    values.map(value => ({
                        key: value.label
                    })),
                    "key"
                    // values.map(value => ({ key: value })),
                    // "key"
                )
                    .map(item => item.key)
                    .map((value, idx) => (
                        <CustomTableLabel
                            key={`value-label-${value}-${idx}`}
                            value={convertDateEventToFormalDate(
                                value,
                                title,
                                ontologyDefined[ontologyType]
                            )}
                            color="grey"
                        />
                    ));
        }
    };
    //
    if (isLoading) {
        return <CustomIsLoading />;
    }
    //
    return (
        <div style={customDivStyle}>
            <Table fixed style={tableStyle}>
                <Table.Header>
                    <Table.Row>
                        <Table.HeaderCell
                            style={{
                                ...fixTableHeaderStyle,
                                ...customEditMenuStyle
                            }}
                        >
                            {" "}
                        </Table.HeaderCell>
                        {headers.map((header, headerId) => (
                            <Table.HeaderCell
                                key={`table-header-cell-${headerId}`}
                                style={fixTableHeaderStyle}
                            >
                                {safeGetProperty(header)}
                            </Table.HeaderCell>
                        ))}
                    </Table.Row>
                </Table.Header>

                <Table.Body>
                    {/* <pre>{JSON.stringify(mergedData, null, 2)}</pre> */}
                    {mergedData.map((item, idx) => {
                        // dataset i18n
                        const datasetLocale = cvtDatasetLocale(
                            item.dataset,
                            globalDataset
                        );

                        const tableCell = (keyValue, props) => {
                            switch (keyValue) {
                                case PROPERTY:
                                    return safeGetProperty(item[PROPERTY]);
                                case DATA:
                                    return Value2CompByTitle(
                                        keyValue,
                                        item["values"]
                                    );
                                case GRAPH:
                                    return datasetLocale.map((dataset, idx) => (
                                        <CustomTableLabel
                                            key={`value-label-${dataset}-${idx}`}
                                            value={dataset}
                                            color="orange"
                                        />
                                    ));
                                default:
                                    return item[props];
                            }
                        };

                        return (
                            <Table.Row
                                key={`basic-table-row-${property}-${idx}`}
                            >
                                <Table.Cell key={`table-row-cell-edit-${idx}`}>
                                    <CustomContentMenu
                                        data={item}
                                        ontologyDomain={ontologyDomain}
                                        ontologyType={ontologyType}
                                    />
                                </Table.Cell>
                                {Object.keys(typeConfig[ontologyType]).map(
                                    (key, keyId) => (
                                        <Table.Cell
                                            key={`table-row-cell-${keyId}`}
                                            style={{
                                                overflowX: "auto",
                                                textOverflow: "initial"
                                            }}
                                        >
                                            {tableCell(
                                                key,
                                                typeConfig[ontologyType][key]
                                            )}
                                        </Table.Cell>
                                    )
                                )}
                            </Table.Row>
                        );
                    })}
                </Table.Body>
            </Table>
        </div>
    );
};

export default CustomTableFixed;
