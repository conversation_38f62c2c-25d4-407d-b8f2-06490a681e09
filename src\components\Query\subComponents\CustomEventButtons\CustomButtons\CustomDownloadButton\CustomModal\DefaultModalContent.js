import React from "react";
import { But<PERSON>, Modal } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";
import CustomTSVButton from "../CustomButtons/CustomTSVButton";

const DefaultModalContent = ({ onGephiBtnClick, open, setOpen }) => {
    return (
        <React.Fragment>
            <Modal.Header>
                <FormattedMessage
                    id="people.Information.download.label"
                    defaultMessage={"Download"}
                />
            </Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description>
                    <FormattedMessage
                        id="people.Information.download.message"
                        defaultMessage={
                            "There are two data types could be downloaded. Please click the below button to have it."
                        }
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <React.Fragment>
                    <Button onClick={() => onGephiBtnClick()}>Gephi</Button>
                    <CustomTSVButton open={open} setOpen={setOpen} />
                </React.Fragment>
            </Modal.Actions>
        </React.Fragment>
    );
};

export default DefaultModalContent;
