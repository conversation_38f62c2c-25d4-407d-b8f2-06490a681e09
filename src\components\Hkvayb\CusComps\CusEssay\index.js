import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusBr from "../CusBr";
import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusAlbum from "../CusAlbum";
import CusPdfLink from "../CusPdfLink";
import CusMarkdown from "../CusMarkdown";
import CusDualPages from "../CusDualPages";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const coverButtonLeftStyle = {
    hkvayb_buttom_border_invert: {
        width: "100%"
    }
};

const coverButtonRightStyle = {
    hkvayb_buttom_border_invert: {
        width: "100%"
    }
};

const coverBrStyle = {
    hkvayb_br: {
        padding: "10px"
    }
};

const CusEssay = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };
    const authorKey = ["hasAuthor", "hasAuthorTmp"];

    const bilingFunc = bilingual(defObj);

    const [authorZh, authorEn] = bilingFunc(data, authorKey);

    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [postIdZh, postIdEn] = bilingFunc(data, "postId");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [keywordZh, keywordEn] = bilingFunc(data, "keyword");
    const [pdfLinkZh, pdfLinkEn] = bilingFunc(data, "pdfLink");
    const [fullWorkZh, fullWorkEn] = bilingFunc(data, "fullWorkAvailableAt");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_essay}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`essays/${year}`}
                    backupPath={"essays"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.essay.author"
                    defaultMessage="Author : "
                />
                <CusValue {...authorZh} />
                <CusValue prefix="/" defVal="" {...authorEn} />
            </CusPara>
            <CusPara>
                <CusDualPages>
                    <CusMarkdown value={fullWorkZh.value} />
                    <CusMarkdown value={fullWorkEn.value} />
                </CusDualPages>
            </CusPara>
            <CusPara>
                <CusValue {...keywordZh} />
                <CusValue prefix="/" defVal="" {...keywordEn} />
            </CusPara>
            <CusPara>
                <div>
                    <CusPdfLink
                        label="閱讀全文"
                        style={coverButtonLeftStyle}
                        value={pdfLinkZh.value}
                        path={`pdf+essays/${year}`}
                    />
                </div>
                <CusBr style={coverBrStyle} />
                <div>
                    <CusPdfLink
                        label="Read the full articles"
                        style={coverButtonRightStyle}
                        value={pdfLinkEn.value}
                        path={`pdf+essays/${year}`}
                    />
                </div>
            </CusPara>
        </div>
    );
};

export default CusEssay;
