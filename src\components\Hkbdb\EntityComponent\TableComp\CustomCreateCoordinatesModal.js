import React, { useEffect, useState } from "react";
import { Icon, Label } from "semantic-ui-react";
import { isEmpty } from "../../../../common/codes";
import axios from "axios";
import { Api } from "../../../../api/hkbdb/Api";
import { saveCoordsChange, getGoogleCoord } from "../utils";
import CustomCoordinatesModal from "./CustomCoordinatesModal";

const CustomCreateCoordinatesModal = ({
    property,
    createData,
    setCreateData
}) => {
    const { ontologyType } = createData;
    const { value: propertyName, label, required } = property;
    const oriPropRange = propertyName.split("__")[1];

    const [open, setOpen] = useState(false);
    const [locations, setLocations] = useState([]);
    const [allCoordinates, setAllCoordinates] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const handleChangeCoords = async coords => {
        const formatedCoords = coords.filter(coord => coord.new.label.trim());
        const locations = formatedCoords
            .filter(coord => coord.status !== "delete")
            .map(
                coord =>
                    `${oriPropRange === "Place" ? "PLA" : "ORG"}${encodeURI(
                        coord.new.label
                    )}`
            );

        if (!isEmpty(propertyName)) {
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyName]: locations
                    }
                }
            }));
            try {
                setIsLoading(true);

                await saveCoordsChange(formatedCoords, oriPropRange);

                setIsLoading(false);
            } catch (e) {
                setIsLoading(false);
                alert("修改經緯度失敗");
                console.log("saveCoordsChange Error: ", e);
            }
        } else {
            console.log("param error, propertyName: ", propertyName);
        }
    };

    const getCoordinates = async (type = "all") => {
        const res = await axios.get(Api.getCoordinates(type));
        return res.data.data;
    };

    useEffect(() => {
        const locs = createData?.willCreatedData?.value?.[propertyName];

        if (locs && locs.length > 0) {
            const decodeLocs = locs.map(loc =>
                decodeURI(loc.replace(/^PLA|^ORG/, ""))
            );

            setLocations(decodeLocs);
        } else {
            setLocations([]);
        }
    }, [createData]);

    useEffect(() => {
        if (!open || !oriPropRange) return;

        const locationType = oriPropRange.toLocaleLowerCase();

        const loadCoordinates = async () => {
            const coordinatesData = await getCoordinates(locationType);
            setAllCoordinates(coordinatesData);
        };

        loadCoordinates();
    }, [open, oriPropRange]);

    return (
        <div style={{ display: "flex" }}>
            <Label
                style={{
                    display: "flex",
                    alignItems: "center",
                    margin: 0,
                    padding: "11px",
                    fontSize: "1em",
                    borderTopRightRadius: "0",
                    borderBottomRightRadius: "0"
                }}
            >
                {label}
                {required && (
                    <Icon
                        style={{ marginLeft: "1.5em" }}
                        color="red"
                        name="asterisk"
                        size="mini"
                    />
                )}
            </Label>
            <div style={{ flex: 1 }}>
                <CustomCoordinatesModal
                    open={open}
                    setOpen={setOpen}
                    allCoordinates={allCoordinates}
                    locations={locations}
                    title={label}
                    isLoading={isLoading}
                    getGoogleCoord={getGoogleCoord}
                    handleChangeCoords={handleChangeCoords}
                />
            </div>
        </div>
    );
};

export default CustomCreateCoordinatesModal;
