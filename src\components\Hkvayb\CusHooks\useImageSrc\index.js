import React from "react";

const useImageSrc = (values, url) => {
    const [images, setImages] = React.useState([]);
    React.useEffect(() => {
        setImages(
            values
                // .reduce((prev, value) => {
                //     return [...prev, ...value.split(";")];
                // }, [])
                .map(f => ({
                    src: `${url}/${f}`
                    // backupSrc: `${url}/${backupPath}/${f}`
                }))
        );
    }, [JSON.stringify(values)]);
    return images;
};

export default useImageSrc;
