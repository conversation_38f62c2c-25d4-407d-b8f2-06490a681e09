// react
import React, { Fragment, useContext } from "react";

// custom
import CustomUpdateButton from "./CustomUpdateButton";
import CustomCancelButton from "./CustomCancelButton";
import CustomSaveButton from "./CustomSaveButton";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../common/codes";

const index = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { user, query } = state;
    const { uid } = user;
    const { selectedAuthorId, selectedQueryId } = query;
    //
    const ShowButtons = () => {
        if (!isEmpty(selectedQueryId)) {
            if (!isEmpty(uid) && !isEmpty(selectedAuthorId)) {
                if (uid === selectedAuthorId) {
                    return (
                        <Fragment>
                            <CustomUpdateButton />
                            <CustomCancelButton />
                        </Fragment>
                    );
                }
            }
        }
        return <CustomSaveButton />;
    };
    //
    return <ShowButtons />;
};

export default index;
