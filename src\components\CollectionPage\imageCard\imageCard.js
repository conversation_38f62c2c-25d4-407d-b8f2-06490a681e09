import React from "react";
import classNames from "classnames";
import LazyAvatar from "../lazyAvatar/LazyAvatar";
import collectionConfig from "../collectionConfig";

import "./imageCard.scss";

const ImageCard = ({ imgObj, setFocusItem, colType, size }) => {
    const { id, imageURL, label, authorDisplay } = imgObj;

    return (
        <div
            key={id}
            className={classNames("ImageCard", "ImageCard--slick")}
            onClick={() => setFocusItem(imgObj)}
        >
            <LazyAvatar
                className={classNames("ImageCard__avatar")}
                bgColor={collectionConfig[colType].bgColor}
                size={size}
                imgSrc={imageURL}
            />
            <div className={"ImageCard__title"}>
                {colType === "manuScript"
                    ? label
                    : label?.includes("《")
                    ? label
                    : `《${label}》`}
                {/* 創作者 */}
                <p>{authorDisplay}</p>
            </div>
        </div>
    );
};

export default ImageCard;
