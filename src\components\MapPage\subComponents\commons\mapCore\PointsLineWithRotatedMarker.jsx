/* eslint-disable no-unused-vars, react/prop-types */
import React, { useEffect, useMemo, useRef } from "react";
import { useMap } from "react-leaflet";
import L from "leaflet";
import "../../../styles/myMap.scss";
import { basicOptions } from "./PointsLine";
import "@elfalem/leaflet-curve";
/**
 * 將值解析為浮點數
 */
const safeParseFloat = value => {
    const num = typeof value === "string" ? parseFloat(value) : value;
    return Number.isNaN(num) ? 0 : num;
};

/**
 * 計算兩點之間的角度 (單位：度) - 從 p1 指向 p2
 */
const calculateAngle = (p1, p2) => {
    if (!p1 || !p2 || (p1.x === p2.x && p1.y === p2.y)) {
        return 0;
    }
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    const angleRad = Math.atan2(dy, dx);
    let angleDeg = (angleRad * 180) / Math.PI;
    return angleDeg;
};

/**
 * PointsLineWithRotatedMarker 元件 - 使用 Leaflet 內建旋轉
 */
const PointsLineWithRotatedMarker = props => {
    const {
        srcPoint,
        dstPoint,
        style,
        showArrow = true,
        lineId,
        mapVersion,
        zoomDashParams
    } = props;

    const map = useMap();
    const lineRef = useRef(null);
    const startMarkerRef = useRef(null);
    const endMarkerRef = useRef(null);

    const {
        color = basicOptions.color || "black",
        weight = basicOptions.weight || 1,
        dashArray = null
    } = style || {};

    const polyLineData = useMemo(() => {
        const srcLat = safeParseFloat(srcPoint?.lat);
        const srcLng = safeParseFloat(srcPoint?.long);
        const dstLat = safeParseFloat(dstPoint?.lat);
        const dstLng = safeParseFloat(dstPoint?.long);

        if (
            srcPoint == null ||
            dstPoint == null ||
            isNaN(srcLat) ||
            isNaN(srcLng) ||
            isNaN(dstLat) ||
            isNaN(dstLng) ||
            (srcLat === 0 && srcLng === 0) ||
            (dstLat === 0 && dstLng === 0) ||
            (srcLat === dstLat && srcLng === dstLng)
        ) {
            return null;
        }
        return {
            latlngs: [
                [srcLat, srcLng],
                [dstLat, dstLng]
            ],
            startPoint: L.latLng(srcLat, srcLng),
            endPoint: L.latLng(dstLat, dstLng)
        };
    }, [srcPoint?.lat, srcPoint?.long, dstPoint?.lat, dstPoint?.long]);

    // 2. 動態創建圖示 (保持簡化的 SVG)
    const arrowIcon = useMemo(() => {
        const arrowSize = 12;
        const arrowSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="${arrowSize}" height="${arrowSize}"><polygon points="0,2 6,5 0,8" fill="${color}" opacity="0.8" /></svg>`;
        return L.divIcon({
            html: arrowSvg,
            className: "leaflet-arrow-icon",
            iconSize: [arrowSize, arrowSize],
            iconAnchor: [arrowSize / 2, arrowSize / 2]
        });
    }, [color]);

    // 3. useEffect 繪製線條和 Markers
    useEffect(() => {
        if (!map || !polyLineData) return;

        if (lineRef.current && map.hasLayer(lineRef.current))
            lineRef.current.remove();
        if (startMarkerRef.current && map.hasLayer(startMarkerRef.current))
            startMarkerRef.current.remove();
        if (endMarkerRef.current && map.hasLayer(endMarkerRef.current))
            endMarkerRef.current.remove();
        lineRef.current = null;
        startMarkerRef.current = null;
        endMarkerRef.current = null;

        // 曲線
        var latlngs = [];

        var latlng1 = polyLineData.latlngs[0];
        var latlng2 = polyLineData.latlngs[1];

        var offsetX = latlng2[1] - latlng1[1];
        var offsetY = latlng2[0] - latlng1[0];
        var r = Math.sqrt(Math.pow(offsetX, 2) + Math.pow(offsetY, 2));
        var theta = Math.atan2(offsetY, offsetX);

        var thetaOffset = 3.14 / 10;

        var r2 = r / 2 / Math.cos(thetaOffset);
        var theta2 = theta + thetaOffset;

        var midpointX = r2 * Math.cos(theta2) + latlng1[1];
        var midpointY = r2 * Math.sin(theta2) + latlng1[0];

        var midpointLatLng = [midpointY, midpointX];

        latlngs.push(latlng1, midpointLatLng, latlng2);

        var pathOptions = {
            ...basicOptions,
            color,
            weight,
            // dashArray,
            // opacity: 0.7,
            interactive: false
            // renderer: L.canvas()
        };

        // var curvedPath = L.curve(
        //     ["M", latlng1, "Q", midpointLatLng, latlng2],
        //     pathOptions
        // ).addTo(map);

        var curvedPath = L.curve(["M", latlng1, "Q", midpointLatLng, latlng2], {
            ...pathOptions,
            // dashArray: zoomDashParams,
            // dashArray: "5, 5",

            smoothFactor: 5 // Increase this value to reduce points
        }).addTo(map);

        // function createZoomAwareDashedCurve(
        //     latlng1,
        //     midpointLatLng,
        //     latlng2,
        //     options
        // ) {
        //     const dashGroup = L.featureGroup();
        //     console.log("zoomDashParams: ", zoomDashParams);
        //     function drawDashedCurve(zoom) {
        //         dashGroup.clearLayers();

        //         const [totalSegments, dashRatio] = zoomDashParams;

        //         // Calculate points along the curve
        //         const points = [];
        //         for (let i = 0; i <= totalSegments; i++) {
        //             const t = i / totalSegments;
        //             const lat =
        //                 Math.pow(1 - t, 2) * latlng1[0] +
        //                 2 * (1 - t) * t * midpointLatLng[0] +
        //                 Math.pow(t, 2) * latlng2[0];
        //             const lng =
        //                 Math.pow(1 - t, 2) * latlng1[1] +
        //                 2 * (1 - t) * t * midpointLatLng[1] +
        //                 Math.pow(t, 2) * latlng2[1];
        //             points.push([lat, lng]);
        //         }

        //         // Calculate segment sizes based on dashRatio
        //         const segmentLength = 1; // Normalized segment length
        //         const dashLength = segmentLength * dashRatio;
        //         const gapLength = segmentLength - dashLength;

        //         // Create dashes with dynamic spacing based on zoom level
        //         let currentPosition = 0;
        //         let isDash = true; // Start with a dash

        //         while (currentPosition < points.length - 1) {
        //             const segmentSize = isDash
        //                 ? Math.max(
        //                     1,
        //                     Math.round(
        //                         (dashLength * totalSegments) / segmentLength
        //                     )
        //                 )
        //                 : Math.max(
        //                     1,
        //                     Math.round(
        //                         (gapLength * totalSegments) / segmentLength
        //                     )
        //                 );

        //             // For dash segments, draw the line
        //             if (isDash) {
        //                 const startIdx = currentPosition;
        //                 const endIdx = Math.min(
        //                     points.length - 1,
        //                     currentPosition + segmentSize
        //                 );

        //                 // Create a polyline for this dash segment
        //                 const dashPoints = points.slice(startIdx, endIdx + 1);
        //                 if (dashPoints.length > 1) {
        //                     L.polyline(dashPoints, options).addTo(dashGroup);
        //                 }
        //             }

        //             // Move to the next segment
        //             currentPosition += segmentSize;
        //             isDash = !isDash;
        //         }
        //     }

        //     // Initial draw
        //     drawDashedCurve(map.getZoom());

        //     // Update on zoom
        //     map.on("zoomend", function () {
        //         drawDashedCurve(map.getZoom());
        //     });

        //     return dashGroup;
        // }
        // // Usage
        // var zoomDashCurve = createZoomAwareDashedCurve(
        //     latlng1,
        //     midpointLatLng,
        //     latlng2,
        //     {
        //         color: color,
        //         weight: weight,
        //         interactive: false
        //     }
        // ).addTo(map);

        // 直線
        // const lineOptions = {
        //     ...basicOptions,
        //     color,
        //     weight,
        //     dashArray,
        //     opacity: 0.7
        // };

        // 直線
        // const line = L.polyline(polyLineData.latlngs, lineOptions);
        // line.addTo(map);
        // lineRef.current = line; // 保存新線條的引用(直線版本)
        // 曲線
        lineRef.current = curvedPath; // 保存新線條的引用(曲線版本)

        // --- Draw Arrows (if enabled) ---
        if (showArrow) {
            try {
                const p1 = map.latLngToLayerPoint(polyLineData.startPoint);
                const p2 = map.latLngToLayerPoint(polyLineData.endPoint);

                if (p1 && p2 && p1.distanceTo(p2) > 5) {
                    const angleEnd = calculateAngle(p1, p2);
                    const angleStart = calculateAngle(p2, p1);
                    // console.log(`[PointsLine MANUAL] ID: ${lineId} | Angles - Start: ${angleStart.toFixed(1)}, End: ${angleEnd.toFixed(1)}`);

                    // --- 旋轉箭頭函數 ---
                    const applyRotationManual = (
                        markerInstance,
                        angle,
                        markerLabel
                    ) => {
                        // Added markerLabel for logging
                        markerInstance.on("add", function () {
                            const markerElement = this.getElement();
                            // console.log(`[PointsLine MANUAL] ${markerLabel} ID: ${lineId} - Marker 'add' event.`);

                            if (markerElement) {
                                // 使用 setTimeout 確保元素已穩定且 Leaflet 已應用初始 transform
                                setTimeout(() => {
                                    try {
                                        const currentTransform =
                                            markerElement.style.transform || "";
                                        // console.log(`[PointsLine MANUAL] ${markerLabel} ID: ${lineId} - Current transform: "${currentTransform}"`);

                                        // 移除舊的 rotate (如果存在)，保留 translate 部分
                                        const transformWithoutRotate = currentTransform
                                            .replace(/ rotate\([^)]+\)/g, "")
                                            .trim();

                                        // 組合新的 transform: 保留 translate/scale 等，附加 rotate
                                        const newTransform = `${transformWithoutRotate} rotate(${angle}deg)`;

                                        markerElement.style.transformOrigin =
                                            "center center"; // 確保旋轉中心正確
                                        markerElement.style.transform = newTransform;

                                        // console.log(
                                        //     `[PointsLine MANUAL] ${markerLabel} ID: ${lineId} - Applied transform: "${newTransform}"`
                                        // );
                                    } catch (e) {
                                        console.error(
                                            `[PointsLine MANUAL] ${markerLabel} ID: ${lineId} - Error in setTimeout:`,
                                            e
                                        );
                                    }
                                }, 0); // 延遲 0ms
                            } else {
                                console.warn(
                                    `[PointsLine MANUAL] ${markerLabel} ID: ${lineId} - Element NOT found on 'add'.`
                                );
                            }
                        });
                    };

                    // --- Create END marker ---
                    const markerEnd = L.marker(polyLineData.endPoint, {
                        icon: arrowIcon,
                        interactive: false,
                        keyboard: false
                    });
                    applyRotationManual(markerEnd, angleEnd, "EndMarker");
                    markerEnd.addTo(map);
                    endMarkerRef.current = markerEnd;

                    // --- Create START marker ---
                    const markerStart = L.marker(polyLineData.startPoint, {
                        icon: arrowIcon,
                        interactive: false,
                        keyboard: false
                    });
                    applyRotationManual(markerStart, angleStart, "StartMarker");
                    markerStart.addTo(map);
                    startMarkerRef.current = markerStart;
                } else {
                    // console.warn("線條太短或座標轉換失敗", {p1, p2});
                }
            } catch (error) {
                console.error("創建箭頭 Marker 時出錯:", error);
            }
        }

        return () => {
            if (lineRef.current && map.hasLayer(lineRef.current))
                lineRef.current.remove();
            if (startMarkerRef.current && map.hasLayer(startMarkerRef.current))
                startMarkerRef.current.remove();
            if (endMarkerRef.current && map.hasLayer(endMarkerRef.current))
                endMarkerRef.current.remove();
            lineRef.current = null;
            startMarkerRef.current = null;
            endMarkerRef.current = null;
        };
    }, [
        map,
        polyLineData,
        showArrow,
        color,
        weight,
        dashArray,
        arrowIcon,
        basicOptions,
        lineId,
        mapVersion,
        zoomDashParams
    ]);

    return null;
};

export default PointsLineWithRotatedMarker;
