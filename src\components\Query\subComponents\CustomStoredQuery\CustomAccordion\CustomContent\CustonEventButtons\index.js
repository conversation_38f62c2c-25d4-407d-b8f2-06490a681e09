// react
import React from "react";

// ui
import { Container } from "semantic-ui-react";

// custom
import CustomLoadButton from "./CustomLoadButton";
import CustonDeleteButton from "./CustomDeleteButton";

const CustonEventButtons = ({ docId, authorId }) => {
    return (
        <Container>
            <CustomLoadButton docId={docId} authorId={authorId} />
            <CustonDeleteButton docId={docId} authorId={authorId} />
        </Container>
    );
};

export default CustonEventButtons;
