import React, { useContext } from "react";
import { makeStyles } from "@material-ui/core/styles";
import Pagination from "@material-ui/lab/Pagination";
import "../../../../style/searchPage.css";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";
const useStyles = makeStyles(theme => ({
    root: {
        "& > *": {
            marginTop: theme.spacing(2)
        }
    }
}));

function PaginationBar(props) {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { pageNumber } = state.searchPage;
    let totalPage = Number.isInteger(props.count / 5)
        ? Math.floor(props.count / 5)
        : Math.floor(props.count / 5) + 1; // 分頁總數，每五筆資料一頁
    const handleChange = (event, value) => {
        dispatch({
            // 設定搜尋的資料總比數
            type: act.SET_PAGENUMBER,
            payload: value
        });
    };

    return (
        <div className={classes.root}>
            <Pagination
                count={totalPage}
                shape="rounded"
                onChange={handleChange}
                page={pageNumber}
            />
        </div>
    );
}

export default PaginationBar;
