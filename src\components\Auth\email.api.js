import axios from "axios";
// import urls from "../api/hkbdb/urls";
import { Api } from "../../api/hkbdb/Api";
import emailConfig from "../../config/config-email";

export async function createEmail(sendMail, timeout = 5000) {
    // timeout
    axios.defaults.timeout = timeout;
    try {
        // axios api
        return axios
            .post(Api.sentEmail(), { sendMail })
            .then(res => ({ state: res?.status === 200 }))
            .catch(err =>
                // console.error('api:axios:catch:error: ', err.message);
                ({ state: false, error: err.message })
            );
    } catch (err) {
        // console.error('api:axios:try:catch:error: ', err.message);
        return { state: false, error: err.message };
    }
}

export const MAIL_TEMPLATE = {
    userSignup: {
        name: "userSignup",
        label: "使用者註冊",
        editMode: true,
        deleteMode: false,
        // eslint-disable-next-line no-use-before-define
        sendMail: mailUserSignup,
        subject: () => `[香港作家及藝術家傳記資料庫]使用者註冊`,
        context: ({ displayName, email, role, originRole }) =>
            `您好，以下資訊是使用者註冊通知\n\n主旨:使用者註冊\n使用者名稱: ${displayName}\nEmail: ${email}\n請至管理平台審核用戶申請\n\n"此信件由系統自動轉發，請勿直接回覆"`
    }
};

export const mailMain = template => (emilInfo, fromEmail, toEmail) => {
    const { displayName, email, subject, text } = emilInfo;
    if (!displayName || !email) {
        return console.error("mailMain: displayName && email is required");
    }
    const findTpl = MAIL_TEMPLATE?.[template];
    const subjectDef = findTpl ? findTpl?.subject() : "";
    const textDef = findTpl ? findTpl?.context(emilInfo) : "";

    const targetEmail = toEmail || emailConfig.cuhk; // 通知 cuhk 管理者
    const thisSubject = subject || subjectDef;
    const thisText = text || textDef;

    if (!thisSubject || !thisText) {
        return console.error("subject & text must not be blank");
    }

    const sendMail = {
        from: fromEmail || emailConfig.daoyidhNoReply,
        to: targetEmail,
        subject: thisSubject,
        text: thisText
        // html: ""  // todo: 待完成編輯 email 功能後可以植入 html 純文字
    };

    return createEmail(sendMail);
};

// 用戶註冊
export function mailUserSignup(emilInfo, fromEmail, toEmail) {
    return mailMain(MAIL_TEMPLATE.userSignup.name)(
        emilInfo,
        fromEmail,
        toEmail
    );
}

export const findSendMailByName = name =>
    (name && MAIL_TEMPLATE?.[name]) || null;
