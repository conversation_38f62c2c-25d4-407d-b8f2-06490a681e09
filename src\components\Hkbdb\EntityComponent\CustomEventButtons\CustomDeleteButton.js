import React, { useContext, useState } from "react";

// intl
import { FormattedMessage } from "react-intl";

// ui
import { Button, Modal, Message, Popup } from "semantic-ui-react";

// api
import { Api, deleteHkbdbData } from "../../../../api/hkbdb/Api";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import {
    // bs64Encode,
    isNotEmpty,
    isTrue
} from "../../../../common/codes";

// auth
import authority from "../../../../App-authority";

// helper
import { displayInstanceName } from "../helper";
import { CLASS_NAME, CLASS_PREFIX } from "../../../../config/config-ontology";
import {
    decodeURIComponentSafe,
    idToUriEncBs64EncId
} from "../../../../common/codes/jenaHelper";

const CustomDeleteButton = ({ type, id, name }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { uid, role } = user;
    //
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    //
    const handleOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };
    const handleDelete = async () => {
        setIsLoading(true);
        //
        // name有可能是空白字串，不做判斷
        // console.log(`delete ${name}`);
        let prefix = "";
        if (type === CLASS_NAME.Person) {
            prefix = CLASS_PREFIX.Person;
        } else if (type === CLASS_NAME.Organization) {
            prefix = CLASS_PREFIX.Organization;
        }
        const entry = {
            srcId: `${prefix}${idToUriEncBs64EncId(id)}`
        };
        //
        const result = await deleteHkbdbData(Api.deletePerson(), entry, user);
        //
        if (result.state) {
            // todo remove all
            //
            dispatch({
                type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                payload: {
                    target: "organization",
                    signal: `delete-${new Date().getTime()}`
                }
            });
            // close
            handleClose();
        }
        //
        setIsLoading(false);
    };
    //
    const customButtonStyle = {
        // backgroundColor: "rgb(0 0 0 / 10%)",
        marginLeft: ".8em",
        marginTop: "1em",
        marginBottom: "1em",
        padding: ".3em"
    };

    if (
        isTrue(process.env.REACT_APP_CRUD_NODE) &&
        isNotEmpty(uid) &&
        authority.People_Information.includes(role)
    ) {
        return (
            <div>
                <Modal
                    size="tiny"
                    open={open}
                    onOpen={handleOpen}
                    onClose={handleClose}
                    dimmer="inverted"
                >
                    <Modal.Header>
                        <FormattedMessage
                            id={"people.Information.delete.person"}
                            defaultMessage={`Delete ｢{name}」`}
                            values={{
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                )
                            }}
                        />
                    </Modal.Header>
                    <Modal.Content image>
                        <Modal.Description>
                            <Message
                                error
                                content={
                                    <FormattedMessage
                                        id={
                                            "organization.Information.delete.message"
                                        }
                                        defaultMessage={
                                            "The {name} will be permanently deleted. {br}Are you sure you want to delete?"
                                        }
                                        values={{
                                            br: <br />,
                                            name: `「${displayInstanceName(
                                                decodeURIComponentSafe(id),
                                                name,
                                                Api.getLocale()
                                            )}」`
                                        }}
                                    />
                                }
                            />
                        </Modal.Description>
                    </Modal.Content>
                    <Modal.Actions>
                        <Button onClick={handleClose}>
                            <FormattedMessage
                                id={"people.Information.delete.cancel"}
                                defaultMessage={"Cancel"}
                            />
                        </Button>
                        <Button
                            loading={isLoading}
                            onClick={handleDelete}
                            color="red"
                        >
                            <FormattedMessage
                                id={"people.Information.delete.person"}
                                defaultMessage={`Delete ｢{name}」`}
                                values={{
                                    name: displayInstanceName(
                                        decodeURIComponentSafe(id),
                                        name,
                                        Api.getLocale()
                                    )
                                }}
                            />
                        </Button>
                    </Modal.Actions>
                </Modal>
                <Popup
                    content={
                        <FormattedMessage
                            id={"people.Information.delete.person"}
                            defaultMessage={`Delete ｢{name}」`}
                            values={{
                                name: displayInstanceName(
                                    decodeURIComponentSafe(id),
                                    name,
                                    Api.getLocale()
                                )
                            }}
                        />
                    }
                    key={"delete"}
                    trigger={
                        <Button
                            style={customButtonStyle}
                            color="red"
                            size="mini"
                            icon="delete"
                            onClick={handleOpen}
                        />
                    }
                />
            </div>
        );
    } else {
        return null;
    }
};

export default CustomDeleteButton;
