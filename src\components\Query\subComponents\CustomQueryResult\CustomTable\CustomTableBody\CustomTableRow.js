// react
import React, { useContext } from "react";

// ui
import { Message, Table } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../common/codes";

const CustomTableRow = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // get query from state
    const { queryResult } = state.query;
    const { data, head, error } = queryResult;
    //
    const fixTableHeader = {
        position: "sticky",
        top: "0"
    };
    //
    const TableRow = () => {
        if (error) {
            return (
                <Table.Row style={fixTableHeader}>
                    <Table.Cell colSpan="3">
                        <Message warning>{error}</Message>
                    </Table.Cell>
                </Table.Row>
            );
        } else if (isEmpty(data)) {
            return (
                <Table.Row style={fixTableHeader}>
                    <Table.Cell colSpan="3">
                        <Message hidden>hidden content</Message>
                    </Table.Cell>
                </Table.Row>
            );
        } else {
            return data.map((item, idx) => {
                // handle table cells
                const cells = head.map((head, idx) => (
                    <Table.Cell key={`table-cell-${idx}-${head}`}>
                        {item[head] || ""}
                    </Table.Cell>
                ));
                // return result table
                return (
                    <Table.Row key={`table-row-${idx}`} style={fixTableHeader}>
                        {cells}
                    </Table.Row>
                );
            });
        }
    };
    //
    return <TableRow />;
};

export default CustomTableRow;
