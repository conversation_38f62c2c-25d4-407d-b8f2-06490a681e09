import React from "react";

import { isEmpty, safeGet } from "../../../../common/codes";

const useObjToRows = object => {
    const [data, setData] = React.useState([]);
    React.useEffect(() => {
        if (object) {
            setData(
                Object.keys(object).reduce((prev, rowId) => {
                    const item = safeGet(object, [rowId], {});
                    return [...prev, item];
                }, [])
            );
        }
    }, [JSON.stringify(object)]);
    return data;
};

export default useObjToRows;
