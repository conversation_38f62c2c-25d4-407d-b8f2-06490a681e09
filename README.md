# hkbdb-web

hkbdb-web is fontend part of hkbdb project.

## How to satrt

- Check node.js version
```
node -v
```
node.js version must be v12 because of node-sass package compatibility.

more info on: https://www.npmjs.com/package/node-sass

- clone project
```
<NAME_EMAIL>:daoyidh/hkbdb-web.git
```
or
```
git clone https://gitlab.com/daoyidh/hkbdb-web.git
```
- install modules
```
yarn install
```
- start client server
```
yarn run client
```


## E2E test with playwright

requirement: node version >= 16
if node version is lower than 16, will get error message like this: 
```shell
Error in reporter TypeError: Intl.Segmenter is not a constructor
```

### Start test
```
npx playwright test
```

### Report
```
npx playwright show-report
```


## Contributing



## License

