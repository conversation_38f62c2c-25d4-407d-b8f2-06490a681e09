// react
import React, { useContext } from "react";

// ui
import { Accordion } from "semantic-ui-react";

// custom
import CustomContent from "./CustomContent";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// common
import { isEmpty, isNotEmpty } from "../../../../../common/codes";

const CustomPrivateAccordion = () => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { uid } = state.user;
    const { queries, searchString } = state.query;
    //
    let privatePanels = [];
    //
    if (!isEmpty(uid)) {
        privatePanels = queries
            // filter if private is true
            .filter(query => query?.author?.uid === uid)
            // filter if search keyword is exist
            .filter(privateQuery => {
                const { displayName, email } = privateQuery.author;
                const { en: titleEn, zh: titleZh } = privateQuery.title;
                if (isEmpty(searchString)) {
                    return true;
                } else {
                    const reg = new RegExp(searchString, "i");
                    return (
                        email.search(reg) !== -1 ||
                        displayName.search(reg) !== -1 ||
                        titleEn.search(reg) !== -1 ||
                        titleZh.search(reg) !== -1
                    );
                }
            })
            .sort((a, b) => {
                let titleA, titleB;
                if (isNotEmpty(a?.title.zh)) {
                    titleA = a?.title?.zh?.toUpperCase() || ""; // ignore upper and lowercase
                    titleB = b?.title?.zh?.toUpperCase() || ""; // ignore upper and lowercase
                } else {
                    titleA = a?.title?.en?.toUpperCase() || ""; // ignore upper and lowercase
                    titleB = b?.title?.en?.toUpperCase() || ""; // ignore upper and lowercase
                }
                if (titleA < titleB) return -1;
                if (titleA > titleB) return 1;
                // names must be equal
                return 0;
            })
            .map((privateQuery, idx) => {
                const {
                    id: docId,
                    query: queryString,
                    author,
                    title
                } = privateQuery;
                const { en: titleEn, zh: titleZh } = title;
                const { uid: authorId, displayName, email } = author;
                return {
                    key: `private-panel-${idx}-${docId}`,
                    title: titleEn,
                    content: {
                        content: (
                            <CustomContent
                                docId={docId}
                                authorId={authorId}
                                query={queryString}
                                email={email}
                                displayName={displayName}
                                translation={titleZh}
                            />
                        )
                    }
                };
            });
    }
    //
    const customDivStyle = {
        overflowY: "auto",
        maxHeight: "550px",
        padding: ".1em"
    };
    //
    return (
        <div style={customDivStyle}>
            <Accordion.Accordion panels={privatePanels} />
        </div>
    );
};

export default CustomPrivateAccordion;
