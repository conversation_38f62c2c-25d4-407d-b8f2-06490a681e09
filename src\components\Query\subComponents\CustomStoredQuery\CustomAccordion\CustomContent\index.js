// react
import React from "react";

// ui
import { Divider, Container } from "semantic-ui-react";

// custom
import CustomEventButtons from "./CustonEventButtons";
import CustomPre from "./CustomPre";
import CustomInfoSpan from "./CustomInfoSpan";

const CustomContent = ({
    docId,
    authorId,
    query,
    displayName,
    translation,
    email
}) => {
    return (
        <Container>
            <CustomPre query={query} />
            <Divider />
            <CustomInfoSpan
                email={email}
                translation={translation}
                displayName={displayName}
            />
            <Divider />
            <CustomEventButtons docId={docId} authorId={authorId} />
        </Container>
    );
};

export default CustomContent;
