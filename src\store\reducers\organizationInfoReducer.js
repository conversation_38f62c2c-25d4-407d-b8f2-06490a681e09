import Act from "../actions";

const initState = {
    results: {
        vars: [],
        bindings: []
    },
    // timelineData stands for person or organization.
    timelineData: {
        vars: null, // []
        bindings: null // []
    },
    familyTreeData: {
        indis: [],
        fams: []
    },
    SNAData: {
        nodes: [],
        links: []
    },
    fetchSnaDataStatus: {
        step1: false,
        step2: false
    },
    step1SnaData: {
        nodes: [],
        links: []
    },
    step2SnaData: {
        nodes: [],
        links: []
    },
    depth: 1,
    genealogy: "",
    persons: null,
    graphs: null,
    birthDate: null,
    deathDate: null,
    fetchDataLoading: true
};

const organizationInfoReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.USER_CLEAR_CACHE:
            return {
                ...initState
            };
        case Act.USER_FETCH_INFO:
            return {
                ...state,
                ...action.payload
            };
        case Act.USER_FETCH_INFO_LOADING:
            return {
                ...state,
                fetchDataLoading: action.payload
            };
        case Act.USER_FETCH_TIMELINE_DATA:
            return {
                ...state,
                timelineData: action.payload.timelineData
            };
        case Act.USER_FETCH_SNA_DATA:
            return {
                ...state,
                SNAData: action.payload
            };
        case Act.SET_SNA_DEPTH:
            return {
                ...state,
                depth: action.payload
            };
        case Act.USER_FETCH_GENEALOGY:
            return {
                ...state,
                genealogy: action.payload
            };
        case Act.FETCH_SNA_DATA_STATUS:
            return {
                ...state,
                fetchSnaDataStatus: action.payload
            };
        case Act.SET_SETP1_SNA_DATA:
            return {
                ...state,
                step1SnaData: action.payload
            };
        case Act.SET_SETP2_SNA_DATA:
            return {
                ...state,
                step2SnaData: action.payload
            };
        default:
            return state;
    }
};

export default organizationInfoReducer;
