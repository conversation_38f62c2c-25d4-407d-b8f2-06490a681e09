// import { FETCH_FIREBASE_DATA_SUCCESS } from './firebaseAction';
import { FETCH_FIREBASE_DATA_SUCCESS } from "./firebaseAction";

const INITIAL_STATE = {
    visits: null,
    wordCloud: null
};

const fbReducer = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case FETCH_FIREBASE_DATA_SUCCESS: {
            return { ...state, ...action.payload };
        }
        default:
            return state;
    }
};

export default fbReducer;
