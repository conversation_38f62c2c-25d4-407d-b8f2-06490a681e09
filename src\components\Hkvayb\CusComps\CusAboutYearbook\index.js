import React, { useContext } from "react";

import { useHistory } from "react-router-dom";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusTitle from "../CusTitle";
import CusButton from "../CusButton";
import CusMarkdown from "../CusMarkdown";

import { StoreContext } from "../../../../store/StoreProvider";

const CusAboutYearbook = ({ data }) => {
    const classes = useStyles();
    const history = useHistory();
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    const handleAbout = () => {
        history.push(`/${locale}/HkvaybAbout`);
    };

    return (
        <div className={classes.hkvayb_about_yearbook}>
            <div className={classes.hkvayb_about_yearbook_image} />
            <div>
                <CusTitle
                    label={
                        <FormattedMessage
                            id="hkvayb.search.about.yearbook"
                            defaultMessage="About the Yearbook"
                        />
                    }
                />
                <div className={classes.hkvayb_about_yearbook_describe}>
                    <CusMarkdown value={data} />
                </div>
                <CusButton
                    borderInvert
                    label={
                        <FormattedMessage
                            id="hkvayb.search.about.more"
                            defaultMessage="more"
                        />
                    }
                    onClick={handleAbout}
                />
            </div>
        </div>
    );
};

export default CusAboutYearbook;
