import React from "react";
import useStyles from "./style";
import { v4 } from "uuid";
import PropTypes from "prop-types";

const CusModalTable = ({ content }) => {
    const classes = useStyles();

    return content.map(item => {
        return (
            <div key={v4()}>
                <table>
                    <tbody>
                        <tr>
                            <td className={classes.hkvayb_tableTitle}>
                                {item.title}
                            </td>
                            <td className={classes.hkvayb_tableContent}>
                                {item.perContent}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        );
    });
};

CusModalTable.defaultProps = {
    content: []
};

CusModalTable.propTypes = {
    content: PropTypes.array
};

export default CusModalTable;
