// react
import React, { useState, useContext, useEffect } from "react";

// ui
import { Modal, Button, Icon, Form, TextArea, Radio } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// firebase api
import cloudStorage from "../../../../../../api/firebase/cloudFirestore/Api";

// common
import { isEmpty } from "../../../../../../common/codes";
import { FormattedMessage, injectIntl } from "react-intl";

const CustomUpdateButton = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // get query from state
    const { user, query } = state;
    const { uid, displayName, email } = user;
    const { queryString, selectedQueryId, queries } = query;
    // modal switch
    const [open, setOpen] = useState(false);
    const [titleEn, setTitleEn] = useState("");
    const [titleZh, setTitleZh] = useState("");
    const [isPrivate, setIsPrivate] = useState(false);
    // handle close modal
    const handleClose = () => {
        // back to default
        setOpen(false);
        setTitleEn("");
        setTitleZh("");
        setIsPrivate(false);
        // clean query Id
        dispatch({ type: Act.QUERY_SELECTED_QUERY_ID_CEL });
    };
    // handle open modal
    const handleOpen = () => {
        if (isEmpty(selectedQueryId)) {
            setTitleEn("");
            setTitleZh("");
            setIsPrivate(false);
        }
        setOpen(true);
    };
    // handle TitleEn Change
    const handleTitleEnChange = (event, { value }) => {
        setTitleEn(value);
    };
    // handle TitleZh Change
    const handleTitleZhChange = (event, { value }) => {
        setTitleZh(value);
    };
    // handle isPrivate Change
    const handleIsPrivate = () => {
        setIsPrivate(!isPrivate);
    };
    // handleSave
    const handleSave = async () => {
        // check params
        if (isEmpty(uid)) {
            console.error("handleSave param error: uid not exist.");
            return;
        }
        if (isEmpty(queryString)) {
            console.error("handleSave param error: queryString not exist.");
            return;
        }
        if (isEmpty(titleEn) && isEmpty(titleZh)) {
            console.error("handleSave param error: title not exist.");
            return;
        }
        // doc params
        const doc = {
            query: queryString,
            author: {
                uid,
                displayName,
                email
            },
            title: {
                en: titleEn,
                zh: titleZh
            },
            private: isPrivate,
            timestamp: new Date().getTime()
        };
        // insert data
        const success = await cloudStorage.updateQuery(doc, selectedQueryId);
        if (success) {
            // update queryReducer state
            dispatch({
                type: Act.QUERY_RELOAD_RENDER_SIGNAL_SET,
                payload: `reload-queries-${new Date().getTime()}`
            });
            // clean query Id
            // dispatch({ type: Act.QUERY_SELECTED_QUERY_ID_CEL });
            // done & close
            handleClose();
        } else {
            console.log("insert query undone");
        }
    };
    //
    const handleLoadSelectedQuery = () => {
        const selectedQuery = queries.filter(
            query => query.id === selectedQueryId
        )[0];
        if (!isEmpty(selectedQuery)) {
            if (selectedQuery.title.en) {
                setTitleEn(selectedQuery.title.en);
            }
            if (selectedQuery.title.zh) {
                setTitleZh(selectedQuery.title.zh);
            }
            if (selectedQuery.private) {
                setIsPrivate(selectedQuery.private);
            }
        }
    };
    //
    useEffect(() => {
        handleLoadSelectedQuery();
    }, [selectedQueryId]);
    //
    return (
        <Modal
            open={open}
            onClose={handleClose}
            onOpen={handleOpen}
            trigger={
                <Button size="tiny" disabled={isEmpty(queryString)}>
                    <span>
                        <FormattedMessage
                            id="custom.updateButton"
                            defaultMessage="Update"
                        />
                    </span>
                </Button>
            }
        >
            <Modal.Header>Stored query</Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description>
                    <Form>
                        <Form.Field>
                            <label>Query title (en)</label>
                            <TextArea
                                placeholder="Query title in English"
                                onChange={handleTitleEnChange}
                                value={titleEn}
                            />
                        </Form.Field>
                        <Form.Field>
                            <label>Query title (zh)</label>
                            <TextArea
                                placeholder="Query title in Chinese"
                                onChange={handleTitleZhChange}
                                value={titleZh}
                            />
                        </Form.Field>
                        <Form.Field>
                            <Radio
                                toggle
                                label="Private"
                                checked={isPrivate}
                                onChange={handleIsPrivate}
                            />
                        </Form.Field>
                    </Form>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleClose} primary>
                    <Icon name="close" />{" "}
                    <FormattedMessage
                        id="custom.cancelButton"
                        defaultMessage="Cancel"
                    />
                </Button>
                <Button color="green" onClick={handleSave}>
                    <Icon name="checkmark" />
                    <span>
                        <FormattedMessage
                            id="custom.updateButton"
                            defaultMessage="Update"
                        />
                    </span>
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default injectIntl(CustomUpdateButton);
