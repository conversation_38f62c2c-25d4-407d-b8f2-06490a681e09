import React, { useState, useEffect, useContext } from "react";

// PropTypes
import PropTypes from "prop-types";

// intl
import { injectIntl } from "react-intl";
//
import queryString from "query-string";
// ui
import {
    Container,
    Button,
    Input,
    Icon,
    Sidebar,
    Popup,
    Header,
    Divider
} from "semantic-ui-react";
import Box from "@material-ui/core/Box";
// custom
import CustomDeleteButton from "../EntityComponent/CustomEventButtons/CustomDeleteButton";
import CustomCreateButton from "../EntityComponent/CustomEventButtons/CustomCreateButton";
import CustomHideButton from "../EntityComponent/CustomEventButtons/CustomHideButton";
import CustomMergeButton from "../EntityComponent/CustomEventButtons/CustomMergeButton";
import CustomDuplicateButton from "../EntityComponent/CustomEventButtons/CustomDuplicateButton";
// component
import AvatarHOC from "../EntityComponent/AvatarHOC";
import AvatarModal from "../EntityComponent/AvatarModal";
// hook
import useFetchPortrait from "../EntityComponent/customHook/useFetchPortrait";
// content
import PageContent from "./PageContent";
// RWD
import { ResponsiveContainer } from "../../../layout/Layout";
// common
import { handleNameSpellCheck, bs64Decode } from "../../../common/codes";
// custom search
import { findEntityByKeyword } from "../../BrowsePage/subComponents/entitySearch";
import { queryBestKnownName } from "./action";
// custom result
import ResultsEntity from "../../BrowsePage/subComponents/entitySearch/ResultsEntity";
// store
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
// style
import useStyles from "../EntityComponent/style/pageStyle";
// helper
import { displayInstanceName } from "../EntityComponent/helper";
// Api
import { Api, FUSEKI_ON } from "../../../api/hkbdb/Api";
// config
import { CLASS_PREFIX, CLASS_NAME } from "../../../config/config-ontology";
import config from "../../../config/config";
// jenaHelper
import {
    bs64EncIdToUriEncBs64EncId,
    bs64EncIdToUriEncId,
    decodeURIComponentSafe
} from "../../../common/codes/jenaHelper";
import ROLE from "../../../App-role";

const SidebarButton = ({ onClick, sidebarVisible }) => {
    return (
        <Button
            type="button"
            onClick={() => {
                if (onClick) onClick();
            }}
            icon
            style={{
                padding: "0em 0.7em",
                height: "40px",
                fontSize: "18px"
            }}
        >
            {sidebarVisible ? (
                <Popup content="Close" trigger={<Icon name="close" />} />
            ) : (
                <Popup
                    content="Search Person"
                    trigger={<Icon name="search" />}
                />
            )}
        </Button>
    );
};

// 依據人物或組織而異動
const CUSTOM_CLASS_PREFIX = CLASS_PREFIX.Person;
const CUSTOM_CLASS_NAME = CLASS_NAME.Person;

const PeoplePage = props => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { role } = state.user;
    const { renderSignal, renderTarget } = state.information;
    //
    const [personId, setPersonId] = useState(""); // 移除 prefix 的 instance id
    const [personName, setPersonName] = useState("");
    const personImgSrcCircle = useFetchPortrait(
        personId,
        "400x400",
        true,
        Api.getPersonPortraitCircle(),
        Api.getPersonPortrait()
    );
    const personImgSrcSquare = useFetchPortrait(
        personId,
        "800x800",
        false,
        Api.getPersonPortraitCircle(),
        Api.getPersonPortrait()
    );
    const [searchValue, setSearchValue] = useState("");
    const [searchResults, setSearchResults] = useState(null);
    const [loading, setLoading] = useState(true);
    const [sidebarVisible, setSidebarVisible] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    // style
    const classes = useStyles();
    //
    const {
        match: {
            params: { name }
        },
        history: {
            location: { pathname, search }
        },
        user: { permission },
        intl
    } = props;

    useEffect(() => {
        // url 的 name 是 person id 移除 "PER" 的 bs64encode 結果
        // 例如, PERJohn_F._JONES => 移除 "PER"  & encode => Sm9obl9GLl9KT05FUw
        const _personId = handleNameSpellCheck(name);

        // 可以依據 fuseki 開啟狀態來 uriEncode id(on/off) & bs64encode(on)
        // 使用 fuseki 資料庫(所有 id 已經 uriEncode)
        // 6YeR5bq4 => 金庸 => %E9%87%91%E5%BA%B8 => JUU5JTg3JTkxJUU1JUJBJUI4
        // 使用 stardog 資料庫(所有 id 沒有 uriEncode)
        // 6YeR5bq4 => 金庸 => 6YeR5bq4
        const encodeId = bs64EncIdToUriEncBs64EncId(_personId);

        // set personId to information state
        dispatch({
            type: Act.INFORMATION_PERSON_ID_SET,
            payload: `${CUSTOM_CLASS_PREFIX}${encodeId}`
        });
        const { name: bestKnowName } = queryString.parse(search || "");
        if (bestKnowName) {
            setPersonName(bs64Decode(bestKnowName));
        }
        let decodeName = "";
        if (name !== undefined) {
            // 可以依據 fuseki 開啟狀態來 uriEncode id(on/off)
            // 使用 fuseki 資料庫(所有 id 已經 uriEncode)
            // 6YeR5bq4 => 金庸 => %E9%87%91%E5%BA%B8
            // 使用 stardog 資料庫(所有 id 沒有 uriEncode)
            // 6YeR5bq4 => 金庸
            decodeName = bs64EncIdToUriEncId(name);
            setPersonId(decodeName);
        }
        if (decodeName) {
            queryBestKnownName(
                renderTarget || null,
                `${CUSTOM_CLASS_PREFIX}${decodeName}`,
                bs64Decode(bestKnowName || ""),
                setPersonName
            );
        } else {
            setPersonName("");
        }
    }, [name, pathname, search, renderSignal, renderTarget]);

    useEffect(() => {
        // 預先搜尋
        send();
        // 離開頁面時,清空資料
        return () => {
            dispatch({
                type: Act.USER_CLEAR_CACHE
            });
        };
    }, []);

    //
    const onSearchChange = (event, data) => {
        // input 變更時，立即更新 search value
        // 去掉頭尾 空白
        setSearchValue(data.value.trim());
    };

    const send = () => {
        setLoading(true);

        findEntityByKeyword(CUSTOM_CLASS_NAME, searchValue)
            .then(res => {
                setSearchResults(
                    Object.assign(
                        {},
                        {
                            // bindings: entityProcessing(res.data, className),
                            bindings: res?.data || [],
                            durationSS: res.durationSS
                        }
                    )
                );
            })
            .catch(() => {
                setSearchResults({
                    vars: [],
                    bindings: []
                });
            })
            .finally(() => {
                setLoading(false);
                setIsEditing(false);
            });
    };

    const onKeyUp = e => {
        if (e.key === "Enter") {
            send();
        }
    };

    const showSearchMessage = results => {
        return results && results.bindings;
    };

    const handleSidebarBtnClick = () => {
        setSidebarVisible(!sidebarVisible);
    };

    return (
        <ResponsiveContainer {...props}>
            <Container>
                <Sidebar.Pushable>
                    <Sidebar
                        as="div"
                        animation="push"
                        direction="left"
                        icon="labeled"
                        visible={sidebarVisible}
                        width="wide"
                        style={{ paddingRight: "1em" }}
                    >
                        <Input
                            fluid
                            type="text"
                            placeholder={intl.formatMessage({
                                id: "browse.inputForSearching",
                                defaultMessage: "Input for searching"
                            })}
                            action
                            onChange={onSearchChange}
                            onKeyUp={onKeyUp}
                        >
                            <input disabled={loading} />
                            <Button type="submit" onClick={send} icon>
                                <Icon name="search" />
                            </Button>
                        </Input>
                        <ResultsEntity
                            showSearchMessage={showSearchMessage(searchResults)}
                            loading={loading}
                            results={searchResults}
                            classType={CUSTOM_CLASS_NAME}
                            style={classes.resultsEntity}
                            searchValue={searchValue}
                            isEditing={isEditing}
                            useShowBtn={false}
                        />
                    </Sidebar>
                    <Sidebar.Pusher>
                        <Box display="flex" alignItems="center">
                            <SidebarButton
                                sidebarVisible={sidebarVisible}
                                onClick={handleSidebarBtnClick}
                            />
                            {/* avatar */}
                            <AvatarHOC
                                personImgSrc={personImgSrcCircle}
                                type={CUSTOM_CLASS_NAME.toLowerCase()}
                                onClick={() =>
                                    personImgSrcSquare &&
                                    personImgSrcSquare !== "" &&
                                    setModalOpen(true)
                                }
                            />
                            <AvatarModal
                                _open={modalOpen}
                                personName={personName}
                                imgUrl={personImgSrcSquare}
                                handleClose={() => setModalOpen(false)}
                            />
                            <div className={classes.headerContainer}>
                                <Header
                                    as={"h1"}
                                    className={classes.nameHeader}
                                >
                                    {displayInstanceName(
                                        decodeURIComponentSafe(personId),
                                        personName,
                                        Api.getLocale()
                                    )}
                                </Header>
                            </div>
                            {role !== ROLE.suggester && (
                                <Box display="flex">
                                    <CustomDeleteButton
                                        type={CUSTOM_CLASS_NAME}
                                        id={personId}
                                        name={personName}
                                    />
                                    <CustomCreateButton
                                        type={CUSTOM_CLASS_NAME}
                                        entitySetting={
                                            config.entity[CUSTOM_CLASS_NAME]
                                        }
                                    />
                                    <CustomHideButton
                                        type={CUSTOM_CLASS_NAME}
                                        id={personId}
                                        name={personName}
                                    />
                                    <CustomDuplicateButton
                                        type={CUSTOM_CLASS_NAME}
                                        id={personId}
                                        name={personName}
                                        entitySetting={
                                            config.entity[CUSTOM_CLASS_NAME]
                                        }
                                    />
                                    <CustomMergeButton
                                        type={CUSTOM_CLASS_NAME}
                                        id={personId}
                                        name={personName}
                                        entitySetting={
                                            config.entity[CUSTOM_CLASS_NAME]
                                        }
                                    />
                                </Box>
                            )}
                        </Box>
                        <Divider />
                        <PageContent
                            name={personId}
                            permission={permission}
                            className={CUSTOM_CLASS_NAME}
                        />
                    </Sidebar.Pusher>
                </Sidebar.Pushable>
            </Container>
        </ResponsiveContainer>
    );
};

PeoplePage.propTypes = {
    match: PropTypes.shape({
        params: PropTypes.shape({
            name: PropTypes.string
        })
    }),
    user: PropTypes.shape({
        permission: PropTypes.number
    }),
    history: PropTypes.object
};
export default injectIntl(PeoplePage);
