import { isEmpty, exportImage, genRandom<PERSON>ey } from "../../../common/codes";

const objectProperty = "ObjectProperty";
const dataProperty = "DatatypeProperty";
const bothProperty = "Both";
const inverseProperty = "inverse";
// download
const fileDownload = require("react-file-download");

// const ontoProtege = "protege";
const ontoDb = "db";

const isInNodes = (_nodes, nodeName) => {
    return _nodes.find(n => {
        return n.id === nodeName;
    });
};

const isInLinks = (_links, domain, range) => {
    return _links.find(n => {
        return n.source === domain && n.target === range;
    });
};

const creaateSameNodes = _ontology => {
    return _ontology.map(o => {
        if (o.domain === o.range) {
            if (o.hasOwnProperty(inverseProperty)) {
                o.domain = "#" + o.domain;
            } else {
                o.range = "#" + o.range;
            }
        }
        return o;
    });
};

const addLink = (_links, { source, property, target, type, ontology }) => {
    const foundLink = isInLinks(_links, source, target);
    if (!foundLink) {
        const link = {
            source: source,
            property: property,
            target: target,
            type: type,
            ontology: ontology,
            value: 1
        };
        _links.push(link);
    } else {
        if (!foundLink.property.includes(property)) {
            foundLink.property += `\n${property}`;
        }
    }
};

const addNode = (_nodes, _classNameArr, { id, type, ontology }) => {
    const foundNode = isInNodes(_nodes, id);
    if (!foundNode) {
        if (_classNameArr.indexOf(id) >= 0) {
            _nodes.push({
                id: id,
                type: bothProperty,
                ontology: ontology
            });
        } else {
            _nodes.push({
                id: id,
                type: type,
                ontology: ontology
            });
        }
    }
};

const applyObjectProperty = (_nodes, _links, o, _classNameArr) => {
    // nodes
    addNode(_nodes, _classNameArr, {
        id: o.domain,
        type: o.type,
        ontology: o.ontology
    });

    addNode(_nodes, _classNameArr, {
        id: o.range,
        type: o.type,
        ontology: o.ontology
    });

    // links
    addLink(_links, {
        source: o.domain,
        property: o.op,
        target: o.range,
        type: o.type,
        ontology: o.ontology,
        value: 1
    });

    // inverse
    if (o.hasOwnProperty(inverseProperty)) {
        addLink(_links, {
            source: o.range,
            property: o.inverse,
            target: o.domain,
            type: o.type,
            ontology: o.ontology,
            value: 1
        });
    }
};

const applyDataProperty = (_nodes, _links, o) => {
    // nodes
    addNode(_nodes, [], {
        id: o.domain,
        type: o.type,
        ontology: o.ontology
    });

    addNode(_nodes, [], {
        id: o.op,
        type: o.type,
        ontology: o.ontology
    });

    // links
    addLink(_links, {
        source: o.domain,
        property: o.range,
        target: o.op,
        type: o.type,
        ontology: o.ontology,
        value: 1
    });
};

const dataToNodesLinks = (_ontology, _classNameArr) => {
    const _nodes = [];
    const _links = [];

    // create the nodes which source == target
    const _newOntology = creaateSameNodes(_ontology);

    // create nodes and links
    _newOntology.forEach(o => {
        if (o.type === objectProperty) {
            applyObjectProperty(_nodes, _links, o, _classNameArr);
        } else if (o.type === dataProperty) {
            applyDataProperty(_nodes, _links, o);
        }
    });

    return { nodes: _nodes, links: _links };
};

const hideShowCtrl = (checked, ele) => {
    if (
        checked === objectProperty &&
        ele.attributes.hasOwnProperty("key") &&
        ele.attributes.key.value === dataProperty
    ) {
        ele.style.visibility = "hidden";
    }

    if (
        checked === dataProperty &&
        ele.attributes.hasOwnProperty("key") &&
        ele.attributes.key.value === objectProperty
    ) {
        ele.style.visibility = "hidden";
    }
};

const ontoHandleChange = (myRef, value) => {
    Object.keys(myRef.current.children[0].children).forEach(c => {
        let ele = myRef.current.children[0].children[c];

        if (ele.nodeName === "g") {
            ele = ele.children;
            Object.keys(ele).forEach(subC => {
                const subE = ele[subC];
                subE.style.visibility = "visible";
                hideShowCtrl(value, subE);
            });
        } else {
            ele.style.visibility = "visible";
            hideShowCtrl(value, ele);
        }
    });
};

const filterDupTriple = _ontology => {
    return _ontology.filter((o, oidx, arr) => {
        if (o.ontology === ontoDb) {
            return true;
        }

        const found = arr.find((a, aidx) => {
            if (oidx === aidx) {
                return false;
            }
            return (
                o.op === a.op && o.domain === a.domain && o.range === a.range
            );
        });

        return !found;
    });
};

const ONTOLOGY = {
    propRefPrefix: "ref"
};

/**
 * 若 link 的 property 有多個, 則增加 propertyRef key
 *
 * @param ontologyClass => {links: [{property: "",...}], nodes: []}
 * @param propRefPrefix => property ref 命名 prefix
 * @param initPropNum => property ref 起始號碼
 * @returns {{links}|*}
 */
const addPropertyRef = (
    ontologyClass,
    propRefPrefix = ONTOLOGY.propRefPrefix,
    initPropNum = 1
) => {
    const _ontologyClass = Object.assign({}, ontologyClass);
    let propertyNum = initPropNum;
    if (_ontologyClass && _ontologyClass.links) {
        _ontologyClass.links.forEach(oc => {
            if (oc.property.split("\n").length > 1) {
                oc.propertyRef = `${propRefPrefix}-${propertyNum}`;

                propertyNum += 1;
            }
        });
        return { ontologyClass: _ontologyClass, propertyNum };
    }
    return { ontologyClass, propertyNum };
};

/**
 * 過濾 Links, 保留有 propertyRef 的 links, 並重新組裝
 * {'ref-1': {}, 'ref-2': {}}
 *
 * @param ontologyClass
 * @param className
 * @returns {null}
 */
const filterLinksPropRef = (ontologyClass, className) => {
    const _ontologyClass = Object.assign({}, ontologyClass);
    if (_ontologyClass && _ontologyClass.links) {
        const filterLinks = _ontologyClass.links.filter(oc => oc.propertyRef);
        const linksObj = {};
        filterLinks.forEach(fl => {
            if (!linksObj[fl.propertyRef]) {
                // 增加 Properties key
                linksObj[fl.propertyRef] = {
                    ...fl,
                    properties: fl.property.split("\n"),
                    className: className
                    // active: false
                };
            }
        });
        return isEmpty(linksObj) ? null : linksObj;
    }
    return null;
};

const EXPORT_FILE_NAME = {
    ontology: "HKBDB-ontology",
    relationOP: "HKBDB-relation-property",
    propertyRef: "HKBDB-property-ref"
};

const exportImageHandler = (element, fileName, options = {}) => {
    return exportImage(element, {
        fileName: `${fileName}-${new Date().getTime()}.png`,
        ...options
    })
        .then(res => {
            if (res.finish) {
                return Promise.resolve("下載檔案完成");
            }
            return Promise.throw({
                error: `下載檔案失敗。`
            });
        })
        .catch(err => {
            alert(`下載檔案失敗。\n Error message: ${err.message}`);
            // eslint-disable-next-line prefer-promise-reject-errors
            return Promise.reject({
                error: `下載檔案失敗。\n Error message: ${err.message}`
            });
        });
};

/**
 * export tsv or csv
 *
 * @param head: ["a", "b",...]
 * @param data: [{a:'',b: '',...},{},...] or {key1: {a:'',b:'',..}, key2: {....}, ...}
 * @param fileName: abcde.tsv
 * @param format: tsv or csv
 */
const exportTCsv = (head, data, fileName, format = "tsv") => {
    const arrayValDelimiter = ",";
    const delimiter = format === "tsv" ? "\t" : ",";
    let DownloadTsvFileName = fileName || `file-${genRandomKey(10)}`;
    DownloadTsvFileName += format === "tsv" ? ".tsv" : ".csv";
    let tsvStr = "";
    head.forEach(h => {
        tsvStr += `${h}${delimiter}`;
    });
    tsvStr += "\r\n";
    const customValue = value => {
        if (!value) return "";
        if (value && Array.isArray(value)) return value.join(arrayValDelimiter);
        return value;
    };

    if (Array.isArray(data)) {
        data.forEach(d => {
            head.forEach(v => {
                const value = d.hasOwnProperty(v) ? d[v] : "";
                tsvStr += `${value}${delimiter}`;
            });
            tsvStr += "\r\n";
        });
    } else {
        Object.keys(data).forEach(pr => {
            head.forEach(h => {
                const value = data[pr].hasOwnProperty(h)
                    ? customValue(data[pr][h])
                    : "";
                tsvStr += `${value}${delimiter}`;
            });
            tsvStr += "\r\n";
        });
    }

    fileDownload(tsvStr, DownloadTsvFileName);
};

// light style for societyNetwork
const ontoGraphStyle = {
    display: "flex",
    justifyContent: "center",
    margin: "10px",
    // backgroundColor: "white",
    border: "1px solid #696969"
};

/**
 * ontology className: 中英文切換
 * @param className: "Person"
 * @param in18nList: {Person: {en:"",zh:""}, Place: {en:"",zh:""},...}
 * @param locale: en, zh, zh-hant, zh-hans...
 */
const cvtOntoClassNameLocale = (className, in18nList, locale = "en") => {
    let _locale = locale === "en" ? "en" : "zh";
    if (!className) return null;
    if (!in18nList) return className;
    return in18nList[className] && in18nList[className][_locale]
        ? in18nList[className][_locale]
        : className;
};

export {
    dataToNodesLinks,
    ontoHandleChange,
    filterDupTriple,
    ONTOLOGY,
    addPropertyRef,
    filterLinksPropRef,
    exportImageHandler,
    EXPORT_FILE_NAME,
    exportTCsv,
    ontoGraphStyle,
    cvtOntoClassNameLocale
};
