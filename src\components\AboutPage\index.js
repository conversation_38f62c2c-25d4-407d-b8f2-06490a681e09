import React, { useContext, useEffect, useState } from "react";
import { Container, Divider } from "semantic-ui-react";
import { injectIntl } from "react-intl";
import { ResponsiveContainer } from "../../layout/Layout";
import service from "./service";
import { StoreContext } from "../../store/StoreProvider";
import Md2React from "../../common/components/Markdown2React";
import FadeIn from "react-fade-in";

const simpleLocale = locale => {
    const defaultLocale = "en";
    if (!locale || typeof locale !== "string") return defaultLocale;
    return locale.indexOf("zh") >= 0 ? "zh" : defaultLocale;
};

const AboutPage = props => {
    // store
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    // local
    const [webConfig, setWebConfig] = useState(null);
    const [pageData, setPageData] = useState(null);
    const [loading, setLoading] = useState(false);
    const bindId = "webintro";

    // 因後台編輯使用的react-quill在處理header時，會固定產生一個<p><br></p>，為了讓前台畫面與後台一致，須處理<p><br></p>
    const removeEmptyPTagsFromContent = data => {
        return data.map(item => {
            const container = document.createElement("div");
            container.innerHTML = item.content;

            const paragraphs = Array.from(container.querySelectorAll("p"));

            let firstEmptyFound = false; // 用來標記是否已經找到第一個空段落
            paragraphs.forEach((p, index) => {
                const isEmpty =
                    !p.textContent?.trim() ||
                    ["<br>", "<br/>", "<br />"].includes(p.innerHTML.trim());

                if (isEmpty) {
                    // 檢查前後的狀況
                    const isPreviousEmpty =
                        index > 0 &&
                        paragraphs[index - 1].innerHTML.trim() === "<br>";
                    const isNextEmpty =
                        index < paragraphs.length - 1 &&
                        paragraphs[index + 1].innerHTML.trim() === "<br>";

                    if (!isPreviousEmpty && !isNextEmpty) {
                        // 第一種情況：如果前後都沒有空的 <p><br></p>，移除
                        p.remove();
                    } else if (!firstEmptyFound) {
                        // 第二種情況：如果是第一個空的 <p><br></p>，保留
                        firstEmptyFound = true;
                    } else {
                        // 如果前後有其他 <p><br></p>，移除
                        p.remove();
                    }
                }
            });

            return {
                ...item,
                content: container.innerHTML
            };
        });
    };

    const fetchWebconfig = async () => {
        try {
            const apiData = await service.getWebConfig();
            setWebConfig(apiData);
            const curPageData = apiData
                .filter(item => item?.bindId.indexOf(bindId) >= 0)
                .filter(item => item.lang === simpleLocale(locale));
            const cleanedCurPageData = removeEmptyPTagsFromContent(curPageData);
            setPageData(cleanedCurPageData);
        } catch (err) {
            // console.log(err)
        }
    };

    useEffect(() => {
        setLoading(!webConfig);
    }, [webConfig]);

    useEffect(() => {
        fetchWebconfig();
    }, []);

    // 語系變更時, 依語系變更內容
    useEffect(() => {
        if (!Array.isArray(webConfig)) return;
        const filterData = webConfig
            .filter(item => item?.bindId.indexOf(bindId) >= 0)
            .filter(item => item.lang === simpleLocale(locale));
        const cleanedFilterData = removeEmptyPTagsFromContent(filterData);
        setPageData(cleanedFilterData);
    }, [locale]);

    return (
        <ResponsiveContainer {...props}>
            <Container textAlign="justified">
                {/* {loading && ( */}
                {/*    <React.Fragment> */}
                {/*        <Dimmer active inverted> */}
                {/*            <Loader inverted size={"large"}> */}
                {/*                Loading */}
                {/*            </Loader> */}
                {/*        </Dimmer> */}
                {/*    </React.Fragment> */}
                {/* )} */}

                {/* <Header as="h2"> */}
                {/*    <FormattedMessage id="about.title" defaultMessage="About" /> */}
                {/* </Header> */}
                <FadeIn transitionDuration={1000}>
                    <div
                        style={{
                            marginTop: "50px",
                            marginBottom: "80px",
                            padding: "0 100px"
                        }}
                    >
                        <Md2React.Normal
                            content={pageData && pageData[0].content}
                        />
                    </div>
                </FadeIn>

                <Divider hidden />
            </Container>
        </ResponsiveContainer>
    );
};

export default injectIntl(AboutPage);
