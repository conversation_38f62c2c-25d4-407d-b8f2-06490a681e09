import React, { useContext } from "react";
import { <PERSON> } from "react-router-dom";
import { Menu } from "semantic-ui-react";
import { Api } from "../api/hkbdb/Api";
import { StoreContext } from "../store/StoreProvider";
// type AppTitleProps = {
//     fixed: boolean,
//     onClick: Function
// };

export const AppTitle = ({ fixed, onClick, mobile, webStyle }) => {
    const [state] = useContext(StoreContext);
    const { main } = state;
    const { imageToken } = main;
    const desktopLogo =
        imageToken && imageToken.desktop && imageToken.desktop.logo;
    const mobilLogo =
        imageToken && imageToken.desktop && imageToken.mobile.logo;

    const locale = Api.getLocale();
    return (
        <Menu.Item
            as={Link}
            header
            name="home"
            to={`/${locale}`}
            onClick={(e, props) => {
                if (typeof onClick === "function") {
                    onClick(e, props);
                }
            }}
            style={{
                padding: mobile ? "5px 5px" : "default",
                margin: mobile ? "5px" : "default"
            }}
        >
            <img
                src={
                    (mobile ? mobilLogo : desktopLogo) ||
                    "/img/img_202102/HKBDB_icon_v6.jpg"
                }
                alt=""
                style={{
                    width: !fixed && !mobile ? "80px" : "3.7em",
                    position: !fixed && !mobile ? "relative" : "inline-block",
                    top: !fixed ? "1.5em" : "0"
                }}
            />
        </Menu.Item>
    );
};
