import React, { useEffect, useState, useContext } from "react";
import * as topola from "topola";
import * as d3 from "d3";
import { StoreContext } from "../../../../store/StoreProvider";
import { queryGenealogy } from "../../People/action";

const width = 1200;
const height = 800;
export const treeStyle = {
    display: "inline-block",
    // justifyContent: 'space-around',
    overflow: "auto",
    width,
    height
};

// const zoom = d3
// 	.zoom()
// 	.scaleExtent([-40, 40])
// 	// eslint-disable-next-line no-use-before-define
// 	.on('zoom', zoomed);
//
// function zoomed() {
// 	d3.select('.view').attr('transform', d3.event.transform);
// }

const Tree = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation } = state;
    const { genealogy } = personInformation;
    const [treeData, setTreeData] = useState([]);
    // const [depth, setDepth] = useState(1);
    // console.log(genealogyData);

    const [treeJson, setTreeJson] = useState(null); // 儲存 treeJson

    const render = (chart, indiInfo) => {
        const info = chart.render({
            startIndi: indiInfo && indiInfo.id,
            baseGeneration: indiInfo && indiInfo.generation
        });
        const svg = d3.select("svg").attr("class", "view");
        // .attr('width', width)
        // .attr('height', height)
        // .attr('viewbox', `0 0 1200 800`)
        // .call(zoom);
        const parent = svg.node().parentElement;

        const scrollTopTween = scrollTop => () => {
            const i = d3.interpolateNumber(parent.scrollTop, scrollTop);
            return t => {
                parent.scrollTop = i(t);
            };
        };
        const scrollLeftTween = scrollLeft => () => {
            const i = d3.interpolateNumber(parent.scrollLeft, scrollLeft);
            return t => {
                parent.scrollLeft = i(t);
            };
        };

        const initialRender = !indiInfo;
        const dx = parent.clientWidth / 2 - info.origin[0];
        const dy = parent.clientHeight / 2 - info.origin[1];
        const svgTransition = svg
            .transition()
            .delay(200)
            .duration(500);
        const transition = initialRender ? svg : svgTransition;
        transition
            .attr(
                "transform",
                `translate(${d3.max([dx, 0])}, ${d3.max([dy, 0])})`
            )
            .attr("width", info.size[0])
            .attr("height", info.size[1]);
        if (initialRender) {
            parent.scrollLeft = -dx;
            parent.scrollTop = -dy;
        } else {
            svgTransition
                .tween("scrollLeft", scrollLeftTween(-dx))
                .tween("scrollTop", scrollTopTween(-dy));
        }
    };

    useEffect(() => {
        const initTree = treeData => {
            // let startFam = TARGET_GENERATION;
            const treeJson = topola.gedcomToJson(treeData);
            if (treeJson?.indis?.length > 0) {
                // 追踪每個家庭的子女
                const familyChildrenMap = new Map();

                // 處理所有家庭的子女
                treeJson.fams.forEach(fam => {
                    if (!familyChildrenMap.has(fam.id)) {
                        familyChildrenMap.set(fam.id, new Set());
                    }
                    if (fam.children) {
                        fam.children.forEach(childId => {
                            familyChildrenMap.get(fam.id).add(childId);
                        });
                    }
                });

                // 更新家庭數據，確保子女只出現一次
                treeJson.fams = treeJson.fams.map(fam => ({
                    ...fam,
                    children: Array.from(familyChildrenMap.get(fam.id) || [])
                }));

                const chart = topola.createChart({
                    json: treeJson,
                    chartType: topola.HourglassChart,
                    renderer: topola.DetailedRenderer,
                    // Rerender with animation when an individual is clicked.
                    indiCallback: info => render(chart, info),
                    animate: true,
                    // SVG size is animated manually in the render() function.
                    updateSvgSize: false,
                    horizontal: true
                });
                render(chart);
            }
        };

        if (treeData && treeData.length > 0) {
            initTree(treeData);
        }
    }, [treeData]);

    useEffect(() => {
        setTreeData(genealogy);
    }, [genealogy]);

    return (
        <div className="diagramContainer" style={treeStyle}>
            <div id="tree-graph">
                <svg />
            </div>
        </div>
    );
};

export default Tree;
