import SourcesPage from "./components/Sources/SourcesPage";
import QueryPage from "./components/Query";
import HomePage from "./components/Home/HomePage";
// import SignIn from "./components/Auth/SignIn";
import SignInLayout from "./components/Auth/SignInLayout";
import SignUpLayout from "./components/Auth/SignUpLayout";
import BrowsePage from "./components/BrowsePage";
import AboutPage from "./components/AboutPage";
import PeoplePage from "./components/Hkbdb/People/People";
import OrganizationPage from "./components/Hkbdb/Organization/Organization";
import ErrorPage from "./layout/ErrorPage";
import SignOut from "./components/Auth/SignOut";
import AccountManagePage from "./components/AccountManage";
import UserPage from "./components/User/UserPage";
import OntologyView from "./components/OntologyView";
import SwaggerAPI from "./components/SwaggerAPI";
import PrivacyPage from "./components/Privacy";
import FocusPointPage from "./components/FocusPointPage/FocusPointPage";
import HotSearchPage from "./components/HotSearchPage/hotSearchPage";
import MapPage from "./components/MapPage/index.jsx";
import GuidePage from "./components/MapPage/subPage/Guide";

// import HkvaybSearchPage from "./components/Hkvayb/tmp/SearchPage";
// import HkvaybSearchPage2 from "./components/Hkvayb/tmp/SearchPage2";
// import HkvaybDetailPage from "./components/Hkvayb/tmp/DetailPage";

// import HkvaybSearch from "./components/Hkvayb/pages/Search";
// import HkvaybResult from "./components/Hkvayb/pages/Result";
// import HkvaybAbout from "./components/Hkvayb/pages/About";
// import HkvaybDescription from "./components/Hkvayb/pages/Description";
// import HkvaybDetail from "./components/Hkvayb/pages/Detail";

import Help from "./components/Help";

import authority from "./App-authority";
import CollectionPage from "./components/CollectionPage";

export const ROUTE_ID = {
    Home: "route-Home",
    Error: "route-Error",
    People: "route-People",
    Organization: "route-Organization",
    Login: "route-Login",
    Signup: "route-Signup",
    Logout: "route-Logout",
    Sources: "route-Sources",
    Browse: "route-Browse",
    Query: "route-Query",
    About: "route-About",
    User: "route-User",
    Management: "route-Management",
    Ontology: "route-Ontology",
    API: "route-API",
    Privacy: "route-Privacy",
    Hkvayb: "route-Hkvayb",
    Help: "route-Help",
    Detail: "route-Detail",
    HkvaybTest: "route-HkvaybTest",
    HkvaybAbout: "route-HkvaybAbout",
    HkvaybResult: "route-HkvaybResult",
    HkvaybResultCategory: "route-HkvaybResultCategory",
    HkvaybDescription: "route-HkvaybDescription",
    HkvaybDetail: "route-HkvaybDetail",
    HotSearch: "route-HotSearch",
    FocusPoint: "router-FocusPoint",
    Collection: "router-Collection",
    ManuScript: "router-Manuscript",
    FeaturedPub: "router-featuredPub",
    Map: "router-Map",
    MapGuide: "router-MapGuide"
};

// authority: 使用者是否可以進入該頁面由 authority 控制
// public: 開發中的頁面, public 設為 false
const routes = [
    {
        id: ROUTE_ID.Home,
        label: "Home",
        path: "/:locale?",
        public: true,
        authority: authority.Home,
        component: HomePage
    },
    {
        id: ROUTE_ID.Error,
        label: "Error",
        path: "/:locale/error/:code",
        public: true,
        authority: authority.Error,
        component: ErrorPage
    },
    {
        id: ROUTE_ID.People,
        label: "People",
        path: "/:locale/people/:name?",
        public: true,
        authority: authority.People,
        component: PeoplePage
    },
    {
        id: ROUTE_ID.Organization,
        label: "Organization",
        path: "/:locale/organization/:name?",
        public: true,
        authority: authority.Organization,
        component: OrganizationPage
    },
    {
        id: ROUTE_ID.Login,
        label: "Login",
        path: "/:locale/login",
        public: true,
        authority: authority.Login,
        component: SignInLayout
    },
    {
        id: ROUTE_ID.Signup,
        label: "Signup",
        path: "/:locale/signup",
        public: true,
        authority: authority.Signup,
        component: SignUpLayout
    },
    {
        id: ROUTE_ID.Logout,
        label: "Logout",
        path: "/:locale/logout",
        public: true,
        authority: authority.Logout,
        component: SignOut
    },
    {
        id: ROUTE_ID.Sources,
        label: "Sources",
        path: "/:locale/sources/:link?",
        public: true,
        authority: authority.Sources,
        component: SourcesPage
    },
    {
        id: ROUTE_ID.Browse,
        label: "Browse",
        path: "/:locale/browse",
        public: true,
        authority: authority.Browse,
        component: BrowsePage
    },
    {
        id: ROUTE_ID.Query,
        label: "Query",
        path: "/:locale/query",
        public: true,
        authority: authority.Query,
        component: QueryPage
    },
    {
        id: ROUTE_ID.About,
        label: "About",
        path: "/:locale/about",
        public: true,
        authority: authority.About,
        component: AboutPage
    },
    {
        id: ROUTE_ID.User,
        label: "User",
        path: "/:locale/user",
        public: false, // 維護中設定 false
        authority: authority.User,
        component: UserPage
    },
    {
        id: ROUTE_ID.Management,
        label: "管理平台",
        path: "/:locale/management",
        public: true, // 維護中設定 false
        authority: authority.Management,
        component: AccountManagePage
    },
    {
        id: ROUTE_ID.Ontology,
        label: "Ontology",
        path: "/:locale/ontology",
        public: true, // 維護中設定 false
        authority: authority.Ontology,
        component: OntologyView
    },
    {
        id: ROUTE_ID.API,
        label: "API",
        path: "/:locale/api",
        public: true,
        authority: authority.API,
        component: SwaggerAPI
    },
    {
        id: ROUTE_ID.Privacy,
        label: "Privacy",
        path: "/:locale/privacy",
        public: true,
        authority: authority.Privacy,
        component: PrivacyPage
    },
    // {
    //     id: "route-Hkvayb",
    //     label: "Hkvayb",
    //     path: "/:locale/Hkvayb",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybSearchPage
    // },
    {
        id: ROUTE_ID.Help,
        label: "Help",
        path: "/:locale/help",
        public: true,
        authority: authority.Help,
        component: Help
    },
    {
        id: ROUTE_ID.HotSearch,
        label: "HotSearch",
        path: "/:locale/hotSearch",
        public: true,
        authority: authority.HotSearch,
        component: HotSearchPage
    },
    {
        id: ROUTE_ID.FocusPoint,
        label: "FocusPoint",
        path: "/:locale/focusPoint",
        public: true,
        authority: authority.FocusPoint,
        component: FocusPointPage
    },
    {
        id: ROUTE_ID.Collection,
        label: "Collection",
        path: "/:locale/collection/:colType",
        public: true,
        authority: authority.Collection,
        component: CollectionPage
    },
    // MapPage
    {
        id: ROUTE_ID.Map,
        label: "Map",
        path: "/:locale/map",
        public: process.env.REACT_APP_GIS_ENABLE === "true",
        authority: authority.Map,
        component: MapPage
    },
    {
        id: ROUTE_ID.MapGuide,
        label: "MapGuide",
        path: "/:locale/map/guide",
        public: process.env.REACT_APP_GIS_ENABLE === "true",
        authority: authority.Map,
        component: GuidePage
    }
    // {
    //     id: ROUTE_ID.ManuScript,
    //     label: "Manuscript",
    //     path: "/:locale/collection/:type",
    //     public: true,
    //     authority: authority.ManuScript,
    //     component: CollectionPage
    // },
    // {
    //     id: ROUTE_ID.FeaturedPub,
    //     label: "FeaturedPub",
    //     path: "/:locale/featuredPub",
    //     public: true,
    //     authority: authority.FeaturedPub,
    //     component: CollectionPage
    // },
    // {
    //     id: "route-18",
    //     label: "Detail",
    //     path: "/:locale/Hkvayb/detail/:searchId",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybDetailPage
    // },
    // {
    //     id: "route-19",
    //     label: "HkvaybTest",
    //     path: "/:locale/HkvaybTest",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybSearchPage2
    // },
    // {
    //     id: ROUTE_ID.Hkvayb,
    //     label: "Hkvayb",
    //     path: "/:locale/Hkvayb",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybSearch
    // },
    // {
    //     id: ROUTE_ID.HkvaybAbout,
    //     label: "HkvaybAbout",
    //     path: "/:locale/HkvaybAbout",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybAbout
    // },
    // {
    //     id: ROUTE_ID.HkvaybResult,
    //     label: "HkvaybResult",
    //     path: "/:locale/HkvaybResult",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybResult
    // },
    // {
    //     id: ROUTE_ID.HkvaybResultCategory,
    //     label: "HkvaybResult",
    //     path: "/:locale/HkvaybResult/:category",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybResult
    // },
    // {
    //     id: ROUTE_ID.HkvaybDescription,
    //     label: "HkvaybDescription",
    //     path: "/:locale/HkvaybDescription",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybDescription
    // },
    // {
    //     id: ROUTE_ID.HkvaybDetail,
    //     label: "HkvaybDetail",
    //     path: "/:locale/HkvaybDetail/:category/:selectedId",
    //     public: true,
    //     authority: authority.Hkvayb,
    //     component: HkvaybDetail
    // }
];

export const getPathById = routeId => {
    const find = routes.find(o => o.id === routeId);
    return find?.path;
};

export default routes;
