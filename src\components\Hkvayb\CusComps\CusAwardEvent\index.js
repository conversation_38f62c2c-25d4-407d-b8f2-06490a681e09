import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusAlbum from "../CusAlbum";
import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const CusAwardEvent = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };
    const organizerKey = ["hasOrganizer", "hasOrganizerTmp"];

    const bilingFunc = bilingual(defObj);

    const [locZh, locEn] = bilingFunc(data, "loc");
    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [winnerZh, winnerEn] = bilingFunc(data, "winner");
    const [remarkZh, remarkEn] = bilingFunc(data, "remark");
    const [mediaIdZh, mediaIdEn] = bilingFunc(data, "mediaId");
    const [catTypeZh, catTypeEn] = bilingFunc(data, "catType");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [organizerZh, organizerEn] = bilingFunc(data, organizerKey);
    const [awardTitleZh, awardTitleEn] = bilingFunc(data, "awardTitle");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_exhibition}>
            <CusBilingualTitle
                titleZh={awardTitleZh.value}
                titleEn={awardTitleEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`awards/${year}`}
                    backupPath={"awards"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.organizer"
                    defaultMessage="Organizer : "
                />
                <CusValue {...organizerZh} />
                <CusValue prefix="/" defVal="" {...organizerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.winner"
                    defaultMessage="Winner(s) : "
                />
                <CusValue {...winnerZh} />
                <CusValue prefix="/" defVal="" {...winnerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.media"
                    defaultMessage="Media : "
                />
                <CusValue {...mediaIdZh} />
                <CusValue prefix="/" defVal="" {...mediaIdEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.loc"
                    defaultMessage="Location : "
                />
                <CusValue {...locZh} />
                <CusValue prefix="/" defVal="" {...locEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.catType"
                    defaultMessage="Type : "
                />
                <CusValue {...catTypeZh} />
                <CusValue prefix="/" defVal="" {...catTypeEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.award.remark"
                    defaultMessage="Remark : "
                />
                <CusValue {...remarkZh} />
            </CusPara>
        </div>
    );
};

export default CusAwardEvent;
