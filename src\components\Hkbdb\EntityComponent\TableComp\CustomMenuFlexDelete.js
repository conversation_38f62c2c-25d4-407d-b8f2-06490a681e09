import React, { Fragment, useContext } from "react";

// common
import CustomSegment from "./CustomSegmentDelete";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { safeGet } from "../../../../common/codes";
import { displayMemberName, SPECIAL_HEADER } from "../../Organization/action";

const CustomMenuFlexDelete = ({
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    //
    const [state] = useContext(StoreContext);
    const { user, setting, personInformation } = state;
    const { role } = user;
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    const { memberInvert } = personInformation;
    //
    const memberNameGroup = memberInvert ? displayMemberName(memberInvert) : [];
    //
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );
    return (
        <Fragment>
            {editData.rowData
                .slice(0)
                .sort((a, b) => {
                    // sort by sorted list
                    const order = fieldPropSortedRecords.map(item =>
                        item.toUpperCase()
                    );
                    const nameA = a.propertyBindRangeStr.toUpperCase();
                    const nameB = b.propertyBindRangeStr.toUpperCase();
                    if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                    if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                    return 0;
                })
                .map(row => {
                    //
                    const { propertyBindRangeStr, rowId } = row;
                    let roles = safeGet(
                        fieldAttrRecords,
                        [propertyBindRangeStr, "roles"],
                        []
                    );
                    //
                    if (memberNameGroup.includes(propertyBindRangeStr)) {
                        roles = safeGet(
                            fieldAttrRecords,
                            [SPECIAL_HEADER, "roles"],
                            []
                        );
                    }
                    //
                    if (roles.includes(role)) {
                        return (
                            <CustomSegment
                                rowIdx={rowId}
                                key={`segment-${rowId}`}
                                rowData={row}
                                editData={editData}
                                setEditData={setEditData}
                                ontologyType={ontologyType}
                            />
                        );
                    }
                })}
        </Fragment>
    );
};

export default CustomMenuFlexDelete;
