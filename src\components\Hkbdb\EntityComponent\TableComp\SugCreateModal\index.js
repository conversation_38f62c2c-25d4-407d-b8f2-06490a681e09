import React, { useContext, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

// semantic ui
import {
    ModalActions,
    Button,
    Modal,
    Grid,
    Divider,
    Segment
} from "semantic-ui-react";

// components
import CustomAlertMessage from "../CustomAlertMessage";
import HeaderSelect from "./HeaderSelect";
import ModalButtons from "./ModalButtons";
import SuggesterForm from "./SuggesterForm";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// utils
import getSugPropOptions from "../../../common/utils/getSugPropOptions";
import { safeGet, isEmpty } from "../../../../../common/codes";

// config
import removeOrgOntologyData from "../../../common/config/removeOrgOntologyData";
import { convertSugOptions } from "../../../common/utils/convertSugOptions";
import {
    Api,
    deleteHkbdbData,
    doRestCreate,
    getEntryGraph,
    readHkbdbData,
    updateHkbdbData
} from "../../../../../api/hkbdb/Api";
import { AUDA_HKLIT_GRAPH } from "../../../../../config/config-ontology";
import Act from "../../../../../store/actions";

/** 元件說明: 用在role為suggester的時候，新增與修改的Button Modal */
function SugCreateModal({ ontologyDomain, ontologyType }) {
    const [state, dispatch] = useContext(StoreContext);
    const { property, setting, user, information } = state;
    const { personInformation: perInfo } = state;
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    const { ontologyDefined, propertyObj } = property;
    const { personId, ontologyOneOfThemData } = information;
    const [createGraphData, setCreateGraphData] = useState();
    const [createData, setCreateData] = useState(() => {
        const ontology = safeGet(ontologyDefined, [ontologyType], []);
        //
        let oneOfThem = safeGet(
            ontologyOneOfThemData,
            [`${ontologyType}`.toLowerCase()],
            []
        );
        //
        if (ontologyDomain === "organization") {
            oneOfThem = oneOfThem.filter(
                prop => removeOrgOntologyData.indexOf(prop) === -1
            );
        }
        //
        return {
            graphs: [], // hkbdb_draft只會新增資料集
            selectedProperties: [],
            willCreatedData: { srcId: personId },
            propertyObj,
            ontology,
            ontologyType,
            ontologyDefined,
            ontologyOneOfThemData: oneOfThem,
            isCreated: false,
            perInfo
        };
    });

    const [editableProps, setEditableProps] = useState([]);

    useEffect(() => {
        if (isEmpty(ontologyDefined)) return;
        const tmpProps = getSugPropOptions(
            ontologyDefined[ontologyType],
            ontologyDomain,
            ontologyType,
            fieldAttr,
            fieldProp,
            property,
            user.role
        );
        const switchedSugOptions = convertSugOptions(tmpProps, ontologyType);
        setEditableProps(switchedSugOptions);
    }, [ontologyDefined]);

    const [open, setOpen] = useState(false);
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const handleClose = async () => {
        if (!createGraphData) return;
        const apiStr = Api.getDraftIDType(createGraphData.srcId, ontologyType);

        const { data } = await readHkbdbData(apiStr);
        // 關閉的時候刪除剛創出來的graph
        const res = await deleteHkbdbData(Api.deleteGraph(), createGraphData);
        setCreateData(prevData => ({
            // ...prevData,
            selectedProperties: [],
            willCreatedData: { srcId: personId },
            isCreated: false
        }));

        setOpen(false);
    };

    return (
        <Segment basic textAlign="center">
            <Grid>
                <Grid.Column width={5}>
                    <Divider />
                </Grid.Column>
                <Grid.Column width={6}>
                    <Modal
                        onOpen={() => setOpen(true)}
                        open={open}
                        trigger={
                            <Button
                                basic
                                size="small"
                                icon="add"
                                labelPosition="left"
                                content={
                                    <FormattedMessage
                                        id="people.Information.createProperty.suggester"
                                        defaultMessage="Create new Property"
                                    />
                                }
                                onClick={async () => {
                                    const graphEntry = {
                                        classType: ontologyType,
                                        srcId: personId,
                                        value: {}
                                    };
                                    const srcId = createData.willCreatedData.srcId.slice(
                                        3
                                    );
                                    const tmpEntry = getEntryGraph(
                                        user,
                                        graphEntry,
                                        srcId
                                    );
                                    const result = await doRestCreate(
                                        user,
                                        tmpEntry
                                    );
                                    // dispatch({
                                    //     type:
                                    //         Act.SET_ENTRY_GRAPH_FOR_CREATING_SUGGESTIONS,
                                    //     payload: tmpEntry
                                    // });

                                    setCreateGraphData(tmpEntry);
                                }}
                            ></Button>
                        }
                    >
                        <Modal.Header>
                            <FormattedMessage
                                id="people.Information.createProperty.suggester"
                                defaultMessage="Create new Property"
                            />
                        </Modal.Header>
                        <Modal.Content image scrolling>
                            <Modal.Description
                                style={{
                                    width: "100%",
                                    paddingTop:
                                        ontologyType === "relationevent"
                                            ? 0
                                            : "inherit"
                                }}
                            >
                                <CustomAlertMessage
                                    alertMsg={alertMsg}
                                    setAlertMsg={setAlertMsg}
                                />
                                {ontologyType === "relationevent" && (
                                    <p
                                        style={{
                                            textAlign: "center",
                                            fontSize: "1rem"
                                        }}
                                    >
                                        單次僅限新增一筆關係
                                    </p>
                                )}
                                {/* show content */}
                                <HeaderSelect />
                                {/* show selected property */}
                                <SuggesterForm
                                    editableProps={editableProps}
                                    createData={createData}
                                    setCreateData={setCreateData}
                                    createGraphData={createGraphData}
                                    ontologyType={ontologyType}
                                />
                            </Modal.Description>
                        </Modal.Content>
                        <ModalActions>
                            <ModalButtons
                                createData={createData}
                                setCreateData={setCreateData}
                                ontologyType={ontologyType}
                                setAlertMsg={setAlertMsg}
                                handleClose={handleClose}
                                createGraphData={createGraphData}
                                setOpen={setOpen}
                            />
                        </ModalActions>
                    </Modal>
                </Grid.Column>
                <Grid.Column width={5}>
                    <Divider />
                </Grid.Column>
            </Grid>
        </Segment>
    );
}

SugCreateModal.propTypes = {
    /** 取得編輯資訊domain */
    ontologyDomain: PropTypes.string,
    /** 取得編輯資訊range */
    ontologyType: PropTypes.string
};

SugCreateModal.defaultProps = {
    /** 取得編輯資訊domain */
    ontologyDomain: "",
    /** 取得編輯資訊range */
    ontologyType: ""
};

export default SugCreateModal;
