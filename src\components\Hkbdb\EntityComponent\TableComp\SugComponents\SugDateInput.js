import React, { useEffect, useState } from "react";

// ui
import { Input, Icon, Label } from "semantic-ui-react";

// common
import { isEmpty, isNotEmpty, isNumeric } from "../../../../../common/codes";

// custom
import CustomDebounce from "../CustomDeBounce";

const CustomDateInput = ({ property, createData, setCreateData }) => {
    //
    const { ontologyType } = createData;
    const { value: propertyName, label, required } = property;
    //
    const [inputDate, setInputDate] = useState("");
    //
    const debInputDate = CustomDebounce(inputDate, 500);
    //
    const handleChange = (event, { value }) => {
        //
        const newValue = value.replaceAll("-", "");
        //
        if (isEmpty(newValue)) {
            setInputDate("");
            return;
        }
        // console.log(value);
        if (isNotEmpty(newValue) && isNumeric(newValue)) {
            //
            const yyyy = newValue.slice(0, 4);
            const MM = newValue.slice(4, 6);
            const dd = newValue.slice(6, 8);
            //
            let dataFormatValue = "";
            //
            if (isNotEmpty(yyyy)) {
                const currYear = new Date().getFullYear();
                if (yyyy > currYear) {
                    dataFormatValue += currYear;
                } else {
                    dataFormatValue += yyyy;
                }
            }
            if (isNotEmpty(MM)) {
                if (MM > 12) {
                    dataFormatValue += "-12";
                } else if (MM < 0) {
                    dataFormatValue += "-00";
                } else {
                    dataFormatValue += `-${MM}`;
                }
            }
            if (isNotEmpty(dd)) {
                //
                const date = new Date();
                const daysInMonth = new Date(
                    date.getFullYear(),
                    date.getMonth() + 1,
                    0
                ).getDate();
                //
                if (dd > daysInMonth) {
                    dataFormatValue += `-${daysInMonth}`;
                } else if (dd < 0) {
                    dataFormatValue += "-00";
                } else {
                    dataFormatValue += `-${dd}`;
                }
            }
            //
            setInputDate(dataFormatValue);
        }
    };
    //
    const handleUpdateState = () => {
        // if (!isEmpty(propertyName) && !isEmpty(debInputDate)) {
        if (!isEmpty(propertyName)) {
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyName]: debInputDate
                    }
                }
            }));
        } else {
            console.log("param error, propertyName: ", propertyName);
        }
    };
    //
    useEffect(() => {
        handleUpdateState();
    }, [debInputDate]);
    //
    return (
        <Input
            fluid
            labelPosition="left"
            type="text"
            value={inputDate}
            onChange={handleChange}
            placeholder="yyyy-MM-dd"
        >
            <Label>
                {label}
                {required && (
                    <Icon
                        style={{ marginLeft: "1.5em" }}
                        color="red"
                        name="asterisk"
                        size="mini"
                    />
                )}
            </Label>
            <input />
        </Input>
    );
};

export default CustomDateInput;
