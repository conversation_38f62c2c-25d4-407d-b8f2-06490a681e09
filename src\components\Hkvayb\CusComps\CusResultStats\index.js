import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

const CusResultStats = ({ count = 0, style = {} }) => {
    const classes = useStyles(style);
    return (
        <div className={classes.hkvaby_result_stats}>
            <FormattedMessage
                id="hkvayb.search.result.state.total"
                defaultMessage="Total "
            />
            {count}
            <FormattedMessage
                id="hkvayb.search.result.state.results"
                defaultMessage=" results were found"
            />
        </div>
    );
};

export default CusResultStats;
