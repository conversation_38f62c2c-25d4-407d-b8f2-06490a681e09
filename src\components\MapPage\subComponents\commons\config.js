// types
// import { type LatLngBoundsExpression, type LatLngExpression } from "leaflet";

// import { type TileLayerProps } from "react-leaflet";

// export enum EnumMapLatLngKey {
//   center = "center",
//   taiwan = "taiwan",
//   worldCenter = "worldCenter",
//   Atlantic = "Atlantic",
//   us = "us",
// }

/** map */
export const mapLatLongDefault = {
    center: [29.212836499999995, 69.22237966666667],
    taiwan: [23.6978, 120.9605],
    hongKong: [22.3193, 114.1694],
    worldCenter: [34.507397648269134, 6.940831662517197],
    Atlantic: [31, -17],
    us: [39.56644325070512, -101.30105465480294]
};

export const zoomDefault = {
    min: 2,
    max: 20,
    desktopInit: 4,
    mobileInit: 3
};

// 用來設定 maxBounds, 使用者要拖曳超過 bounds, 會回彈至 bounds 裡面
// [西南,東北]
export const boundsDefault = {
    max: [
        [-70, -180],
        [80, 180]
    ]
};

export const heatmapDefault = {
    heatmapRadius: 48,
    heatmapBlur: 55,
    max: 1
};

/**
 * marker has two type:
 * 1.point
 * 2.cluster(with many points)
 * */

export const paletteStyleDefault = "dark";

// marker style list
export const markerPaletteStyle = [
    { id: "palette-style-02", color: "dark" },
    { id: "palette-style-01", color: "light" }
];

// eslint-disable-next-line no-shadow
// export enum PaletteKey {
//   light = "light",
//   dark = "dark",
// }

// point palette
export const dfPointMarkerPalette = {
    // 亮色系
    light: [
        {
            id: "color-regular-01",
            pubsRange: "0-10",
            color: "#C0FA6D",
            min: 0,
            max: 10
        },
        {
            id: "color-regular-02",
            pubsRange: "11-25",
            color: "#e5fa6d",
            min: 11,
            max: 25
        },
        {
            id: "color-regular-03",
            pubsRange: "26-50",
            color: "#fae06d",
            min: 26,
            max: 50
        },
        {
            id: "color-regular-04",
            pubsRange: "51-100",
            color: "#fabf6d",
            min: 51,
            max: 100
        },
        {
            id: "color-regular-05",
            pubsRange: ">100",
            color: "#fa766d",
            min: 101,
            max: 1000
        }
    ],
    // 暗色系
    dark: [
        {
            id: "color-orange-01",
            pubsRange: "0-10",
            color: "#ded4d1",
            min: 0,
            max: 10
        },
        {
            id: "color-orange-02",
            pubsRange: "11-25",
            color: "#e1c4bf",
            min: 11,
            max: 25
        },
        {
            id: "color-orange-03",
            pubsRange: "26-50",
            color: "#c68a84",
            min: 26,
            max: 50
        },
        {
            id: "color-orange-04",
            pubsRange: "51-100",
            color: "#a29290",
            min: 51,
            max: 100
        },
        {
            id: "color-orange-05",
            pubsRange: ">100",
            color: "#5c4e44",
            min: 101,
            max: 1000
        }
    ]
};

// cluster palette
export const clusterMarkerColor = {
    primary: "EED817",
    secondary: "#FFBC42"
};

// layer type
export const layerType = {
    balloon: "balloon",
    heat: "heat"
};

// interface DfTileLayerInfo extends TileLayerProps {
//     name: string;
//     checked: boolean;
//     display: boolean;
//     apikey?: string;
// }

export const linePallet = [
    {
        color: "#BC4112",
        weight: 2
        // dashArray: "2, 2" // Dotted line
    },
    {
        color: "#4183C4",
        weight: 2
        // dashArray: "2, 2"
    },
    {
        color: "#21BA45",
        weight: 2
        // dashArray: "2, 2"
    }
    // {
    //     color: "#FF4500", // OrangeRed
    //     weight: 2,
    //     dashArray: "5, 5" // Dotted line
    // }
    // {
    //     color: "#1E90FF", // DodgerBlue
    //     weight: 2,
    //     dashArray: "10, 5" // Dashed line
    // },
    // {
    //     color: "#228B22", // ForestGreen
    //     weight: 2,
    //     dashArray: "" // Solid line
    // },
    // {
    //     color: "#FF69B4", // HotPink
    //     weight: 2,
    //     dashArray: "2, 4" // Small dashes
    // },
    // {
    //     color: "#DC143C", // Crimson (Fire)
    //     weight: 2,
    //     dashArray: "8, 4, 2, 4" // Mixed dash
    // },
    // {
    //     color: "#9370DB", // MediumPurple
    //     weight: 2,
    //     dashArray: "" // Solid line
    // },
    // {
    //     color: "#DAA520", // GoldenRod
    //     weight: 2,
    //     dashArray: "5, 10" // Long dashes
    // },
    // {
    //     color: "#696969", // DimGray
    //     weight: 2,
    //     dashArray: "4, 4" // Even dashed lines
    // },
    // {
    //     color: "#2E8B57", // SeaGreen
    //     weight: 2,
    //     dashArray: "" // Solid line
    // },
    // {
    //     color: "#8B0000", // DarkRed
    //     weight: 2,
    //     dashArray: "10, 3" // Bold dashed line
    // }
];

export const circleMarkerPallet = [
    {
        color: "#FF4500", // OrangeRed (Border)
        fillColor: "#FFD700" // Gold (Fill)
    },
    {
        color: "#1E90FF", // DodgerBlue (Border)
        fillColor: "#00CED1" // DarkTurquoise (Fill)
    },
    {
        color: "#228B22", // ForestGreen (Border)
        fillColor: "#98FB98" // PaleGreen (Fill)
    },
    {
        color: "#FF69B4", // HotPink (Border)
        fillColor: "#FFB6C1" // LightPink (Fill)
    },
    {
        color: "#DC143C", // Crimson (Border)
        fillColor: "#00BFFF" // DeepSkyBlue (Fill)
    },
    {
        color: "#9370DB", // MediumPurple (Border)
        fillColor: "#D8BFD8" // Thistle (Fill)
    },
    {
        color: "#DAA520", // GoldenRod (Border)
        fillColor: "#F4A460" // SandyBrown (Fill)
    },
    {
        color: "#696969", // DimGray (Border)
        fillColor: "#708090" // SlateGray (Fill)
    },
    {
        color: "#2E8B57", // SeaGreen (Border)
        fillColor: "#87CEFA" // LightSkyBlue (Fill)
    },
    {
        color: "#8B0000", // DarkRed (Border)
        fillColor: "#FF6347" // Tomato (Fill)
    }
];

// default tileLayerInfo
export const dfTileLayerInfo = [
    // {
    //     // CartoDB.Positron
    //     attribution:
    //         '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    //     url: "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
    //     subdomains: "abcd",
    //     name: "CartoDB.Positron",
    //     checked: true,
    //     display: true,
    //     maxZoom: 20
    // }
    // {
    //   // CartoDB.PositronNoLabels
    //   attribution:
    //     '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
    //   url: "https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png",
    //   subdomains: "abcd",
    //   name: "CartoDB.PositronNoLabels",
    //   checked: true,
    //   display: true,
    //   maxZoom: 20,
    // },
    // {
    //   // Thunderforest.Neighbourhood
    //   // Free:150,000 tile requests per month
    //   // Solo Developer £95 per month => 1,500,000 tile requests per month
    //   attribution:
    //     '&copy; <a href="http://www.thunderforest.com/">Thunderforest</a>, &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    //   url: "https://{s}.tile.thunderforest.com/neighbourhood/{z}/{x}/{y}.png?apikey={apikey}",
    //   subdomains: "abcd",
    //   name: "Thunderforest.Neighbourhood",
    //   apikey: "<your apikey>",
    //   checked: false,
    //   display: true,
    //   maxZoom: 22,
    // },
    // {
    //   // stadiamaps
    //   // free 200,000 credits / month => (1 credit/tile)
    //   // starter ($20/month) 1,000,000 credits / month => (1 credit/tile)
    //   // 必須到官網註冊 domain (一個使用者只能申請一個 domain)
    //   attribution:
    //     "Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ, TomTom, Intermap, iPC, USGS, FAO, NPS, NRCAN, GeoBase, Kadaster NL, Ordnance Survey, Esri Japan, METI, Esri China (Hong Kong), and the GIS User Community",
    //   url: "https://tiles.stadiamaps.com/tiles/alidade_smooth/{z}/{x}/{y}{r}.png",
    //   name: "stadiamaps",
    //   checked: false, // 預設
    //   display: true, // 是否顯示
    // },
    // {
    //   // 地圖特色:國界明顯
    //   // stadiamaps
    //   // free 200,000 credits / month => (1 credit/tile)
    //   // starter ($20/month) 1,000,000 credits / month => (1 credit/tile)
    //   attribution:
    //     '&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>, &copy; <a href="https://openmaptiles.org/">OpenMapTiles</a> &copy; <a href="http://openstreetmap.org">OpenStreetMap</a> contributors',
    //   url: "https://tiles.stadiamaps.com/tiles/outdoors/{z}/{x}/{y}{r}.png",
    //   name: "stadiamaps default",
    //   checked: false,
    //   display: true,
    // },
    // {
    //   // 地圖特色:beautiful
    //   // Esri.WorldTopoMap
    //   attribution:
    //     "Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ, TomTom, Intermap, iPC, USGS, FAO, NPS, NRCAN, GeoBase, Kadaster NL, Ordnance Survey, Esri Japan, METI, Esri China (Hong Kong), and the GIS User Community",
    //   url: "https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}",
    //   name: "Esri.WorldTopoMap",
    //   checked: false,
    //   display: true,
    // },
    // {
    //     attribution:
    //         '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    //     url: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    //     subdomains: "abc",
    //     name: "OSM.Traditional",
    //     checked: true,
    //     display: true,
    //     maxZoom: 19,
    //     language: "zh-TW" // 設定繁體中文
    // }
    // {
    //     attribution: "&copy; 高德地图",
    //     url:
    //         "https://wprd0{s}.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scl=1&style=7",
    //     subdomains: "1234",
    //     name: "GaoDe.Normal",
    //     checked: true,
    //     display: true,
    //     maxZoom: 18
    // }
    {
        attribution:
            '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        // url: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        url:
            "https://tiles.stadiamaps.com/tiles/alidade_smooth/{z}/{x}/{y}{r}.png",
        subdomains: "abc",
        name: "Stadia.AlidadeSmooth",
        checked: true,
        display: true,
        maxZoom: 20
        // language: "zh-TW" // 設定繁體中文
    }
];

export const circleStyleDef = {
    stroke: 3,
    radius: 10,
    color: "#fff",
    weight: 2,
    fillColor: "#F2711C",
    secondaryFillColor: "#BC4112",
    fillOpacity: 1
};

export const mapFilterOptions = {
    hasPlaceOfBirth: "#e6194b",
    EducationEvent: "#3cb44b",
    EmploymentEvent: "#4363d8",
    Award: "#f58231",
    Event: "#911eb4",
    hasPlaceOfDeath: "#46f0f0"
};
