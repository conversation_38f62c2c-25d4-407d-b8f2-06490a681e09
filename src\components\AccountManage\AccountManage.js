import React from "react";
import { FormattedMessage } from "react-intl";

// ui
import { Segment, Divider, Container } from "semantic-ui-react";

// custom
import AlertMessage from "./alertMessage";
import CustomTab from "./CustomTab";

const AccountManage = () => {
    console.log("Hi, I am accountManagement");

    return (
        <Container>
            <Segment basic compact>
                <h2>帳號管理</h2>
            </Segment>
            {/*<Divider />*/}
            {/*<AlertMessage />*/}
            {/*<CustomTab />*/}
            {/*<br />*/}
        </Container>
    );
};

export default AccountManage;
