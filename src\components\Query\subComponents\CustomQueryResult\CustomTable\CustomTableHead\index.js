// react
import React from "react";

// ui
import { Table } from "semantic-ui-react";

// custom
import CustomTableCell from "./CustomTableCell";

const index = () => {
    const fixTableHeader = {
        position: "sticky",
        top: "0"
    };
    return (
        <Table.Header>
            <Table.Row style={fixTableHeader}>
                <CustomTableCell />
            </Table.Row>
        </Table.Header>
    );
};

export default index;
