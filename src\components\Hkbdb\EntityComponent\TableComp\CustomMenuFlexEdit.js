// import React, { Fragment } from "react";
//
// import { Divider, Label, Segment } from "semantic-ui-react";
//
// // common
// import CustomRawDataSegment from "./CustomRawDataSegment";
// import CustomSelectedPropDataSegment from "./CustomSelectedPropDataSegment";
// import CustomPropertyDropdown from "./CustomPropertyDropdownFlexEdit";
//
// // common
// import { isNotEmpty } from "../../../../common/codes";
//
// // lang
// import { FormattedMessage } from "react-intl";
//
// const CustomMenuFlexEdit = ({
//     editData,
//     setEditData,
//     ontologyDomain,
//     ontologyType
// }) => {
//     //
//     // ontology or selectedProperties 都不存在 graph 資料，因此必須從原始的資料來源擷取。
//     const findData = editData.rowData.find(item =>
//         isNotEmpty(item?.graph) ? item.graph : ""
//     );
//     //
//     const { graph, eventId, awardId, pubId, artId, owId, personId } = findData;
//     //
//     return (
//         <Fragment>
//             {editData.rowData.map(cellData => {
//                 //
//                 const { rowId, isAllowProp } = cellData;
//                 //
//                 if (isAllowProp) {
//                     return (
//                         <CustomRawDataSegment
//                             key={`segment-${rowId}`}
//                             rowIdx={rowId}
//                             eventId={
//                                 eventId ||
//                                 awardId ||
//                                 pubId ||
//                                 artId ||
//                                 owId ||
//                                 personId
//                             }
//                             cellData={cellData}
//                             editData={editData}
//                             setEditData={setEditData}
//                             ontologyType={ontologyType}
//                         />
//                     );
//                 }
//             })}
//             {editData.selectedProperties.map((prop, propIdx) => {
//                 //
//                 return (
//                     <CustomSelectedPropDataSegment
//                         key={`segment-select-prop-${propIdx}`}
//                         rowIdx={propIdx}
//                         graph={graph}
//                         eventId={eventId || awardId || pubId || artId || owId}
//                         selectedProp={prop}
//                         editData={editData}
//                         setEditData={setEditData}
//                         ontologyDomain={ontologyDomain}
//                         ontologyType={ontologyType}
//                     />
//                 );
//             })}
//             <Divider />
//             <Segment>
//                 <Label attached="top left">
//                     <FormattedMessage
//                         id={"people.Information.properties"}
//                         defaultMessage={"Properties"}
//                     />
//                 </Label>
//                 <CustomPropertyDropdown
//                     editData={editData}
//                     setEditData={setEditData}
//                     ontologyDomain={ontologyDomain}
//                     ontologyType={ontologyType}
//                 />
//             </Segment>
//         </Fragment>
//     );
// };
//
// export default CustomMenuFlexEdit;
