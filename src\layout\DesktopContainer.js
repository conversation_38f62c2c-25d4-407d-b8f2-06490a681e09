import React from "react";
import { Responsive } from "semantic-ui-react";
import DesktopMenu from "./DesktopMenu";

import { injectIntl } from "react-intl";
import uiConfig from "../config/config-ui";

// type DesktopContainerProps = {
//     children: Array<any>
// };

const DesktopContainer = ({ ...props }) => {
    return (
        <Responsive minWidth={uiConfig.BP_DESK_MIN_WIDTH}>
            <DesktopMenu {...props} />
            {props.children}
        </Responsive>
    );
};

export default injectIntl(DesktopContainer);
