import { doRestCreate } from "../../../api/hkbdb/Api";
import { isEmpty } from "../../../common//codes/index";

const NAME_TYPE = {
    nnBestKnownName: {
        key: "nnBestKnownName",
        prop: "nnBestKnownName",
        serialStart: "00001"
    },
    originalName: {
        key: "originalName",
        prop: "originalName",
        serialStart: "00002"
    },
    penName: {
        key: "penName",
        prop: "penName",
        serialStart: "01001"
    },
    zi: {
        key: "zi",
        prop: "zi",
        serialStart: "02001"
    },
    hao: {
        key: "hao",
        prop: "hao",
        serialStart: "03001"
    },
    joinPenName: {
        key: "joinPenName",
        prop: "joinPenName",
        serialStart: "04001"
    },
    name: {
        key: "name",
        prop: "name",
        serialStart: "05001"
    }
};

const getLocalNameId = (nameId, nameType) => {
    if (nameId && nameType && nameType in NAME_TYPE) {
        return `${nameId}${NAME_TYPE[nameType].serialStart}`;
    }
};

export const createNameNodeId = nameId => {
    if (!nameId) return null;
    const localNameId4BestKnowName = getLocalNameId(
        nameId,
        NAME_TYPE.nnBestKnownName.key
    );
    return `NNI${localNameId4BestKnowName}`;
};

export const createNameNode = async (
    srcId,
    nameId,
    graph,
    bestKnownName,
    user = {}
) => {
    if (!nameId || !bestKnownName || isEmpty(user)) return;

    const localNameId4BestKnowName = getLocalNameId(
        nameId,
        NAME_TYPE.nnBestKnownName.key
    );
    // 因應 API 邏輯調整 post body 資料格式
    // nameNode ID 改為 randomId, 由 API 產生
    const entry = {
        graph: graph,
        srcId: srcId, // 這邊的 srcId 是 perId or orgId
        classType: "namenode",
        value: {
            label_namenode__string: "",
            nnBestKnownName__string: bestKnownName,
            localNameId__string: localNameId4BestKnowName
        }
    };
    // const entry = {
    //     graph: graph,
    //     srcId: `NNI${bs64Encode(localNameId4BestKnowName)}`,
    //     classType: "NameNode",
    //     value: {
    //         label: [""],
    //         nnBestKnownName: bestKnownName,
    //         localNameId: localNameId4BestKnowName
    //     }
    // };
    const result = await doRestCreate(user, entry);
    //
    if (result?.state) {
        return { state: true };
    } else {
        return { state: false };
    }
};
