import React, { Fragment, useContext } from "react";

// ui
import { Divider, Label, Segment } from "semantic-ui-react";

// i18n
import { FormattedMessage } from "react-intl";

// common
import CustomSegment from "./CustomSegmentEdit";
import CustomPropertyDropdown from "../../People/Information/CustomComp/CustomPropertyDropdownFixedEdit";
import CustomSelectedPropDataSegment from "./CustomSelectedPropDataSegment";

// common
import { isNotEmpty, safeGet } from "../../../../common/codes";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import CustomRawDataSegment from "./CustomRawDataSegment";

const CustomMenuFixedEdit = ({
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    //
    const [state] = useContext(StoreContext);
    const { user, setting } = state;
    const { role } = user;
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    //
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );
    //
    const findData = editData.rowData.find(item => {
        return item && isNotEmpty(item?.graph) ? item.graph || "" : "";
    });

    //
    const { graph, eventId, srcId } = findData || {};
    // console.log(findData);
    //
    return (
        <Fragment>
            {/* {editData.rowData.map(cellData => { */}
            {/*    // */}
            {/*    const { rowId, isAllowProp } = cellData; */}
            {/*    // */}
            {/*    if (isAllowProp) { */}
            {/*        return ( */}
            {/*            <CustomRawDataSegment */}
            {/*                key={`segment-${rowId}`} */}
            {/*                rowIdx={rowId} */}
            {/*                eventId={eventId} */}
            {/*                cellData={cellData} */}
            {/*                editData={editData} */}
            {/*                setEditData={setEditData} */}
            {/*                ontologyType={ontologyType} */}
            {/*            /> */}
            {/*        ); */}
            {/*    } */}
            {/* })} */}
            {editData.rowData
                .slice(0)
                .sort((a, b) => {
                    // sort by sorted list
                    const order = fieldPropSortedRecords.map(item =>
                        item.toUpperCase()
                    );
                    const nameA = a.propertyBindRangeStr.toUpperCase();
                    const nameB = b.propertyBindRangeStr.toUpperCase();
                    if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                    if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                    return 0;
                })
                .map(rowData => {
                    //
                    const { propertyBindRangeStr, rowId } = rowData;
                    // console.log(rowData);
                    //
                    const roles = safeGet(
                        fieldAttrRecords,
                        [propertyBindRangeStr, "roles"],
                        []
                    );
                    //
                    if (roles.includes(role)) {
                        return (
                            <CustomSegment
                                key={`segment-${rowId}`}
                                rowIdx={rowId}
                                rowData={rowData}
                                editData={editData}
                                setEditData={setEditData}
                                ontologyType={ontologyType}
                            />
                        );
                    }
                })}
            {editData.selectedProperties.map((prop, propIdx) => {
                //
                return (
                    <CustomSelectedPropDataSegment
                        key={`segment-select-prop-${propIdx}`}
                        rowIdx={propIdx}
                        graph={graph}
                        eventId={eventId || srcId}
                        selectedProp={prop}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                );
            })}
            <Divider />
            <Segment>
                <Label attached="top left">
                    <FormattedMessage
                        id={"people.Information.properties"}
                        defaultMessage={"Properties"}
                    />
                </Label>
                <CustomPropertyDropdown
                    editData={editData}
                    setEditData={setEditData}
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            </Segment>
        </Fragment>
    );
};

export default CustomMenuFixedEdit;
