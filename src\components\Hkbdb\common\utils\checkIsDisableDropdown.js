import { isEmpty } from "../../../../common/codes";

// only for SuggesterForm hasAwardedForWork
const checkIsDisableDropdown = (data, key) => {
    if (!data.willCreatedData.value) return false;
    switch (key) {
        case "hasAwardedForWork__Publication":
            if (
                "hasAwardedForWork__Article" in data.willCreatedData.value &&
                !isEmpty(
                    data.willCreatedData.value["hasAwardedForWork__Article"]
                )
            ) {
                return true;
            }
            break;

        case "hasAwardedForWork__Article":
            if (
                "hasAwardedForWork__Publication" in
                    data.willCreatedData.value &&
                !isEmpty(
                    data.willCreatedData.value["hasAwardedForWork__Publication"]
                )
            ) {
                return true;
            }
            break;

        default:
            return false;
    }
};

export default checkIsDisableDropdown;
