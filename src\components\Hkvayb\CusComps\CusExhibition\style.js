import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_exhibition: props => ({
        ...props.hkvayb_exhibition
    }),
    hkvaby_detail_title: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "32px",
        fontWeight: "500",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.88",
        letterSpacing: "0.51px",
        textAlign: "left",
        color: "#333",
        ...props.hkvaby_detail_title
    })
});

export default useStyles;
