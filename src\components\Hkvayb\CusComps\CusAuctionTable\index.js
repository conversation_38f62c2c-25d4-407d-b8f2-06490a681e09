import React, { useContext } from "react";

import { FormattedMessage } from "react-intl";

import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import TableHead from "@mui/material/TableHead";
import TableContainer from "@mui/material/TableContainer";
import TableCell from "@mui/material/TableCell";

import config from "../../config";

import CusValue from "../CusValue";
import CusLoading from "../CusLoading";

import useFetch from "../../CusHooks/useFetch";
import useObjToRows from "../../CusHooks/useObjToRows";
import useTripleMerge from "../../CusHooks/useTripleMerge";

import { url } from "../../../../api/hkvayb";

import { bs64Encode, isEmpty, safeGet } from "../../../../common/codes";

import { StoreContext } from "../../../../store/StoreProvider";

const coverLoadingDiv = {
    hkvayb_div: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "40px 0 40px 0"
    }
};

const coverCusValueStyle = {
    hkvayb_div: {
        marginLeft: "unset"
    }
};

const CusAuctionTable = ({ eventId }) => {
    const [state] = useContext(StoreContext);
    const { locale } = state.user;

    const api =
        locale === config.languages.zhHans
            ? url.hkvayb.DETAILPAGE_ZH_DETAIL_EVENT_INFORMATION
            : url.hkvayb.DETAILPAGE_EN_DETAIL_EVENT_INFORMATION;
    const apiUrl = api.replace("{keyword}", `${bs64Encode(eventId || "")}`);
    const result = useFetch(apiUrl);
    const { data, loading } = result;

    const mergedData = useTripleMerge(data.data);
    const rows = useObjToRows(mergedData);

    if (loading) {
        return <CusLoading style={coverLoadingDiv} />;
    }

    if (isEmpty(rows)) {
        return null;
    }

    return (
        <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.no"
                                defaultMessage="No."
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.lotName"
                                defaultMessage="Lot Name"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.artist"
                                defaultMessage="Artist"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.year"
                                defaultMessage="Year"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.price"
                                defaultMessage="Price"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.auction.table.header.description"
                                defaultMessage="Description"
                            />
                        </TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {rows &&
                        rows.map(cells => {
                            const defObj = { type: null, value: [] };
                            const lotNo = safeGet(cells, ["lotNo"], defObj);
                            const label = safeGet(cells, ["label"], defObj);
                            const artist = safeGet(cells, ["hasArtist"], defObj);
                            const year = safeGet(cells, ["displayAuctionYear"], defObj);
                            const price = safeGet(cells, ["price"], defObj);
                            const desc = safeGet(cells, ["desc"], defObj);
                            return (
                                <TableRow
                                    key={lotNo.value}
                                    sx={{
                                        "&:last-child td, &:last-child th": {
                                            border: 0
                                        }
                                    }}
                                >
                                    <TableCell component="th" scope="row">
                                        <CusValue
                                            {...lotNo}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...label}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...artist}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...year}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            prefix="HK$"
                                            {...price}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...desc}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                </TableBody>
            </Table>
        </TableContainer>
    );
};

export default CusAuctionTable;
