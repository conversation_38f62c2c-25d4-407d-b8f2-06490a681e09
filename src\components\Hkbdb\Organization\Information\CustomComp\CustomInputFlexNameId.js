import React, { useEffect, useState } from "react";

// ui
import { Label, Input, Icon } from "semantic-ui-react";

// common comp
import CustomDebounce from "../../../EntityComponent/TableComp/CustomDeBounce";
import CustomNameIdList from "../CustomComp/CustomNameIdList";

// common func
import { isNotEmpty } from "../../../../../common/codes";

const CustomInput = ({ property, createData, setCreateData }) => {
    //
    const { ontologyType } = createData;
    const { value: propertyName, label, required } = property;
    //
    const [selectedValue, setSelectedValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSelectedValue = CustomDebounce(selectedValue, 500);
    //
    const { perInfo } = createData;

    const handleUpdateState = () => {
        if (isNotEmpty(propertyName) && isNotEmpty(debSelectedValue)) {
            // if (isNotEmpty(propertyName)) {
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyName]: debSelectedValue
                    }
                }
            }));
        } else {
            console.log(
                "CustomInput param error, propertyName: ",
                propertyName,
                "debSelectedValue:",
                debSelectedValue
            );
        }
    };
    //
    const handleChange = (event, { value }) => {
        // console.log(value);
        if (isNotEmpty(value)) {
            setSelectedValue(value);
        }
    };
    //
    useEffect(() => {
        handleUpdateState();
    }, [debSelectedValue]);
    //
    return (
        <>
            <Input
                fluid
                labelPosition="left"
                type="text"
                onChange={handleChange}
            >
                <Label>
                    {label}
                    {required && (
                        <Icon
                            style={{ marginLeft: "1.5em" }}
                            color="red"
                            name="asterisk"
                            size="mini"
                        />
                    )}
                </Label>
                <input />
            </Input>
            <CustomNameIdList
                perInfo={perInfo?.infoData || {}}
                propertyName={propertyName}
            />
        </>
    );
};

export default CustomInput;
