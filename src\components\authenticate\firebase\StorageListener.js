import { useContext, useEffect } from "react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import { getSwgJson } from "../../../api/firebase/storage";
import axios from "axios";

const StorageListener = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { user, main } = state;
    const { uid, currentUser } = user;
    const { webStyle, image, swgJSON } = main;

    // get webStyle setting
    useEffect(() => {
        if (!swgJSON.storage) return;
        getSwgJson(swgJSON.storage).then(urltoken => {
            // console.log("urltoken", urltoken);

            // todo: use fetch or axios to get json file
            fetch(urltoken, { mode: "no-cors" })
                .then(res => {
                    // console.log("res", res);
                    return res.blob();
                })
                .then(data => {
                    // console.log("data", data);
                })
                .catch(err => {
                    console.log(err);
                });
        });
    }, [swgJSON]);

    return null;
};

export default StorageListener;
