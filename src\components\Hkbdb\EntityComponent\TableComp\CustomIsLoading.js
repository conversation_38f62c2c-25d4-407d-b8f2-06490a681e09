import React from "react";

// ui
import { Message, Icon } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

const CustomIsLoading = () => {
    return (
        <Message icon>
            <Icon name="circle notched" loading />
            <Message.Content>
                {/* We are fetching that content for you. */}
                <FormattedMessage
                    id={"people.Information.isLoading"}
                    defaultMessage={"Loading..."}
                />
            </Message.Content>
        </Message>
    );
};

export default CustomIsLoading;
