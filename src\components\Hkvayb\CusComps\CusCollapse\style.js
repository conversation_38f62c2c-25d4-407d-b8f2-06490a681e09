import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_collapse: props => ({
        width: "100%",
        ...props.hkvayb_collapse
    }),
    hkvayb_divider: props => ({
        height: "1px",
        margin: "23.5px 0 15.5px",
        backgroundColor: "#676767",
        ...props.hkvayb_divider
    }),
    hkvayb_downArrow: props => ({
        width: "12px",
        height: "6px",
        margin: "24px 457.5px 0 458.5px",
        objectFit: "contain",
        ...props.hkvayb_downArrow
    }),
    hkvayb_div: props => ({
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        cursor: "pointer",
        ...props.hkvayb_div
    })
});

export default useStyles;
