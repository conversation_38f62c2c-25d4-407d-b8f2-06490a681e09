import React from "react";

import useStyles from "./style";

const CusResultItem = ({ label, children, primary, secondary, style = {} }) => {
    const classes = useStyles(style);
    let className = classes.hkvayb_default;
    if (primary) className = classes.hkvayb_primary;
    if (secondary) className = classes.hkvayb_secondary;
    return (
        <div>
            <div className={className}>{label || children}</div>
        </div>
    );
};

export default CusResultItem;
