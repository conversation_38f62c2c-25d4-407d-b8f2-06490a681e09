import { configureStore } from "@reduxjs/toolkit";
//
import commonReducer from "./commonSlice";

export default configureStore({
    reducer: {
        common: commonReducer
    },
    middleware: getDefaultMiddleware =>
        getDefaultMiddleware({
            // 忽略 non-serialization 的 value:例如 function
            serializableCheck: {
                // Ignore these action types
                ignoredActions: ["common/setCommonDialogContext"],
                // Ignore these field paths in all actions
                ignoredActionPaths: [],
                // Ignore these paths in the state
                ignoredPaths: ["common.dialogContext.onYes"]
            }
        })
});
