import Act from "../actions";

const initState = {
    queryString: "",
    searchString: "",
    selectedAuthorId: "",
    selectedQueryId: "",
    queries: [],
    privateQueries: [],
    publicQueries: [],
    queryResult: { head: [], data: [] },
    pagination: {
        activePage: 1,
        limit: 10
    },
    renderSignal: ""
};

const queryReducer = (state = initState, action) => {
    switch (action.type) {
        // queryString
        case Act.QUERY_QUERY_STRING_SET:
            return { ...state, queryString: action.payload };
        case Act.QUERY_QUERY_STRING_CEL:
            return { ...state, queryString: "" };
        // queryId
        case Act.QUERY_SELECTED_QUERY_ID_SET:
            return { ...state, selectedQueryId: action.payload };
        case Act.QUERY_SELECTED_QUERY_ID_CEL:
            return { ...state, selectedQueryId: "" };
        // authorId
        case Act.QUERY_SELECTED_AUTHOR_ID_SET:
            return { ...state, selectedAuthorId: action.payload };
        case Act.QUERY_SELECTED_AUTHOR_ID_CEL:
            return { ...state, selectedAuthorId: "" };
        // store queries
        case Act.QUERY_QUERIES_SET:
            return { ...state, queries: action.payload };
        case Act.QUERY_PRIVATE_QUERIES_SET:
            return { ...state, privateQueries: action.payload };
        case Act.QUERY_PUBLIC_QUERIES_SET:
            return { ...state, publicQueries: action.payload };
        // search keyword
        case Act.QUERY_SEARCH_KEYWORD_SET:
            return { ...state, searchString: action.payload };
        // query result
        case Act.QUERY_RESULT_SET:
            return { ...state, queryResult: action.payload };
        case Act.QUERY_RESULT_CEL:
            return {
                ...state,
                queryResult: { head: { vars: [] }, results: { bindings: [] } }
            };
        // ui render signal
        case Act.QUERY_RELOAD_RENDER_SIGNAL_SET:
            return { ...state, renderSignal: action.payload };
        //
        case Act.QUERY_PAGINATION_ACTIVE_PAGE_SET:
            return {
                ...state,
                pagination: { ...state.pagination, activePage: action.payload }
            };
        case Act.QUERY_PAGINATION_LIMIT_SET:
            return {
                ...state,
                pagination: { ...state.pagination, limit: action.payload }
            };
        case Act.QUERY_PAGINATION_LIMIT_CEL:
            return {
                ...state,
                pagination: { ...state.pagination, limit: 10 }
            };
        default:
            return state;
    }
};

export default queryReducer;
