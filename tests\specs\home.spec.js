// @ts-check
// const { test, expect } = require("@playwright/test");

const { test, expect, suite, chromium } = require("@playwright/test");

const {
    domain,
    locales,
    defaultDomain,
    defaultLocale
} = require("../config/base");

// group tests in a group
test.describe("Home Page/basic", () => {
    // Expect url to be visible in 3 seconds
    test("url is visible in 3 seconds", async ({ page }) => {
        await page.goto(`${defaultDomain}/${defaultLocale}/`);

        // Expect the url to be visible in the page after 3 seconds
        await page.waitForTimeout(3000);
    });

    // Expect title to be 'HKBDB'
    test("title is 'HKBDB'", async ({ page }) => {
        await page.goto(`${defaultDomain}/${defaultLocale}/`);

        const title = await page.title(); // Get the title of the page
        expect(title).toBe("HKBDB"); // Assert that the title is "HKBDB"
    });
});

const navigationNoAuth = [
    { label: "瀏覽", url: "/browse", text: "輸入檢索詞" },
    { label: "數據來源", url: "/sources", text: "數據來源" },
    { label: "簡介", url: "/about", text: "簡介" }
];

// test navbar
test.describe("Home Page/navbar", () => {
    // Expect url to be visible in 3 seconds
    test("navbar", async ({ page }) => {
        await page.goto(`${defaultDomain}/${defaultLocale}/`);

        // Expect the url to be visible in the page after 3 seconds
        await page.waitForTimeout(3000);

        // Expect .secondary.menu to be visible
        const navbar = await page.isVisible(".secondary.menu");

        // iterate through navigationNoAuth
        // each item should be visible inside .secondary.menu
        for (let item of navigationNoAuth) {
            // locate the item inside .secondary.menu with textContent equal to item.label
            const navItem = await page.isVisible(
                `.secondary.menu a.item span:has-text("${item.label}")`
            );
            expect(navItem).toBe(true);

            // click the item and expect the url to be equal to item.url
            await page.click(
                `.secondary.menu a.item span:has-text("${item.label}")`
            );
            // check if the url changed after clicking the item
            // await page.waitForNavigation();
            // wait for 3 seconds
            // await page.waitForTimeout(3000);

            const expectedUrl = `${defaultDomain}/${defaultLocale}${item.url}`;
            console.log("expectedUrl", expectedUrl);
            await page.waitForURL(expectedUrl);

            const url = page.url();
            console.log("url", url);
            //
            expect(page.url()).toBe(expectedUrl);

            // Expect div (not .desktop__menu) to has textContent equal to item.text
            const div = await page.isVisible(`div:has-text("${item.text}")`);

            // go back
            await page.goBack();
        }
    });
});

// test footer
test.describe("Home Page/footer", () => {
    // Expect url to be visible in 3 seconds
    test("footer is visible", async ({ page }) => {
        await page.goto(`${defaultDomain}/${defaultLocale}/`);

        // Expect the url to be visible in the page after 3 seconds
        await page.waitForTimeout(3000);

        const footer = await page.isVisible(".home-footer");
        expect(footer).toBe(true);

        const footerText = await page.innerText(".home-footer");
        expect(footerText).toBe("© 2024 香港中文大學圖書館 版權所有");
    });
});
