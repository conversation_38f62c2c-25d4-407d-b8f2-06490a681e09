import React, { useContext, useState } from "react";
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";
import CustomAlertMessage from "../CustomAlertMessage";
import {
    Api,
    deleteHkbdbData,
    readHkbdbData
} from "../../../../../api/hkbdb/Api";
import { bs64EncodeId } from "../../../../../common/codes/jenaHelper";
import { StoreContext } from "../../../../../store/StoreProvider";
import SugFixedDeleteTable from "./SugFixedDeleteTable";
import Act from "../../../../../store/actions";

/** Suggester delete modal */
function SugFixedDeleteModal({
    open,
    setOpen,
    data,
    ontologyType,
    headers,
    handleDelete,
    type
}) {
    const [state, dispatch] = useContext(StoreContext);
    const { property } = state;
    const { ontologyDefined } = property;

    const errorMsg = {
        updateFailed: "Update unsuccessful !",
        notFoundPropStr: "PropObj not found !"
    };

    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const handleClose = () => {
        // close modal
        setOpen(false);
    };

    const handleDeleteFct = async () => {
        const { graph, prop, val, id, sheetName } = data;
        const apiStr = Api.getDraftIDType(id, sheetName);

        try {
            const propObj = ontologyDefined[ontologyType].find(
                ({ property }) => property === prop
            );
            if (propObj) {
                const { data } = await readHkbdbData(apiStr);
                const [{ type }] = data;
                const entry = {
                    classType: type,
                    srcId: bs64EncodeId(id),
                    graph,
                    value: {
                        [propObj.propertyBindRangeStr]: [val]
                    }
                };
                const res =
                    sheetName === "relationevent"
                        ? await deleteHkbdbData(Api.deleteGraph(), entry)
                        : await deleteHkbdbData(Api.restfulDraft(), entry);

                if (!res.state) {
                    throw new Error(errorMsg.updateFailed);
                }

                setAlertMsg(prevMsg => ({
                    ...prevMsg,
                    title: "已更新",
                    type: "success",
                    content: `已成功刪除 property。`,
                    renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
                }));

                dispatch({
                    type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                    payload: {
                        target: null,
                        signal: `delete-${new Date().getTime()}`
                    }
                });
            } else {
                throw new Error(errorMsg.notFoundPropStr);
            }
        } catch (err) {
            console.log(err);
            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: "未更新",
                type: "error",
                content: `未成功刪除 property。`,
                renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
            }));
        }
    };

    return (
        <Modal open={open}>
            <Modal.Header>
                <FormattedMessage
                    id={"people.Information.header.delete.content"}
                    defaultMessage={"Delete Content"}
                />
            </Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description style={{ width: "100%" }}>
                    {/* alert */}
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    <SugFixedDeleteTable
                        data={data}
                        property={property}
                        headers={headers}
                        ontologyType={ontologyType}
                        type={type}
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    // loading={isLoading}
                    onClick={
                        type === "temporary" ? handleDelete : handleDeleteFct
                    }
                    color="red"
                >
                    <FormattedMessage
                        id="people.Information.button.delete"
                        defaultMessage="Delete"
                    />
                </Button>
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id="people.Information.button.cancel"
                        defaultMessage="Cancel"
                    />
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

export default SugFixedDeleteModal;
