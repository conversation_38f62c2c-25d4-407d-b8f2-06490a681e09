import base64url from "base64url";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import * as _ from "lodash";
import { bs64EncodeId } from "./jena<PERSON><PERSON>per";

const { Api } = require("../../api/hkbdb/Api");
const { GV } = require("./globalVars");

/**
 *
 * @param obj
 * @returns {boolean}
 */
const isEmpty = obj => Object.keys(obj || {}).length === 0;

/**
 *
 * @param obj
 * @returns {boolean}
 */
const isNotEmpty = obj => !isEmpty(obj);

/**
 *
 * @param obj
 * @returns {boolean}
 */
const isTrue = obj =>
    `${obj}`.toLowerCase().trim() === "true" ||
    `${obj}`.toLowerCase().trim() === "1";

/**
 *
 * @param num
 * @returns {boolean}
 */
const isNumeric = num => {
    const intN = parseInt(num);
    return typeof intN === "number" && !isNaN(intN);
};

/**
 *
 * @param value
 * @param defValue
 * @returns {*}
 */
const isShow = (value, defValue) => {
    return isNotEmpty(value) ? value : defValue;
};

/**
 *
 * @param obj
 * @returns {boolean}
 */
const isFunction = obj => typeof obj === "function";

/**
 *
 * @param keys
 * @param obj
 * @param defVal
 * @returns {*}
 */
const safeGet = (obj, keys, defVal = null) =>
    keys.reduce(
        (prevObj, key) => (prevObj && prevObj[key] ? prevObj[key] : defVal),
        obj
    );

/**
 * 禁止用在大的 object 上
 * @param obj1
 * @param obj2
 * @returns {boolean}
 */
const isEquals = (obj1, obj2) => {
    if (
        (isEmpty(obj1) && !isEmpty(obj2)) ||
        (!isEmpty(obj1) && isEmpty(obj2))
    ) {
        return false;
    }
    //
    const o1Keys = Object.keys(obj1);
    const o2Keys = Object.keys(obj2);
    if (o1Keys.length !== o2Keys.length) {
        return false;
    }
    //
    o1Keys.forEach(key => {
        if (!o2Keys.includes(key)) {
            return false;
        }
    });
    o2Keys.forEach(key => {
        if (!o1Keys.includes(key)) {
            return false;
        }
    });
    //
    const sortedKeys = [...new Set([...o1Keys, ...o2Keys].sort())];
    const newObj1 = JSON.stringify(
        sortedKeys.map(key => ({ [key]: obj1[key] }))
    );
    const newObj2 = JSON.stringify(
        sortedKeys.map(key => ({ [key]: obj2[key] }))
    );
    return newObj1 === newObj2;
};

// check if values in obj all empty
const leastOneValueEmpty = obj => {
    let isEmpty = false;
    const values = Object.values(obj || {});
    values.forEach(v => {
        if (!v) isEmpty = true;
    });
    return values.length === 0 || isEmpty;
};

const getDate = () => {
    const nowDate = new Date();
    let mm = nowDate.getMonth() + 1;
    let dd = nowDate.getDate();
    return [
        nowDate.getFullYear() - 1911,
        (mm > 9 ? "" : "0") + mm,
        (dd > 9 ? "" : "0") + dd
    ].join("-");
};

const getTimeSince = date => {
    if (typeof date !== "object") {
        date = new Date(date);
    }

    let seconds = Math.floor((new Date() - date) / 1000);
    let intervalType = "";
    let interval = Math.floor(seconds / 31536000);

    if (interval >= 1) {
        intervalType = "year";
    } else {
        interval = Math.floor(seconds / 2592000);
        if (interval >= 1) {
            intervalType = "month";
        } else {
            interval = Math.floor(seconds / 86400);
            if (interval >= 1) {
                intervalType = "day";
            } else {
                interval = Math.floor(seconds / 3600);
                if (interval >= 1) {
                    intervalType = "hour";
                } else {
                    interval = Math.floor(seconds / 60);
                    if (interval >= 1) {
                        intervalType = "minute";
                    } else {
                        interval = seconds;
                        intervalType = "second";
                    }
                }
            }
        }
    }

    if (interval > 1 || interval === 0) {
        intervalType += "s";
    }

    return interval + " " + intervalType + " age";
};

const findPropertyRange = (property, propertyArr = []) => {
    const found = propertyArr.find(
        p => p.value === property || p.property === property
    );
    return found ? found.type || found.range : null;
};

const isDisplayDate = property => {
    if (!property.startsWith(GV.DATE_PROPERTY_STARTS)) {
        return false;
    }
    return property.endsWith(GV.DATE_PROPERTY_ENDS);
};

const convertDateEventToFormalDate = (dateEvent, property, propertyArr) => {
    // Error check
    if (!dateEvent) {
        return dateEvent;
    }

    // 只有 type 是 DateEvent 才做 convert
    const type = findPropertyRange(property, propertyArr);
    if (!type) {
        return dateEvent;
    }

    // display[...]Date pass
    const isDateStr = isDisplayDate(property);
    if (type !== GV.DATE_EVENT && !isDateStr) {
        return dateEvent;
    }

    // 資料格式錯誤
    if (!dateEvent.includes("-")) {
        return dateEvent;
    }

    const splitDate = dateEvent.split("-");

    // 資料格式錯誤
    if (splitDate.length !== 3) {
        return dateEvent;
    }

    let resStr = "";
    for (let idx = 0; idx < splitDate.length; idx += 1) {
        // 1898--
        if (splitDate[idx] === "") {
            break;
        }
        // 00
        if (parseInt(splitDate[idx], 10) === 0) {
            break;
        }

        if (idx === 0) {
            resStr += splitDate[idx];
        } else {
            resStr += `-${splitDate[idx]}`;
        }
    }
    return resStr;
};

// date format : "DAE_2024-01-10" => 2024-01-10
const convertDateEvent2 = dateEvent => {
    // Error check
    if (!dateEvent) {
        return dateEvent;
    }

    const splitDate = dateEvent.split("-");
    // 資料格式錯誤
    if (splitDate.length !== 3) {
        return dateEvent;
    }

    // ['DAE_2024', '01', "10"]
    let resStr = "";
    for (let idx = 0; idx < splitDate.length; idx += 1) {
        // 1898--
        if (splitDate[idx] === "") {
            break;
        }
        if (idx === 0) {
            resStr += splitDate[idx].split("DAE_")[1];
        } else {
            if (parseInt(splitDate[idx], 10) === 0) {
                resStr += "-00";
            } else {
                resStr += `-${splitDate[idx]}`;
            }
        }
    }
    return resStr;
};

const getFormatUser = user => {
    const {
        uid,
        displayName,
        email,
        emailVerified,
        isAnonymous,
        providerData,
        metadata
    } = user;
    const safeProviderId =
        providerData && providerData[0]
            ? providerData && providerData[0].providerId
            : "unKnown";
    const {
        a: creationTimestamp,
        b: lastSignInTimestamp,
        creationTime,
        lastSignInTime
    } = metadata;
    return {
        uid: uid,
        displayName: displayName,
        email: email,
        emailVerified: emailVerified,
        isAnonymous: isAnonymous,
        providerId: safeProviderId,
        creationTimestamp: creationTimestamp,
        lastSignInTimestamp: lastSignInTimestamp,
        creationTime: creationTime,
        lastSignInTime: lastSignInTime
    };
};

const getYear = date => {
    if (!date) return null;
    const reg = /\d+/;
    /**
     * ca(大約);  fl(全盛時期); active
     date => match
     1999 => [1999]
     888 => [888]
     ca. 1008 => [1008]
     fl. 1007 => [1007]
     active 1146 => [1146]
     1008 or 1009  => [1008, 1009]
     1920 June 16  => [1920, 16]
     1927 April  => [1927]
     */

    const match = date.match(reg);
    if (match) return match[0];
    return null;
};

const getProperty = (property, propertyObj) => {
    const lang = Api.getLocale().startsWith("zh") ? "zh" : Api.getLocale();
    return Object.keys(propertyObj[lang]).indexOf(property) < 0
        ? property
        : propertyObj[lang][property];
};

const genRandomKey = length => {
    let result = "";
    let characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
    }
    return result;
};

const handleNameSpellCheck = name => {
    if (isEmpty(name)) {
        return "";
    }

    // bad_chars_list convert list
    const badCharsChange = {
        " ": "_",
        "^": "_",
        "\\\\": "_",
        "|": "_",
        "<": "_",
        ">": "_",
        "{": "_",
        "}": "_",
        '"': "_",
        "`": "_"
    };
    if (name.length > 0) name = name.trim();

    const chars = (name || "").split("").map(c => {
        let tmpC = c;
        Object.keys(badCharsChange).forEach(bc => {
            const cvtChar = badCharsChange[bc];
            if (c && c.indexOf(bc) >= 0) {
                tmpC = cvtChar;
            }
        });
        return tmpC;
    });
    return chars.join("").replace(/\\/gm, "_");

    // 下面這個方法在 名字中有 | 這個符號時會失敗
    // e.g. 柯靈|陳蝶衣
    // # remove bad_chars
    // Object.keys(badCharsChange).forEach(bc => {
    //     const cvtChar = badCharsChange[bc];
    //     if (name && name.indexOf(bc) >= 0) {
    //         name = `${name || ""}`.replace(new RegExp(bc, "g"), cvtChar);
    //     }
    // });
    //
    // return name.replace(/\\/gm, "_");
};

/**
 * encode url (include dot .) to prevent path match error
 * e.g. "abc.def"  => "abc%2Edef"
 *
 * @param url
 * @returns {string}
 */
const encodeUrl = url => {
    return encodeURIComponent(url).replace(/\./g, "%2E");
};

// base64 encode url
const bs64Encode = word => {
    // safe string to prevent encode error
    return base64url.encode(word || "");
};

// base64 decode url
const bs64Decode = word => {
    return base64url.decode(word);
};

const exportImage = async (element, options = {}) => {
    try {
        const fileName = options.fileName || "image.png";
        const resolution = options.resolution || 3;

        const opts = {
            useCORS: true,
            scale: resolution,
            scrollX: 0,
            scrollY: -window.scrollY // fix scroll bottom problem
        };
        if (options.width) {
            opts.width = options.width + 50;
        }
        if (options.height) {
            opts.height = options.height + 50;
        }

        const canvas = await html2canvas(element, opts);
        const url = canvas.toDataURL("image/png", 1);
        const aTag = document.createElement("a");
        aTag.href = url;
        aTag.download = fileName;
        document.body.appendChild(aTag);
        aTag.click();
        aTag.remove();
        // export image successfully
        return Promise.resolve({ finish: true });
    } catch (err) {
        // failed to export image
        return Promise.reject(err);
    }
};

const exportPdfHelper = (ref, docTitle, filename) => {
    // use lodash to check if ref is DOM element
    if (!_.isElement(ref)) return;
    // Default export is a4 paper, portrait, using millimeters for units
    // eslint-disable-next-line new-cap
    const doc = new jsPDF({
        unit: "px",
        format: [595.28, 841.89]
    });
    doc.text(docTitle || "", 20, 30);
    const timeStamp = new Date();
    const timeStampStr = `${timeStamp.getFullYear()}-${timeStamp.getMonth()}-${timeStamp.getDate()}-${Math.floor(
        Math.random() * 1000
    )}`;
    doc.html(ref, {
        callback: function(doc) {
            doc.save(filename || `export-pdf-${timeStampStr}`);
        },
        x: 20,
        y: 60
    });
};

// 針對 dataset 名稱, 那些語系情況下需要變更 dataset 名稱
const GRAPH_LOCALE_CHANGE = ["zh"];
/**
 * 轉換 dataset 的中英文名稱
 * @param names: ["auda_spc", "hkwrpr"] or "auda_spc"
 * @param datasets: [
 * { dataset: "abcwhkp", label: "《香港古典詩文集經眼錄》", lang: "zh" },
 * { dataset: "hklit", label: "香港文學作家傳略", lang: "zh" },...
 * ]
 * @param chLocale: 那些語系需要變更
 * @returns: ["", ""]
 */
const cvtDatasetLocale = (
    names = [],
    datasets = [],
    chLocale = GRAPH_LOCALE_CHANGE
) => {
    if (!Array.isArray(datasets)) return names;
    const datasetMap = datasets.reduce((acc, cur, curIdx) => {
        if (!cur.dataset) return acc;
        acc[cur.dataset] = Object.assign({}, cur);
        return acc;
    }, {});

    if (Array.isArray(names)) {
        const namesLocale = Object.assign([], names);
        return namesLocale.map(name => {
            return datasetMap[name] &&
                datasetMap[name].label &&
                chLocale.indexOf(datasetMap[name].lang) >= 0
                ? datasetMap[name].label
                : name;
        });
    }

    switch (names) {
        case "maw":
            return "作家及藝術家增訂";
        default:
            return datasetMap?.[names] &&
                datasetMap[names].label &&
                chLocale.indexOf(datasetMap[names].lang) >= 0
                ? datasetMap[names].label
                : names;
    }
};

const findSpecialId = (type, data) => {
    // 基本資料/人際關係 的資料結構與其它不同
    // { title: "penName", values: (3) […], dataset: (2) […] }
    if (
        `${type}`.toLowerCase() === "relationevent" ||
        `${type}`.toLowerCase() === "person"
    ) {
        // 不同處理方式，寫這段判斷為防止誤用。
        return { newSrcId: null, newSrcKey: null };
    }

    // 其它大部份的資料結構:
    // { hasEndDate: "1967-00-00", hasEducatedAt: "香港中文大學",
    // _eventId: "EDE8", hasAcademicDiscipline: "中文系", graph: "hkwrpr" }
    // 如果有定義系統名稱(開頭為'_')，則換 srcId
    let newSrcId = Object.keys(data).filter(
        p =>
            p[0] === GV.SYSTEM_PREFIX || GV.SYSTEM_HIDE_PROPERTY.indexOf(p) >= 0
    );
    let newSrcKey = null;

    // 過濾掉系統名稱
    if (newSrcId && newSrcId[0] && newSrcId[0].length > 0) {
        newSrcKey = newSrcId[0];
        newSrcId = bs64EncodeId(data[newSrcKey]);
    } else {
        newSrcId = null;
    }
    return { newSrcId, newSrcKey };
};

/**
 * 判斷是否為正式站 db, 若不包含關鍵字, 就不是正式站 db
 *
 * @param dbObj => e.g. {dbName: "正式站", label:"hkbdb"}
 * {dbName: "測式站", label:"hkbdb2"}
 * @returns {boolean}
 */
const isProductionDb = dbObj => {
    if (!dbObj) return true;
    else if (
        dbObj &&
        dbObj.dbName &&
        (dbObj.dbName.includes("正式") ||
            dbObj.dbName === "db正式站" ||
            dbObj.label === "hkbdb")
    )
        return true;
    return false;
};

/**
 * 判斷是否需要隱藏，如果 hide 為 1，則需要隱藏。
 *
 * @returns {boolean}
 * @param basicInfo
 */
const isHide = basicInfo => {
    if (!basicInfo) {
        return false;
    }
    // { basicProperty: "hide", basicData: "1", graph: "control" }
    const found = basicInfo.find(
        row => row.basicProperty === GV.FEATURE_HIDE_PROPERTY
    );
    return found ? found.basicData === GV.FEATURE_HIDE_VALUE_HIDE : false;
};

/**
 * 將 true/false 轉換為 "1"/"0"
 *
 * @returns {string}
 * @param hide
 */
const convertHideValue = hide => {
    return hide ? GV.FEATURE_HIDE_VALUE_HIDE : GV.FEATURE_HIDE_VALUE_UN_HIDE;
};

// transform ms to mm:ss
const millisToMMSS = millis => {
    const minutes = Math.floor(millis / 60000);
    const seconds = ((millis % 60000) / 1000).toFixed(0);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
};

const millisToSS = millis => {
    return millis / 1000;
};

export {
    isEmpty,
    isNotEmpty,
    isTrue,
    isEquals,
    isNumeric,
    isShow,
    isFunction,
    leastOneValueEmpty,
    getDate,
    convertDateEventToFormalDate,
    getTimeSince,
    getFormatUser,
    getYear,
    getProperty,
    genRandomKey,
    encodeUrl,
    handleNameSpellCheck,
    safeGet,
    bs64Encode,
    bs64Decode,
    exportImage,
    cvtDatasetLocale,
    findSpecialId,
    isProductionDb,
    isHide,
    convertHideValue,
    exportPdfHelper,
    millisToMMSS,
    millisToSS,
    findPropertyRange,
    convertDateEvent2
};
