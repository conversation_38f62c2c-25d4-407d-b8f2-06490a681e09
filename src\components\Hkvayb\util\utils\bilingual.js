import { safeGet } from "../../../../common/codes";

const bilingual = defObj => (data, key) => {
    if (Array.isArray(key)) {
        const [keyA, KeyB] = key;
        return [
            safeGet(data, ["zh", keyA], defObj),
            safeGet(data, ["en", KeyB], defObj)
        ];
    }
    return [
        safeGet(data, ["zh", key], defObj),
        safeGet(data, ["en", key], defObj)
    ];
};

export default bilingual;