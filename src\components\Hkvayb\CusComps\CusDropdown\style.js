import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_dropdown: props => ({
        backgroundColor: "#fff",
        borderRadius: "unset!important",
        width: "100%",
        maxWidth: "50%",
        height: "48px",
        marginRight: "16px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            marginRight: "0px!important"
        },
        "& .MuiOutlinedInput-notchedOutline": {
            borderStyle: "unset",
            borderWidth: "unset",
            borderColor: "unset",
            border: "0"
        },
        "& .MuiSelect-select": {
            color: "#b79d79",
            // fontSize: "16px",
            fontFamily: "NotoSansHK"
        },
        ...props.hkvayb_dropdown
    })
});

export default useStyles;
