import React, { useState, useEffect, useContext } from "react";
// import PropTypes from "prop-types";
import EntitySearch from "./subComponents/entitySearch";
import { Container, Grid } from "semantic-ui-react";
import { ResponsiveContainer } from "../../layout/Layout";
import { injectIntl } from "react-intl";
import TreeView from "./subComponents/TreeView";
import IndividualsPanel from "./subComponents/IndividualsPanel";
import EntityBody from "./subComponents/entitySearch/EntityBody";

// store
import { StoreContext } from "../../store/StoreProvider";

import { getDataset } from "../Sources/action";
//
import "./browsePage.scss";
import useWindowSize from "../../hook/useWindowSize";
import uiConfig from "../../config/config-ui";

// TODO: propagate selected class to context of the app
const BrowsePage = props => {
    const [state, dispatch] = useContext(StoreContext);
    const { user: globalUser } = state;
    const { locale } = globalUser;
    const { user } = props;
    const [name, setName] = useState("");
    const [path, setPath] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    // hook
    const size = useWindowSize();
    const isMobile = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= uiConfig.BP_PAD_MIN_WIDTH;
    };

    const isPad = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= uiConfig.BP_DESK_MIN_WIDTH;
    };

    // 儲存目前選取的類別 name(e.g. 'Person', 'Organization')
    // & 該類別的總數，用於計算分頁頁碼總數
    const onClassClicked = (name, path, totalDisplayedIndividuals) => {
        if (user && user.isAnonymous && user.readablePeople) {
            setName(name);
            setPath(path);
        } else {
            setName(name);
            setPath(path);
        }
    };

    // 取得資料集清單
    useEffect(() => {
        // get dataset and store in global state
        getDataset(dispatch, locale, setIsLoading);
    }, [locale]);

    const leftColWidth = () => {
        if (isPad()) return 16;
        return 7;
    };
    const rightColWidth = () => {
        if (isPad()) return 16;
        return 9;
    };

    return (
        <div className={"browsePage"}>
            <ResponsiveContainer {...props}>
                {/* search bar */}
                <EntitySearch EntityBody={EntityBody} />

                <Container>
                    <Grid stackable padded>
                        <Grid.Column width={leftColWidth()}>
                            {/* left side 階層圖 */}
                            <TreeView onClassClicked={onClassClicked} />
                        </Grid.Column>

                        <Grid.Column width={rightColWidth()}>
                            {/* right side 實體清單 */}
                            <IndividualsPanel
                                user={user}
                                name={name}
                                path={path}
                            />
                        </Grid.Column>
                    </Grid>
                </Container>
            </ResponsiveContainer>
        </div>
    );
};

export default injectIntl(BrowsePage);
