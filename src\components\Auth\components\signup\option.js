import PersonOutlineOutlined from "@material-ui/icons/PersonOutlineOutlined";
import LocationCityOutlined from "@material-ui/icons/LocationCityOutlined";
import WorkOutline from "@material-ui/icons/WorkOutline";
import MailOutlineIcon from "@material-ui/icons/MailOutline";
import Book from "@material-ui/icons/Book";
import HttpsOutlined from "@material-ui/icons/HttpsOutlined";
import React from "react";
import {
    validateEmail,
    validatePassword
} from "../../../../common/codes/validator";

export const fieldName = {
    username: "username",
    institution: "institution",
    position: "position",
    researchPurpose: "researchPurpose",
    email: "email",
    password: "password",
    passwordConfirm: "passwordConfirm"
};

const passwdHint = intl =>
    intl.formatMessage({
        id: "signup.input.password.hint",
        defaultMessage:
            "At least 8 characters containing uppercase, lowercase letters and numbers"
    });

/**
 * ==fieldsCfg key 說明==
 * className: input 的 className
 * focusEle: focus element clasname
 * placeholder: input placeholder
 * required: 是否為必填欄位
 * hint: input 輸入提示
 * error: input 是否輸入錯誤
 * display: 該欄位使否顯示
 * writeRtDb: 使用者輸入資訊是否寫入 realtime db
 * step: 在哪幾個步驟顯示
 */

export const fieldsCfg = [
    {
        name: fieldName.username,
        className: "signup__input signup__input--username",
        focusEle: ".signup__input--username input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            intl.formatMessage({
                id: "signup.input.username.required",
                defaultMessage: "Last & First Name (Chinese / English) *"
            }),
        inputType: "text",
        required: ({ intl, value, isVip, startEdit }) => true,
        Icon: PersonOutlineOutlined,
        iconAlt: "icon username",
        hint: ({ intl, value, isVip, startEdit }) => "",
        error: ({ intl, value, isVip, startEdit }) => startEdit && !value,
        display: true,
        writeRtDb: true,
        step: 1
    },
    {
        name: fieldName.institution,
        className: "signup__input signup__input--institution",
        focusEle: ".signup__input--institution input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            isVip
                ? intl.formatMessage({
                      id: "signup.input.institution",
                      defaultMessage: "Institution"
                  })
                : intl.formatMessage({
                      id: "signup.input.institution.required",
                      defaultMessage: "Institution *"
                  }),
        inputType: "text",
        required: ({ intl, value, isVip, startEdit }) => !isVip,
        Icon: LocationCityOutlined,
        iconAlt: "icon institution",
        hint: ({ intl, value, isVip, startEdit }) => "",
        error: ({ intl, value, isVip, startEdit }) =>
            isVip ? false : startEdit && !value,
        display: true,
        writeRtDb: true,
        step: 1
    },
    {
        name: fieldName.position,
        className: "signup__input signup__input--position",
        focusEle: ".signup__input--position input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            isVip
                ? intl.formatMessage({
                      id: "signup.input.position",
                      defaultMessage: "Position"
                  })
                : intl.formatMessage({
                      id: "signup.input.position.required",
                      defaultMessage: "Position *"
                  }),
        inputType: "text",
        required: ({ intl, value, isVip, startEdit }) => !isVip,
        Icon: WorkOutline,
        iconAlt: "icon post",
        hint: ({ intl, value, isVip, startEdit }) => "",
        error: ({ intl, value, isVip, startEdit }) =>
            isVip ? false : startEdit && !value,
        display: true,
        writeRtDb: true,
        step: 1
    },
    {
        name: fieldName.researchPurpose,
        className: "signup__input signup__input--researchPurpose",
        focusEle: ".signup__input--researchPurpose input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            isVip
                ? intl.formatMessage({
                      id: "signup.input.research.purpose",
                      defaultMessage: "Research purpose *"
                  })
                : intl.formatMessage({
                      id: "signup.input.research.purpose.required",
                      defaultMessage: "Research or Teaching Purpose *"
                  }),
        inputType: "text",
        required: ({ intl, value, isVip, startEdit }) => !isVip,
        Icon: Book,
        iconAlt: "icon research purpose",
        hint: ({ intl, value, isVip, startEdit }) => "",
        error: ({ intl, value, isVip, startEdit }) =>
            isVip ? false : startEdit && !value,
        display: true,
        writeRtDb: true,
        step: 1
    },
    {
        name: fieldName.email,
        className: "signup__input signup__input--email",
        focusEle: ".signup__input--email input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            intl.formatMessage({
                id: "signup.input.email.required",
                defaultMessage: "Email *"
            }),
        inputType: "text",
        required: ({ intl, value, isVip, startEdit }) => true,
        Icon: MailOutlineIcon,
        iconAlt: "icon email",
        hint: ({ intl, value, isVip, startEdit }) => "",
        errHint: ({ intl, value, isVip, startEdit }) =>
            (startEdit &&
                !(value && validateEmail(value)) &&
                intl.formatMessage({
                    id: "signup.input.email.err.hint",
                    defaultMessage: "Invalid email"
                })) ||
            "",
        error: ({ intl, value, isVip, startEdit }) =>
            startEdit && !(value && validateEmail(value)),
        display: true,
        writeRtDb: true,
        step: 0
    },
    {
        name: fieldName.password,
        className: "signup__input signup__input--password",
        focusEle: ".signup__input--password input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            intl.formatMessage({
                id: "signup.input.password.required",
                defaultMessage: "Password *"
            }),
        inputType: ({ showPassword }) => (showPassword ? "text" : "password"),
        required: ({ intl, value, isVip, startEdit }) => true,
        Icon: HttpsOutlined,
        iconAlt: "icon password",
        hint: ({ intl, value, isVip, startEdit }) => passwdHint(intl),
        error: ({ intl, value, isVip, startEdit }) =>
            startEdit && !(value && validatePassword(value).pass),
        display: true,
        writeRtDb: false,
        step: 0
    },
    {
        name: fieldName.passwordConfirm,
        className: "signup__input signup__input--passwordConfirm",
        focusEle: ".signup__input--passwordConfirm input",
        placeholder: ({ intl, value, isVip, startEdit }) =>
            intl.formatMessage({
                id: "signup.input.passwordConfirm.required",
                defaultMessage: "Confirm password *"
            }),
        inputType: ({ showPassword }) => (showPassword ? "text" : "password"),
        required: ({ intl, value, isVip, startEdit }) => true,
        Icon: HttpsOutlined,
        iconAlt: "icon password",
        hint: ({ intl, value, isVip, startEdit }) => "",
        error: ({ intl, value, isVip, startEdit }) =>
            startEdit && !(value && validatePassword(value).pass),
        display: true,
        writeRtDb: false,
        step: 0
    }
];

// 預設 fields
export const fieldsDef = Object.keys(fieldName).reduce((acc, key) => {
    acc[key] = { value: "", error: "", startEdit: false };
    return acc;
}, {});

export const findFieldByName = name => {
    return fieldsCfg.find(o => o.name === name);
};

export const findHintByName = name => {
    return fieldsCfg.find(o => o.name === name)?.hint;
};

export const findErrHintByName = name => {
    return fieldsCfg.find(o => o.name === name)?.errHint;
};

const PRIVACY_LINK = {
    zh: "https://www.cuhk.edu.hk/policy/pdo/b5/",
    en: "https://www.cuhk.edu.hk/policy/pdo/en/"
};

export const getPrivacyLinkByLocale = locale => {
    if (locale.indexOf("zh") >= 0) return PRIVACY_LINK.zh;
    if (locale.indexOf("en") >= 0) return PRIVACY_LINK.en;
    return PRIVACY_LINK.zh;
};
