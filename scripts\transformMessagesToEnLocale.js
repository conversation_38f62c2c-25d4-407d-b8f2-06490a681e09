const path = require("path");
const fs = require("fs");

function transformMessagesToEnLocale() {
    const pathMessages = path.join(
        __dirname,
        "../.build/messages.json"
    );
    const pathEnJson = path.join(__dirname, "../src/lang/en.json");
    fs.readFile(pathMessages, (error, data) => {
        if (error) {
            throw error;
        }
        const messages = JSON.parse(data);
        const translation = messages.reduce((acc, message) => {
            acc[message.id] = message.defaultMessage;
            return acc;
        }, {});
        fs.writeFileSync(pathEnJson, JSON.stringify(translation));
        console.log("Messages transformed to ", pathEnJson);
    });
}

transformMessagesToEnLocale();
