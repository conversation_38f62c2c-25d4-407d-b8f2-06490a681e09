import React, { useContext } from "react";

// material ui
import { CssBaseline, Container } from "@material-ui/core";

// custom component
import Cus<PERSON>lert from "./CusAlert";
import CusSearchBar from "./CusSearchBar";
import CusResultList from "./CusResultList";
import CusPagination from "./CusPagination";

// RWD Layout
import { ResponsiveContainer } from "../../../../layout/Layout";

// store
// import { StoreContext } from "../../../store/StoreProvider";

const index = props => {
    // const [state] = useContext(StoreContext);
    // const { result, ...restResult } = state.searchPage2;
    return (
        <ResponsiveContainer {...props}>
            <CssBaseline />
            {/* <pre>{JSON.stringify({ restResult }, null, 2)}</pre> */}
            <Container fixed>
                <CusAlert />
                <CusSearchBar />
                <br />
                <CusResultList />
                <br />
                <CusPagination />
                <br />
            </Container>
        </ResponsiveContainer>
    );
};

export default index;
