import React, { useEffect, useState } from "react";
import CustomCoordinatesModal from "./CustomCoordinatesModal";
import { saveCoordsChange, getGoogleCoord } from "../utils";
import axios from "axios";
import { Api } from "../../../../api/hkbdb/Api";
import { isNotEmpty, safeGet } from "../../../../common/codes";

const CustomEditCoordinatesModal = ({
    title,
    rowIdx,
    graph,
    eventId,
    setEditData,
    defaultValue,
    ontologyType,
    propRange,
    propertyBindRangeStr
}) => {
    const [open, setOpen] = useState(false);
    const [locations, setLocations] = useState([]);
    const [allCoordinates, setAllCoordinates] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const handleChangeCoords = async coords => {
        const formatedCoords = coords.filter(coord => coord.new.label.trim());
        const values = formatedCoords
            .filter(coord => coord.status !== "delete")
            .map(coord => ({
                label: coord.new.label,
                value: coord.new.id
                    ? coord.new.id
                    : `${propRange === "Place" ? "PLA" : "ORG"}${encodeURI(
                          coord.new.label
                      )}`
            }));

        const changedData = {
            [rowIdx]: {
                graph,
                srcId: eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: values
            }
        };

        if (isNotEmpty(safeGet(changedData, [rowIdx, "values"], []))) {
            try {
                setIsLoading(true);
                await saveCoordsChange(formatedCoords, propRange);
            } catch (e) {
                alert("修改經緯度失敗");
                console.log("saveCoordsChange Error: ", e);
            } finally {
                setIsLoading(false);
            }

            const locs = formatedCoords
                .filter(coord => coord.status !== "delete")
                .map(coord => coord.new.label);

            setLocations(locs);

            setEditData(prev => ({
                ...prev,
                changedData: {
                    ...prev.changedData,
                    ...changedData
                }
            }));
        } else {
            setEditData(prev => {
                const { [rowIdx]: _, ...restChangedData } = prev.changedData;
                return {
                    ...prev,
                    changedData: restChangedData
                };
            });
        }
    };

    const getCoordinates = async (type = "all") => {
        const res = await axios.get(Api.getCoordinates(type));
        return res.data.data;
    };

    useEffect(() => {
        if (defaultValue?.length) {
            const locs = defaultValue.map(loc => loc.label);

            setLocations(locs);
        } else {
            setLocations([]);
        }
    }, [defaultValue]);

    useEffect(() => {
        if (!open || !propRange) return;

        const locationType = propRange.toLocaleLowerCase();

        const loadCoordinates = async () => {
            const coordinatesData = await getCoordinates(locationType);
            setAllCoordinates(coordinatesData);
        };

        loadCoordinates();
    }, [open, propRange]);

    return (
        <CustomCoordinatesModal
            open={open}
            setOpen={setOpen}
            allCoordinates={allCoordinates}
            locations={locations}
            title={title}
            isLoading={isLoading}
            getGoogleCoord={getGoogleCoord}
            handleChangeCoords={handleChangeCoords}
        />
    );
};

export default CustomEditCoordinatesModal;
