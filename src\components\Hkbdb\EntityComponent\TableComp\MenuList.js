import React from "react";

import { FixedSizeList as List } from "react-window";

const MenuList = ({ children, ...restProps }) => {
    //
    const {
        // MenuListHeader = null,
        MenuListFooter = null
    } = restProps.selectProps.components;
    //
    // children 必須為 Array
    if (!children || !Array.isArray(children)) {
        return null;
    }
    //
    const baseHeight = 200;
    const dataHeight = children.length * 40;
    const heightValue = dataHeight < baseHeight ? dataHeight : baseHeight;
    //
    return (
        <div>
            <List
                height={heightValue}
                itemCount={children.length}
                itemSize={45}
            >
                {({ index, style }) => (
                    <div style={style}>{children[index]}</div>
                )}
            </List>
            {MenuListFooter && MenuListFooter}
        </div>
    );
};

export default MenuList;
