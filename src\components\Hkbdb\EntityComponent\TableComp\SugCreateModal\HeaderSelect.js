import React, { useContext } from "react";
import { Input, Label, Dropdown } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";
import { StoreContext } from "../../../../../store/StoreProvider";

function HeaderSelect() {
    const [state] = useContext(StoreContext);
    const { displayName } = state.user;
    const oneOpt = [
        { key: "opt1", text: `Suggester： ${displayName}`, value: "opt1" }
    ];

    return (
        <Input fluid labelPosition="left" type="text">
            <Label color="orange">
                <FormattedMessage
                    id={"people.Information.formTable.dataset"}
                    defaultMessage={"Dataset"}
                />
            </Label>
            <Dropdown
                options={oneOpt}
                disabled
                fluid
                selection
                defaultValue={oneOpt[0].value}
            />
        </Input>
    );
}

export default HeaderSelect;
