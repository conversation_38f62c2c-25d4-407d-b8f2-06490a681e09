import axios from "axios";

import en from "react-intl/src/en";

import queryString from "query-string";
import base64url from "base64url";
import role from "../../App-role";

/**
 *
 * @param obj
 * @returns {boolean}
 */
const isEmpty = obj => Object.keys(obj || {}).length === 0;

let baseUrl;

export const LOCALE_LANG = {
    LOCALE_DEFAULT: "zh-hans",
    LOCALE_ZH: "zh-hans",
    LOCALE_EN: "en"
};
let locale = LOCALE_LANG.LOCALE_DEFAULT;

if (process.env.NODE_ENV === "production") {
    baseUrl = process.env.REACT_APP_API_NODE;
    // Disable the console log in production.
    // eslint-disable-next-line no-console
    console.log = () => {};
} else {
    baseUrl = process.env.REACT_APP_API_NODE;
}
export const FUSEKI_ON = process.env.REACT_DB_FUSEKI === "true";

export const Api = {
    locale_lang: LOCALE_LANG,
    getLocale: () => locale,
    setLocale(lang) {
        locale = lang;
    },
    getAxios: () => axios,
    setAxiosAuth(token) {
        // 不加 prefix "Bearer "
        axios.defaults.headers.Authorization = `${token}`;
        axios.defaults.headers.common["Authorization"] = `${token}`;
    },
    // FIXME: maybe put the language json files into firestore.
    getLocaleJson: `${baseUrl}data`,
    // a
    getArticle: () =>
        `${baseUrl}/{locale}/person/article/2.0?limit=-1&offset=0&name={name}`,
    getAward: () =>
        `${baseUrl}/{locale}/person/award/2.0?limit=-1&offset=0&name={name}`,
    // b
    getBaseUrl: () => `${baseUrl}`,
    getBasicInfo: () =>
        `${baseUrl}/{locale}/hkbdb/basicInfo/2.1?limit=-1&offset=0&id={id}`,
    // c
    getClassList: () =>
        `${baseUrl}/{locale}/class/list/2.0?limit={limit}&offset=0&class={class}&name={name}`,
    getClassPersonList: () =>
        `${baseUrl}/{locale}/class/list/2.7?limit={limit}&personLimit={limit}&offset=0&keyword={keyword}`,
    getClassOrgList: () =>
        `${baseUrl}/{locale}/class/list/2.6?limit={limit}&orgLimit={limit}&offset=0&keyword={keyword}`,
    // d
    // 族譜
    getDepthGenealogy1: () =>
        `${baseUrl}/{locale}/person/genealogy/1step/2.0?limit=-1&offset=0&personId={personId}`,
    getDepthGenealogy2: () =>
        `${baseUrl}/{locale}/person/genealogy/2step/2.0?limit=-1&offset=0&={personId}personId`,
    // SNA for person
    getDepthSNARelation1s: () =>
        FUSEKI_ON
            ? `${baseUrl}/{locale}/person/SNARelation/1step/3.0?limit=-1&offset=0&ids={ids}`
            : `${baseUrl}/{locale}/person/SNARelation/1step/2.1?limit=-1&offset=0&ids={ids}`,
    // SNA for organization
    getDepthOrgSNARelation1s: () =>
        `${baseUrl}/{locale}/organization/SNARelation/1step/2.1?limit=-1&offset=0&ids={ids}`,
    // e
    // getEducation: () =>
    //     `${baseUrl}/{locale}/person/education/2.0?limit=-1&offset=0&name={name}`,
    // getEmployment: () =>
    //     `${baseUrl}/{locale}/person/employment/2.0?limit=-1&offset=0&name={name}`,
    // getEvent: () =>
    //     `${baseUrl}/{locale}/person/event/2.0?limit=-1&offset=0&name={name}`,
    // i
    // getInformation: () =>
    //     `${baseUrl}/{locale}/person/information/2.0?limit=-1&offset=0&name={name}`,
    getInformation: () =>
        `${baseUrl}/{locale}/person/information/2.6?limit=-1&offset=0&name={name}`,
    getPersonNameNode: () =>
        `${baseUrl}/{locale}/person/nameNode/2.1?limit=-1&offset=0&name={name}`,
    getPersonNameNodeIds: () =>
        `${baseUrl}/{locale}/person/nameNode/ids/2.0?limit=-1&offset=0&name={name}`,
    // n
    getNameByLocale: () =>
        `${baseUrl}/{locale}/getNameByLocale/2.3?limit=-1&offset=0&ids={ids}`,
    getNameByLocaleSna: () =>
        `${baseUrl}/{locale}/getNameByLocale/2.2?limit=-1&offset=0&ids={ids}`,
    // o
    getOrganization: () =>
        `${baseUrl}/{locale}/person/organization/2.0?limit=-1&offset=0&name={name}`,
    getOrganizationMemberInvert: () =>
        `${baseUrl}/{locale}/backend/member/events/2.0?limit=-1&offset=0`,
    getOrganizationList: () =>
        `${baseUrl}/{locale}/organization/list/allGraph/2.0?limit={limit}&offset={offset}`,
    getOrganizationListByGraph: () =>
        `${baseUrl}/{locale}/organization/list/oneGraph/2.0?limit={limit}&offset={offset}&graph={graph}`,

    // getOrganizationByKeyword 進版3.0(新增 NameNode 架構)
    getOrganizationByKeyword: () =>
        FUSEKI_ON
            ? `${baseUrl}/{locale}/search/organization/4.0?limit={limit}&offset=0&keyword={keyword}`
            : `${baseUrl}/{locale}/search/organization/3.2?limit={limit}&offset=0&keyword={keyword}`,
    getOrganizationCount: () =>
        `${baseUrl}/{locale}/organization/count/allGraph/2.0?limit=-1&offset=0`,
    getOrganizationCountByGraph: () =>
        `${baseUrl}/{locale}/organization/count/oneGraph/2.0?limit=-1&offset=0&graph={graph}`,
    getOrgTimelineData: () =>
        `${baseUrl}/{locale}/organization/timeline/2.0?limit=-1&offset=0&name={name}`,
    getOrganizationBasicInfo: () =>
        `${baseUrl}/{locale}/organization/information/2.4?limit=-1&offset=0&name={name}`,
    getOrganizationMember: () =>
        `${baseUrl}/{locale}/organization/members/2.0?limit=-1&offset=0&name={name}`,
    getOntologyProtege: () =>
        `${baseUrl}/{locale}/ontology/protege/list/2.0?limit=-1&offset=0`,
    // getOntologyDefined: () =>
    //     `${baseUrl}/{locale}/ontology/property/defined/list/2.0?limit=-1&offset=0`,
    /* getOntologyDefined 版本更新至 2.1:增加 NameNode */
    getOntologyDefined: () =>
        // `${baseUrl}/{locale}/ontology/property/spo/2.0?limit=-1&offset=0`,
        // `${baseUrl}/{locale}/ontology/property/spo/2.1?limit=-1&offset=0`,
        `${baseUrl}/{locale}/ontology/property/spo/2.2?limit=-1&offset=0`,
    getOtherWork: () =>
        `${baseUrl}/{locale}/person/otherwork/2.0?limit=-1&offset=0&name={name}`,
    // p
    // 抓 ontology 的 property 中英對應
    getPropertyMap: () =>
        `${baseUrl}/{locale}/ontology/property/2.1?limit=-1&offset=0`,

    // getPersonTimelineData 進版3.0(新增 hasEndDate)
    getPersonTimelineData: () =>
        `${baseUrl}/{locale}/person/timeline/3.1?limit=-1&offset=0&name={name}`,
    getOrganizationTimelineData: () =>
        `${baseUrl}/{locale}/organization/timeline/2.4?limit=-1&offset=0&name={name}`,
    getPublication: () =>
        `${baseUrl}/{locale}/person/publication/2.0?limit=-1&offset=0&name={name}`,
    getPersonList: () =>
        `${baseUrl}/{locale}/person/list/allGraph/2.0?limit={limit}&offset={offset}`,
    getPersonListByGraph: () =>
        `${baseUrl}/{locale}/person/list/oneGraph/2.0?limit={limit}&offset={offset}&graph={graph}`,

    // getPersonByKeyword 進版3.0(新增 NameNode 架構)
    //  to -> 3.1 2022/04/20 by jheng
    getPersonByKeyword: () =>
        FUSEKI_ON
            ? `${baseUrl}/{locale}/search/person/4.2?limit={limit}&offset=0&keyword={keyword}`
            : `${baseUrl}/{locale}/search/person/3.3?limit={limit}&offset=0&keyword={keyword}`,
    getPersonCount: () =>
        `${baseUrl}/{locale}/person/count/allGraph/2.0?limit=-1&offset=0`,
    getPersonCountByGraph: () =>
        `${baseUrl}/{locale}/person/count/oneGraph/2.0?limit=-1&offset=0&graph={graph}`,
    getPersonPortrait: () =>
        `${baseUrl}/{locale}/person/portrait/2.0?limit=-1&offset=0&name={name}&size={size}`,
    getPersonPortraitCircle: () =>
        `${baseUrl}/{locale}/person/portrait/circle/2.0?limit=-1&offset=0&name={name}&size={size}`,
    getOrgPortrait: () =>
        `${baseUrl}/{locale}/organization/portrait/2.0?limit=-1&offset=0&name={name}&size={size}`,
    getOrgPortraitCircle: () =>
        `${baseUrl}/{locale}/organization/portrait/circle/2.0?limit=-1&offset=0&name={name}&size={size}`,
    // r
    getRelation: () =>
        `${baseUrl}/{locale}/person/relation/2.5?limit=-1&offset=0&name={name}`,
    getRelationOP: () => `${baseUrl}/{locale}/relationOP/2.0?limit=-1&offset=0`,
    // s
    getDataset: () =>
        `${baseUrl}/{locale}/settings/dataset/2.1?limit=-1&offset=0`,
    getDatabase: () =>
        `${baseUrl}/{locale}/settings/database/2.0?limit=-1&offset=0`,
    getWebConfig: () =>
        `${baseUrl}/{locale}/settings/webconfig/2.0?limit=-1&offset=0`,
    // auery page api
    getQueryAndCount: () => `${baseUrl}/queryAndCount`,
    // hkbdb
    restfulHKBDB: () => `${baseUrl}/{locale}/generic/2.0`,
    // hkbdb_draft
    restfulDraft: () => `${baseUrl}/draft/{locale}/generic/2.0`,
    // hkbdb
    deletePerson: () => `${baseUrl}/{locale}/deletePerson/2.0`,
    deleteGraph: () => `${baseUrl}/draft/{locale}/deleteGraph/1.0`,
    deleteHkbdbEvent: () => `${baseUrl}/{locale}/deleteEvent/1.0`,
    deleteEvent: () => `${baseUrl}/draft/{locale}/deleteEvent/1.0`,
    mergeInstance: () => `${baseUrl}/merge/mergeInstances/2.0`,
    duplicateInstance: () => `${baseUrl}/duplicate/duplicateInstances/2.0`,
    getInstanceLIst: type =>
        `${baseUrl}/{locale}/${type}/list/v2/1.0?limit=10&offset=0`,
    getInverseRelation: () =>
        `${baseUrl}/{locale}/relation/invert/list/2.0?limit=-1&offset=0`,
    findIdByValue: () =>
        `${baseUrl}/{locale}/class/findId/2.0?class={class}&keyword={keyword}&limit=-1&offset=0`,
    /* getHkbdbPersonInfo 版本更新至 2.2:增加 NameNode */
    getHkbdbPersonInfo: () =>
        // `${baseUrl}/{locale}/hkbdb/person/information/2.1/?limit=-1&offset=0&class={class}&keyword={keyword}`,
        `${baseUrl}/{locale}/hkbdb/person/information/2.2/?limit=-1&offset=0&class={class}&keyword={keyword}`,
    // 取得下一個 nameId
    getNextNameId: () =>
        `${baseUrl}/{locale}/get/nextNameId/2.0/?limit=-1&offset=0&class={class}`,
    getHkbdbOntologyOneOfThemInfo: () =>
        `${baseUrl}/{locale}/hkbdb/ontology/oneofthem/2.0/?limit=-1&offset=0`,

    // Person :: searchEventIds
    getPersonSearchArticleEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/article/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchPublicationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/publication/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchEducationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/education/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchOrganizationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/organization/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchEmploymentEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/employment/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchAwardEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/award/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/event/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchOtherWorkEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/otherwork/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getPersonSearchNameNodeEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/namenode/search/eventids/2.2?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,

    // Person :: eventIds
    getPersonArticleEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/article/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonPublicationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/publication/eventids/2.3?limit={limit}&offset={offset}&name={name}`,
    getPersonEducationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/education/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonOrganizationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/organization/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonEmploymentEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/employment/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonAwardEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/award/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/event/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonOtherWorkEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/otherwork/eventids/2.2?limit={limit}&offset={offset}&name={name}`,
    getPersonNameNodeEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/namenode/eventids/2.2?limit={limit}&offset={offset}&name={name}`,

    // Person :: event
    getPersonArticleEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/article/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    postPersonArticleEvent: () =>
        `${baseUrl}/zh-hans/post/hkbdb/person/information/article/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonPublicationEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/publication/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonEducationEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/education/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonOrganizationEvent: () =>
        // `${baseUrl}/zh-hans/hkbdb/person/information/organization/2.3?limit=-1&offset=0&name={name}&eventids={eventids}`,
        `${baseUrl}/zh-hans/hkbdb/person/information/organization/2.4?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonEmploymentEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/employment/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonAwardEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/award/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/event/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonOtherWorkEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/otherwork/2.2?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getPersonNameNodeEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/person/information/namenode/2.4?limit=-1&offset=0&eventids={eventids}`,

    // Organization :: searchEventIds
    getOrgSearchArticleEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/article/search/eventids/2.0?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getOrgSearchPublicationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/publication/search/eventids/2.0?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getOrgSearchEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/event/search/eventids/2.0?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getOrgSearchOtherWorkEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/otherwork/search/eventids/2.0?limit={limit}&offset=$offset}&name={name}&keyword={keyword}`,
    getOrgSearchNameNodeEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/namenode/search/eventids/2.0?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,
    getOrgSearchMemberEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/member/search/eventids/2.1?limit={limit}&offset={offset}&name={name}&keyword={keyword}`,

    // Organization :: eventIds
    getOrgArticleIEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/article/eventids/2.0?limit={limit}&offset={offset}&name={name}`,
    getOrgPublicationEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/publication/eventids/2.0?limit={limit}&offset={offset}&name={name}`,
    getOrgEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/event/eventids/2.1?limit={limit}&offset={offset}&name={name}`,
    getOrgOtherWorkEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/otherwork/eventids/2.0?limit={limit}&offset={offset}&name={name}`,
    getOrgNameNodeEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/namenode/eventids/2.0?limit={limit}&offset={offset}&name={name}`,
    getOrgMemberEventIds: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/member/eventids/2.0?limit={limit}&offset={offset}&name={name}`,

    // Organization :: event
    getOrgArticleEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/article/2.0?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getOrgPublicationEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/publication/2.0?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getOrgEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/event/2.1?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getOrgOtherWorkEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/otherwork/2.0?limit=-1&offset=0&name={name}&eventids={eventids}`,
    getOrgNameNodeEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/namenode/2.2?limit=-1&offset=0&eventids={eventids}`,
    getOrgMemberEvent: () =>
        `${baseUrl}/zh-hans/hkbdb/organization/information/member/2.3?limit=-1&offset=0&name={name}&ids={ids}`,

    // Organization :: all
    getGraphOrganization: graph =>
        `${baseUrl}/zh-hans/graph/organization/1.0?limit=-1&offset=0&graph=${graph}`,

    // Place :: all
    getGraphPlace: graph =>
        `${baseUrl}/zh-hans/graph/place/1.0?limit=-1&offset=0&graph=${graph}`,

    // mail
    sentEmail: () => `${baseUrl}/mail`,

    // focuspoint
    getFocusPointData: () =>
        `${baseUrl}/zh-hans/personPhoto/month/list/1.1?limit=-1&offset=0&size={size}&months={month}`,

    // suggester
    // 根據點選的表單，取得hkbdb_draft資訊
    getDraftInfo: (srcId, sheetName) =>
        `${baseUrl}/draft/zh-hans/suggester/sheet/info/1.2?limit=-1&offset=0&srcId=${srcId}&sheetName=${sheetName}`,
    getDraftInfoHasR: (srcId, sheetName, relation) =>
        `${baseUrl}/draft/zh-hans/suggester/sheet/info/hasR/1.2?limit=-1&offset=0&srcId=${srcId}&sheetName=${sheetName}&relationP=${relation}`,
    getDraftIDType: (srcId, sheetName) =>
        `${baseUrl}/draft/zh-hans/suggester/id/type/1.0?limit=-1&offset=0&srcId=${srcId}&sheetName=${sheetName}`,
    getDraftInfoAtObj: (srcId, sheetName) =>
        `${baseUrl}/draft/zh-hans/suggester/sheet/info/atObj/1.1?limit=-1&offset=0&srcId=${srcId}&sheetName=${sheetName}`,
    getBookName: bookId =>
        `${baseUrl}/zh-hans/suggester/book/1.0?limit=-1&offset=0&bookId=${bookId}`,
    getSuggesterDropdown: (graph, keyword) =>
        `${baseUrl}/draft/zh-hans/suggester/dropdown/1.0?limit=-1&offset=0&graph=${graph}&keyword=${keyword}`,
    getSuggesterDropdownList: type =>
        `${baseUrl}/draft/zh-hans/suggester/dropdown/list/1.0?limit=-1&offset=0&type=${type}`,
    getSuggesterDropdownListByBestKnownName: type =>
        `${baseUrl}/draft/zh-hans/suggester/dropdown/list/bestKnownName/1.0?limit=-1&offset=0&type=${type}`,
    getSuggesterDropdownAddItem: (graph, type, keyword) =>
        `${baseUrl}/draft/zh-hans/suggester/dropdown/addItem/1.0?limit=-1&offset=0&graph=${graph}&type=${type}&keyword=${keyword}`,
    getSuggesterAddBookLabel: id =>
        `${baseUrl}/draft/zh-hans/suggester/addbook/label/1.0?limit=-1&offset=0&id=${id}`,
    getSuggestionData: graph =>
        `${baseUrl}/draft/zh-hans/suggest/list/data/1.0?limit=-1&offset=0&graph=${graph}`,

    // manuscript
    getManuscriptList: size =>
        `${baseUrl}/zh-hans/manuscript/list/1.0?limit=-1&offset=0&size=${size}`,
    getManuscriptInfo: (size, id) =>
        `${baseUrl}/zh-hans/manuscript/info/1.1?limit=-1&offset=0&id=${id}`,

    // bookcover
    getBookcoverList: size =>
        `${baseUrl}/zh-hans/bookcover/list/1.0?limit=-1&offset=0&size=${size}`,
    getBookcoverInfo: (size, id) =>
        `${baseUrl}/zh-hans/bookcover/info/1.1?limit=-1&offset=0&id=${id}`,

    // map page
    // getMapSearchPerson: (keyword, limit = 20, offset = 0) =>
    //     `${baseUrl}/zh-hans/web/map/search/person/1.0?limit=${limit}&offset=${offset}&keyword=${keyword}`,
    getMapPersonsPlaceList: ids =>
        `${baseUrl}/{locale}/web/person/place/list/1.6?limit=-1&offset=0&ids=${ids}`,
    getMapSearchInfomation: (limit = -1, offset = 0) =>
        `${baseUrl}/{locale}/map/search/information/1.6?limit=${limit}&offset=${offset}`,
    getMapSearchPersonList: (limit = -1, offset = 0) =>
        `${baseUrl}/zh-hans/map/search/person/list/1.0?limit=${limit}&offset=${offset}`,
    getMapTimelineRange: (limit = -1, offset = 0) =>
        `${baseUrl}/zh-hans/map/timeline/range/1.0?limit=${limit}&offset=${offset}`,
    getMapGuideContent: (limit = -1, offset = 0) =>
        `${baseUrl}/zh-hans/map/guide/content/1.0?limit=${limit}&offset=${offset}`,
    getCoordinates: type =>
        `${baseUrl}/zh-hans/get/${type}/coordinates/1.0?limit=-1&offset=0`,
    getGoogleCoord: `${baseUrl}/gis`
};

export const eventSearchIdsAPIs = {
    person: {
        article: Api.getPersonSearchArticleEventIds(),
        publication: Api.getPersonSearchPublicationEventIds(),
        educationevent: Api.getPersonSearchEducationEventIds(),
        organizationevent: Api.getPersonSearchOrganizationEventIds(),
        employmentevent: Api.getPersonSearchEmploymentEventIds(),
        awardevent: Api.getPersonSearchAwardEventIds(),
        event: Api.getPersonSearchEventIds(),
        otherwork: Api.getPersonSearchOtherWorkEventIds(),
        namenode: Api.getPersonSearchNameNodeEventIds()
    },
    organization: {
        article: Api.getOrgSearchArticleEventIds(),
        publication: Api.getOrgSearchPublicationEventIds(),
        event: Api.getOrgSearchEventIds(),
        otherwork: Api.getOrgSearchOtherWorkEventIds(),
        namenode: Api.getOrgSearchNameNodeEventIds(),
        member: Api.getOrgSearchMemberEventIds()
    }
};
//
export const eventidsAPIs = {
    person: {
        article: Api.getPersonArticleEventIds(),
        publication: Api.getPersonPublicationEventIds(),
        educationevent: Api.getPersonEducationEventIds(),
        organizationevent: Api.getPersonOrganizationEventIds(),
        employmentevent: Api.getPersonEmploymentEventIds(),
        awardevent: Api.getPersonAwardEventIds(),
        event: Api.getPersonEventIds(),
        otherwork: Api.getPersonOtherWorkEventIds(),
        namenode: Api.getPersonNameNodeEventIds()
    },
    organization: {
        article: Api.getOrgArticleIEventIds(),
        publication: Api.getOrgPublicationEventIds(),
        event: Api.getOrgEventIds(),
        otherwork: Api.getOrgOtherWorkEventIds(),
        namenode: Api.getOrgNameNodeEventIds(),
        member: Api.getOrgMemberEventIds()
        // member: `${baseUrl}/member/eventids/2.3?limit=${limit}&offset=${offset}&name=&ids`
    }
};
//
export const eventAPIs = {
    person: {
        // article: Api.getPersonArticleEvent(),
        article: Api.postPersonArticleEvent(),
        publication: Api.getPersonPublicationEvent(),
        educationevent: Api.getPersonEducationEvent(),
        organizationevent: Api.getPersonOrganizationEvent(),
        employmentevent: Api.getPersonEmploymentEvent(),
        awardevent: Api.getPersonAwardEvent(),
        event: Api.getPersonEvent(),
        otherwork: Api.getPersonOtherWorkEvent(),
        namenode: Api.getPersonNameNodeEvent()
    },
    organization: {
        article: Api.getOrgArticleEvent(),
        publication: Api.getOrgPublicationEvent(),
        event: Api.getOrgEvent(),
        otherwork: Api.getOrgOtherWorkEvent(),
        namenode: Api.getOrgNameNodeEvent(),
        member: Api.getOrgMemberEvent()
    }
};

export const apiSwithcer = {
    Person: {
        count: Api.getPersonCount(),
        countByGraph: Api.getPersonCountByGraph(),
        list: Api.getPersonList(),
        listByGraph: Api.getPersonListByGraph(),
        listByeKeyword: Api.getPersonByKeyword(),
        findEntityByKeyword: Api.getPersonByKeyword(),
        inverseRelation: Api.getInverseRelation()
    },
    Organization: {
        count: Api.getOrganizationCount(),
        countByGraph: Api.getOrganizationCountByGraph(),
        list: Api.getOrganizationList(),
        listByGraph: Api.getOrganizationListByGraph(),
        listByeKeyword: Api.getOrganizationByKeyword(),
        findEntityByKeyword: Api.getOrganizationByKeyword()
    }
};

// vars in return data from API
export const varSwitcher = {
    Person: {
        count: ["count"],
        list: ["personName", "g"],
        listByeKeyword: ["personName"],
        entityIdKey: ["perId"],
        findEntityByKeyword: ["personName"]
    },
    Organization: {
        count: ["count"],
        list: ["organizationName", "g"],
        listByeKeyword: ["organizationName"],
        entityIdKey: ["orgId"],
        findEntityByKeyword: ["organizationName"]
    }
};

export const apiParamsDefault = {
    countIndividuals: {
        limit: -1,
        offset: 0
    },
    loadIndividuals: {
        limit: 50,
        offset: 0
    },
    findEntityByKeyword: {
        limit: 20,
        offset: 0
    }
};

const excEncodeQueryParam = ["limit", "offset"];

/**
 *
 * @param queryStr {string}
 * @param decodeURIComp {boolean} 是否要 decodeURIComponent.
 * e.g. "%E9%A6%99%E6%B8%AF" => "香港"
 * @returns {string}
 */
const encodeQueryStr = (queryStr, decodeURIComp = false) => {
    // queryString will parse '+' as a space. So, we have to set {decode: false}.
    // https://github.com/sindresorhus/query-string/issues/305
    const queryObj = queryString.parse(queryStr, { decode: false });

    let encQueryStr = [];
    Object.keys(queryObj).forEach(qo => {
        let val = decodeURIComp
            ? decodeURIComponent(queryObj[qo])
            : queryObj[qo];
        if (excEncodeQueryParam.indexOf(qo) < 0) {
            // safe encode
            val = base64url.encode(val || "");
            queryObj[qo] = val;
        }
        encQueryStr.push(`${qo}=${val}`);
    });
    return encQueryStr.join("&");
};

export const encodeUrl = (apiStr, decodeURIComp) => {
    const preApiStr = apiStr.split("?")[0];
    const queryStr = apiStr.split("?")[1];

    if (!queryStr) return apiStr;

    const queryStrEncode = encodeQueryStr(queryStr, decodeURIComp);

    return `${preApiStr}?${queryStrEncode}`;
};

export const addAxiosInterceptor = (_axios, _millisToSS, _millisToMMSS) => {
    const addDurationAttr = (obj, duration) => {
        if (_millisToSS) obj.durationSS = _millisToSS(duration);
        if (_millisToMMSS) obj.durationMMSS = _millisToMMSS(duration);
        return obj;
    };

    // Add a request interceptor
    _axios.interceptors.request.use(
        function(config) {
            // Do something before request is sent
            config.metadata = { startTime: new Date() };
            return config;
        },
        function(error) {
            // Do something with request error
            return Promise.reject(error);
        }
    );

    // Add a response interceptor
    _axios.interceptors.response.use(
        function(response) {
            // Any status code that lie within the range of 2xx cause this function to trigger
            // Do something with response data
            response.config.metadata.endTime = new Date();
            response.duration =
                response.config.metadata.endTime -
                response.config.metadata.startTime;
            return addDurationAttr(response, response.duration);
        },
        function(error) {
            // Any status codes that falls outside the range of 2xx cause this function to trigger
            // Do something with response error
            error.config.metadata.endTime = new Date();
            error.duration =
                error.config.metadata.endTime - error.config.metadata.startTime;
            return addDurationAttr(error, error.duration);
        }
    );
};

export const getHkbdbAxios = () => axios;

/**
 * read HKBDB
 * @param apiStr {string}
 * @param timeout {number}
 * @param debug {boolean}
 * @param isCancelled {boolean}
 * @param locale {string}
 * @param useEncodeUrl {boolean} 是否要 encode url
 * @returns {{data: *[], error}|Promise<{durationSS: *, data}>}
 */
export const readHkbdbData = (
    apiStr,
    timeout = 15000,
    debug = false,
    isCancelled = false,
    locale,
    useEncodeUrl = true,
    fixedTable = false
) => {
    // lang
    let curLocale = locale || Api.getLocale();
    let api = apiStr.replace("{locale}", curLocale);
    // timeout
    axios.defaults.timeout = timeout;
    // encode query string (except limit and offset)
    if (useEncodeUrl) {
        api = encodeUrl(api);
    }

    try {
        // abort if it tyr too long
        // const controller = new AbortController();
        // const signal = controller.signal;
        // // to stop fetch if timeout
        // if (isCancelled) {
        //     controller.abort();
        // } else {
        //     setTimeout(() => controller.abort(), timeout);
        // }
        // replace api prefix for docker in production
        if (process.env.NODE_ENV === "production") {
            if (process.env.REACT_APP_MODE === "docker") {
                api = api.replace(process.env.REACT_APP_API_NODE, "");
            }
        }

        // debug
        if (debug) {
            console.log(api);
            console.log("Authorization axios", axios.defaults.headers);
        }

        // fetch hkbdb api
        return (
            axios
                .get(api)
                // handle data to json
                .then(response => {
                    if (fixedTable) {
                        // fixedTable need 'head'
                        return {
                            head: response?.data?.head || [],
                            data: response?.data?.data || [],
                            durationSS: response?.durationSS
                        };
                    }
                    return {
                        data: response?.data?.data || [],
                        durationSS: response?.durationSS
                    };
                })
                // handle catch
                .catch(err => {
                    // handle AbortController event and return error message
                    if (err.name === "AbortError") {
                        console.error(
                            "api:hkbdb:fetch:get:aborted:cancle:or:timeout"
                        );
                        return { data: [], error: err.message };
                    }
                    // handle other error event and return error message
                    else {
                        console.error(
                            "api:hkbdb:fetch:get:error:",
                            err.message
                        );
                        return { data: [], error: err.message };
                    }
                })
        );
    } catch (err) {
        console.error("api:hkbdb:fetch:catch:error: ", err.message);
        return { data: [], error: err.message };
    }
};

/**
 * for queryPage
 * @param apiStr
 * @param queryStr
 * @param limit
 * @param offset
 * @param timeout
 * @returns {Promise<AxiosResponse<T> | {head: {}, total: number, error: *, results: {}}>|{head: {}, total: number, error: *, results: {}}}
 */
export const queryHkbdbData = (
    apiStr,
    queryStr,
    limit,
    offset,
    timeout = 5000
) => {
    // timeout
    axios.defaults.timeout = timeout;
    try {
        // post data
        const data = {
            query: queryStr,
            limit: limit,
            offset: offset
        };
        // axios nmtl api
        return axios
            .post(apiStr, data, {
                headers: {
                    // 因cloudflare會擋query的部分用詞，需額外加header跳過cloudflare的檢查規則
                    "x-safe-query": "true"
                }
            })
            .then(res => {
                if (res && res.data) res.data.durationSS = res?.durationSS;
                return res.data;
            })
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { head: {}, results: {}, total: -1, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:catch:error: ", err.message);
        return { head: {}, results: {}, total: -1, error: err.message };
    }
};

export const getEntryGraph = (user, entry, srcId, ts = 0, entryType = "") => {
    const { role: usrRole, uid, email } = user;
    const { classType } = entry;
    const tmpTs = ts || Date.now();
    switch (usrRole) {
        case role.suggester:
            // hkbdb_draft graph rule: uid(user id)/ts(time stamp)/entryType(entry類別)
            const basicForm = [
                uid,
                tmpTs,
                classType,
                base64url.encode(email),
                srcId
            ];

            const sepSymbol = "/";
            const newG = entryType
                ? basicForm.concat(entryType).join(sepSymbol)
                : basicForm.join(sepSymbol);
            return { ...entry, graph: newG };
        default:
            return entry;
    }
};

const checkUser = user => {
    if (isEmpty(user)) {
        return { state: false, error: "No user information" };
    }
    return { state: true };
};

// delete
export const deleteHkbdbData = (
    apiStr,
    entry,
    limit,
    offset,
    timeout = 5000
) => {
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;
    const config = {
        // headers: {
        //     Authorization: ""
        // },
        data: { entry }
    };
    //
    try {
        // axios api
        return axios
            .delete(api, config)
            .then(res => ({ state: res?.data?.data === "OK" }))
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { state: false, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

// update
export const updateHkbdbData = (
    apiStr,
    entrySrc,
    entryDst,
    limit,
    offset,
    timeout = 5000
) => {
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;

    // time stamp
    const entry = {
        entrySrc,
        entryDst
    };

    //
    try {
        // axios api
        return axios
            .put(api, entry)
            .then(res => {
                return { state: res?.data?.data === "OK" };
            })
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { state: false, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

export const duplicateHkbdbData = (
    apiStr,
    entrySrc,
    entryDst,
    srcName,
    nameId,
    nameIdNumber,
    limit,
    offset,
    timeout = 5000
) => {
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;
    const entry = {
        entrySrc,
        entryDst,
        srcName,
        nameId,
        nameIdNumber
    };
    //
    try {
        // axios api
        return axios
            .put(api, entry)
            .then(res => {
                return { state: res?.data?.data === "OK" };
            })
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { state: false, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

export const mergeHkbdbData = (
    apiStr,
    entrySrc,
    entryDst,
    typeSrc,
    typeDst,
    limit,
    offset,
    timeout = 5000
) => {
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;
    const entry = {
        entrySrc,
        entryDst,
        typeSrc,
        typeDst
    };

    //
    try {
        // axios api
        return axios
            .put(api, entry)
            .then(res => {
                return { state: res?.data?.data === "OK" };
            })
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { state: false, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

// create
export const createHkbdbData = (apiStr, entry, timeout = 5000) => {
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;

    try {
        // axios api
        return (
            axios
                .post(api, { entry })
                // .then(res => ({ state: res.data === "OK" }))
                .then(res => {
                    return { state: res?.data?.data === "OK" };
                })
                .catch(err => {
                    console.error("api:axios:catch:error: ", err.message);
                    return { state: false, error: err.message };
                })
        );
    } catch (err) {
        console.error("api:axios:try:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

/** 與createHkbdbData差別:
 * 更換graph名稱 => getEntryGraph
 * */
export const createHkbdbDraftData = (apiStr, entry, user, timeout = 5000) => {
    const checkUsrInfo = checkUser(user);
    if (!checkUsrInfo.state) {
        return checkUsrInfo;
    }
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;
    const data = { entry: getEntryGraph(user, entry) };

    try {
        // axios api
        return (
            axios
                .post(api, data)
                // .then(res => ({ state: res.data === "OK" }))
                .then(res => {
                    return { state: res?.data?.data === "OK" };
                })
                .catch(err => {
                    console.error("api:axios:catch:error: ", err.message);
                    return { state: false, error: err.message };
                })
        );
    } catch (err) {
        console.error("api:axios:try:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

export const createHkbdbDraftDataV2 = (
    apiStr,
    entryGraph,
    user,
    timeout = 5000
) => {
    const checkUsrInfo = checkUser(user);
    if (!checkUsrInfo.state) {
        return checkUsrInfo;
    }
    let api = apiStr.replace("{locale}", Api.getLocale());
    // timeout
    axios.defaults.timeout = timeout;
    const data = { entry: entryGraph };
    // console.log("data ", data);

    try {
        // axios api
        return (
            axios
                .post(api, data)
                // .then(res => ({ state: res.data === "OK" }))
                .then(res => {
                    return { state: res?.data?.data === "OK" };
                })
                .catch(err => {
                    console.error("api:axios:catch:error: ", err.message);
                    return { state: false, error: err.message };
                })
        );
    } catch (err) {
        console.error("api:axios:try:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

// according to user role to execute "create" restful API
export const doRestCreate = (user, entry) => {
    const checkUsrInfo = checkUser(user);
    if (!checkUsrInfo.state) {
        return checkUsrInfo;
    }

    if (isEmpty(entry)) {
        return new Error("Lack entry");
    }

    const { role: usrRole } = user;
    switch (usrRole) {
        // suggester用createHkbdbDraftData
        case role.suggester:
            // return createHkbdbDraftData(Api.restfulDraft(), entry, user);
            return createHkbdbDraftDataV2(Api.restfulDraft(), entry, user);
        default:
            // create
            return createHkbdbData(Api.restfulHKBDB(), entry);
    }
};
