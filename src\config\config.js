import role from "../App-role";
import { peopleUrl, organizationUrl } from "../services/common";

const GlobalBasicAuthority = [
    role.admin,
    role.editor,
    role.reader,
    role.developer,
    role.anonymous,
    role.suggester
];
const GlobalNniAuthority = [
    role.admin,
    role.editor,
    role.developer,
    role.suggester
];

const config = {
    FILE_SERVER: "https://fs.daoyidh.com/hkvayb",
    // 參數有多個
    MULTIPLE_VALUES: ["nnBestKnownName__string"],

    DEF_ORG_DOMAIN: "organization",
    DEF_PER_DOMAIN: "person",
    DEF_OTR_DOMAIN: "other",
    DEF_PER_RANGE: "Person",
    DEF_ORG_RANGE: "Organization",
    DEF_ORG_TYPE: "organization",
    DEF_NNI_TYPE: "namenode",
    DEF_MEM_TYPE: "member",
    DEF_PUB_TYPE: "publication",
    DEF_ART_TYPE: "article",
    DEF_OTW_TYPE: "otherwork",
    DEF_EVT_TYPE: "event",
    DEF_PER_TYPE: "person",
    DEF_EDUEVT_TYPE: "educationevent",
    DEF_EMPEVT_TYPE: "employmentevent",
    DEF_ORGEVT_TYPE: "organizationevent",
    DEF_RELEVT_TYPE: "relationevent",
    DEF_AWDEVT_TYPE: "awardevent",

    // todo: authority config store on firebase
    basicAuthority: GlobalBasicAuthority,
    NNIAuthority: GlobalNniAuthority,
    getNNIPermission: userRole => GlobalNniAuthority.indexOf(userRole) >= 0,

    // 限定一般使用者在 Person 的 BasicInfo 可以看到的資訊
    PersonAllowNameNodeProps: [
        "originalName",
        "penName",
        "hao",
        "joinPenName",
        "zi",
        "name"
    ],
    // 限定一般使用者在 Organization 的 BasicInfo 可以看到的資訊
    OrgAllowNameNodeProps: [
        "appEnable",
        "hasParentOrganization",
        "hasOffice",
        "hasSubOffice",
        "otherName"
    ],
    nameNodeProp: ["hasNameId"],
    educationeventProp: ["hasEducation"],
    employmenteventProp: ["hasEmployment"],
    publicationProp: ["Publication"],
    articleProp: ["Article"],
    otherworkProp: ["OtherWork"],
    organizationeventProp: ["hasOrganization"],
    relationeventProp: ["hasRelation"],
    eventProp: ["hasEvent"],
    awardeventProp: ["hasAward"],

    // 不同 Entity type 需要做的設定
    entity: {
        Person: {
            reloadInfo: "basicInfo",
            href: peopleUrl,
            // FIXME
            infoModalHeader: "people.Information.create.person",
            infoInputLabel: "people.Information.label.person",
            infoCreatePlaceHolder: "people.Information.create.placeholder",
            infoCreated: "people.Information.create.person"
        },
        Organization: {
            reloadInfo: "organization",
            href: organizationUrl,
            // FIXME
            infoModalHeader: "organization.Information.create.organization",
            infoInputLabel: "organization.Information.label.organization",
            infoCreatePlaceHolder:
                "organization.Information.create.placeholder",
            infoCreated: "organization.Information.create.organization"
        }
    },

    hasAwardedForWork: "hasAwardedForWork",
    hasPublishedIn: "hasPublishedIn",
    isEditionOrTranslationOf: "isEditionOrTranslationOf"
};

export default config;
