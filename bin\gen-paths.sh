#!/bin/bash
#stardog query -f text yjc "PATHS SHORTEST START ?x = tbio:丁根杜 END ?y = tbio:Division_Content via ?p" | head -n 1
arr=()
while IFS= read -r line; do
    arr+=`awk -F "\t" '{print $2}'`
done < /tmp/tbio-id-entity-class-yjc-pwc.tsv

totalNames=0
total=0
for name1 in $arr
do
    echo $totalNames
    totalNames=$[totalNames + 1]
    for name2 in $arr
    do
        # stardog query -f text tbio "PATHS SHORTEST ..." | head -n 1
        total=$[total + 1]
    done
done
echo $total

