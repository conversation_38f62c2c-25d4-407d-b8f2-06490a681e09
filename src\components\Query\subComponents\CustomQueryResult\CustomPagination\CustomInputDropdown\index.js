// react
import React, { useContext, useEffect, useState } from "react";

// ui
import { Input } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// debounce
import CustomDebounce from "./CustomDeBounce";
import CustomDropdown from "./CustomDropdown";
import { injectIntl } from "react-intl";

const Index = ({ intl }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const {
        pagination: { activePage }
    } = state.query;
    //
    const [inputValue, setInputValue] = useState(activePage);
    const debSearchValue = CustomDebounce(inputValue, 500);
    //
    const customInputStyle = {
        textAlign: "center",
        maxWidth: "60px",
        verticalAlign: "middle"
    };
    //
    const handleChange = (event, { value }) => {
        setInputValue(value);
    };
    const handleUpdateActivatePage = () => {
        dispatch({
            type: Act.QUERY_PAGINATION_ACTIVE_PAGE_SET,
            payload: inputValue
        });
    };
    //
    useEffect(() => {
        handleUpdateActivatePage();
    }, [debSearchValue]);
    //
    return (
        <Input
            action={<CustomDropdown />}
            style={customInputStyle}
            placeholder={intl.formatMessage({
                id: "query.page",
                defaultMessage: "page"
            })}
            value={activePage}
            onChange={handleChange}
        />
    );
};

export default injectIntl(Index);
