import React, { useCallback, useState, useContext } from "react";
import { FormattedMessage, injectIntl } from "react-intl";
import {
  Modal,
  Button,
  TextArea,
  ModalHeader,
  ModalContent,
  ModalActions,
  ModalDescription,
  Message
} from "semantic-ui-react";
import axios from "axios";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// 檢查功能是否啟用
const isSparqlEnabled = process.env.REACT_APP_SPARQL_ENABLE === "true";

const textAreaStyles = {
  textarea: {
    minHeight: "400px",
    width: "100%",
    padding: "1rem",
    fontSize: "small",
    // fontFamily: "'Courier New', monospace",
    border: "1px solid rgba(34,36,38,.15)",
    borderRadius: "4px",
    resize: "vertical",
    lineHeight: "1.6",
    backgroundColor: "#f8f9fa",
    color: "#333"
  },
  message: {
    marginTop: "1rem"
  },
  toast: {
    position: "fixed",
    bottom: "1rem",
    left: "1rem",
    zIndex: 1000
  }
};

const CustomModal = ({ intl, setTextareaValue }) => {
  if (!isSparqlEnabled) {
    return null;
  }
  const [state, dispatch] = useContext(StoreContext);
  const [open, setOpen] = useState(false);
  const [inputText, setInputText] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showToast, setShowToast] = useState(false);

  const handleClose = useCallback(() => {
    setOpen(false);
    setError(null);
    setInputText("");
  }, []);

  const handleOpen = useCallback(() => setOpen(true), []);

  const handleInputChange = useCallback((e, { value }) => {
    setInputText(value);
    setError(null);
  }, []);

  const showSuccessToast = useCallback(() => {
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000); // 3秒後自動關閉
  }, []);

  const handleSend = useCallback(async () => {
    if (!inputText.trim()) {
      setError("請輸入查詢描述");
      return;
    }

    setLoading(true);
    setError(null);

    axios
      .post(
        process.env.REACT_APP_SPARQL_API_ENDPOINT,
        { text: inputText },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: process.env.REACT_APP_SPARQL_TOKEN
          }
        }
      )
      .then(response => {
        const { status, sparql, error } = response.data;

        if (status === 200 && sparql) {
          setTextareaValue(sparql);
          dispatch({
            type: Act.QUERY_QUERY_STRING_SET,
            payload: sparql
          });
          showSuccessToast();
          handleClose();
          return;
        }

        switch (status) {
          case 400:
            setError(error.error.error || "查詢描述不能為空");
            break;
          case 401:
            setError(
              error.error.error ||
              "認證失敗，請確認 API Token 是否有效"
            );
            break;
          default:
            setError(`服務錯誤 (${status})`);
        }
      })
      .catch(() => {
        setError("發生錯誤，請稍後再試");
      })
      .finally(() => {
        setLoading(false);
      });
  }, [inputText, setTextareaValue, handleClose, showSuccessToast]);

  return (
    <>
      <Modal
        onClose={handleClose}
        onOpen={handleOpen}
        open={open}
        closeOnDimmerClick={false}
        trigger={
          <Button style={{ marginBottom: "1rem" }}>
            <FormattedMessage
              id="query.ai.button.text"
              defaultMessage="AI 對話式產生SPARQL"
            />
          </Button>
        }
      >
        <ModalHeader>
          <FormattedMessage
            id="query.ai.button.modal.title"
            defaultMessage="AI 對話式產生SPARQL"
          />
        </ModalHeader>
        <ModalContent>
          <ModalDescription>
            <TextArea
              style={textAreaStyles.textarea}
              rows={16}
              placeholder={intl.formatMessage({
                id: "query.ai.button.modal.desc",
                defaultMessage: "請輸入您的查詢描述..."
              })}
              value={inputText}
              onChange={handleInputChange}
              spellCheck="false"
              autoComplete="off"
            />
            {error && (
              <Message negative style={textAreaStyles.message}>
                <Message.Header>
                  <FormattedMessage
                    id="query.ai.error"
                    defaultMessage="錯誤"
                  />
                </Message.Header>
                <p>{error}</p>
              </Message>
            )}
          </ModalDescription>
        </ModalContent>
        <ModalActions>
          <Button
            color="black"
            onClick={handleClose}
            disabled={loading}
          >
            <FormattedMessage
              id="query.ai.cancel"
              defaultMessage="Cancel"
            />
          </Button>
          <Button
            content={
              <FormattedMessage
                id="query.ai.send"
                defaultMessage="Send"
              />
            }
            labelPosition="right"
            icon="checkmark"
            onClick={handleSend}
            positive
            loading={loading}
            disabled={loading}
          />
        </ModalActions>
      </Modal>

      {showToast && (
        <div style={textAreaStyles.toast}>
          <Message positive>
            <Message.Header>
              <FormattedMessage
                id="query.ai.success"
                defaultMessage="成功"
              />
            </Message.Header>
            <FormattedMessage
              id="query.ai.produced"
              defaultMessage="SPARQL查詢已成功生成"
            />
          </Message>
        </div>
      )}
    </>
  );
};

export default injectIntl(React.memo(CustomModal));
