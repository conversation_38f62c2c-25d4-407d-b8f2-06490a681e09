{"name": "hkbdb-web", "version": "1.0.1", "license": "MIT", "private": true, "scripts": {"dev": "concurrently --kill-others-on-fail \"npm run server\" \"npm run client\"", "start": "webpack-dev-server --mode development --config ./webpack.config.js", "build": "webpack --mode production --config ./webpack.config.js --optimize-minimize --progress", "build:develop": "webpack --mode development --config ./webpack.config.js --optimize-minimize --progress", "test": "jest", "lang:extract": "react-intl-cra 'src/**/*.{js,jsx}' -o '.build/messages.json' && node ./scripts/transformMessagesToEnLocale.js", "lang:manage": "node ./scripts/manageTranslations.js"}, "dependencies": {"@daoyi/coordinates-editor-modal": "^1.2.3", "@elfalem/leaflet-curve": "^0.9.2", "@emotion/styled": "^11.6.0", "@material-ui/core": "^4.11.4", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.58", "@material-ui/styles": "^4.11.5", "@mui/icons-material": "^5.15.13", "@mui/lab": "^5.0.0-alpha.110", "@mui/material": "^5.6.4", "@reduxjs/toolkit": "^1.8.2", "@turf/turf": "^7.2.0", "@types/geojson": "^7946.0.15", "animejs": "^3.2.1", "axios": "^0.21.1", "base64url": "^3.0.1", "classnames": "^2.3.2", "crypto-js": "^4.0.0", "d3": "^5.16.0", "firebase": "^8.2.1", "formdata-node": "^4.4.1", "html-react-parser": "^1.2.7", "html2canvas": "^1.0.0-rc.7", "jspdf": "^2.3.1", "jszip": "^3.5.0", "leaflet": "^1.9.4", "leaflet-arrowheads": "^1.4.0", "lodash": "^4.17.20", "markdown-it": "^12.0.6", "matter-js": "^0.19.0", "query-string": "^7.0.0", "ramda": "^0.28.0", "react": "^16.14.0", "react-d3-graph": "^2.0.2", "react-dom": "^16.14.0", "react-fade-in": "^2.0.1", "react-file-download": "^0.3.5", "react-firebaseui": "^4.1.0", "react-icons": "^3.11.0", "react-intl": "^2.9.0", "react-lazy-load-image-component": "^1.6.0", "react-lazyload": "^3.2.0", "react-leaflet": "^4.2.1", "react-leaflet-arrowheads": "^1.1.0", "react-material-ui-carousel": "^2.3.11", "react-number-format": "^4.4.1", "react-redux": "^8.0.2", "react-remarkable": "^1.1.3", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-select": "^4.2.1", "react-slick": "^0.29.0", "react-vertical-timeline-component": "^2.6.2", "react-wordcloud": "^1.2.7", "semantic-ui-css": "^2.4.1", "semantic-ui-react": "^0.84.0", "slick-carousel": "^1.8.1", "supercluster": "^8.0.1", "swagger-ui-react": "^3.44.1", "swiper": "^7.3.1", "topola": "^3.5.1", "traverse": "^0.6.6", "twchar": "1.0.22", "use-supercluster": "^1.2.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "7.15.5", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.17.12", "@babel/plugin-proposal-private-methods": "7.14.5", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/preset-env": "7.15.6", "@babel/preset-react": "7.14.5", "@playwright/test": "^1.49.1", "@types/node": "^22.10.2", "async": "^2.6.1", "autoprefixer": "9.3.1", "babel-cli": "^6.26.0", "babel-eslint": "^10.0.1", "babel-loader": "8.1.0", "babel-plugin-lodash": "^3.3.4", "babel-plugin-react-intl": "^2.4.0", "clsx": "^1.1.1", "concurrently": "^4.1.0", "copy-webpack-plugin": "^4.6.0", "css-loader": "^2.1.0", "dotenv": "^6.2.0", "dotenv-webpack": "^1.7.0", "eslint": "^5.8.0", "eslint-config-prettier": "^2.9.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jest": "^21.15.1", "eslint-plugin-node": "^5.2.1", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-react": "^7.11.1", "eslint-plugin-standard": "^4.0.0", "favicons": "^6.2.2", "favicons-webpack-plugin": "^4.2.0", "file-loader": "^6.2.0", "html-loader": "^0.5.5", "html-webpack-plugin": "^4.0.0", "less-loader": "7.3.0", "lodash-webpack-plugin": "^0.11.5", "mini-css-extract-plugin": "0.4.4", "postcss-import": "12.0.1", "postcss-loader": "3.0.0", "prettier": "^1.9.2", "prop-types": "^15.6.2", "react-css-modules": "^4.7.9", "react-intl-cra": "^0.3.3", "react-intl-translations-manager": "^5.0.3", "react-window": "^1.8.6", "sass": "1.27.0", "sass-loader": "10.0.1", "style-loader": "^0.23.1", "svg-inline-loader": "^0.8.0", "url-loader": "^1.1.2", "webpack": "^4.28.4", "webpack-bundle-analyzer": "^3.0.3", "webpack-cli": "^3.2.1", "webpack-dev-server": "^3.11.3"}, "engines": {"node": "14.x"}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}