import axios from "axios";

// common code
import { isEmpty } from "../../common/codes";

// axios cancel method
const CancelToken = axios.CancelToken;

const method = {};

method.hkvaybQuery = async (url, timeout = 5000, isCancel = false) => {
    //
    let result = { data: [] };
    //
    let config = {};
    //
    axios.defaults.timeout = timeout;
    //
    if (isEmpty(url)) {
        return {
            ...result,
            error: "url error"
        };
    }
    //
    if (isCancel) {
        config = {
            ...config,
            cancelToken: CancelToken
        };
    }
    //
    try {
        // get data from api
        const res = await axios
            .get(url, config)
            .then(res => res)
            .catch(err => {
                if (axios.isCancel(err)) {
                    console.error("Request canceled", err.message);
                } else {
                    console.error("unknown error", err.message);
                }
            });
        // handle data
        return {
            ...result,
            data: res?.data || {},
            durationSS: res?.durationSS
        };
    } catch (err) {
        console.error(err.message);
        return {
            ...result,
            error: err.message
        };
    }
};

export default method;
