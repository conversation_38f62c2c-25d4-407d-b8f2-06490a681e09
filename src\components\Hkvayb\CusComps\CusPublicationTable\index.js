import React, { useContext } from "react";

import { FormattedMessage } from "react-intl";

import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableBody from "@mui/material/TableBody";
import TableHead from "@mui/material/TableHead";
import TableContainer from "@mui/material/TableContainer";
import TableCell from "@mui/material/TableCell";

import CusValue from "../CusValue";
import CusLoading from "../CusLoading";

import config from "../../config";

import useFetch from "../../CusHooks/useFetch";
import useObjToRows from "../../CusHooks/useObjToRows";
import useTripleMerge from "../../CusHooks/useTripleMerge";

import { url } from "../../../../api/hkvayb";

import { bs64Encode, isEmpty, safeGet } from "../../../../common/codes";

import { StoreContext } from "../../../../store/StoreProvider";

const coverLoadingDiv = {
    hkvayb_div: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "40px 0 40px 0"
    }
};

const coverCusValueStyle = {
    hkvayb_div: {
        marginLeft: "unset"
    }
};

const CusPublicationTable = ({ eventId }) => {
    const [state] = useContext(StoreContext);
    const { locale } = state.user;

    const api =
        locale === config.languages.zhHans
            ? url.hkvayb.DETAILPAGE_ZH_DETAIL_EVENT_INFORMATION
            : url.hkvayb.DETAILPAGE_EN_DETAIL_EVENT_INFORMATION;
    const apiUrl = api.replace("{keyword}", `${bs64Encode(eventId || "")}`);
    const result = useFetch(apiUrl);
    const { data, loading } = result;

    const mergedData = useTripleMerge(data.data);
    const rows = useObjToRows(mergedData);

    if (loading) {
        return <CusLoading style={coverLoadingDiv} />;
    }

    if (isEmpty(rows)) {
        return null;
    }

    return (
        <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                    <TableRow>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.publication.table.header.date"
                                defaultMessage="Date"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.publication.table.header.title"
                                defaultMessage="Title"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.publication.table.header.author"
                                defaultMessage="Author"
                            />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage
                                id="hkvayb.search.publication.table.header.page"
                                defaultMessage="Page"
                            />
                        </TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {rows &&
                        rows.map(cells => {
                            const defObj = { type: null, value: [] };
                            const date = safeGet(
                                cells,
                                ["hasInceptionDate"],
                                defObj
                            );
                            const label = safeGet(cells, ["label"], defObj);
                            const author = safeGet(
                                cells,
                                ["hasAuthor"],
                                defObj
                            );
                            const page = safeGet(cells, ["page"], defObj);
                            return (
                                <TableRow
                                    key={label.value}
                                    sx={{
                                        "&:last-child td, &:last-child th": {
                                            border: 0
                                        }
                                    }}
                                >
                                    <TableCell component="th" scope="row">
                                        <CusValue
                                            {...date}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...label}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...author}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <CusValue
                                            {...page}
                                            style={coverCusValueStyle}
                                        />
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                </TableBody>
            </Table>
        </TableContainer>
    );
};

export default CusPublicationTable;
