import parse from "html-react-parser";

const md = require("markdown-it")({
    html: true, // Enable HTML tags in source
    xhtmlOut: false,
    breaks: true,
    langPrefix: "language-",
    linkify: true,
    typographer: true,
    quotes: "“”‘’",
    highlight(/* str, lang */) {
        return "";
    }
});

const domNodeCheck = ["h1", "h2", "h3", "h4", "h5", "h5", "p", "ul", "li"];

const parseOption = {
    replace: domNode => {
        if (domNode.attribs && domNode.name === "a") {
            domNode.attribs.target = "_blank";
            domNode.attribs.rel = "noreferrer noopener";
            domNode.attribs.class = "md2react-a-tag";
            return domNode;
        }
        if (domNode.attribs && domNodeCheck.indexOf(domNode.name) >= 0) {
            if (domNode.attribs.class?.startsWith("ql-indent")) {
                // render quill class indent
                domNode.attribs.class = `md2react-${domNode.name}-tag ${domNode.attribs.class}`;
                return domNode;
            }
            domNode.attribs.class = `md2react-${domNode.name}-tag`;
            return domNode;
        }
    }
};

// eslint-disable-next-line import/prefer-default-export
export const convert2HtmlEntities = text => {
    if (text === undefined) {
        return "";
    }

    return parse(md.render(text), parseOption);
};
