import React, { useContext, useState, useEffect } from "react";
import { <PERSON><PERSON>, Container, Input, Select, Header } from "semantic-ui-react";
import { useHistory } from "react-router-dom";
import { FormattedMessage, injectIntl } from "react-intl";
import * as _ from "lodash";
// sorting chinese words
import { sortedByStroke } from "twchar";
import { StoreContext } from "../../../../store/StoreProvider";
// api
import {
    readHkbdbData,
    apiSwithcer,
    varSwitcher,
    apiParamsDefault,
    Api
} from "../../../../api/hkbdb/Api";
import { rootClasses, options } from "../../browseConfig";
import ResultsEntity from "./ResultsEntity";
import { bs64Decode, bs64Encode } from "../../../../common/codes";
import queryString from "query-string";
import { escapeRegExpKeyword } from "../../../../common/codes/escapeRegExp";
import useLocaleRoute from "../../../../hook/useLocaleRoute";
import { getPathById, ROUTE_ID } from "../../../../App-route";
// images
import SearchIcon from "../../../../images/icon_search/icon_search.svg";
import "../../browsePage.scss";

// create common name key in all kinds of entity
export const entityProcessing = (data, className) => {
    return data.map(d => ({
        ...d,
        name: d[varSwitcher[className].findEntityByKeyword[0]]
    }));
};

/**
 * 依據 ids, 取得 bestKnowName by locale
 * @param ids {string[]} 所有人物及組織的 id
 * @returns {Promise<null|unknown extends (object & {then(onfulfilled: infer F): any}) ? (F extends ((value: infer V, ...args: any) => any) ? Awaited<V> : never) : unknown|*[]>}
 */
const getNameByLocale = async ids => {
    try {
        if (!ids) return null;
        let encodeIdsLen = 0;
        let tmpIds = [];
        // url 有長度限制(2048 characters), 所以 10 個人(或組織)為一組
        const asyncQuery = [];
        const MAX_URL_LENGTH = 2000;

        // 因為有些 id 可能很長(encodeUrl後 >1000字元)
        // 每個 url 的限制 < 2000 字元
        for (let i = 0; i < ids.length; i += 1) {
            const encodeId = bs64Encode(ids[i]);
            if (encodeIdsLen + encodeId.length > MAX_URL_LENGTH) {
                // API 若發現字串中 \n, 則會以 \n 當作分隔 id 的依據
                // 必須在串聯ids的最前面+ "\n",如果 只有一個 id 時,供 API split id 的參考依據
                const url = Api.getNameByLocale().replace(
                    "{ids}",
                    bs64Encode("\n" + tmpIds.join("\n"))
                );
                asyncQuery.push(
                    readHkbdbData(
                        url,
                        undefined,
                        false,
                        false,
                        undefined,
                        false
                    )
                );
                tmpIds = [ids[i]];
                encodeIdsLen = encodeId.length;
            } else {
                tmpIds.push(ids[i]);
                encodeIdsLen += encodeId.length;
            }
        }
        if (tmpIds.length > 0) {
            // API 若發現字串中 \n, 則會以 \n 當作分隔 id 的依據
            // 必須在串聯ids的最前面+ "\n",如果 只有一個 id 時,供 API split id 的參考依據
            const url = Api.getNameByLocale().replace(
                "{ids}",
                bs64Encode("\n" + tmpIds.join("\n"))
            );
            asyncQuery.push(
                readHkbdbData(url, undefined, false, false, undefined, false)
            );
        }
        const response = await Promise.all(asyncQuery);
        const totalData = response.reduce((acc, cur) => {
            return acc.concat(cur.data);
        }, []);
        const totalDuration = response.reduce((acc, cur) => {
            acc += cur?.durationSS || 0;
            return acc;
        }, 0);
        return {
            data: totalData,
            // 使用平均搜尋時間當作 durationSS
            durationSS: totalDuration / asyncQuery.length
        };
    } catch (err) {
        return [];
    }
};

export const findEntityByKeyword = async (className, searchValue) => {
    try {
        if (!className || !searchValue) return;
        const limit = apiParamsDefault.findEntityByKeyword.limit;
        let api = apiSwithcer[className].findEntityByKeyword
            // 先處理跳脫字元後,再 bs64encdoe
            .replace("{keyword}", bs64Encode(escapeRegExpKeyword(searchValue)))
            .replace("{limit}", limit);
        // initial duration
        let durationSSTotal = 0;
        // step 1 search: search uniq perId
        const res = await readHkbdbData(
            api,
            undefined,
            false,
            false,
            undefined,
            false
        );
        if (res?.data) {
            durationSSTotal += res?.durationSS || 0;
            const uniqIdKey = varSwitcher[className].entityIdKey[0];
            const idsAry = res.data.map(item => item?.[uniqIdKey]);

            // step 2 search: search personName by perId
            const res2 = await getNameByLocale(idsAry);
            if (res2?.data) {
                durationSSTotal += res2?.durationSS || 0;
                return {
                    // 按筆畫排序
                    data: sortedByStroke(
                        res2.data.map(item => ({
                            ...item,
                            name: item.srcName
                        })),
                        "name"
                    ),
                    durationSS: durationSSTotal
                };
            } else {
                return {
                    data: [],
                    durationSS: durationSSTotal
                };
            }
        } else {
            return {
                data: [],
                durationSS: durationSSTotal
            };
        }
    } catch (err) {
        return {
            data: [],
            durationSS: 0
        };
    }
};

/**
 * type State = {
        isLoading: boolean,
        searchResults: Object,
        searchValue: string,
        error: Object | null,
        className: string
   };
 * */
const EntitySearch = ({ intl, EntityBody }) => {
    // route
    const history = useHistory();
    const {
        location: { pathname, search }
    } = history;
    // store
    const [state] = useContext(StoreContext);
    const { main } = state;
    const { webStyle } = main;
    const [isLoading, setIsLoading] = useState(false);
    const [searchResults, setSearchResults] = useState(null);
    const [searchValue, setSearchValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    // const debSearchValue = CustomDebounce(searchValue, 500);

    const [error, setError] = useState(null);
    const [className, setClassName] = useState("Person");
    const [selectionOpts, setSelectionOpts] = useState(options);
    const [isEditing, setIsEditing] = useState(false);
    const [fetchSignal, setFetchSignal] = useState(null);

    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    /**
     * 搜尋邏輯:
     * 統一由 網址的改變來觸發 fetch API
     * 1-1.進入頁面後偵測 query string 的 name 及 type
     * 1-2.若 name 及 type 不為空, 則 search
     * 2-1.若使用者 click "搜尋",則 history.push() 改變網址
     */

    const onResultSelect = (event, data) => {
        setClassName(data.value);
    };

    const onSearchChange = (event, data) => {
        // input 變更時，立即更新 search value
        // 這邊不能去掉頭尾 空白(trim), 因為 input 為 controllable
        // 去掉頭尾空白時, 使用者無法輸入 空白字元 e.g. john denver
        setSearchValue(data.value);
    };

    const send = () => {
        setIsLoading(true);
        findEntityByKeyword(className, searchValue)
            .then(res => {
                setSearchResults({
                    // bindings: entityProcessing(res.data, className),
                    bindings: res?.data || [],
                    durationSS: res.durationSS
                });
                setError(null);
            })
            .catch(error => {
                setSearchResults({
                    vars: [],
                    bindings: []
                });
                setError(error);
            })
            .finally(() => {
                setIsLoading(false);
                setIsEditing(false);
            });
    };

    const pushHistory = () => {
        if ((searchValue || "").trim() && className) {
            // 統一導向 browse page
            const url = handleLocaleRoute(getPathById(ROUTE_ID.Browse));
            history.push(
                `${url}?${queryString.stringify({
                    name: bs64Encode(searchValue.trim()),
                    type: className
                })}`
            );
        }
    };

    const onKeyUp = e => {
        if (e.key === "Enter") {
            pushHistory();
        }
    };

    const showSearchMessage = results => {
        return results && results.bindings;
    };

    // 當語系變更時，切換 selection option 的語系
    useEffect(() => {
        const _selectionOpt = options.map(ot => ({
            key: ot.key,
            text: intl.formatMessage({
                id:
                    ot.key === rootClasses[0].name
                        ? "browse.classPerson"
                        : "browse.classOrganization",
                defaultMessage: ot.text
            }),
            value: ot.value,
            disabled: ot.disabled
        }));
        setSelectionOpts(_selectionOpt);
    }, [intl]);

    useEffect(() => {
        const { name, type } = queryString.parse(search || "");
        const queryHasType =
            type &&
            options
                .map(o => o.value.toLowerCase())
                .includes(type.toLowerCase());
        if (queryHasType) {
            setClassName(_.capitalize(type));
        }
        if (name) {
            setSearchValue(bs64Decode(name).trim());
        }
        // 若 query string 沒有帶 type, 使用預設的 clasName
        if (name) {
            setFetchSignal({
                time: Date.now()
            });
        }
    }, [pathname, search]);

    useEffect(() => {
        if (fetchSignal) {
            send();
        }
    }, [fetchSignal]);

    return (
        <Container
            className={"entitySearch"}
            style={{
                // boxShadow:
                //     "rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px",
                zIndex: "9999",
                maxWidth: "480px"
            }}
        >
            <Input
                className={"entitySearch__input"}
                fluid
                type="text"
                placeholder={intl.formatMessage({
                    id: "browse.inputForSearchingHomepage",
                    defaultMessage: "Enter search terms"
                })}
                action
                onChange={onSearchChange}
                onKeyUp={onKeyUp}
            >
                <Select
                    className={"entitySearch__select"}
                    options={selectionOpts}
                    defaultValue={className}
                    onChange={onResultSelect}
                    disabled={isLoading}
                    style={{
                        border: "0",
                        color: "#104860",
                        marginRight: "1rem"
                    }}
                />
                <input
                    disabled={isLoading}
                    value={searchValue}
                    style={{ border: "0", marginRight: "1rem" }}
                />
                <Button
                    type="submit"
                    className={"entitySearch__submit"}
                    style={{
                        borderRadius: "4px",
                        // marginLeft: "8px",
                        // backgroundColor: webStyle.darkColor,
                        color: webStyle.lightColor
                    }}
                    disabled={isLoading}
                    onClick={() => {
                        pushHistory();
                    }}
                >
                    {/* <FormattedMessage */}
                    {/*    id="browse.entitySearchBtn" */}
                    {/*    defaultMessage="Search" */}
                    {/* /> */}
                    <img src={SearchIcon} alt="Search Icon" />
                </Button>
                <div className={"entitySearch__desc"}>
                    <Header
                        className={"entitySearch__desc--text"}
                        as="h4"
                        onClick={() => {
                            history.push(pathname.replace("browse", "help"));
                        }}
                    >
                        <FormattedMessage
                            id="browse.description"
                            defaultMessage="Description"
                        />
                    </Header>
                </div>
            </Input>

            <ResultsEntity
                showSearchMessage={showSearchMessage(searchResults)}
                loading={isLoading}
                results={searchResults}
                classType={className}
                searchValue={searchValue}
                isEditing={isEditing}
                EntityBody={EntityBody}
            />
        </Container>
    );
};

export default injectIntl(EntitySearch);
