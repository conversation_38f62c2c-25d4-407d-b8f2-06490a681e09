import React, { useContext, useEffect, useState } from "react";
import { FormattedMessage, injectIntl } from "react-intl";

// semantic ui
import { <PERSON><PERSON> } from "semantic-ui-react";

// utils
import {
    bs64Decode,
    bs64Encode,
    isEmpty,
    isNotEmpty,
    safeGet
} from "../../../../../common/codes";

// action
import { SPECIAL_HEADER } from "../../../Organization/action";

// config
import config from "../../../../../config/config";

// api
import {
    Api,
    deleteHkbdbData,
    doRestCreate,
    getEntryGraph,
    readHkbdbData
} from "../../../../../api/hkbdb/Api";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import allRoles from "../../../../../App-role";
import Act from "../../../../../store/actions";
import { createEmail } from "../../../../Auth/email.api";
import emailConfig from "../../../../../config/config-email";

function ModalButtons(props) {
    const {
        createData,
        setCreateData,
        ontologyType,
        createGraphData,
        setOpen,
        setAlertMsg,
        intl,
        handleClose
    } = props;
    const [state, dispatch] = useContext(StoreContext);
    const { information, user } = state;
    const { personInformation: perInfo } = state;
    const { memberInvert } = perInfo;
    const { personId, ontologyOneOfThemData } = information;
    const [isDisabled, setIsDisabled] = useState(false);
    const [isLoading, setIsLoading] = useState(() => false);
    const { email, displayName } = user;

    const deleteOriginEvent = async ogData => {
        if (
            ontologyType === "person" ||
            ontologyType === "article" ||
            ontologyType === "publication" ||
            ontologyType === "otherwork" ||
            ontologyType === "relationevent"
        )
            return;

        if (isEmpty(ogData.data)) return;

        const subjectKey = "PER" + bs64Decode(createGraphData.srcId.slice(3));
        const eventKey = ogData.data.find(i => i.s === subjectKey)?.o;

        // eslint-disable-next-line no-undef
        const cloneData = structuredClone(createGraphData);

        cloneData.value = {};
        cloneData.srcId = eventKey.slice(0, 3) + btoa(eventKey.slice(3));

        await deleteHkbdbData(Api.deleteEvent(), cloneData);
    };

    useEffect(() => {
        // 在publication、article、otherwork需判斷willCreateData內是否含有目前的主體(srcId)
        const { willCreatedData } = createData || {};
        const classType = willCreatedData?.classType;
        if (
            classType !== "publication" &&
            classType !== "article" &&
            classType !== "otherwork"
        )
            return;

        const srcId = safeGet(createData, ["willCreatedData", "srcId"], "");
        const _prefix = srcId.slice(0, 3);
        const _perName = bs64Decode(srcId.slice(3));
        const _value = `${_prefix}${_perName}`;

        if (!createData.willCreatedData?.value) return;
        const isPresent = Object.values(
            createData.willCreatedData?.value
        ).some(arr => arr.includes(_value));

        if (isPresent) {
            setIsDisabled(false);
        } else {
            setIsDisabled(true);
        }
    }, [createData]);

    // const handleCreate = async () => {
    //     //
    //     setIsLoading(true);
    //     if (!isEmpty(personId)) {
    //         createGraphData.value = createData.willCreatedData.value;
    //         let entry = createGraphData;
    //         //
    //         // 針對組織 member 「新增」做特別處理
    //         if (ontologyType === "member") {
    //             //
    //             const name = entry.value?.[SPECIAL_HEADER];
    //             //
    //             let memberType = entry.classType;
    //             //
    //             const memberValue = Object.keys(entry.value).reduce(
    //                 (acc, curKey) => {
    //                     // 特定 key 和值為空時不做處理，直接回傳
    //                     if (
    //                         curKey === SPECIAL_HEADER ||
    //                         isEmpty(entry.value[curKey])
    //                     )
    //                         return acc;
    //                     //
    //                     if (memberInvert[curKey]) {
    //                         memberType = memberInvert[curKey].classtype;
    //                         acc[memberInvert[curKey].property] = name;
    //                     }
    //
    //                     acc[curKey] = entry.value[curKey];
    //                     return acc;
    //                 },
    //                 {}
    //             );
    //             //
    //             entry.classType = memberType;
    //             entry.value = memberValue;
    //         }
    //
    //         const tmpValueKeyArr = Object.keys(entry?.value);
    //         // 檢查是否有 label 沒有則補上空 label
    //         if (
    //             tmpValueKeyArr.filter(
    //                 tmpPropKey => tmpPropKey.indexOf("label_") === -1
    //             ) &&
    //             ontologyType !== "member"
    //         ) {
    //             entry.value = {
    //                 [`label_${ontologyType}__string`]: "",
    //                 ...entry.value // 擺第二個如果原本就有值的話以最後一個為準再覆蓋回來
    //             };
    //         }
    //
    //         // 多語系
    //         Object.keys(entry.value).forEach(valKey => {
    //             if (config.MULTIPLE_VALUES.indexOf(valKey) > -1) {
    //                 entry.value[valKey] = entry.value[valKey].split("\n");
    //             }
    //         });
    //
    //         const values = Object.values(entry.value).flat();
    //         if (values.includes("test")) {
    //             console.log("Word exists");
    //         }
    //
    //         const createGraphDataFuseki = await readHkbdbData(
    //             Api.getSuggestionData(createGraphData.graph)
    //         );
    //         // Create obj
    //         const result = await doRestCreate(user, entry);
    //         // 刪除一開始的event
    //         await deleteOriginEvent(createGraphDataFuseki);
    //         // get result
    //         if (!isEmpty(result)) {
    //             //
    //             setCreateData(prevData => ({
    //                 ...prevData,
    //                 isCreated: true
    //             }));
    //             //
    //             setAlertMsg(prevMsg => ({
    //                 ...prevMsg,
    //                 title: intl.formatMessage({
    //                     id: "alertMsg.createModalFlex.title.success.suggester",
    //                     defaultMessage: `Create successfully ...`
    //                 }),
    //                 type: "success",
    //                 content: intl.formatMessage({
    //                     id:
    //                         "alertMsg.createModalFlex.content.success.suggester",
    //                     defaultMessage: `Create new property successfully ...`
    //                 }),
    //                 renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
    //             }));
    //             const sendMail = {
    //                 from: emailConfig.daoyidhNoReply,
    //                 to: email,
    //                 cc: emailConfig.cuhk,
    //                 subject: "「香港作家及藝術家傳記資料庫」增補建議",
    //                 html: `
    //                 <p>${displayName}：</p>
    //                 <p>感謝閣下提供的建議，我們會盡快處理及回覆。</p>
    //                 <br>
    //                 <p>香港中文大學圖書館</p>
    //                 `
    //             };
    //             // createEmail(sendMail);
    //         }
    //         // result error
    //         else {
    //             setAlertMsg(prevMsg => ({
    //                 ...prevMsg,
    //                 title: intl.formatMessage({
    //                     id: "alertMsg.createModalFlex.title.Error",
    //                     defaultMessage: `Create error ...`
    //                 }),
    //                 type: "error",
    //                 content: intl.formatMessage({
    //                     id: "alertMsg.createModalFlex.content.Error",
    //                     defaultMessage: `Create new property error ...`
    //                 }),
    //                 renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
    //             }));
    //         }
    //         dispatch({
    //             type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
    //             payload: {
    //                 target: null,
    //                 signal: `create-${new Date().getTime()}`
    //             }
    //         });
    //     }
    //
    //     setIsLoading(false);
    //     setOpen(false);
    // };

    const handleCreate = async () => {
        setIsLoading(true);
        createGraphData.value = createData.willCreatedData.value;
        let entry = createGraphData;
        dispatch({
            type: Act.SET_ENTRY_GRAPH_FOR_CREATING_SUGGESTIONS,
            payload: entry
        });
        dispatch({
            type: Act.SET_TEMP_UPDATE_SUGGESTINFO,
            payload: entry
        });
        // setCreateData(prevData => ({
        //     ...prevData
        //     // isCreated: true
        // }));
        setCreateData(prevData => ({
            ...prevData,
            selectedProperties: [],
            willCreatedData: { srcId: personId }
            // isCreated: false
        }));
        setIsLoading(false);
        setOpen(false);
    };

    if (createData.isCreated) {
        return (
            <Button onClick={handleClose} color="green">
                <FormattedMessage
                    id={"people.Information.button.close"}
                    defaultMessage={"Close"}
                />
            </Button>
        );
    } else {
        //
        const {
            srcId,
            graph,
            ...restWillCreatedData
        } = createData.willCreatedData;
        //
        const isEmptyData = isEmpty(restWillCreatedData);
        //
        const requiredArr = createData.selectedProperties.reduce(
            (prevObj, item) => {
                if (item.hasOwnProperty("required")) {
                    return [...prevObj, item.value];
                }
                return prevObj;
            },
            []
        );
        //
        const selectProperties = safeGet(
            createData,
            ["selectedProperties"],
            []
        );
        //
        const oneOfThemData = safeGet(
            createData,
            ["ontologyOneOfThemData"],
            []
        );
        //
        const entryValue = safeGet(createData.willCreatedData, ["value"], {});
        //
        const isNotDoneRequired =
            requiredArr
                .map(prop => {
                    if (isNotEmpty(safeGet(entryValue, [prop], []))) {
                        return prop;
                    }
                })
                .filter(item => item !== undefined).length !==
            requiredArr.length;
        //
        const isNotSelectedOneOfThem =
            selectProperties.findIndex(item => {
                oneOfThemData
                    .map(value => value.toLowerCase())
                    .includes(safeGet(item, ["property"], "").toLowerCase());
            }) === -1;
        // useEffect(() => {
        //     // 任何欄位都不存在時，則 true
        //     // if (isEmptyData) {
        //     //     setIsDisabled(true);
        //     // }
        //     // // 必填清單存在時，且欄位尚未完全填完，則 true
        //     // else if (isNotEmpty(requiredArr) && isNotDoneRequired) {
        //     //     setIsDisabled(true);
        //     // }
        //     // 最少要填一項欄位清單存在時，如果連一項都沒選，則 true
        //     // else {
        //     //     const g = isNotEmpty(oneOfThemData);
        //     //     const zz = isNotEmpty(oneOfThemData) && isNotSelectedOneOfThem;
        //     //     setIsDisabled(
        //     //         isNotEmpty(oneOfThemData) && isNotSelectedOneOfThem
        //     //     );
        //     // }
        //     //
        // }, [
        //     isEmptyData,
        //     requiredArr,
        //     isNotDoneRequired,
        //     oneOfThemData,
        //     isNotSelectedOneOfThem
        // ]);
        // let isDisabled = false;
        //
        // // 任何欄位都不存在時，則 true
        // if (isEmptyData) {
        //     isDisabled = true;
        // }
        // // 必填清單存在時，且欄位尚未完全填完，則 true
        // else if (isNotEmpty(requiredArr) && isNotDoneRequired) {
        //     isDisabled = true;
        // }
        // // 最少要填一項欄位清單存在時，如果連一項都沒選，則 true
        // else isDisabled = isNotEmpty(oneOfThemData) && isNotSelectedOneOfThem;
        // //
        return (
            <>
                <Button
                    color="green"
                    loading={isLoading}
                    disabled={isDisabled}
                    onClick={handleCreate}
                >
                    <FormattedMessage
                        id="people.Information.button.confirm"
                        defaultMessage="Confirm"
                    />
                </Button>
                <Button color="red" onClick={handleClose}>
                    <FormattedMessage
                        id="people.Information.button.cancel"
                        defaultMessage="Cancel"
                    />
                </Button>
            </>
        );
    }
}

export default injectIntl(ModalButtons);
