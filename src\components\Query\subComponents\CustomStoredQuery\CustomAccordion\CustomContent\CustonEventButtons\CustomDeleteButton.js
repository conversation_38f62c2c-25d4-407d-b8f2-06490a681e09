// react
import React, { useContext } from "react";

// ui
import { Button } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
import Act from "../../../../../../../store/actions";

// firebase api
import cloudStorage from "../../../../../../../api/firebase/cloudFirestore/Api";
import { injectIntl } from "react-intl";

const CustonDeleteButton = ({ docId, authorId, intl }) => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { uid } = user;
    //
    const handleDelete = async () => {
        if (uid === authorId) {
            if (docId) {
                const success = await cloudStorage.deleteQuery(docId);
                if (success) {
                    // update queryReducer state
                    dispatch({
                        type: Act.QUERY_RELOAD_RENDER_SIGNAL_SET,
                        payload: `reload-queries-${new Date().getTime()}`
                    });
                } else {
                    console.log("delete query undone");
                }
            }
        }
    };
    const ShowButton = () => {
        if (uid === authorId) {
            return (
                <Button
                    size="tiny"
                    color="red"
                    content={intl.formatMessage({
                        id: "custom.deleteButton",
                        defaultMessage: "Delete"
                    })}
                    onClick={handleDelete}
                />
            );
        } else {
            return null;
        }
    };
    //
    return <ShowButton />;
};

export default injectIntl(CustonDeleteButton);
