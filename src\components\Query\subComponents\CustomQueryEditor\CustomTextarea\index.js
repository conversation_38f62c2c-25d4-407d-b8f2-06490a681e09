// react
import React, { useContext, useState, useEffect } from "react";

// ui
import { TextArea, Form } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// common
import { isEmpty } from "../../../../../common/codes";
import { injectIntl } from "react-intl";

const index = ({ intl, setTextareaValue, textareaValue }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { uid } = state.user;
    const { selectedAuthorId, selectedQueryId, queries } = state.query;
    // const [textareaValue, setTextareaValue] = useState("");
    const { formatMessage } = intl;

    // handleChange
    const handleTextareaChange = (event, { value }) => {
        setTextareaValue(value);
        // save value to queryReducer state
        dispatch({
            type: Act.QUERY_QUERY_STRING_SET,
            payload: value
        });
    };
    //
    const handleLoadSelectedQuery = () => {
        if (!isEmpty(selectedQueryId)) {
            const selectedQuery = queries.filter(
                query => query.id === selectedQueryId
            )[0];
            if (!isEmpty(selectedQuery)) {
                if (selectedQuery.query) {
                    setTextareaValue(selectedQuery.query);
                    // save value to queryReducer state
                    dispatch({
                        type: Act.QUERY_QUERY_STRING_SET,
                        payload: selectedQuery.query
                    });
                }
            }
        }
    };
    //
    useEffect(() => {
        handleLoadSelectedQuery();
    }, [selectedQueryId]);
    // custom style
    const customTextareaStyle = {
        width: "100%",
        minHeight: 100
    };
    const customTextareaEditModeStyle = {
        ...customTextareaStyle,
        backgroundColor: "#3eff490d"
    };
    const switchStyle =
        selectedQueryId && uid === selectedAuthorId
            ? customTextareaEditModeStyle
            : customTextareaStyle;
    //
    return (
        <Form>
            {/* <pre>{ JSON.stringify(state.query, null, 2) }</pre> */}
            {/* <pre>{ JSON.stringify(state.user.currentUser, null, 2) }</pre> */}
            <TextArea
                rows={8}
                value={textareaValue}
                style={switchStyle}
                placeholder={formatMessage({
                    id: "query.boxPrompt",
                    defaultMessage:
                        "Type SPARQL query, e.g. SELECT * WHERE \\{ ?s ?p ?o \\}"
                })}
                onChange={handleTextareaChange}
            />
        </Form>
    );
};

export default injectIntl(index);
