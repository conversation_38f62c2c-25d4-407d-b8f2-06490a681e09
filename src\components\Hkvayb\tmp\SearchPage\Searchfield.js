import React, { useState, useContext } from "react";
import {
    createMuiTheme,
    ThemeProvider,
    makeStyles
} from "@material-ui/core/styles";
import InputBase from "@material-ui/core/InputBase";
import Button from "@material-ui/core/Button";
import SearchIcon from "@material-ui/icons/Search";
import { teal, red } from "@material-ui/core/colors";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";
const theme = createMuiTheme({
    palette: {
        primary: teal,
        secondary: { main: red[500] }
    }
});

const useStyles = makeStyles(theme => ({
    root: {
        padding: "2px 4px",
        display: "flex",
        alignItems: "center",
        width: "80%",
        height: "3rem",
        border: "1px solid #F0F0F0",
        borderRadius: "8px"
    },
    input: {
        marginLeft: theme.spacing(2),
        flex: 1,
        fontSize: "1.3rem"
    },
    Button: {
        fontSize: "1.2rem"
    }
}));

const Searchfield = () => {
    const classes = useStyles();
    const [, dispatch] = useContext(StoreContext);
    const [tempWord, setTempWord] = useState("");
    const handleOnChange = e => {
        setTempWord(e.target.value);
    };
    const handleOnClick = () => {
        dispatch({
            type: act.SET_KEYWORD,
            payload: tempWord
        });
        dispatch({
            type: act.SET_ISSEARCH,
            payload: false
        });
        setTimeout(() => {
            dispatch({
                type: act.SET_ISSEARCH,
                payload: true
            });
        }, 1200);
    };
    const handleKeyDown = (event) => {
        if (event.key === "Enter") { // 新增按下Enter可以做搜尋
            dispatch({
                type: act.SET_KEYWORD,
                payload: tempWord
            });
            dispatch({
                type: act.SET_ISSEARCH,
                payload: false
            });
            setTimeout(() => {
                dispatch({
                    type: act.SET_ISSEARCH,
                    payload: true
                });
            }, 1200);
        }
    };

    return (
        <ThemeProvider theme={theme}>
            <div className={classes.root}>
                <InputBase
                    className={classes.input}
                    onChange={handleOnChange}
                    onKeyDown={handleKeyDown}
                    placeholder="Keywords"
                />
                <Button
                    type="submit"
                    color="primary"
                    startIcon={<SearchIcon />}
                    className={classes.Button}
                    onClick={handleOnClick}
                >
                    Search
                </Button>
            </div>
        </ThemeProvider>
    );
};
export default Searchfield;
