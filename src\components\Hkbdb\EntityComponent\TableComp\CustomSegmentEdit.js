import React, { useContext } from "react";

// custom
import CustomInput from "./CustomInputFixedMenu";
import CustomDateInput from "./CustomDateInputEdit";
import CustomDropdown from "./CustomDropdownFixedEdit";

// common
import {
    cvtDatasetLocale,
    getProperty,
    isEmpty
} from "../../../../common/codes";

// ui
import { Container, Segment, Header, Label, Divider } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import { convertNormalColHeaderName } from "../../common/utils/convertSugOptions";
import CustomEditCoordinatesModal from "./CustomEditCoordinatesModal";

const CustomSegmentEdit = ({
    rowIdx,
    rowData,
    editData,
    setEditData,
    ontologyType
}) => {
    //
    const { property, range, propertyBindRangeStr, graph, values } = rowData;
    //
    const [state] = useContext(StoreContext);
    const { personId } = state.information;
    const { property: propData, source } = state;
    const { dataset: globalDataset } = source;
    //
    const dividerStyle = {
        margin: ".5rem 0"
    };
    const segmentStyle = () => {
        if (editData.updatedRowIds.includes(rowIdx)) {
            return {
                // backgroundColor: "#f8ffff"
                backgroundColor: "#fcfff5"
            };
        } else {
            return {};
        }
    };
    //
    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(propData?.propertyObj)
            ? _property
            : getProperty(_property, propData.propertyObj);
    };

    const title = convertNormalColHeaderName(
        propertyBindRangeStr,
        ontologyType,
        safeGetProperty(property)
    );

    const showForm = type => {
        switch (type) {
            case "Place":
            case "Organization":
                return (
                    <CustomEditCoordinatesModal
                        title={title}
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={personId}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propRange={range}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "string":
            case "float":
                return (
                    <CustomInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={personId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "DateEvent":
                return (
                    <CustomDateInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={personId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            default:
                return (
                    <CustomDropdown
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={personId}
                        editData={editData}
                        setEditData={setEditData}
                        defaultValue={values}
                        ontologyType={ontologyType}
                        propRange={range}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
        }
    };
    //
    return (
        <Segment style={segmentStyle()}>
            <Header as="h5">{title}</Header>
            <Container>{showForm(range)}</Container>
            <Divider hidden style={dividerStyle} />
            <Label color="orange">
                {/* {graph} */}
                {cvtDatasetLocale(graph, globalDataset)}
            </Label>
        </Segment>
    );
};

export default CustomSegmentEdit;
