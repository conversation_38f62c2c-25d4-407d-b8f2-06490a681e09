// // @ts-check
// const { test, expect, chromium } = require("@playwright/test");
//
// const { test: base, expect, suite } = require("@playwright/test");
//
// export const test = base.extend({
//     forEachTest: [
//         async ({ page }, use) => {
//             // This code runs before every test.
//             await page.goto("http://localhost:3000");
//             await use();
//             // This code runs after every test.
//             console.log("Last URL:", page.url());
//         },
//         { auto: true }
//     ] // automatically starts for every test.
// });
