import React, { useContext } from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import config from "../../config";

import CusFooter from "../../CusComps/CusFooter";
import CusSecondaryHeader from "../../CusComps/CusSecondaryHeader";

import { safeGet } from "../../../../common/codes";

import { StoreContext } from "../../../../store/StoreProvider";

import Md2React from "../../../../common/components/Markdown2React";

const coverHeaderBgStyle = {
    hkvayb_header_background: {
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/description_3x.png')"
    }
};

const Description = ({ style = {} }) => {
    const classes = useStyles(style);
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const yearBookAbout = safeGet(config.yearBookAbout, [locale], "");

    return (
        <div>
            <CusSecondaryHeader
                style={coverHeaderBgStyle}
                label={
                    <FormattedMessage
                        id="hkvayb.search.header.description"
                        defaultMessage="Description"
                    />
                }
            />
            <div className={classes.hkvayb_description}>
                <Md2React.Normal content={yearBookAbout} />
            </div>
            <CusFooter />
        </div>
    );
};

export default Description;
