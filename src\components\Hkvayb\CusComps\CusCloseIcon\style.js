import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_close_icon: props => ({
        position: "absolute",
        top: "10px",
        right: "10px",
        cursor: "pointer",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            top: "5px",
            right: "5px"
        },
        ...props.hkvayb_close_icon
    })
});

export default useStyles;
