import React from "react";

import useStyles from "./style";

import Fade from "@mui/material/Fade";
import Box from "@mui/material/Box";
import Modal from "@mui/material/Modal";
import Backdrop from "@mui/material/Backdrop";
import Typography from "@mui/material/Typography";

import CusCloseIcon from "../CusCloseIcon";
import CusModalTable from "../CusModalTable";

const systemStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 1000,
    bgcolor: "background.paper",
    p: 4,
    "@media screen and (max-width: 600px)": {
        maxWidth: "100%"
    }
};

const CusModal = ({
    image,
    children,
    label,
    title = "",
    content = [],
    style = {}
}) => {
    const classes = useStyles(style);
    const [open, setOpen] = React.useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    return (
        <div>
            <div onClick={handleOpen}>{label || children}</div>
            <Modal
                aria-labelledby="transition-modal-title"
                aria-describedby="transition-modal-description"
                open={open}
                onClose={handleClose}
                closeAfterTransition
                BackdropComponent={Backdrop}
                BackdropProps={{
                    timeout: 500
                }}
            >
                <Fade in={open}>
                    <Box sx={systemStyle}>
                        <CusCloseIcon onClick={handleClose} />
                        <div
                            className={
                                title && content
                                    ? classes.hkvayb_div
                                    : classes.hkvayb_div_img_only
                            }
                        >
                            {image}
                            <div className={classes.hkvayb_desc}>
                                {title && (
                                    <Typography
                                        id="transition-modal-title"
                                        variant="h6"
                                        component="h2"
                                    >
                                        <div
                                            className={
                                                classes.hkvayb_info_title
                                            }
                                        >
                                            {`《${title}》`}
                                        </div>
                                    </Typography>
                                )}
                                {content && <CusModalTable content={content} />}
                            </div>
                        </div>
                    </Box>
                </Fade>
            </Modal>
        </div>
    );
};

export default CusModal;
