import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_about_grid: props => ({
        // minWidth: "1280px",
        // margin: "0 80px 0 80px",
        height: "inherit",
        ...props.hkvayb_about_grid
    }),
    hkvayb_card_group_div: props => ({
        display: "flex",
        justifyContent: "space-between",
        flexWrap: "wrap",
        ...props.hkvayb_card_group_div
    }),
    hkvayb_about_yearbooks_sort: props => ({
        margin: "40px 0",
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "flex-end",
        ...props.hkvayb_about_yearbooks_sort
    }),
    hkvayb_about_declare: props => ({
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "80px 0 80px 0",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#333",
        "@media screen and (max-width: 600px)": {
            padding: "80px 40px"
        },
        ...props.hkvayb_about_declare
    }),
    hkvayb_description: props => ({
        padding: "80px 136px",
        textAlign: "justify",
        "@media screen and (max-width: 600px)": {
            padding: "80px 40px"
        },
        ...props.hkvayb_description
    })
});

export default useStyles;
