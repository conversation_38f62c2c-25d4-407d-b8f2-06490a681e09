import React, { useContext, useState } from "react";

// custom
import CustomList from "./CustomList";
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";

// ui
import { Header, Label, Divider, Checkbox, Table } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import {
    cvtDatasetLocale,
    getProperty,
    isEmpty
} from "../../../../common/codes";
import { displayMemberName, SPECIAL_HEADER } from "../../Organization/action";

const CustomSegmentDelete = ({
    rowIdx,
    rowData,
    editData,
    setEditData,
    ontologyType
}) => {
    const { property, graph, values } = rowData;
    const [state] = useContext(StoreContext);
    const { property: propData, source, personInformation } = state;
    const { dataset: globalDataset } = source;
    const { memberInvert } = personInformation;
    //
    const memberNameGroup = memberInvert ? displayMemberName(memberInvert) : [];
    //
    const [createState, setCreateState] = useState(() => {
        // value -> object
        return {
            isDisabled: true,
            isLoading: false,
            options: values || [],
            value: values || []
        };
    });
    //
    const customStyles = {
        container: styles => ({
            ...styles
            // margin: "-9px",
            // maxWidth: "500px"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            // borderRadius: "unset",
            backgroundColor: controlColor
        })
    };
    //
    const dividerStyle = {
        margin: ".5rem 0"
    };
    const tableWithCheckboxStyle = {
        borderRight: "1px solid rgba(34,36,38,.1)"
    };

    const handleChange = (e, { checked }) => {
        if (checked) {
            setEditData(prevEditData => ({
                ...prevEditData,
                deletedData: [...prevEditData.deletedData, rowIdx]
            }));
        } else {
            setEditData(prevEditData => ({
                ...prevEditData,
                deletedData: [
                    ...prevEditData.deletedData.filter(
                        _rowIdx => _rowIdx !== rowIdx
                    )
                ]
            }));
        }
    };

    const tableRowStyle = () => {
        if (editData.deletedRowIds.includes(rowIdx)) {
            return {
                // backgroundColor: "rgb(0 0 0 / 3%)"
                backgroundColor: "#fff6f6"
            };
        } else {
            return {};
        }
    };

    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(propData?.propertyObj)
            ? _property
            : getProperty(_property, propData.propertyObj);
    };

    const showHeader = _property => {
        if (
            ontologyType === "member" &&
            memberNameGroup.includes(`${_property}__Person`)
        ) {
            return safeGetProperty(SPECIAL_HEADER.split("__")[0]);
        }

        return safeGetProperty(_property);
    };
    //

    return (
        <Table>
            <Table.Body>
                <Table.Row style={tableRowStyle()}>
                    <Table.Cell collapsing style={tableWithCheckboxStyle}>
                        <Checkbox
                            disabled={editData.isDeleted}
                            onChange={handleChange}
                        />
                    </Table.Cell>
                    <Table.Cell>
                        <Header as="h5">{showHeader(property)}</Header>
                        <CreatableSelect
                            isMulti
                            isClearable
                            styles={customStyles}
                            isDisabled={createState.isDisabled}
                            isLoading={createState.isLoading}
                            options={createState.options}
                            value={createState.value}
                            // onCreateOption={handleCreate}
                            components={{ CustomList }}
                            filterOption={createFilter({
                                ignoreAccents: false
                            })}
                        />
                        <Divider hidden style={dividerStyle} />
                        <Label color="orange">
                            {/* {graph} */}
                            {cvtDatasetLocale(graph, globalDataset)}
                        </Label>
                    </Table.Cell>
                </Table.Row>
            </Table.Body>
        </Table>
    );
};

export default CustomSegmentDelete;
