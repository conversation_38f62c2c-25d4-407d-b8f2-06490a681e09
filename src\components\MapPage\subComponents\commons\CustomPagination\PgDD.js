import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Dropdown } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../../../common/codes";
import { FormattedMessage } from "react-intl";

function PgDD({ handleDDPage, totalPages, currentPage }) {
    const [optionArr, setOptionArr] = useState([]);

    useEffect(() => {
        const tmpOption = [];
        for (let i = 0; i < totalPages; i += 1) {
            tmpOption.push({
                key: i + 1,
                value: i + 1,
                text: i + 1
            });
        }
        setOptionArr(tmpOption);
    }, [totalPages]);

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <FormattedMessage
                    id="map.current.page"
                    defaultMessage="目前位於"
                />
            </div>
            {!isEmpty(optionArr) && (
                <Dropdown
                    compact
                    selection
                    options={optionArr}
                    onChange={handleDDPage}
                    value={optionArr[currentPage - 1]?.value}
                />
            )}
            <div style={{ marginLeft: "0.5rem" }}>
                <FormattedMessage
                    id="map.total.page"
                    defaultMessage="頁，共 {totalPages} 頁"
                    values={{
                        totalPages: totalPages
                    }}
                />
            </div>
        </React.Fragment>
    );
}

PgDD.propTypes = {
    /** page change callback */
    handleDDPage: PropTypes.func,
    /** 目前頁碼 */
    currentPage: PropTypes.number,
    /** 總頁數 */
    totalPages: PropTypes.number
};

PgDD.defaultProps = {
    /** page change callback */
    handleDDPage: () => {},
    /** 目前頁碼 */
    currentPage: 1,
    /** 總頁數 */
    totalPages: 1
};

export default PgDD;
