import React, { useState } from "react";
import { But<PERSON>, I<PERSON>, Label } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";

// 針對 localNameId, 要編輯時顯示 localNameId list 供使用者參考
const propertyNameForShow = [
    { propertyName: "localNameId", propertyNameType: "localNameId__string" }
];

const getLocalNameIds = (perInfo, propertyNameType) => {
    let localNameIds = [];
    // perInfo 的資料結構若變動時,這邊的邏輯要配合修改
    // if (perInfo?.nameNode) {
    //     const propertySplit = (propertyNameType || "").split("__");
    //     const searchProperty = propertySplit?.[0] || "";
    //     const searchRange = propertySplit?.[1] || "";
    //
    //     localNameIds = perInfo.nameNode.reduce((acc, cur) => {
    //         // cur example:
    //         // { basicData: "000033001017"
    //         // basicProperty: "localNameId"
    //         // basicRange: "string"
    //         // graph: "auda_hklit" }
    //
    //         if (
    //             cur &&
    //             cur?.basicProperty === searchProperty &&
    //             cur?.basicRange === searchRange
    //         ) {
    //             acc.push(cur.basicData);
    //         }
    //         return acc;
    //     }, []);
    // }
    if (perInfo?.namenode) {
        localNameIds = perInfo.namenode.reduce((acc, cur) => {
            if (
                propertyNameType in cur &&
                Array.isArray(cur[propertyNameType])
            ) {
                acc.push(cur[propertyNameType].map(obj => obj.value));
            }
            return acc;
        }, []);
    }

    return localNameIds.sort();
};

const showInfoList = (perInfo, propertyNameType) => {
    if (
        !(
            perInfo &&
            propertyNameType &&
            propertyNameForShow
                .map(item => item.propertyNameType)
                .includes(propertyNameType)
        )
    ) {
        return null;
    }
    return getLocalNameIds(perInfo, propertyNameType);
};

// 顯示目前 property 的資料清單
const CustomNameIdList = ({ perInfo, propertyName }) => {
    const infoList = showInfoList(perInfo, propertyName);
    const [open, setOpen] = useState(false);

    return (
        Array.isArray(infoList) &&
        infoList.length > 0 && (
            <div style={{ marginTop: "10px" }}>
                <Button
                    basic
                    size="mini"
                    onClick={() => {
                        setOpen(!open);
                    }}
                >
                    {open ? (
                        <FormattedMessage
                            id={"people.Information.formTable.currentData.Hide"}
                            defaultMessage={"Hide current data"}
                        />
                    ) : (
                        <FormattedMessage
                            id={
                                "people.Information.formTable.currentData.Display"
                            }
                            defaultMessage={"Display current data"}
                        />
                    )}
                    <span style={{ marginLeft: "10px" }}>
                        <Icon
                            name={open ? "angle up" : "angle down"}
                            size="small"
                        />
                    </span>
                </Button>
                <div style={{ marginTop: "8px" }}>
                    {open &&
                        infoList.map((item, idx) => (
                            <Label
                                key={idx.toString()}
                                style={{
                                    marginRight: "5px",
                                    marginBottom: "5px"
                                }}
                            >
                                {item}
                            </Label>
                        ))}
                </div>
            </div>
        )
    );
};

export default CustomNameIdList;
