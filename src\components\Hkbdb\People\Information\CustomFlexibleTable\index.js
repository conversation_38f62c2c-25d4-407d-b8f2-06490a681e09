import React, { useCallback, useContext, useEffect } from "react";

// ui
import { Segment } from "semantic-ui-react";

// common
import { isEmpty, safeGet } from "../../../../../common/codes";

// custom
import CustomTable from "./CustomTableFlex";
import CustomMessage from "../../../EntityComponent/TableComp/CustomMessage";
import CustomLoading from "../../../EntityComponent/TableComp/CustomLoading";
import CustomCreateModal from "../../../EntityComponent/TableComp/CustomCreateModalFlex";
import CustomSearchInput from "../../../EntityComponent/TableComp/CustomSearchInput";
import SugCreateModal from "../../../EntityComponent/TableComp/SugCreateModal";

import { StoreContext } from "../../../../../store/StoreProvider";
import allRoles from "../../../../../App-role";
import { queryPersonPost } from "../../action";
import Act from "../../../../../store/actions";
import config from "../../../../../config/config";

const index = ({ info, ontologyDomain, ontologyType, name = "" }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { user, personInformation } = state;
    const { suggestInfo, tempUpdateSuggestInfo } = personInformation;
    const { searchInputs } = state.information;
    //
    const keyword = safeGet(searchInputs, [ontologyType], "");
    const dataType = keyword ? "infoSearchData" : "infoData";
    const pageType = keyword ? "infoSearchPage" : "infoPage";
    const loadType = keyword ? "infoSearchLoad" : "infoLoad";
    //
    const data = safeGet(info, [dataType, ontologyType], []);
    const page = safeGet(info, [pageType, ontologyType], []);
    const load = safeGet(info, [loadType, ontologyType], false);
    //
    useEffect(() => {
        if (!name) return;

        // 已經抓過資料就不重抓一次
        // if (!isEmpty(perInfo?.[dataType]?.[ontologyType]) && isEmpty(keyword))
        //     return;

        // 特定資料量多的表單使用
        (async () => {
            const singleFetchTypes = [config.DEF_ART_TYPE];
            if (singleFetchTypes.includes(ontologyType)) {
                if (isEmpty(keyword)) {
                    // no searching
                    dispatch({
                        type: Act.USER_FETCH_DATA_LOAD,
                        payload: {
                            [ontologyType]: true
                        }
                    });

                    const {
                        resData,
                        fetchIdsInfo,
                        pageTotal
                    } = await queryPersonPost(name, ontologyType);

                    dispatch({
                        type: Act.USER_FETCH_DATA_INFO,
                        payload: {
                            [ontologyType]: resData
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_IDS_INFO,
                        payload: {
                            [ontologyType]: fetchIdsInfo
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_DATA_PAGE_TOTAL_INFO,
                        payload: {
                            [ontologyType]: pageTotal
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_DATA_LOAD,
                        payload: {
                            [ontologyType]: false
                        }
                    });
                } else {
                    // searching
                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_LOAD,
                        payload: {
                            [ontologyType]: true
                        }
                    });
                    const {
                        resData,
                        fetchIdsInfo,
                        pageTotal
                    } = await queryPersonPost(name, ontologyType, keyword);

                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_INFO,
                        payload: {
                            [ontologyType]: resData
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_SEARCH_IDS_INFO,
                        payload: {
                            [ontologyType]: fetchIdsInfo
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_PAGE_TOTAL_INFO,
                        payload: {
                            [ontologyType]: pageTotal
                        }
                    });

                    dispatch({
                        type: Act.USER_FETCH_SEARCH_DATA_LOAD,
                        payload: {
                            [ontologyType]: false
                        }
                    });
                }
            }
        })();
    }, [keyword]);
    //
    const MemoLoading = useCallback(() => <CustomLoading />, []);
    //
    const MemoNoDataMsg = useCallback(
        () => <CustomMessage header="目前結果為無資料。" />,
        []
    );
    //
    const MemoTable = useCallback(
        () => (
            <CustomTable
                data={data}
                page={page}
                ontologyDomain={ontologyDomain}
                ontologyType={ontologyType}
            />
        ),
        [JSON.stringify(data), suggestInfo, JSON.stringify(page)]
    );
    //
    const MemoModal = useCallback(
        () =>
            !load &&
            (user.role === allRoles.suggester ? (
                <SugCreateModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            ) : (
                <CustomCreateModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            )),
        [load]
    );

    const ShowInfo = ({ component: Component, ...rest }) => {
        return (
            <Segment>
                <Segment size={"big"} basic>
                    <CustomSearchInput ontologyType={ontologyType} />
                    {/* show content */}
                    <Component {...rest} />
                    {/* create modal */}
                    <MemoModal />
                </Segment>
            </Segment>
        );
    };
    //
    if (load) {
        return <ShowInfo component={MemoLoading} />;
    }
    //
    else if (!isEmpty(suggestInfo) || !isEmpty(tempUpdateSuggestInfo)) {
        return <ShowInfo component={MemoTable} />;
    }
    //
    else if (isEmpty(data)) {
        return <ShowInfo component={MemoNoDataMsg} />;
    }
    //
    else {
        return <ShowInfo component={MemoTable} />;
    }
};

export default index;
