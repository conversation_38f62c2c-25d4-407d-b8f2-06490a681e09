import React, { useState, useEffect } from "react";

import debounce from "./debounce";

const useDebounce = (value, delay, state = null, setState = null) => {
    if (state !== null && setState !== null) {
        const debouncedValue = debounce(state, delay);
        return [debouncedValue, state, setState];
    }
    const [tmpValue, setTmpValue] = useState(value);
    useEffect(() => {
        setTmpValue(value);
    }, [value]);

    const debouncedValue = debounce(tmpValue, delay);
    return [debouncedValue, tmpValue, setTmpValue];
};

export default useDebounce;
