import React, { useContext, useEffect, useState, useRef } from "react";

// ui
import { Icon, Pagination, Input } from "semantic-ui-react";

// custom
import CustomDebounce from "./CustomDeBounce";

// action
import Act from "../../../../store/actions";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common code
import { isEmpty, isNumeric, safeGet } from "../../../../common/codes";

// custom style for div
const styleDiv = { textAlign: "center" };

const CustomPagination = ({ totalPages, ontologyType }) => {
    //

    const [state, dispatch] = useContext(StoreContext);
    const { personInformation, information } = state;
    const { paginations, searchInputs } = information;
    const { globalActivePage } = paginations;
    const searchKeyword = safeGet(searchInputs, [ontologyType], "");
    const { localActivePage, searchActivePage } = safeGet(
        paginations,
        [ontologyType],
        {}
    );

    const currentTotalPages = totalPages <= 1 ? 1 : totalPages;
    const currentActivePage = isEmpty(searchKeyword)
        ? isNumeric(localActivePage)
            ? localActivePage
            : globalActivePage
        : isNumeric(searchActivePage)
        ? searchActivePage
        : globalActivePage;
    const infoReloadFunc = safeGet(
        personInformation,
        ["infoReloadFunc", ontologyType],
        () => {}
    );

    //
    const [inputValue, setInputValue] = useState(currentActivePage);
    const debInputValue = CustomDebounce(inputValue, 800);
    const prevPageNumRef = useRef(currentActivePage);
    //
    const handlePageUpdate = value => {
        // 事件說明(UI來看不會有變化，但API打了兩次):
        // 1. 分頁點擊改變 value
        // 2. 分頁被改變時同步更新分頁 input's value
        // 所以在喧染時候會有兩個事件一起被觸發循序執行兩次該方法，
        // 但是兩個方法輸出的 value 其實是一樣的，
        // 所以透過 useRef 紀錄上一次的 prevValue，
        // 透過比較 "上一次的 value" 和 "目前的 value" 判斷是否執行
        // 不使用 useState 是因為在重新喧染畫面時值會被初始化
        // 使用 useRef.current 並不會被初始化。

        if (prevPageNumRef.current === value) {
            return;
        }
        if (isNumeric(value)) {
            //
            prevPageNumRef.current = value;
            //
            if (isEmpty(searchKeyword)) {
                dispatch({
                    type: Act.INFORMATION_PAGINATIONS,
                    payload: {
                        [ontologyType]: {
                            localActivePage: value,
                            searchActivePage: 1 // 在搜尋狀態下，分頁內容會不一致，因此另外補上 1(預設顯示第一頁)
                        }
                    }
                });
                // call the preStore function from queryPersonInfoV2 of action.js
                // infoReloadFunc(value);
            } else {
                dispatch({
                    type: Act.INFORMATION_PAGINATIONS,
                    payload: {
                        [ontologyType]: {
                            localActivePage: localActivePage,
                            searchActivePage: value
                        }
                    }
                });
            }
        }
    };
    //
    const handleOnInputChange = (event, { value: page }) => {
        setInputValue(page);
    };
    const handleOnPageChange = (event, { activePage: page }) => {
        handlePageUpdate(page);
    };

    useEffect(() => {
        let safePage;
        if (debInputValue > currentTotalPages) {
            safePage = currentTotalPages;
        } else {
            if (debInputValue <= 0) {
                safePage = 1;
            } else {
                safePage = debInputValue;
            }
        }
        handlePageUpdate(safePage);
    }, [debInputValue]);
    //
    useEffect(() => {
        setInputValue(currentActivePage);
    }, [currentActivePage]);

    //
    return (
        <div style={styleDiv}>
            <Pagination
                disabled={currentTotalPages === 1}
                size="mini"
                activePage={currentActivePage}
                ellipsisItem={{
                    content: <Icon name="ellipsis horizontal" />,
                    icon: true
                }}
                firstItem={{
                    content: <Icon name="angle double left" />,
                    icon: true
                }}
                lastItem={{
                    content: (
                        <React.Fragment>
                            <Icon
                                name="angle double right"
                                style={{ marginRight: "15px" }}
                            />
                            <div
                                onClick={event => event.stopPropagation()}
                                onFocus={event => event.stopPropagation()}
                            >
                                <Input
                                    disabled={currentTotalPages === 1}
                                    value={inputValue}
                                    size="mini"
                                    style={{
                                        maxWidth: "55px",
                                        maxHeight: "20px"
                                    }}
                                    onChange={handleOnInputChange}
                                />
                            </div>
                        </React.Fragment>
                    ),
                    icon: true
                }}
                prevItem={{ content: <Icon name="angle left" />, icon: true }}
                nextItem={{ content: <Icon name="angle right" />, icon: true }}
                totalPages={currentTotalPages}
                onPageChange={handleOnPageChange}
            />
        </div>
    );
};

export default CustomPagination;
