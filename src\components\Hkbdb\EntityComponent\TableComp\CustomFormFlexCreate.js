import React from "react";

import { Message, Table } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomDropdown from "./CustomDropdownFlexCreate";
import CustomGraphDropdown from "./CustomGraphDropdown";
// import CustomInput from "../../People/Information/CustomComp/CustomInputFlexNameId";
import CustomInput from "./CustomInputFlexForm";
import CustomDateInput from "./CustomDateInput";

// common
import {
    getProperty,
    isEmpty,
    isNotEmpty,
    safeGet
} from "../../../../common/codes";
import CustomCreateCoordinatesModal from "./CustomCreateCoordinatesModal";

const CustomForm = ({ createData, setCreateData }) => {
    //
    const {
        selectedProperties,
        ontologyOneOfThemData,
        isCreated,
        propertyObj
    } = createData;
    //
    const isNotSelectedOneOfThem =
        selectedProperties.findIndex(item =>
            ontologyOneOfThemData
                .map(value => value.toLowerCase())
                .includes(safeGet(item, ["property"], "").toLowerCase())
        ) === -1;
    //
    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(propertyObj)
            ? _property
            : getProperty(_property, propertyObj);
    };
    //
    return (
        <Table celled>
            <Table.Body>
                <Table.Row disabled={isCreated}>
                    <Table.Cell>
                        {/* <pre>{JSON.stringify(createData.selectedProperties, null, 2)}</pre> */}
                        {/* select graph */}
                        <CustomGraphDropdown
                            createData={createData}
                            setCreateData={setCreateData}
                        />
                    </Table.Cell>
                </Table.Row>
                {/* show selected value by dropdown */}
                {isEmpty(selectedProperties) ? (
                    <Table.Row disabled={isCreated}>
                        <Table.Cell>
                            <Message
                                warning
                                header={
                                    <FormattedMessage
                                        id={"people.Information.formTable.item"}
                                        defaultMessage={
                                            "There are no property in Form Table"
                                        }
                                    />
                                }
                            />
                        </Table.Cell>
                    </Table.Row>
                ) : (
                    selectedProperties.map((property, idx) => {
                        //
                        const { range } = property;
                        // console.log(property);
                        //
                        switch (range) {
                            case "Place":
                            case "Organization":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={createData.isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomCreateCoordinatesModal
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            case "string":
                            case "float":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            case "DateEvent":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomDateInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            default:
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomDropdown
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                        }
                    })
                )}
                {isNotEmpty(ontologyOneOfThemData) && isNotSelectedOneOfThem && (
                    <Table.Row disabled={isCreated}>
                        <Table.Cell>
                            {/* {JSON.stringify(selectedProperties, null, 2)} */}
                            <Message warning>
                                <Message.Header>
                                    <FormattedMessage
                                        id={
                                            "people.Information.formTable.at.least.one.item"
                                        }
                                        defaultMessage={
                                            "You must choose at least one of them:"
                                        }
                                    />
                                </Message.Header>
                                <Message.List
                                    items={ontologyOneOfThemData.map(prop =>
                                        safeGetProperty(prop)
                                    )}
                                />
                            </Message>
                        </Table.Cell>
                    </Table.Row>
                )}
            </Table.Body>
        </Table>
    );
};

export default CustomForm;
