import React, { useContext } from "react";

import { IconButton } from "@material-ui/core";

import SearchIcon from "@material-ui/icons/Search";

import { makeStyles } from "@material-ui/core/styles";

import { StoreContext } from "../../../../../../store/StoreProvider";

import act from "../../../../../../store/actions";

import { method, url } from "../../../../../../api/hkvayb";

import { safeGet, isNumeric } from "../../../../../../common/codes";

const useStyles = makeStyles(theme => ({
    iconButton: {
        padding: theme.spacing(1)
    }
}));

const CusInput = () => {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { types } = state.searchPage2;

    const handleOnClick = async () => {
        try {
            dispatch({
                type: act.SET_SEARCHPAGE_SEARCHBAR_ISLOADING,
                payload: true
            });

            const promises = types.map(type => {
                const api = url.hkvayb.SEARCHPAGE_SIMPLE_INFORMATION.replace(
                    "{type}",
                    type && type[0].toLowerCase() + type.slice(1)
                ).replace("{keyword}", "");
                return method.hkvaybQuery(api);
            });

            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);

            const { resData, resCount, resDuration } = results.reduce(
                (prevObj, res, resIdx) => {
                    if (res.status === "fulfilled") {
                        const _data = safeGet(
                            res,
                            ["value", "data", "data"],
                            []
                        );
                        const _total = safeGet(
                            res,
                            ["value", "data", "total"],
                            0
                        );
                        const _duration = safeGet(
                            res,
                            ["value", "durationSS"],
                            0
                        );
                        return {
                            ...prevObj,
                            resData: {
                                ...prevObj.resData,
                                [types[resIdx]]: _data
                            },
                            resCount:
                                prevObj.resCount +
                                (isNumeric(_total) ? parseInt(_total) : 0),
                            resDuration:
                                prevObj.resDuration +
                                (isNumeric(_duration) ? _duration : 0)
                        };
                    }
                    return prevObj;
                },
                {
                    resData: {},
                    resCount: 0,
                    resDuration: 0
                }
            );

            dispatch({
                type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT,
                payload: resData
            });

            dispatch({
                type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT_COUNT,
                payload: resCount
            });

            dispatch({
                type: act.SET_SEARCHPAGE_SEARCHBAR_RESULT_DURATION,
                payload: resDuration
            });
        } catch (exception) {
            // 印出錯誤內容
            console.error(
                `${new Date()} ${exception.name}: ${exception.message}`
            );
            dispatch({
                type: act.SET_SEARCHPAGE_SEARCHBAR_ERROR,
                payload: {
                    type: "error",
                    message: `${exception.name}: ${exception.message}`
                }
            });
        } finally {
            // 太快載入資料反而會沒有載入資料的真實感，所以加了一秒改善體驗。
            // 或者也可以彈出 alert 通知用戶已經成功載入資料了
            setTimeout(() => {
                dispatch({
                    type: act.SET_SEARCHPAGE_SEARCHBAR_ISLOADING,
                    payload: false
                });
            }, 1000);
        }
    };

    return (
        <IconButton
            type="submit"
            className={classes.iconButton}
            aria-label="search"
            onClick={handleOnClick}
        >
            <SearchIcon />
        </IconButton>
    );
};

export default CusInput;
