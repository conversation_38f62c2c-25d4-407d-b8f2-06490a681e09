const Act = {
    // MockUp User Data
    DEMO_USER: "DEMO_USER",
    DEMO_USER_CLEAN: "DEMO_USER_CLEAN",
    SET_USER_LOCALE: "SET_USER_LOCALE",
    // user
    FIREBASE_LOGIN_USER: "FIREBASE_LOGIN_USER",
    FIREBASE_USER_TOKEN: "FIREBASE_USER_TOKEN",
    FIREBASE_LOGOUT_USER: "FIREBASE_LOGOUT_USER",
    USER_USER_SETTINGS_SET: "USER_USER_SETTINGS_SET",
    // property
    PROPERTY: "PROPERTY",
    PROPERTY_CLEAN: "PROPERTY_CLEAN",
    PROTEGE_GET_DATA: "PROTEGE_GET_DATA",
    PROTEGE_DEFINED_DATA_SET: "PROTEGE_DEFINED_DATA_SET",
    ONTO_PROPERTY_REF: "ONTO_PROPERTY_REF",
    ONTO_CUR_PROPERTIES: "ONTO_CUR_PROPERTIES",
    ONTO_ACTIVE_PROP_REF: "ONTO_ACTIVE_PROP_REF",
    DRAW_ONTO_DATA: "DRAW_ONTO_DATA",
    ONTO_CLASS_NAMES: "ONTO_CLASS_NAMES",
    // main
    SET_WEB_STYLE: "SET_WEB_STYLE",
    SET_DEFAULT_WEB_STYLE: "SET_DEFAULT_WEB_STYLE",
    SET_IMAGE: "SET_IMAGE",
    SET_IMAGE_TOKEN: "SET_IMAGE_TOKEN",
    SET_IMAGE_DESK_BG: "SET_IMAGE_DESK_BG",
    SET_IMAGE_DESK_BANNER: "SET_IMAGE_DESK_BANNER",
    SET_IMAGE_DESK_LOGO: "SET_IMAGE_DESK_LOGO",
    SET_IMAGE_DESK_MANUSCRIPT: "SET_IMAGE_DESK_MANUSCRIPT",
    SET_IMAGE_DESK_BOOKCOVER: "SET_IMAGE_DESK_BOOKCOVER",
    SET_IMAGE_MOBILE_BG: "SET_IMAGE_MOBILE_BG",
    SET_IMAGE_MOBILE_BANNER: "SET_IMAGE_MOBILE_BANNER",
    SET_IMAGE_MOBILE_LOGO: "SET_IMAGE_MOBILE_LOGO",
    SET_IMAGE_MOBILE_MANUSCRIPT: "SET_IMAGE_MOBILE_MANUSCRIPT",
    SET_IMAGE_MOBILE_BOOKCOVER: "SET_IMAGE_MOBILE_BOOKCOVER",
    SET_SWG_JSON: "SET_SWG_JSON",
    SET_SWG_JSON_FILE: "SET_SWG_JSON_FILE",
    SET_PRODUCTION: "SET_PRODUCTION",
    REFRESH_ANONYMOUS_TOKEN: "REFRESH_ANONYMOUS_TOKEN",
    SET_DATABASE: "SET_DATABASE",
    // browse:
    SET_BROWSE_DATASET: "SET_BROWSE_DATASET",
    SET_CUR_GRAPH: "SET_CUR_GRAPH",
    SET_FILTER_GRAPH_ON: "SET_FILTER_GRAPH_ON",
    // organization
    ORG_FETCH_INFO: "ORG_FETCH_INFO",
    ORG_FETCH_TIMELINE: "ORG_FETCH_TIMELINE",
    ORG_FETCH_SNA: "ORG_FETCH_SNA",
    // person
    USER_CLEAR_CACHE: "USER_CLEAR_CACHE",
    USER_FETCH_INFO: "USER_FETCH_INFO",
    USER_FETCH_INFO_HEADER: "USER_FETCH_INFO_HEADER",
    USER_FETCH_IDS_INFO: "USER_FETCH_IDS_INFO",
    USER_FETCH_SEARCH_IDS_INFO: "USER_FETCH_SEARCH_IDS_INFO",
    USER_FETCH_DATA_INFO: "USER_FETCH_DATA_INFO",
    USER_FETCH_SEARCH_DATA_INFO: "USER_FETCH_SEARCH_DATA_INFO",
    USER_FETCH_DATA_LOAD: "USER_FETCH_DATA_LOAD",
    USER_FETCH_SEARCH_DATA_LOAD: "USER_FETCH_SEARCH_DATA_LOAD",
    USER_FETCH_DATA_RELOAD_FUNC: "USER_FETCH_DATA_RELOAD_FUNC",
    USER_FETCH_DATA_SEARCH_FUNC: "USER_FETCH_DATA_SEARCH_FUNC",
    USER_FETCH_DATA_PAGE_TOTAL_INFO: "USER_FETCH_DATA_PAGE_TOTAL_INFO",
    USER_FETCH_SEARCH_DATA_PAGE_TOTAL_INFO:
        "USER_FETCH_SEARCH_DATA_PAGE_TOTAL_INFO",
    USER_FETCH_INFO_LOADING: "USER_FETCH_INFO_LOADING",
    USER_FETCH_NAME_NODE: "USER_FETCH_NAME_NODE",
    USER_FETCH_NAME_NODE_LOADING: "USER_FETCH_NAME_NODE_LOADING",
    USER_FETCH_NAME_NODE_IDS: "USER_FETCH_NAME_NODE_IDS",
    USER_FETCH_NAME_NODE_IDS_LOADING: "USER_FETCH_NAME_NODE_IDS_LOADING",
    USER_FETCH_TIMELINE_DATA: "USER_FETCH_TIMELINE_DATA",
    USER_FETCH_SNA_DATA: "USER_FETCH_SNA_DATA",
    USER_FETCH_GENEALOGY: "USER_FETCH_GENEALOGY",
    FETCH_SNA_DATA_STATUS: "FETCH_SNA_DATA_STATUS",
    SET_SETP1_SNA_DATA: "SET_SETP1_SNA_DATA",
    SET_SETP2_SNA_DATA: "SET_SETP2_SNA_DATA",
    // modal
    //
    MODAL_LOCK_SET: "MODAL_LOCK_SET",
    MODAL_LOCK_CLEAR: "MODAL_LOCK_CLEAR",
    // Query
    // {reducerName}_{any}_{action: SET|GET|DEL|CEL|...}
    QUERY_QUERY_STRING_SET: "QUERY_QUERY_STRING_SET",
    QUERY_QUERY_STRING_CEL: "QUERY_QUERY_STRING_CEL",
    QUERY_SELECTED_QUERY_ID_SET: "QUERY_SELECTED_QUERY_ID_SET",
    QUERY_SELECTED_QUERY_ID_CEL: "QUERY_SELECTED_QUERY_ID_CEL",
    QUERY_SELECTED_AUTHOR_ID_SET: "QUERY_SELECTED_AUTHOR_ID_SET",
    QUERY_SELECTED_AUTHOR_ID_CEL: "QUERY_SELECTED_AUTHOR_ID_CEL",
    QUERY_QUERIES_SET: "QUERY_QUERIES_SET",
    QUERY_PRIVATE_QUERIES_SET: "QUERY_PRIVATE_QUERIES_SET",
    QUERY_PUBLIC_QUERIES_SET: "QUERY_PUBLIC_QUERIES_SET",
    QUERY_SEARCH_KEYWORD_SET: "QUERY_SEARCH_KEYWORD",
    QUERY_RELOAD_RENDER_SIGNAL_SET: "QUERY_RELOAD_RENDER_SIGNAL_SET",
    QUERY_RESULT_SET: "QUERY_RESULT_SET",
    QUERY_RESULT_CEL: "QUERY_RESULT_CEL",
    QUERY_PAGINATION_LIMIT_SET: "QUERY_PAGINATION_LIMIT_SET",
    QUERY_PAGINATION_LIMIT_CEL: "QUERY_PAGINATION_LIMIT_CEL",
    QUERY_PAGINATION_ACTIVE_PAGE_SET: "QUERY_PAGINATION_ACTIVE_PAGE",
    // information
    // {reducerName}_{any}_{action: SET|GET|DEL|CEL|...}
    INFORMATION_PERSON_ID_SET: "INFORMATION_PERSON_ID_SET",
    INFORMATION_PERSON_ID_CEL: "INFORMATION_PERSON_ID_CEL",
    INFORMATION_DATA_RENDER_SIGNAL_SET: "INFORMATION_DATA_RENDER_SIGNAL_SET",
    INFORMATION_DATA_RENDER_SIGNAL_CEL: "INFORMATION_DATA_RENDER_SIGNAL_CEL",
    INFORMATION_DATA_IS_LOADING_SET: "INFORMATION_DATA_IS_LOADING_SET",
    INFORMATION_DATA_IS_LOADING_CEL: "INFORMATION_DATA_IS_LOADING_CEL",
    INFORMATION_MENU_RAW_DATA_SET: "INFORMATION_MENU_EDIT_RAW_DATA_SET",
    INFORMATION_MENU_RAW_DATA_CEL: "INFORMATION_MENU_EDIT_RAW_DATA_CEL",
    INFORMATION_MENU_CHANGED_DATA_SET: "INFORMATION_MENU_CHANGED_DATA_SET",
    INFORMATION_MENU_CHANGED_DATA_CEL: "INFORMATION_MENU_CHANGED_DATA_CEL",
    INFORMATION_MENU_DELETED_DATA_SET: "INFORMATION_MENU_DELETED_DATA_SET",
    INFORMATION_MENU_DELETED_DATA_CEL: "INFORMATION_MENU_DELETED_DATA_CEL",
    INFORMATION_MENU_CREATED_DATA_SET: "INFORMATION_MENU_CREATED_DATA_SET",
    INFORMATION_MENU_CREATED_DATA_CEL: "INFORMATION_MENU_CREATED_DATA_CEL",
    INFORMATION_SORTED_RECORD_SET: "INFORMATION_SORTED_RECORD_SET",
    INFORMATION_RELOAD_SORTED_RECORD_SET:
        "INFORMATION_RELOAD_SORTED_RECORD_SET",
    INFORMATION_INVERSE_RELATION_DATA_SET:
        "INFORMATION_INVERSE_RELATION_DATA_SET",
    INFORMATION_ONTOLOGY_ONE_OF_THEM_DATA_SET:
        "INFORMATION_ONTOLOGY_ONE_OF_THEM_DATA_SET",
    INFORMATION_SEARCH_INPUTS: "INFORMATION_SEARCH_INPUTS",
    INFORMATION_PAGINATIONS: "INFORMATION_PAGINATIONS",
    INFORMATION_CURRENTLY_OPEN_TAB: "INFORMATION_CURRENTLY_OPEN_TAB",
    //
    SET_SNA_DEPTH: "SET_SNA_DEPTH",
    // source
    SET_DATASET: "SET_DATASET",
    // account
    FIREBASE_USER_ROLE_CHANGED: "FIREBASE_USER_ROLE_CHANGED",
    FIREBASE_USER_ROLE_CHANGED_CLEAN: "FIREBASE_USER_ROLE_CHANGED_CLEAN",
    FIREBASE_USER_REMOVE: "FIREBASE_USER_REMOVE",
    FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL:
        "FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL",
    DATA_MESSAGE: "DATA_MESSAGE",
    DATA_MESSAGE_CLEAN: "DATA_MESSAGE_CLEAN",
    ACCOUNT_SIGNUP_FORM_USER_AGREE: "ACCOUNT_SIGNUP_FORM_USER_AGREE",
    ACCOUNT_SIGNUP_FORM_CLEAR: "ACCOUNT_SIGNUP_FORM_CLEAR",
    // settings (default)
    SETTINGS_SORTED_HEADER_SET: "SETTINGS_SORTED_HEADER_SET",
    SETTINGS_FIELD_SETTING_SET: "SETTINGS_FIELD_SETTING_SET",
    SETTINGS_EMAIL_CONFIG: "SETTINGS_EMAIL_CONFIG",
    SETTINGS_SIGNUP_CONFIG: "SETTINGS_SIGNUP_CONFIG",
    // message
    MESSAGE_NOTIFICATION_SET: "MESSAGE_NOTIFICATION_SET",
    MESSAGE_NOTIFICATION_CLE: "MESSAGE_NOTIFICATION_CLE",
    // backend web
    SET_BACKEND_WEB: "SET_BACKEND_WEB",
    // searchPage
    SET_LABEL: "SET_LABEL",
    MINUS_COUNT: "MINUS_COUNT",
    SET_COUNT: "SET_COUNT",
    SET_KEYWORD: "SET_KEYWORD",
    SET_ISSEARCH: "SET_ISSEARCH",
    SET_DURATION: "SET_DURATION",
    // searchPage2
    SET_SEARCHPAGE_SEARCHBAR_KEYWORD: "SET_SEARCHPAGE_SEARCHBAR_KEYWORD",
    SET_SEARCHPAGE_SEARCHBAR_TYPES: "SET_SEARCHPAGE_SEARCHBAR_TYPES",
    SET_SEARCHPAGE_SEARCHBAR_FIELDS: "SET_SEARCHPAGE_SEARCHBAR_FIELDS",
    CLE_SEARCHPAGE_SEARCHBAR_FIELDS: "CLE_SEARCHPAGE_SEARCHBAR_FIELDS",
    SET_SEARCHPAGE_SEARCHBAR_RESULT: "SET_SEARCHPAGE_SEARCHBAR_RESULT",
    SET_SEARCHPAGE_SEARCHBAR_RESULT_COUNT:
        "SET_SEARCHPAGE_SEARCHBAR_RESULT_COUNT",
    SET_SEARCHPAGE_SEARCHBAR_RESULT_DURATION:
        "SET_SEARCHPAGE_SEARCHBAR_RESULT_DURATION",
    SET_SEARCHPAGE_SEARCHBAR_ISLOADING: "SET_SEARCHPAGE_SEARCHBAR_ISLOADING",
    SET_SEARCHPAGE_SEARCHBAR_RESET: "SET_SEARCHPAGE_SEARCHBAR_RESET",
    SET_SEARCHPAGE_SEARCHBAR_ERROR: "SET_SEARCHPAGE_SEARCHBAR_ERROR",
    CLE_SEARCHPAGE_SEARCHBAR_ERROR: "CLE_SEARCHPAGE_SEARCHBAR_ERROR",
    SET_SEARCHPAGE_SEARCHBAR_RESULT_PAGE:
        "SET_SEARCHPAGE_SEARCHBAR_RESULT_PAGE",
    SET_SEARCHPAGE_SEARCHBAR_RESULT_ROWS_PAGE:
        "SET_SEARCHPAGE_SEARCHBAR_RESULT_ROWS_PAGE",
    //
    SET_HKVAYB_SEARCHBAR_FIELDS: "SET_HKVAYB_SEARCHBAR_FIELDS",
    SET_HKVAYB_SEARCHBAR_FIELDS_RESET: "SET_HKVAYB_SEARCHBAR_FIELDS_RESET",
    SET_HKVAYB_SEARCHBAR_RESULTS: "SET_HKVAYB_SEARCHBAR_RESULTS",
    SET_HKVAYB_SEARCHBAR_QUERY_STRING: "SET_HKVAYB_SEARCHBAR_QUERY_STRING",
    SET_HKVAYB_SEARCHBAR_LIMIT: "SET_HKVAYB_SEARCHBAR_LIMIT",
    SET_HKVAYB_SEARCHBAR_OFFSET: "SET_HKVAYB_SEARCHBAR_OFFSET",
    SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE: "SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE",
    SET_HKVAYB_SEARCHBAR_SELECTED_ID: "SET_HKVAYB_SEARCHBAR_SELECTED_ID",
    // 換頁動作
    SET_SEARCHDATACOUNT: "SET_SEARCHDATACOUNT",
    SET_PAGENUMBER: "SET_PAGENUMBER",
    SET_CHECKSELECTALL: "SET_CHECKSELECTALL",
    // suggester
    SET_SUGGESTINFO: "SET_SUGGESTINFO",
    SET_IS_LOADING_SUG_DATA: "SET_IS_LOADING_SUG_DATA",

    /** for TimeLine component */
    /** add by bennis */
    TRANSFORM_MAP_DATA: "TRANSFORM_MAP_DATA",
    TRANSFORM_SEARCH_DATA: "TRANSFORM_SEARCH_DATA",
    SET_TEMP_UPDATE_SUGGESTINFO: "SET_TEMP_UPDATE_SUGGESTINFO",
    UPDATE_TEMP_UPDATE_SUGGESTINFO: "UPDATE_TEMP_UPDATE_SUGGESTINFO",
    RESET_TEMP_UPDATE_SUGGESTINFO: "RESET_TEMP_UPDATE_SUGGESTINFO",
    SET_ENTRY_GRAPH_FOR_CREATING_SUGGESTIONS:
        "SET_ENTRY_GRAPH_FOR_CREATING_SUGGESTIONS",

    SET_MAP_FILTER_OPTIONS: "SET_MAP_FILTER_OPTIONS",
    SET_TRACE_MAP_PERSON_SELECTED: "SET_TRACE_MAP_PERSON_SELECTED",
    SET_GENERAL_MAP_FILTER_SELECTED: "SET_GENERAL_MAP_FILTER_SELECTED"
};

export default Act;
