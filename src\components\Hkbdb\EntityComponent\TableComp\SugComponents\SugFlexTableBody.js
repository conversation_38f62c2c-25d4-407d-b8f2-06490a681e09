import React, {
    use<PERSON><PERSON>back,
    useContext,
    useEffect,
    useMemo,
    useState
} from "react";
import { StoreContext } from "../../../../../store/StoreProvider";

// semantic ui
import { Table } from "semantic-ui-react";

// config
import role from "../../../../../App-role";

// component
import CustomContentMenu from "../CustomContextMenuFlex";
import CustomTableLabel from "../CustomTableLabel";

// utils
import { isEmpty } from "../../../../../common/codes";
import transfromSugPrefix from "../../../common/utils/transfromSugPrefix";

// hook
import useGetUsers from "../../../../../hook/useGetUsers";
import {
    Api,
    deleteHkbdbData,
    readHkbdbData
} from "../../../../../api/hkbdb/Api";
import config from "../../../../../config/config";
import SugContextMenuFixed from "./SugContextMenuFixed";
import SugContextMenuFlex from "./SugContextMenuFlex";
import Act from "../../../../../store/actions";

const fixTableHeaderStyle = {
    position: "sticky",
    top: "0",
    zIndex: "998"
};
const fixColumnWidthStyle = {
    whiteSpace: "nowrap",
    maxWidth: "250px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    margin: "0 auto"
};

const customTableCellStyle = {
    minWidth: "150px"
};

/** @description 只用在顯示從hkbdb_draft資料庫抓到的資料 */
function SugFlexTableBody({
    headers,
    ontologyDomain,
    ontologyType,
    startIndex,
    endIndex
}) {
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation, property, user } = state;
    const { uid: userUid } = user;
    const { suggestInfo, tempUpdateSuggestInfo } = personInformation;
    const { ontologyDefined } = property;
    const allUsers = useGetUsers();
    const [sugNames, setSugNames] = useState({});
    const [displaySug, setDisplaySug] = useState([]);
    const [tempUpdateData, setTempUpdateData] = useState(suggestInfo);
    // 將data內只有value不同的整合成一個Obj
    const mergeObjectsWithSameVal = data => {
        if (isEmpty(data)) return;
        const valMap = data.reduce((acc, obj) => {
            const key = `${obj.graph}_${obj.id}_${obj.prop}_${obj.uid}_${obj.sheetName}_${obj.email}`;
            if (!acc[key]) {
                acc[key] = { ...obj, val: [obj.val] };
            } else {
                acc[key].val.push(obj.val);
            }
            return acc;
        }, {});

        return Object.values(valMap).map(obj => {
            obj.val = obj.val.join(",");
            return obj;
        });
    };

    const mergedData = useMemo(() => mergeObjectsWithSameVal(suggestInfo), [
        suggestInfo
    ]);

    const getBookName = async bookId => {
        if (!bookId) return;
        const apiStr = Api.getBookName(bookId);
        try {
            let res = await readHkbdbData(apiStr);
            return res?.data[0]?.o;
        } catch (err) {
            console.log(err);
        }
    };

    // 取下拉選單新增的著作或文章名稱
    const getAddBookLabel = async bookId => {
        if (!bookId) return;
        const apiStr = Api.getSuggesterAddBookLabel(bookId);
        try {
            let res = await readHkbdbData(apiStr);
            return res?.data[0]?.o;
        } catch (err) {
            console.log(err);
        }
    };

    useEffect(() => {
        if (isEmpty(allUsers)) return;
        const uidMap = allUsers
            .filter(el => el.role && el.role === role.suggester)
            .reduce((acc, next) => {
                const { uid, displayName } = next;
                return { ...acc, [uid]: displayName };
            }, {});
        setSugNames(uidMap);
    }, [allUsers]);

    useEffect(() => {
        if (isEmpty(mergedData)) return;
        // 主要針對曾獲獎項內的獲獎作品作特殊處理
        const formatBook = async () => {
            const copySuggestInfo = JSON.parse(JSON.stringify(mergedData));
            const updateSuggestInfo = async data => {
                for (let i = 0; i < data.length; i++) {
                    const item = data[i];
                    if (
                        item.prop === config.hasAwardedForWork ||
                        item.prop === config.hasPublishedIn ||
                        item.prop === config.isEditionOrTranslationOf
                    ) {
                        const valArray = item.val.split(",");

                        const bookNames = await Promise.all(
                            valArray.map(async val => {
                                const trimVal = val.trim();
                                let bookName = await getBookName(trimVal);
                                if (!bookName) {
                                    bookName = await getAddBookLabel(trimVal);
                                }
                                if (val.includes("ART")) {
                                    bookName += "ART/";
                                }
                                return bookName;
                            })
                        );
                        const updatedVal = bookNames.join(",");

                        if (copySuggestInfo[i]) {
                            copySuggestInfo[i].val = updatedVal;
                        }
                    }
                }
            };
            const cloneSuggestInfo = JSON.parse(JSON.stringify(mergedData));

            await Promise.all([updateSuggestInfo(cloneSuggestInfo)]);
            return copySuggestInfo;
        };

        // 將Data整理成便於顯示在jsx的格式
        const processGraphData = (tmpHeaders, inputData) => {
            const result = [];
            const splitHeaders = tmpHeaders.map(header => {
                return header.split("__")[0];
            });
            inputData.forEach(item => {
                const existingItem = result.find(
                    resultItem => resultItem.graph === item.graph
                );
                const matchingHeader = splitHeaders.find(
                    header =>
                        header.includes("label") &&
                        item.prop === "label" &&
                        header !== "label"
                );

                const newItem = {
                    graph: item.graph,
                    uid: item.uid,
                    sheetName: item.sheetName,
                    id: item.id,
                    val: {}
                };
                if (existingItem) {
                    if (splitHeaders.includes(item.prop)) {
                        existingItem.val[item.prop] = item.val;
                    } else if (matchingHeader) {
                        existingItem.val[matchingHeader] = item.val;
                    }
                } else {
                    headers.forEach(header => {
                        const [tmpHd, type] = header.split("__");
                        if (header === "graph") return;
                        newItem.val[tmpHd] =
                            item.prop === (tmpHd || header) ? item.val : "";
                    });
                    result.push(newItem);
                }
            });

            const newResult = result.filter(item =>
                Object.values(item.val).some(value => value !== "")
            );
            setDisplaySug(newResult.slice(startIndex, endIndex));
            return result;
        };

        formatBook().then(res => {
            const filteredRes = res.filter(i => i.uid === userUid);
            processGraphData(headers, res && res);
        });
    }, [headers, suggestInfo, startIndex, endIndex]);

    const getSpecificClassTypeData = (tempUpdateSuggestInfo, classType) => {
        // 檢查 tempUpdateSuggestInfo 是否存在，且是否包含指定的 classType
        if (
            tempUpdateSuggestInfo &&
            tempUpdateSuggestInfo[classType] &&
            Array.isArray(tempUpdateSuggestInfo[classType])
        ) {
            // 如果條件都滿足，直接回傳該 classType 的資料
            return tempUpdateSuggestInfo[classType];
        }

        // 如果找不到資料，回傳空陣列
        return [];
    };

    // only for temporary suggestions
    const handleDelete = useCallback(
        async rowData => {
            if (isEmpty(tempUpdateData)) return;
            const newData = tempUpdateData?.filter(
                item => item.id !== rowData.id
            );
            const deletedData = tempUpdateData?.filter(
                item => item.id === rowData.id
            );
            setTempUpdateData(newData);

            const removeMatchingData = (deleteData, allTempSugData) => {
                // 創建一個新物件來避免直接修改原始資料
                const newData = { ...allTempSugData };

                // 取得要刪除的 id 列表
                const idsToRemove = deleteData.map(item => item.id);

                // 遍歷所有的陣列（包括 namenode 和其他類型）
                Object.keys(newData).forEach(key => {
                    if (Array.isArray(newData[key])) {
                        // 過濾掉匹配的 id
                        newData[key] = newData[key].filter(
                            item => !idsToRemove.includes(item.id)
                        );
                    }
                });

                return newData;
            };

            const updateTempUpdateSuggestInfo = removeMatchingData(
                deletedData,
                tempUpdateSuggestInfo
            );

            dispatch({
                type: Act.UPDATE_TEMP_UPDATE_SUGGESTINFO,
                payload: updateTempUpdateSuggestInfo
            });

            // 刪除臨時建立的graph
            const values = rowData.graph.split("/");
            const desiredValue = `PER${values[values.length - 1]}`;

            const entry = {
                classType: "type",
                srcId: desiredValue,
                graph: rowData.graph,
                value: {}
            };

            const res = await deleteHkbdbData(Api.deleteGraph(), entry);

            if (res && !res.state) {
                throw new Error("delete failed");
            }
        },
        [dispatch, tempUpdateData]
    );

    const processGraphData2 = async (tmpHeaders, inputData) => {
        const result = [];
        const splitHeaders = tmpHeaders.map(header => header.split("__")[0]);

        await Promise.all(
            inputData.map(async item => {
                const newItem = {
                    graph: item.graph,
                    uid: userUid,
                    sheetName: ontologyType,
                    id: item.id,
                    val: {}
                };

                splitHeaders.forEach(header => {
                    if (header === "graph") return;
                    newItem.val[header] = "";
                });

                await Promise.all(
                    Object.entries(item).map(async ([key, value]) => {
                        if (key === "id") return;
                        const headerKey = key.split("__")[0];
                        if (headerKey in newItem.val) {
                            if (Array.isArray(value)) {
                                if (
                                    key === "hasAwardedForWork__Publication" ||
                                    key === "hasAwardedForWork__Article" ||
                                    key === "hasPublishedIn__Publication" ||
                                    key ===
                                        "isEditionOrTranslationOf__Publication"
                                ) {
                                    const bookNames = await Promise.all(
                                        value.map(async val => {
                                            const trimVal = val.trim();
                                            let bookName = await getBookName(
                                                trimVal
                                            );
                                            if (!bookName) {
                                                bookName = await getAddBookLabel(
                                                    trimVal
                                                );
                                            }
                                            if (val.includes("ART")) {
                                                bookName += "ART/";
                                            }
                                            return bookName;
                                        })
                                    );
                                    newItem.val[headerKey] = bookNames.join(
                                        ","
                                    );
                                } else {
                                    newItem.val[headerKey] = value.join(",");
                                }
                            } else {
                                newItem.val[headerKey] = value;
                            }
                        }
                    })
                );
                result.push(newItem);
            })
        );

        const newResult = result.filter(item =>
            Object.values(item.val).some(value => value !== "")
        );

        setTempUpdateData(newResult.slice(startIndex, endIndex));
        return result;
    };

    useEffect(() => {
        const fetchData = async () => {
            const temp = getSpecificClassTypeData(
                tempUpdateSuggestInfo,
                ontologyType
            );
            await processGraphData2(headers, temp);
        };

        fetchData();
    }, [tempUpdateSuggestInfo, ontologyType, headers, startIndex, endIndex]);

    return (
        <React.Fragment>
            {!isEmpty(displaySug) &&
                displaySug.map((item, idx) => {
                    const { graph, uid } = item;
                    if (uid !== userUid) return null;
                    // 用於判斷曾獲獎項內的獲獎作品顯示
                    const hasAwardedForWorkArticle = suggestInfo
                        .filter(i => i.graph === item.graph)
                        .filter(
                            ii =>
                                ii.prop === config.hasAwardedForWork &&
                                ii.val.includes("ART")
                        );

                    return (
                        <Table.Row key={`basic-table-row-${graph}-${idx}`}>
                            <Table.Cell
                                key={`table-row-cell-edit-${idx}`}
                                textAlign="center"
                                style={customTableCellStyle}
                            >
                                <CustomContentMenu
                                    rowData={item}
                                    ontologyDomain={ontologyDomain}
                                    ontologyType={ontologyType}
                                    headers={headers}
                                />
                            </Table.Cell>
                            {Object.entries(item.val).map(([prop, val]) => {
                                // 針對曾獲獎項做區分處理
                                if (
                                    !isEmpty(hasAwardedForWorkArticle) &&
                                    prop === config.hasAwardedForWork
                                ) {
                                    return (
                                        <Table.Cell
                                            key={prop}
                                            textAlign="center"
                                            style={customTableCellStyle}
                                        ></Table.Cell>
                                    );
                                } else {
                                    return (
                                        <Table.Cell
                                            key={prop}
                                            textAlign="center"
                                            style={customTableCellStyle}
                                        >
                                            {transfromSugPrefix(val)}
                                        </Table.Cell>
                                    );
                                }
                            })}
                            {/* suggestInfo資料會有prop重複的問題(hasAwardedForWork有publication跟article)，沒辦法透過processGraphData經由headers去動態處理 */}
                            {item.sheetName === config.DEF_AWDEVT_TYPE && (
                                <Table.Cell
                                    textAlign="center"
                                    style={customTableCellStyle}
                                >
                                    {!isEmpty(hasAwardedForWorkArticle)
                                        ? hasAwardedForWorkArticle[0].graph ===
                                              item.graph &&
                                          // item.val.hasAwardedForWork.slice(
                                          //     0,
                                          //     -3
                                          // )
                                          item.val.hasAwardedForWork.replaceAll(
                                              "ART/",
                                              ""
                                          )
                                        : ""}
                                </Table.Cell>
                            )}
                            <Table.Cell
                                textAlign="center"
                                style={customTableCellStyle}
                            >
                                <CustomTableLabel
                                    key={`value-label-${graph}-${idx}`}
                                    value={`Suggester: ${sugNames[uid] || ""}`}
                                    color="orange"
                                />
                            </Table.Cell>
                        </Table.Row>
                    );
                })}
            {!isEmpty(tempUpdateData) &&
                tempUpdateData?.map((item, idx) => (
                    <Table.Row key={`basic-table-row-${idx}`}>
                        <Table.Cell
                            key={`table-row-cell-edit-${idx}`}
                            textAlign="center"
                            style={customTableCellStyle}
                        >
                            <SugContextMenuFlex
                                rowData={item}
                                ontologyDomain={ontologyDomain}
                                ontologyType={ontologyType}
                                headers={headers}
                                handleDelete={() => {
                                    handleDelete(item);
                                }}
                            />
                        </Table.Cell>
                        {/* 使用 headers 來確保順序 */}
                        {headers.map(header => {
                            const prop = header.split("__")[0];
                            if (prop === "graph") return null;
                            const val = item.val[prop];
                            const hasAwardedForType = val?.includes("ART/")
                                ? "ART"
                                : "PUB";

                            if (
                                (header === "hasAwardedForWork__Publication" &&
                                    hasAwardedForType === "ART") ||
                                (header === "hasAwardedForWork__Article" &&
                                    hasAwardedForType === "PUB")
                            ) {
                                return (
                                    <Table.Cell
                                        key={prop}
                                        textAlign="center"
                                        style={customTableCellStyle}
                                    ></Table.Cell>
                                );
                            } else if (
                                header === "hasAwardedForWork__Article"
                            ) {
                                return (
                                    <Table.Cell
                                        key={prop}
                                        textAlign="center"
                                        style={customTableCellStyle}
                                    >
                                        {/* {val.slice(0, -3)} */}
                                        {val.replaceAll("ART/", "")}
                                    </Table.Cell>
                                );
                            } else {
                                return (
                                    <Table.Cell
                                        key={prop}
                                        textAlign="center"
                                        style={customTableCellStyle}
                                    >
                                        {transfromSugPrefix(val)}
                                    </Table.Cell>
                                );
                            }
                        })}
                        <Table.Cell
                            textAlign="center"
                            style={customTableCellStyle}
                        >
                            <CustomTableLabel
                                key={`value-label-${idx}`}
                                value={`Suggester: ${sugNames[userUid] || ""}`}
                                color="orange"
                            />
                        </Table.Cell>
                    </Table.Row>
                ))}
        </React.Fragment>
    );
}

export default SugFlexTableBody;
