*{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

//.listItem{
//  display: flex;
//  justify-content: space-around;
//  width: 80%;
//  .imgIllustration {
//    display: flex;
//    align-self: center;
//    //align-items: center;
//    justify-content: center;
//    width: 40%;
//    //border: 1px red solid;
//    height: 10vh;
//    overflow: hidden;
//    img{
//      cursor: pointer;
//    }
//  }
//  .description{
//    width: 40%;
//  }
//}



.listItem{
  display: flex;
  //border: 1px lightgreen solid;
  width: 100%;
  justify-content: space-around;
  .content{
    //border: 1px blue solid;
    width: 60%;
  }
  .topic{
    //border: 1px red solid;
    align-self: flex-end;
    width: 30%;
  }
}

.noData{
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
}

.showWholeImg{
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1110;
  background-color: darkgray;
  justify-content: center;
  align-items: center;
  display: none;
  opacity: 0;
  transition: all 2s ease;
  border-radius: 8px;
}

.progressing{
  width: 100%;
  height: 100%;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pageBar{
  width: 100%;
  display: flex;
  justify-content: center;
}