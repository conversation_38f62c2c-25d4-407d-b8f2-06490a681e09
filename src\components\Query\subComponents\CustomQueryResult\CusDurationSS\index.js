// react
import React, { useContext, useState, useEffect } from "react";

// ui
import { Message, Table } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// common code
import { isNotEmpty } from "../../../../../common/codes";

const index = () => {
    // get state
    const [state, _] = useContext(StoreContext);
    // get query from state
    const { queryResult } = state.query;
    const { data, total, durationSS } = queryResult;

    const [showMessage, setShowMessage] = useState(false);
    const [customMessage, setCustomMessage] = useState("");

    useEffect(() => {
        if (isNotEmpty(data)) {
            setShowMessage(true);
            setCustomMessage(
                <FormattedMessage
                    id="query.durationSS"
                    defaultMessage="About {total} results, Show {length} results ({durationSS} seconds)"
                    values={{
                        total: total,
                        length: data?.length || 0,
                        durationSS: durationSS
                    }}
                />
            );
        } else {
            setShowMessage(false);
            setCustomMessage("");
        }
    }, [data]);

    return (
        <React.Fragment>
            {showMessage && <Message content={customMessage} />}
        </React.Fragment>
    );
};

export default index;
