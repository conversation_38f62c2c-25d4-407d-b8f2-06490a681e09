import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusAlbum from "../CusAlbum";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const CusExhibition = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };
    const venueKey = ["hasVenue", "hasVenueTmp"];
    const curatorKey = ["hasCurator", "hasCuratorTmp"];
    const participantKey = ["hasParticipant", "hasParticipantTmp"];

    const bilingFunc = bilingual(defObj);

    const [venueZh, venueEn] = bilingFunc(data, venueKey,);
    const [curatorZh, curatorEn] = bilingFunc(data, curatorKey);
    const [participantZh, participantEn] = bilingFunc(data, participantKey);

    const [locZh, locEn] = bilingFunc(data, "loc");
    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [mediaZh, mediaEn] = bilingFunc(data, "mediaType");
    const [remarkZh, remarkEn] = bilingFunc(data, "remark");
    const [endDateZh, endDateEn] = bilingFunc(data, "displayEndDate");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [districtZh, districtEn] = bilingFunc(data, "district");
    const [startDateZh, startDateEn] = bilingFunc(data, "displayStartDate");
    const [organizerZh, organizerEn] = bilingFunc(data, "hasOrganizer");
    const [coOragnizerZh, coOragnizerEn] = bilingFunc(data, "hasCoOragnizer");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_exhibition}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`exhibitions/${year}`}
                    backupPath={"exhibitions"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.startDate"
                    defaultMessage="Start Date : "
                />
                <CusValue {...startDateZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.endDate"
                    defaultMessage="End Date : "
                />
                <CusValue {...endDateZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.venue"
                    defaultMessage="Venue : "
                />
                <CusValue {...venueZh} />
                <CusValue prefix="/" defVal="" {...venueEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.loc"
                    defaultMessage="Location : "
                />
                <CusValue {...locZh} />
                <CusValue prefix="/" defVal="" {...locEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.district"
                    defaultMessage="District : "
                />
                <CusValue {...districtZh} />
                <CusValue prefix="/" defVal="" {...districtEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.media"
                    defaultMessage="Media : "
                />
                <CusValue {...mediaZh} />
                <CusValue prefix="/" defVal="" {...mediaEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.organizer"
                    defaultMessage="Organizer : "
                />
                <CusValue {...organizerZh} />
                <CusValue prefix="/" defVal="" {...organizerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.coOragnizer"
                    defaultMessage="Co-organizer(s): "
                />
                <CusValue {...coOragnizerZh} />
                <CusValue prefix="/" defVal="" {...coOragnizerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.curator"
                    defaultMessage="Curator(s) : "
                />
                <CusValue {...curatorZh} />
                <CusValue prefix="/" defVal="" {...curatorEn} />
            </CusPara>
            <CusPara wrap>
                <FormattedMessage
                    id="hkvayb.search.exhibition.participant"
                    defaultMessage="Participant(s) : "
                />
                <CusValue {...participantZh} />
                <CusValue prefix="/" defVal="" {...participantEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.exhibition.remark"
                    defaultMessage="Remark : "
                />
                <CusValue {...remarkZh} />
                <CusValue prefix="/" defVal="" {...remarkEn} />
            </CusPara>
        </div>
    );
};

export default CusExhibition;
