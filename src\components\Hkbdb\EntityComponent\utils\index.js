import axios from "axios";
import { Api } from "../../../../api/hkbdb/Api";
import base64url from "base64url";

const EDIT_COORD_TYPES = ["Place", "Organization"];

const encodeLocationId = id => {
    if (!id.startsWith("PLA") && !id.startsWith("ORG")) return null;

    const prefix = id.startsWith("PLA") ? "PLA" : "ORG";
    const baseEncodeName = base64url.encode(id.replace(/^PLA|^ORG/, ""));
    return `${prefix}${baseEncodeName}`;
};

const makeCreateEntry = (graph, srcId, classtype, valueObj) => ({
    entry: {
        graph: `${graph}`,
        classType: `${classtype}`,
        srcId: `${srcId}`,
        value: valueObj
    }
});

const makeUpdateEntry = (graph, srcId, classType, srcValue, dstValue) => ({
    entryDst: {
        graph,
        srcId,
        classType,
        value: dstValue
    },
    entrySrc: {
        graph,
        srcId,
        classType,
        value: srcValue
    }
});

const createDataToDB = async (url, entry) => {
    try {
        await axios.post(url, entry);
    } catch (e) {
        console.log("createDataToDB failed: ", e, url);
    }
};

const updateDataInDB = async (url, entry) => {
    try {
        await axios.put(url, entry);
    } catch (e) {
        console.log("updateDataInDB failed: ", e, url);
    }
};

export const saveCoordsChange = async (coords, type) => {
    if (
        !type ||
        !EDIT_COORD_TYPES.includes(type) ||
        !Array.isArray(coords) ||
        !coords.length
    )
        return;

    const graph = "geo";
    const url = Api.restfulHKBDB().replace("{locale}", Api.getLocale());

    for (const coord of coords) {
        const id =
            coord.status === "create"
                ? `${type === "Place" ? "PLA" : "ORG"}${base64url.encode(
                      encodeURI(coord.new.label)
                  )}`
                : encodeLocationId(coord.new.id);

        if (id) {
            const newValue = {
                [`label_${type}`]: coord.new.label,
                geoLatitude: coord.new.latitude,
                geoLongitude: coord.new.longitude
            };

            const oldValue = {
                [`label_${type}`]: coord.old.label,
                geoLatitude: coord.old.latitude,
                geoLongitude: coord.old.longitude
            };

            switch (coord.status) {
                case "create": {
                    const entry = makeCreateEntry(graph, id, type, newValue);

                    await createDataToDB(url, entry);
                    break;
                }
                case "update": {
                    const entry = makeUpdateEntry(
                        graph,
                        id,
                        type,
                        oldValue,
                        newValue
                    );

                    await updateDataInDB(url, entry);
                    break;
                }
                default:
                    break;
            }
        }
    }
};

export const getGoogleCoord = async location => {
    const defaultCoord = {
        id: "",
        label: location,
        longitude: "",
        latitude: ""
    };

    try {
        const entry = { address: location };
        const res = await axios.post(Api.getGoogleCoord, entry);

        return res?.data
            ? {
                  ...defaultCoord,
                  longitude: res.data.lng,
                  latitude: res.data.lat
              }
            : defaultCoord;
    } catch (e) {
        console.log("getGoogleCoord failed: ", e);
        return defaultCoord;
    }
};
