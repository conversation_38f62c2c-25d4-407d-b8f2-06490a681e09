import React from "react";
import { Pagination, PaginationItem } from "@mui/material";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import { styled } from "@material-ui/styles";
import "./CustomPagination.scss";

const StyledPagination = styled(Pagination)(({ theme }) => ({
    zIndex: 999,
    "& li button": {
        border: "solid 1px #dbdbdb",
        backgroundColor: "#fff",
        color: "#f5f0f0",
        borderRadius: 0
    },
    "& .MuiPaginationItem-page.Mui-selected": {
        border: "solid 1px #404040",
        backgroundColor: "#104860",
        color: "#fff"
    },
    "& .MuiPaginationItem-root": {
        color: "#104860"
    }
}));

const CustomPagination = ({ totalPage = 0, curPage, setCurPage }) => {
    return (
        <StyledPagination
            className="Pagination"
            variant="outlined"
            shape="rounded"
            count={totalPage}
            page={curPage}
            onChange={(e, value) => {
                console.log(value);
                setCurPage(value);
            }}
            renderItem={item => (
                <PaginationItem
                    components={{
                        first: KeyboardArrowLeftIcon,
                        last: KeyboardArrowRightIcon
                    }}
                    {...item}
                />
            )}
        />
    );
};

export default CustomPagination;
