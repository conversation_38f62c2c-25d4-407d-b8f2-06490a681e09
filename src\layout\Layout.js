import React from "react";
//
import MobileContainer from "./MobileContainer";
import DesktopContainer from "./DesktopContainer";
import Footer from "./Footer";
/* eslint-disable react/no-multi-comp */
/* Heads up! HomepageHeading uses inline styling, however it's not the best practice. Use CSS or styled components for
 * such things.
 */

/* Heads up!
 * Neither Semantic UI nor Semantic UI React offer a responsive navbar, however, it can be implemented easily.
 * It can be more complicated, but you can create really flexible markup.
 */

//
// type ResponsiveContainerProps = {
//     children: Array<any>
// };

export const ResponsiveContainer = props => (
    <div>
        <DesktopContainer {...props} mobile={false}>
            {props.children}
            {props.withFooter && <Footer {...props} mobile={false} />}
            {/* {props.withFooter && <Footer mobile={false}/>} */}
        </DesktopContainer>

        <MobileContainer {...props} mobile={true}>
            {props.children}
            {props.withFooter && <Footer {...props} mobile={true} />}
        </MobileContainer>
    </div>
);
