import React, { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { FormattedMessage } from "react-intl";
import PropTypes from "prop-types";
import classNames from "classnames";
import { StoreContext } from "../../../store/StoreProvider";

import EntitySearch from "../../BrowsePage/subComponents/entitySearch";
import EntityBody from "./EntityBody";
import HomePageBgCard from "./HomePageBgCard";
import useWindowSize from "../../../hook/useWindowSize";
import uiConfig from "../../../config/config-ui";

import focusPointImg from "../../../images/homepage_background_carousel/focuspoint.png";
import hotSearchImg from "../../../images/homepage_background_carousel/hotSearch.png";
import homepageTitle from "../../../images/homepageTitle.svg";

export const HomePageHeading = ({ mobile, webStyle }) => {
    const size = useWindowSize();
    const [state, _] = useContext(StoreContext);
    const { user, main } = state;
    const { locale } = user;
    const { imageToken } = main;
    const [localLocale, setLocalLocale] = useState(locale);
    const [manuscriptImg, setManuscriptImg] = useState("");
    const [bookCoverImg, setBookCoverImg] = useState("");
    const [curImage, setCurImg] = useState(null);

    const windowHeight = window.innerHeight;
    const isTablet = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= uiConfig.BP_DESK_MIN_WIDTH;
    };
    const isMobile = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= 568;
    };

    const tempHeight = isTablet() ? 120 : 70;

    useEffect(() => {
        setLocalLocale(locale);
    }, [locale]);

    useEffect(() => {
        const getFallbackImg = (obj, keys = [], defaultImgPath = "") => {
            let imgPath = { ...(obj || {}) };
            keys.forEach(key => {
                if (typeof imgPath === "object" && key in imgPath) {
                    imgPath = imgPath[key];
                }
            });
            return (
                (imgPath && typeof imgPath === "string" && imgPath) ||
                defaultImgPath
            );
        };

        const desktopMs = getFallbackImg(
            imageToken,
            ["desktop", "manuScript"],
            ""
        );
        const desktopBc = getFallbackImg(
            imageToken,
            ["desktop", "bookCover"],
            ""
        );

        const mobileMs = getFallbackImg(
            imageToken,
            ["mobile", "manuScript"],
            ""
        );

        const mobileBc = getFallbackImg(
            imageToken,
            ["mobile", "bookCover"],
            ""
        );

        if (isMobile()) {
            setBookCoverImg(mobileBc);
            setManuscriptImg(mobileMs);
        } else {
            setBookCoverImg(desktopBc);
            setManuscriptImg(desktopMs);
        }
    }, [imageToken.desktop, imageToken.mobile]);

    const mobileHomePageBgProducer = (imgArr, dirPath, dataObj) => {
        // 因外層div與footer及header不同層，需根據螢幕寬度及高度調整圖片大小
        const tmpHighHeightSize = isMobile() ? "150px" : "180px";
        const tmpLowHeightSize = isMobile() ? "120px" : "180px";
        const tmpSize =
            windowHeight < 900 ? tmpLowHeightSize : tmpHighHeightSize;

        const handleClick = tmpImg => {
            setCurImg(tmpImg);
            setTimeout(() => {
                setCurImg(null);
            }, 2000);
        };

        return (
            <div
                style={{
                    display: "flex",
                    gap: "1rem",
                    marginTop: "1rem",
                    overflow: "hidden"
                }}
            >
                {dataObj.map(i => {
                    const isCurImg = curImage === i.image;
                    return (
                        <Link
                            to={{ pathname: `/${localLocale}/${i.path}` }}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                                position: "relative",
                                overflow: "hidden",
                                width: tmpSize,
                                height: tmpSize
                            }}
                            onClick={() => {
                                handleClick(i.image);
                            }}
                            key={i.path}
                        >
                            <img
                                src={i.image}
                                alt="Small card image"
                                style={{
                                    width: tmpSize,
                                    height: tmpSize,
                                    cursor: "pointer",
                                    transition: isCurImg
                                        ? "all 0.1s ease"
                                        : null,
                                    transform: isCurImg ? "scale(1.1)" : null,
                                    filter: isCurImg ? "blur(5px)" : null,
                                    maxWidth: "100%",
                                    maxHeight: "100%",
                                    overflow: "hidden"
                                }}
                                className={classNames("homepage__imgBox", {
                                    homepage__imgBox__effect: isCurImg
                                })}
                            />
                            <div
                                style={{
                                    position: "absolute",
                                    top: "0%",
                                    left: "50%",
                                    width: "100%",
                                    height: "100%",
                                    transform: "translateX(-50%)"
                                }}
                            >
                                <button
                                    style={{
                                        display: isCurImg ? "flex" : "none",
                                        backgroundColor: "transparent",
                                        position: "absolute",
                                        color: "#104860",
                                        top: "40%",
                                        left: "50%",
                                        border: "none",
                                        transform: "translateX(-50%)",
                                        fontSize: "16px"
                                    }}
                                >
                                    <FormattedMessage
                                        id={"button.forward"}
                                        defaultMessage={"Go"}
                                    />
                                </button>
                            </div>
                        </Link>
                    );
                })}
            </div>
        );
    };

    return (
        <>
            <div
                className="homePage__heading"
                // text
                style={{
                    textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    perspective: "1700px",
                    marginTop: "8rem"
                }}
            >
                <div
                    style={{
                        maxWidth: "640px",
                        marginBottom: isTablet() ? "40px" : "1rem",
                        padding: "0 24px",
                        zIndex: "999"
                    }}
                >
                    <img
                        src={homepageTitle}
                        alt="homepage title"
                        style={{ width: "100%" }}
                    />
                </div>
                <div
                    style={{
                        maxWidth: "480px",
                        width: mobile ? "calc(100vw)" : "calc(100vw)-336px",
                        zIndex: "9999"
                    }}
                >
                    <EntitySearch EntityBody={EntityBody} />
                </div>
                {isTablet() ? (
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column"
                        }}
                    >
                        {manuscriptImg &&
                            mobileHomePageBgProducer(
                                [manuscriptImg, hotSearchImg],
                                ["collection/manuScript", "hotSearch"],
                                [
                                    {
                                        path: "collection/manuScript",
                                        image: manuscriptImg
                                    },
                                    { path: "hotSearch", image: hotSearchImg }
                                ]
                            )}
                        {bookCoverImg &&
                            mobileHomePageBgProducer(
                                [bookCoverImg, focusPointImg],
                                ["collection/featuredPub", "focusPoint"],
                                [
                                    {
                                        path: "collection/featuredPub",
                                        image: bookCoverImg
                                    },
                                    { path: "focusPoint", image: focusPointImg }
                                ]
                            )}
                    </div>
                ) : (
                    <HomePageBgCard imgArr={[manuscriptImg, bookCoverImg]} />
                )}
            </div>
        </>
    );
};

HomePageHeading.propTypes = {
    mobile: PropTypes.bool
};
