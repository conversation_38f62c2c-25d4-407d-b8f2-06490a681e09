import React, { Fragment, useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";

// custom
import CustomMenu from "./CustomMenuFixedDelete";
import CustomAlertMessage from "./CustomAlertMessage";

// lang
import { FormattedMessage } from "react-intl";

// common
import { isEmpty } from "../../../../common/codes";
import { _reformatData } from "../commonAction";

// api
import { Api, deleteHkbdbData } from "../../../../api/hkbdb/Api";
import Act from "../../../../store/actions";

// store
import { StoreContext } from "../../../../store/StoreProvider";

const CustomDeleteModalFixed = ({
    open,
    setOpen,
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const [isLoading, setIsLoading] = useState(() => false);
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const [deleteResults, setDeleteResults] = useState(() => ({
        deletedRowIds: [],
        success: 0,
        failed: 0
    }));

    const handleInitDeletedData = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            deletedData: []
        }));
    };
    const handleInitIsDeleted = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            isDeleted: false,
            deletedRowIds: []
        }));
    };
    const handleInitResult = () => {
        setDeleteResults(prevState => ({
            ...prevState,
            deletedRowIds: [],
            success: 0,
            failed: 0
        }));
    };
    const handleRefresh = () => {
        if (deleteResults.success) {
            // 通知 API 重新拉取
            dispatch({
                type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                payload: {
                    target: ontologyType,
                    signal: `delete-${new Date().getTime()}`
                }
            });
            // 通知編輯後是否顯示 isLoading(這是區域重新載入，不是整個頁面，e.g. information's publication)
            dispatch({
                type: Act.INFORMATION_DATA_IS_LOADING_SET,
                payload: true
            });
        }
    };

    const handleClose = () => {
        handleRefresh();
        // init status
        handleInitDeletedData();
        handleInitIsDeleted();
        handleInitResult();
        // close modal
        setOpen(false);
    };
    const handleOpen = () => {
        // open modal
        setOpen(true);
    };
    const handleDelete = async () => {
        setIsLoading(true);

        if (!isEmpty(editData.deletedData)) {
            const deletedRowIds = editData.deletedData;
            const promises = deletedRowIds.map(rowId => {
                const entry = _reformatData(
                    editData.rowData[rowId],
                    ontologyType
                );
                return deleteHkbdbData(Api.restfulHKBDB(), entry);
            });
            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);

            results.forEach((res, idx) => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的。
                    // 這裡不使用 setDeleteResult 來更新資料因為 deleteResults 和 setDeleteResults 之間是「非同步」的
                    // 因此這裡選擇直接對 deleteResults 進行操作
                    deleteResults.deletedRowIds.push(deletedRowIds[idx]);
                    // 紀錄成功資料筆數
                    deleteResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    deleteResults.failed++;
                }
            });
            //
            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: "更新",
                type: "success",
                content: `已刪除: ${deleteResults.success}, 已失敗: ${deleteResults.failed}`,
                renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
            }));
            //
            setEditData(prevEditData => ({
                ...prevEditData,
                isDeleted: true,
                deletedRowIds: [...deleteResults.deletedRowIds]
            }));
            //
        }
        //
        setIsLoading(false);
        // init status
        handleInitDeletedData();
        // setOpen(false);
    };
    const handleCancel = () => {
        // init status
        handleClose();
        // close modal
        setOpen(false);
    };
    //
    const SwitchButton = () => {
        if (editData.isDeleted && isEmpty(editData.deletedData)) {
            return (
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            return (
                <Fragment>
                    <Button
                        loading={isLoading}
                        disabled={isEmpty(editData.deletedData)}
                        onClick={handleDelete}
                        color="red"
                    >
                        <FormattedMessage
                            id={"people.Information.button.delete"}
                            defaultMessage={"Delete"}
                        />
                    </Button>
                    <Button onClick={handleCancel} color="green">
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };
    //
    const modalContentStyle = {
        width: "100%"
    };

    return (
        <Modal open={open} onClose={handleClose} onOpen={handleOpen}>
            {/* <pre>{JSON.stringify(editData, null, 2)}</pre> */}
            <Modal.Header>
                <FormattedMessage
                    id={"people.Information.header.delete.content"}
                    defaultMessage={"Delete Content"}
                />
            </Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description style={modalContentStyle}>
                    {/* alert */}
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    {/* content */}
                    <CustomMenu
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <SwitchButton />
            </Modal.Actions>
        </Modal>
    );
};

export default CustomDeleteModalFixed;
