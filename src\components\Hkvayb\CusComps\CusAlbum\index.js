import React from "react";

import useStyles from "./style";

import CusSwiper from "../CusSwiper";

import useImageSrc from "../../CusHooks/useImageSrc";
import useImageValid from "../../CusHooks/useImageValid";

import { url } from "../../../../api/hkvayb";

const coverCardStyle = {
    hkvayb_card_box: {
        height: "unset"
    }
};

const CusAlbum = ({
    value,
    path,
    backupPath,
    width = 300,
    height = 230,
    style = {},
    ...rest
}) => {
    const classes = useStyles(style);
    const targetUrl = url.hkvayb.EXTERNAL_CONNECTION;
    const srcImages = useImageSrc(value, targetUrl);
    const verifiedImages = useImageValid(srcImages);
    return (
        <div className={classes.hkvayb_div}>
            {verifiedImages.length > 0 && (
                <CusSwiper
                    style={coverCardStyle}
                    width={width}
                    height={height}
                    data={verifiedImages}
                    {...rest}
                />
            )}
        </div>
    );
};

export default CusAlbum;
