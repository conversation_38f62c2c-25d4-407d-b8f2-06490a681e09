import React, {
    useCallback,
    useEffect,
    useState,
    useMemo,
    useRef
} from "react";

// mui
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";

// hooks
import useDebounce from "../../../../../common/hooks/useDebounce";

import resetIcon from "../../../../../images/icon_reset/reset.svg";
import replayIcon from "../../../../../images/icon_replay/replay.svg";
import replayOnceIcon from "../../../../../images/icon_replay/replayOnce.svg";
import replayDisabledIcon from "../../../../../images/icon_replay/icon_disable_replay.svg";
import replayOnceDisabledIcon from "../../../../../images/icon_replay/icon_disable_replay_once.svg";
import PlayCircleIcon from "@mui/icons-material/PlayCircle";
import PauseCircleIcon from "@mui/icons-material/PauseCircle";
// config

// store

// types
import PropTypes from "prop-types";
import drawTimeLine from "../../../utils/drawTimeLine";
import debounce from "lodash/debounce";
import { isEmpty } from "../../../../../common/codes";
import Button from "@mui/material/Button";
import { FormattedMessage } from "react-intl";
import YearInput from "./subComponents/YearInput";

// style
import "./MapTimeline.scss";

// Constants moved here
const initialStartYear = 1800;

const ANCHOR_MODE = {
    YEARS_INDEX: "year",
    EVERY_DURATION: "every"
};
const DURATION_YEARS = 1;
const EXTRA_YEARS = 10;

const speedOptions = [
    { value: 0.25, label: "0.25x" },
    { value: 0.5, label: "0.50x" },
    { value: 0.75, label: "0.75x" },
    { value: 1, label: "1x" },
    { value: 2, label: "2x" },
    { value: 3, label: "3x" }
];

const getIntervalBySpeed = speed => {
    let interval1x = 125;
    return interval1x / Number(speed);
};

const curYear = new Date().getFullYear();

const iconStyle = disabled => ({
    color: disabled ? "#DCDCDC" : "#336F89",
    fontSize: "2rem",
    "&:hover": {
        color: "#104860",
        cursor: "pointer",
        transition: "color 0.3s ease"
    }
});

// TimeLine component
const MapTimeLine = props => {
    const {
        dataYears,
        defaultYears,
        onAnchorYearChange,
        onStartYearChange,
        onEndYearChange,
        onPlayStatusChange,
        onRestart,
        variant
    } = props;

    // Use ref for interval instead of a global variable
    const intervalRef = useRef(null);

    // local state
    const [anchorMode, setAnchorMode] = useState(ANCHOR_MODE.EVERY_DURATION);
    const [anchorYearIndex, setAnchorYearIndex] = useState(0);
    const [anchorYear, setAnchorYear] = useState(0);
    const [years, setYears] = useState([]);
    const [startYear, setStartYear] = useState(0);
    const [endYear, setEndYear] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [playSpeed, setPlaySpeed] = useState(3);
    const [loopCount, setLoopCount] = useState(0);
    const [timeLineStartYear, setTimeLineStartYear] = useState(
        initialStartYear
    );
    const [timeLineEndYear, setTimeLineEndYear] = useState(curYear);
    const [disableReset, setDisableReset] = useState(true);

    const debounceStartYear = useDebounce(startYear, 300);
    const debounceEndYear = useDebounce(endYear, 300);

    // Memoize debouncedSetStartYear and debouncedSetEndYear functions
    const debouncedSetStartYear = useCallback(
        debounce(newYear => {
            if (newYear < timeLineStartYear || newYear > timeLineEndYear - 1) {
                setStartYear(timeLineStartYear);
            } else if (newYear > anchorYear) {
                // setStartYear(anchorYear);
                setStartYear(newYear);
                setAnchorYear(newYear);
            }
        }, 1000),
        [timeLineEndYear, timeLineStartYear, anchorYear]
    );

    const debouncedSetEndYear = useCallback(
        debounce(newYear => {
            if (newYear < timeLineStartYear || newYear > timeLineEndYear - 1) {
                setEndYear(timeLineEndYear);
            } else if (newYear < anchorYear) {
                setEndYear(anchorYear);
            }
        }, 1000),
        [timeLineEndYear, timeLineStartYear, anchorYear]
    );

    // Handle data years changes
    useEffect(() => {
        setIsPlaying(false);
        if (Array.isArray(dataYears) && dataYears.length > 0) {
            setYears(dataYears);
            setStartYear(dataYears[0]);
            setTimeLineStartYear(dataYears[0]);
            setAnchorYearIndex(0);
            setAnchorYear(dataYears[0]);
            setIsPlaying(true);

            const lastYearValue =
                dataYears.length > 1
                    ? dataYears[dataYears.length - 1]
                    : curYear;

            setEndYear(lastYearValue);
            setTimeLineEndYear(lastYearValue);
        }
        // Handle empty data
        else if (isEmpty(dataYears) && years.length > 0) {
            setYears([]);
            setStartYear(initialStartYear);
            setTimeLineStartYear(initialStartYear);
            setAnchorYearIndex(0);
            setAnchorYear(0);
            setEndYear(curYear);
            setTimeLineEndYear(curYear);
            setIsPlaying(false);
        }
    }, [dataYears]);

    // Memoize timeline config for drawTimeLine
    const timelineConfig = useMemo(
        () => ({
            years,
            anchorYear,
            startYear,
            endYear,
            timeLineStartYear,
            timeLineEndYear,
            onYearClick: e => {
                console.log("year click", e);
            },
            onYearHover: e => {
                console.log("year hover", e);
            },
            onBarDrag: year => setAnchorYear(year),
            onStartYearChange: year => {
                setStartYear(year);
                if (typeof onStartYearChange === "function")
                    onStartYearChange(year);
            },
            onEndYearChange: year => {
                setEndYear(year);
                if (typeof onEndYearChange === "function")
                    onEndYearChange(year);
            },
            onMouseOnBar: e => {
                setIsPlaying(false);
            },
            onMouseOutBar: e => {
                // setIsPlaying(true)
            }
        }),
        [
            years,
            anchorYear,
            startYear,
            endYear,
            timeLineStartYear,
            timeLineEndYear,
            onStartYearChange,
            onEndYearChange
        ]
    );

    // Draw timeline
    useEffect(() => {
        if (
            startYear < initialStartYear ||
            startYear > curYear - 1 ||
            endYear > curYear ||
            endYear < initialStartYear + 1
        ) {
            return;
        }

        drawTimeLine(timelineConfig);
    }, [timelineConfig]);

    // Handle playing and interval
    useEffect(() => {
        if (isPlaying) {
            clearInterval(intervalRef.current);
            const intervalMs = getIntervalBySpeed(playSpeed);

            intervalRef.current = setInterval(() => {
                if (anchorMode === ANCHOR_MODE.EVERY_DURATION) {
                    setAnchorYear(prevYear => {
                        let nextYear = Number(prevYear) + DURATION_YEARS;
                        if (nextYear > Number(endYear)) {
                            if (loopCount === 0) {
                                setIsPlaying(false);
                                return prevYear;
                            } else if (loopCount > 0) {
                                setLoopCount(prevCount => {
                                    if (prevCount >= 1) {
                                        return prevCount - 1;
                                    }
                                    return prevCount;
                                });
                            }
                            // Restart
                            if (onRestart) {
                                onRestart();
                            }
                            nextYear = startYear;
                        }
                        return nextYear;
                    });
                } else {
                    setAnchorYearIndex(prevIdx => {
                        let nextIndex = prevIdx + 1;
                        if (nextIndex >= years.length) {
                            nextIndex = 0;
                        }
                        return nextIndex;
                    });
                }
            }, intervalMs);
        } else {
            clearInterval(intervalRef.current);
        }

        return () => clearInterval(intervalRef.current);
    }, [
        isPlaying,
        startYear,
        endYear,
        playSpeed,
        loopCount,
        anchorMode,
        years.length,
        onRestart
    ]);

    // Update anchor year from index when in YEARS_INDEX mode
    useEffect(() => {
        if (anchorMode === ANCHOR_MODE.YEARS_INDEX && years.length > 0) {
            setAnchorYear(years[anchorYearIndex]);
        }
    }, [anchorYearIndex, anchorMode, years]);

    // Call onPlayStatusChange when isPlaying changes
    useEffect(() => {
        if (typeof onPlayStatusChange === "function") {
            onPlayStatusChange(isPlaying);
        }
    }, [isPlaying, onPlayStatusChange]);

    // Call onStartYearChange when debounceStartYear changes
    useEffect(() => {
        if (
            debounceStartYear != null &&
            typeof onStartYearChange === "function"
        ) {
            onStartYearChange(debounceStartYear);
        }
    }, [debounceStartYear, onStartYearChange]);

    // Call onEndYearChange when debounceEndYear changes
    useEffect(() => {
        if (debounceEndYear != null && typeof onEndYearChange === "function") {
            onEndYearChange(debounceEndYear);
        }
    }, [debounceEndYear, onEndYearChange]);

    // Call onAnchorYearChange when anchorYear changes
    useEffect(() => {
        if (anchorYear != null && typeof onAnchorYearChange === "function") {
            onAnchorYearChange(anchorYear);
        }
    }, [anchorYear, onAnchorYearChange]);

    // Set default years when in general variant
    useEffect(() => {
        if (variant === "general") return;

        if (isEmpty(years) && defaultYears && defaultYears[0]) {
            const minYear = Number(defaultYears[0]?.minYear);
            const maxYear = Number(defaultYears[0]?.maxYear);

            setStartYear(minYear);
            setEndYear(maxYear);
            setTimeLineStartYear(minYear);
            setTimeLineEndYear(maxYear);
        }
    }, [defaultYears, years, variant]);

    // Update disable reset status
    useEffect(() => {
        const isDisable =
            (Array.isArray(years) &&
                startYear === years[0] &&
                endYear === years[years.length - 1]) ||
            dataYears.length === 0;

        setDisableReset(isDisable);
    }, [years, startYear, endYear, dataYears]);

    // Memoize handlers
    const resetYear = useCallback(() => {
        if (Array.isArray(years) && years.length > 0) {
            setStartYear(years[0]);
            setTimeLineStartYear(years[0]);
            setAnchorYear(years[0]);

            const lastYearValue =
                years.length > 1 ? years[years.length - 1] : curYear;
            setEndYear(lastYearValue);
            setTimeLineEndYear(lastYearValue);
        } else {
            setStartYear(initialStartYear);
            setEndYear(curYear);
            setTimeLineStartYear(initialStartYear);
            setTimeLineEndYear(curYear);
        }
    }, [years]);

    const handlePlayStart = useCallback(() => {
        // 播放結束後再按開始才重複播放，其餘保持原本播放次數
        setLoopCount(pre => (pre === 0 && anchorYear === endYear ? 1 : pre));
        setIsPlaying(true);
    }, [anchorYear, endYear]);

    const handlePlayStop = useCallback(() => {
        setIsPlaying(false);
    }, []);

    const handleSpeedChange = useCallback(e => {
        setPlaySpeed(e.target.value);
    }, []);

    const handleStartYearChange = useCallback(
        newYear => {
            setStartYear(newYear);
            if (newYear > anchorYear) setAnchorYear(newYear);
            debouncedSetStartYear(newYear);
        },
        [debouncedSetStartYear]
    );

    const handleEndYearChange = useCallback(
        newYear => {
            setEndYear(newYear);
            // if (newYear < anchorYear) setEndYear(anchorYear);
            debouncedSetEndYear(newYear);
        },
        [debouncedSetEndYear]
    );

    const handleLoopToggle = useCallback(() => {
        setLoopCount(count => (count < 0 ? 0 : -1));
    }, []);

    // Memoize disabled state
    const isControlsDisabled = useMemo(() => dataYears.length === 0, [
        dataYears.length
    ]);

    return (
        <Box>
            <Box
                sx={{
                    display: "flex",
                    minHeight: "120px",
                    flexDirection: "column"
                }}
            >
                <Box>
                    {/* startYear & endYear control */}
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center"
                        }}
                    >
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                width: "70%"
                            }}
                        >
                            <YearInput
                                id="start-year-input"
                                type="start"
                                value={startYear}
                                onChange={handleStartYearChange}
                                min={initialStartYear}
                                max={curYear - 1}
                            />
                            <YearInput
                                id="end-year-input"
                                type="end"
                                value={endYear}
                                onChange={handleEndYearChange}
                                min={initialStartYear + 1}
                                max={curYear}
                            />
                            <Button
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    color: disableReset ? "#7C7C7C" : "#FFFFFF",
                                    backgroundColor: disableReset
                                        ? "#DCDCDC"
                                        : "#104860",
                                    padding: "8px 16px",
                                    flexDirection: "row",
                                    gap: "0.5rem",
                                    borderRadius: "5px",
                                    height: "42px",
                                    cursor: !disableReset && "pointer",
                                    "&:hover": {
                                        backgroundColor: disableReset
                                            ? "#DCDCDC"
                                            : "#043348"
                                    }
                                }}
                                onClick={resetYear}
                                disabled={disableReset}
                            >
                                <Box>
                                    <img src={resetIcon} alt="reset" />
                                </Box>
                                <FormattedMessage
                                    id="map.reset"
                                    defaultMessage="重新設定"
                                />
                            </Button>
                        </Box>
                        <Box sx={{ marginLeft: "auto" }}>
                            <FormControl fullWidth sx={{ width: "120px" }}>
                                <InputLabel
                                    id="demo-simple-select-label"
                                    sx={{ color: "#89ACBB" }}
                                >
                                    <FormattedMessage
                                        id="map.speed"
                                        defaultMessage="速度"
                                    />
                                </InputLabel>
                                <Select
                                    labelId="demo-simple-select-label"
                                    id="demo-simple-select"
                                    value={playSpeed}
                                    onChange={handleSpeedChange}
                                    size={"small"}
                                    sx={{
                                        height: 42,
                                        "& .MuiSelect-select": {
                                            height: "100%",
                                            display: "flex",
                                            alignItems: "center"
                                        }
                                    }}
                                >
                                    {speedOptions.map(option => (
                                        <MenuItem
                                            key={option.value}
                                            value={option.value}
                                        >
                                            {option.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    </Box>
                </Box>
                <Box>
                    <Box sx={{ minHeight: "100px", display: "flex" }}>
                        {/* 進度條 */}
                        <Box
                            sx={{
                                width: "100%",
                                border: "0px solid black"
                            }}
                            id={"canvas"}
                        ></Box>
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                alignItems: "center",
                                columnGap: "1rem"
                            }}
                        >
                            {isPlaying ? (
                                <IconButton
                                    onClick={handlePlayStop}
                                    disabled={isControlsDisabled}
                                    sx={{ padding: "0" }}
                                >
                                    <PauseCircleIcon
                                        sx={iconStyle(isControlsDisabled)}
                                    />
                                </IconButton>
                            ) : (
                                <IconButton
                                    onClick={handlePlayStart}
                                    disabled={isControlsDisabled}
                                    sx={{ padding: "0" }}
                                >
                                    <PlayCircleIcon
                                        sx={iconStyle(isControlsDisabled)}
                                    />
                                </IconButton>
                            )}
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    rowGap: "8px"
                                }}
                            >
                                {/* Replay controls */}
                                <IconButton
                                    onClick={handleLoopToggle}
                                    disabled={isControlsDisabled}
                                    sx={{ padding: "0" }}
                                >
                                    {loopCount < 0 ? (
                                        isControlsDisabled ? (
                                            <img
                                                src={replayDisabledIcon}
                                                alt="repeat"
                                            />
                                        ) : (
                                            <img
                                                src={replayIcon}
                                                alt="repeat"
                                            />
                                        )
                                    ) : isControlsDisabled ? (
                                        <img
                                            src={replayOnceDisabledIcon}
                                            alt="repeat"
                                        />
                                    ) : (
                                        <img
                                            src={replayOnceIcon}
                                            alt="repeat"
                                        />
                                    )}
                                </IconButton>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </Box>
        </Box>
    );
};

MapTimeLine.propTypes = {
    dataYears: PropTypes.array,
    onAnchorYearChange: PropTypes.func,
    onStartYearChange: PropTypes.func,
    onEndYearChange: PropTypes.func,
    onPlayStatusChange: PropTypes.func,
    onRestart: PropTypes.func
};

export default React.memo(MapTimeLine);
