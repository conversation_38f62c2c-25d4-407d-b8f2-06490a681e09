import React, { useEffect, useState } from "react";
import { readHkbdbData } from "../../../../api/hkbdb/Api";

import { bs64Encode } from "../../../../common/codes";

const useFetchPortrait = (
    name,
    size = "400x400",
    isCircle,
    getPortraitCircle,
    getPortrait
) => {
    const [imgSrc, setImgSrc] = useState(null);

    const imgSize = size || "400x400";
    const fetch = async () => {
        try {
            let url = isCircle ? getPortraitCircle : getPortrait;
            // url = url.replace("{name}", name).replace("{size}", imgSize);
            url = url
                .replace("{name}", bs64Encode(name))
                .replace("{size}", bs64Encode(imgSize));
            const response = await readHkbdbData(
                url,
                6 * 1000,
                false,
                false,
                undefined,
                false
            );

            //
            const { data } = response;
            const img =
                Array.isArray(data) && data.length > 0
                    ? data[0].portraitUrl
                    : "";
            setImgSrc(img);
        } catch (err) {
            console.log(err);
        }
    };
    useEffect(() => {
        if (name) {
            fetch();
        }
    }, [name]);

    return imgSrc;
};

export default useFetchPortrait;
