import React from "react";
import Box from "@mui/material/Box";
import { Link } from "react-router-dom";

import iconInfo from "../../../../images/icon_info/icon_info.svg";
import { FormattedMessage } from "react-intl";

const GuideBox = props => {
    const { tabIdx } = props;
    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <Link
                to={`map/guide?mode=${
                    tabIdx === 0 ? "mapMode" : "trackingMode"
                }`}
                target="_blank"
            >
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        gap: "0.5rem",
                        cursor: "pointer"
                    }}
                >
                    <img src={iconInfo} alt="guide" />
                    <span style={{ fontSize: "14px", color: "#4183C4" }}>
                        <FormattedMessage
                            id="map.info"
                            defaultMessage="使用說明"
                        />
                    </span>
                </Box>
            </Link>
        </Box>
    );
};

export default GuideBox;
