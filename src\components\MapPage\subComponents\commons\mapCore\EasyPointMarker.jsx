/* eslint-disable no-unused-vars, react/prop-types */
import React, { useRef } from "react";
//
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
// leaflet
import { Marker, Popup } from "react-leaflet";
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
//
import EasyMapIcon from "./EasyMapIcon.jsx";
import L from "leaflet";
// import {
//   type ICluster,
//   type IPoint,
// } from "@/app/[lang]/activity/replus2023/subComponent/TypeScriptProps.tsx";
// import { type PaletteKey } from "@/app/[lang]/activity/replus2023/subComponent/config.ts";

const fetchPointIcon = () => {
    return EasyMapIcon;
};

// interface EasyPointMarkerProps {
//     clusterIndex: string | number;
//     cluster: IPoint | ICluster;
//     latitude: number;
//     longitude: number;
//     clusterPoints?: any[];
//     isLocNameDisplay?: boolean;
//     paletteStyle?: PaletteKey;
//     onPopupOpen?: (
//         cluster: IPoint,
//         ref?: React.RefObject<L.Marker<any>>
//     ) => void;
//     onClick: (cluster: IPoint, ref?: React.RefObject<L.Marker<any>>) => void;
//     onTooltipOpen?: (cluster: IPoint) => void;
//     showPopup?: boolean;
//     showTooltip?: boolean;
//     pointIconClassName: string;
//     popupElement?: React.JSX.Element;
//     // popupElement?: React.FC<any>;
// }

const EasyPointMarker = ({
    clusterIndex,
    cluster,
    latitude,
    longitude,
    clusterPoints,
    isLocNameDisplay,
    paletteStyle,
    onPopupOpen,
    onPopupClose,
    onClick,
    onTooltipOpen,
    showPopup,
    showTooltip,
    pointIconClassName,
    popupElement
}) => {
    const ref = useRef(null);
    const { location } = cluster.properties;

    // 若定義 Marker 的 click event, 則 Popup 的 onOpen event 則會失效
    return (
        <Marker
            ref={ref}
            /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
            // @ts-expect-error
            className="pointMarker"
            key={`cluster-${clusterIndex}-${cluster.properties.locId}`}
            position={L.latLng({ lat: latitude, lng: longitude })}
            icon={fetchPointIcon()}
            eventHandlers={{
                click: e => {
                    if (isFunction(onClick)) {
                        onClick(cluster, ref);
                    }
                }
            }}
        >
            {showPopup && popupElement
                ? popupElement
                : showPopup && (
                      <Popup
                          /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
                          // @ts-expect-error
                          // onOpen={() => {
                          //     if (isFunction(onPopupOpen)) {
                          //         onPopupOpen(cluster);
                          //     }
                          // }}
                          eventHandlers={{
                              popupopen: e => {
                                  if (isFunction(onPopupOpen)) {
                                      onPopupOpen(cluster, ref);
                                  }
                              },
                              popupclose: e => {
                                  if (isFunction(onPopupClose)) {
                                      onPopupClose(cluster, ref);
                                  }
                              }
                          }}
                      >
                          <Box>
                              <Typography
                                  variant="subtitle1"
                                  sx={{ marginBottom: 1 }}
                              >
                                  {location}
                              </Typography>
                          </Box>
                      </Popup>
                  )}
        </Marker>
    );
};

export default EasyPointMarker;
