// react
import React, { useContext } from "react";

// ui
import { Dropdown } from "semantic-ui-react";

import { injectIntl } from "react-intl";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

const CustomDropdown = ({ intl }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // init options
    const options = [
        { key: "limit-1", text: "10", value: 10 },
        { key: "limit-2", text: "20", value: 20 },
        { key: "limit-3", text: "30", value: 30 },
        { key: "limit-4", text: "50", value: 50 },
        { key: "limit-5", text: "100", value: 100 }
    ];
    //
    const handleChange = (event, { value }) => {
        dispatch({
            type: Act.QUERY_PAGINATION_LIMIT_SET,
            payload: value
        });
    };
    //
    return (
        <Dropdown
            button
            basic
            floating
            options={options}
            onChange={handleChange}
            placeholder={intl.formatMessage({
                id: "query.limit",
                defaultMessage: "limit"
            })}
        />
    );
};

export default injectIntl(CustomDropdown);
