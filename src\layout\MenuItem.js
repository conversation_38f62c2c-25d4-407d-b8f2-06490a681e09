import { Link } from "react-router-dom";
import React from "react";
import { Menu } from "semantic-ui-react";

// type MenuItemProps = {
//     hasPermit: boolean,
//     name: string,
//     label: string,
//     activeItem: string | null,
//     onClick: Function
// };

export const MenuItem = ({
    hasPermit,
    name,
    label,
    activeItem,
    onClick,
    mobile,
    webStyle
}) => {
    return hasPermit ? (
        <Menu.Item
            as={Link}
            name={name}
            to={name}
            active={activeItem === name}
            onClick={onClick}
            style={{
                lineHeight: !mobile ? "35px" : "25px",
                color: webStyle.header.font.color,
                fontSize: !mobile ? webStyle.header.font.fontSize : "default"
            }}
        >
            {label}
        </Menu.Item>
    ) : null;
};
