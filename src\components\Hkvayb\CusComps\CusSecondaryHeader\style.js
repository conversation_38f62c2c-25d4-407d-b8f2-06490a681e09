import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_header_grid: props => ({
        // minWidth: "1280px",
        // margin: "0 80px 0 80px",
        height: "inherit",
        ...props.hkvayb_header_grid
    }),
    hkvayb_header_background: props => ({
        width: "100%",
        minHeight: "278px",
        backgroundImage: "url('https://fs-root.daoyidh.com/hkvayb/desktop/about_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        padding: "56px 136px 0px 136px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            minHeight: "158px",
            padding: "56px 40px 0px"
        },
        ...props.hkvayb_header_background
    }),
    hkvayb_header_title: props => ({
        margin: "0 0 24px",
        fontFamily: "NotoSansHK",
        fontSize: "32px",
        fontWeight: "500",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "0.88",
        letterSpacing: "0.51px",
        textAlign: "left",
        color: "#fff",
        ...props.hkvayb_header_title
    })
});

export default useStyles;
