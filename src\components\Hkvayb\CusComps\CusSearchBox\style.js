import { makeStyles } from "@material-ui/styles";

const searchBoxForm = {
    display: "flex",
    alignContent: "space-around",
    justifyContent: "flex-start",
    alignItems: "center"
};

const searchBoxFormDubGroup = {
    width: "85%",
    display: "flex",
    justifyContent: "space-between",
    // eslint-disable-next-line
    ["@media screen and (max-width: 600px)"]: {
        width: "100%"
    }
};

const useStyles = makeStyles({
    root: {},
    hkvayb_search_box: props => ({
        minHeight: "206px",
        margin: "56px 136px 0",
        padding: "40px 40px 16px",
        backgroundColor: "rgba(46, 41, 34, 0.6)",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            margin: "0",
            padding: "16px 16px 16px"
        },
        ...props.hkvayb_search_box
    }),
    hkvayb_search_box_form_group: props => ({
        ...searchBoxForm,
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexDirection: "column"
        },
        ...props.hkvayb_search_box_form_group
    }),
    hkvayb_search_box_form_group_margin_bottom: props => ({
        ...searchBoxForm,
        marginBottom: "24px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexDirection: "column"
        },
        ...props.hkvayb_search_box_form_group_margin_bottom
    }),
    hkvayb_search_box_form_sub_group: props => ({
        ...searchBoxFormDubGroup,
        ...props.hkvayb_search_box_form_sub_group
    }),
    hkvayb_search_box_form_sub_group_margin_bottom: props => ({
        ...searchBoxFormDubGroup,
        marginBottom: "24px",
        ...props.hkvayb_search_box_form_sub_group_margin_bottom
    })
});

export default useStyles;
