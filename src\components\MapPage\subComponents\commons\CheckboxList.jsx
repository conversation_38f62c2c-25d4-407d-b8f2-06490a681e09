import React, { useContext, useEffect, useState, useCallback } from "react";
import {
    Checkbox,
    FormControlLabel,
    FormGroup,
    Typography
} from "@mui/material";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import debounce from "../../utils/debounce";

const CheckboxList = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { mapFilterOptions } = state.map;
    const [localOptions, setLocalOptions] = useState(mapFilterOptions);

    // Debounced dispatch function
    const debouncedDispatch = useCallback(
        debounce(updatedOptions => {
            dispatch({
                type: Act.SET_MAP_FILTER_OPTIONS,
                payload: updatedOptions
            });
        }, 500),
        [dispatch]
    );

    useEffect(() => {
        setLocalOptions(mapFilterOptions);
    }, [mapFilterOptions]);

    const handleChange = event => {
        const { name, checked } = event.target;
        const updatedOptions = {
            ...localOptions,
            [name]: { ...localOptions[name], status: checked }
        };
        setLocalOptions(updatedOptions);

        debouncedDispatch(updatedOptions);
    };

    return (
        <FormGroup style={{ width: "100%" }}>
            {Object.keys(localOptions).map(item => (
                <div
                    key={item}
                    style={{
                        display: "flex",
                        justifyContent: "space-between",
                        width: "100%",
                        marginBottom: "8px"
                    }}
                >
                    <div
                        style={{
                            display: "flex",
                            alignItems: "flex-start",
                            flex: "1",
                            paddingRight: "8px"
                        }}
                    >
                        <Checkbox
                            checked={localOptions[item].status}
                            onChange={handleChange}
                            name={item}
                            sx={{
                                "&.Mui-checked": {
                                    color: "#104860"
                                },
                                padding: "2px 4px 0 0",
                                "& .MuiSvgIcon-root": {
                                    fontSize: "18px"
                                }
                            }}
                        />
                        <Typography
                            sx={{
                                fontSize: "12px",
                                lineHeight: 1.3,
                                paddingTop: "3px",
                                maxWidth: "calc(100% - 30px)"
                            }}
                        >
                            {localOptions[item].label}
                        </Typography>
                    </div>
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center",
                            height: "24px"
                        }}
                    >
                        <div
                            style={{
                                width: "8px",
                                height: "8px",
                                backgroundColor: localOptions[item].color,
                                borderRadius: "50%",
                                flexShrink: 0
                            }}
                        />
                    </div>
                </div>
            ))}
        </FormGroup>
    );
};

export default CheckboxList;
