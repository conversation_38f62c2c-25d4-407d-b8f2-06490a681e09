import React, { useContext, useEffect, useState } from "react";

import { useHistory } from "react-router-dom";

import useStyles from "./style";

import { changeLang } from "../../../../common/codes/utils/languageTools";

import Act from "../../../../store/actions";

import { Api } from "../../../../api/hkbdb/Api";

import { StoreContext } from "../../../../store/StoreProvider";

const CusLangSwitcher = ({ style = {} }) => {
    const classes = useStyles(style);
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const [, setLocalLocale] = useState(locale);
    const history = useHistory();

    const onLocaleChanged = newLocale => {
        changeLang({
            history,
            currentLanguage: locale,
            nextLanguage: newLocale
        });

        dispatch({
            type: Act.SET_USER_LOCALE,
            payload: newLocale
        });
    };

    useEffect(() => {
        setLocalLocale(locale);
    }, [locale]);

    if (locale === Api.locale_lang.LOCALE_ZH) {
        return (
            <div
                className={classes.hkvayb_lang_en}
                onClick={() => onLocaleChanged(Api.locale_lang.LOCALE_EN)}
            >
                en
            </div>
        );
    }

    return (
        <div
            className={classes.hkvayb_lang_zh}
            onClick={() => onLocaleChanged(Api.locale_lang.LOCALE_ZH)}
        >
            中
        </div>
    );
};

export default CusLangSwitcher;
