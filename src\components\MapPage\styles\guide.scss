.gisUserGuide {
  ul, ol {
    list-style-position: inside;
    margin: 1em 0;
    padding-left: 1.5em;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  blockquote {
    border-left: 4px solid #ccc;
    margin: 0 0 16px;
    padding-left: 16px;
    color: #666;
  }
  h1 {
    color: #104860;
    font-weight: 600;
    font-size: 24px;
    line-height: 125%;
    margin: 0 0 24px 0;
    span {
      font-weight: 600;
      font-size: 24px;
      line-height: 125%;
      margin: 0 0 24px 0;
    }
  }
  h2 {
    font-weight: 600;
    font-size: 16px;
    line-height: 125%;
    margin: 0 0 8px 0;
    strong {
      font-weight: 600;
      font-size: 16px;
      line-height: 125%;
      margin: 0 0 8px 0;
    }
  }
  h3 {
    font-weight: 600;
    font-size: 14px;
    line-height: 125%;
    margin: 0 0 8px 0;
    strong {
      font-weight: 600;
      font-size: 14px;
      line-height: 125%;
      margin: 0 0 8px 0;
    }
  }
  p {
    font-weight: 400;
    font-size: 14px;
    line-height: 125%;
    margin: 0 0 8px 0;
    strong {
      font-weight: 600;
      font-size: 14px;
      line-height: 125%;
      margin: 0 0 8px 0;
    }
  }
  .ql-indent-1 {
    text-indent: 2em;
  }

  .ql-indent-2 {
    text-indent: 4em;
  }

  .ql-indent-3 {
    text-indent: 6em;
  }

  .ql-indent-4 {
    text-indent: 8em;
  }

  .ql-indent-5 {
    text-indent: 10em;
  }

  .ql-indent-6 {
    text-indent: 12em;
  }

  .ql-indent-7 {
    text-indent: 14em;
  }

  .ql-indent-8 {
    text-indent: 16em;
  }

}
