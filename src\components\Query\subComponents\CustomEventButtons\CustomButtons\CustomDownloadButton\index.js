// react
import React, { useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

// custom
// import CustomGephiButton from "./CustomButtons/CustomGephiButton";
// import CustomTSVButton from "./CustomButtons/CustomTSVButton";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
// import { isEmpty } from "../../../../../../common/codes";
import { isPermitting } from "../../../../../../App-header";
import authority from "../../../../../../App-authority";
import GephiModalContent from "./CustomModal/GephiModalContent";
import DefaultModalContent from "./CustomModal/DefaultModalContent";
import { FormattedMessage, injectIntl } from "react-intl";

const fileTypes = {
    default: "",
    gephi: "gephi",
    tsv: "tsv"
};

const index = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { role } = user;
    const { queryResult } = state.query;
    const { data } = queryResult;

    //
    const [open, setOpen] = useState(false);
    const [fileType, setFileType] = useState(fileTypes.default);

    const onModalClose = () => {
        setFileType(fileTypes.default);
        setOpen(false);
    };

    //
    return (
        role &&
        authority.Query_advance &&
        isPermitting(role, authority.Query_advance) && (
            <Modal
                open={open}
                onClose={() => {
                    console.log("modal close");
                    onModalClose();
                }}
                onOpen={() => setOpen(true)}
                size={"large"}
                trigger={
                    <Button
                        color="orange"
                        floated="right"
                        size="tiny"
                        disabled={!(data && data.length > 0)}
                    >
                        <FormattedMessage
                            id="custom.downloadButton"
                            defaultMessage="Download"
                        />
                    </Button>
                }
            >
                {fileType === fileTypes.default && (
                    <DefaultModalContent
                        onGephiBtnClick={() => setFileType(fileTypes.gephi)}
                        open={open}
                        setOpen={setOpen}
                        onModalClose={onModalClose}
                    />
                )}
                {fileType === fileTypes.gephi && (
                    <GephiModalContent
                        onCancelBtnClick={() => setFileType(fileTypes.default)}
                        open={open}
                        setOpen={setOpen}
                        onModalClose={onModalClose}
                    />
                )}
            </Modal>
        )
    );
};

export default injectIntl(index);
