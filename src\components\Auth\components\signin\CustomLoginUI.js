import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { injectIntl } from "react-intl";
//
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { styled } from "@material-ui/styles";
import { useHistory } from "react-router-dom";
// component, hooks
import LoginTop from "../common/LoginTop";
import LoginBottom from "../common/LoginBottom";
import EmailLogin from "./EmailLogin";
import useLocaleRoute from "../../../../hook/useLocaleRoute";
// store
// config,utils
import { getPathById, ROUTE_ID } from "../../../../App-route";
//
import { Api } from "../../../../api/hkbdb/Api";
import ButtonMain from "../../../../common/components/button/ButtonMain";

// style

const StyledLinkBlue = styled(
    "span",
    {}
)(({ theme }) => ({
    color: "#1e70bf",
    cursor: "pointer"
}));

const CustomLoginUI = props => {
    // props
    const { intl } = props;
    const { locale } = intl;
    // route,intl
    const history = useHistory();

    // store

    // local state

    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    const handleGoSignup = () => {
        const url = handleLocaleRoute(getPathById(ROUTE_ID.Signup));
        history.push(url);
    };

    const hintForSignup = (
        <Box>
            <Typography textAlign="center">
                <span>
                    {intl.formatMessage({
                        id: "login.notyet.signup.question",
                        defaultMessage: "Have you joined yet?"
                    })}
                </span>
                <StyledLinkBlue
                    onClick={handleGoSignup}
                    style={{ paddingLeft: "8px" }}
                >
                    {intl.formatMessage({
                        id: "login.notyet.signup.go",
                        defaultMessage: "Register now"
                    })}
                </StyledLinkBlue>
            </Typography>
        </Box>
    );

    return (
        <Box
            width="368px"
            display="flex"
            flexDirection="column"
            alignItems="center"
            mb={8}
        >
            <Box
                width="100%"
                mb={5}
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent={"center"}
            >
                {/* <LoginTop /> */}

                <Typography
                    variant={"h4"}
                    sx={{
                        marginBottom: 4,
                        fontSize: "34px",
                        fontWeight: "bold"
                    }}
                >
                    {intl.formatMessage({
                        id: "login.page.title",
                        defaultMessage: "Login"
                    })}
                </Typography>

                {/* email login */}
                <EmailLogin />

                {/* {hintForSignup} */}
            </Box>

            {/* <Box width="100%"> */}
            {/*    <LoginBottom /> */}
            {/* </Box> */}
        </Box>
    );
};

CustomLoginUI.propTypes = {};

CustomLoginUI.defaultProps = {};

export default injectIntl(CustomLoginUI);
