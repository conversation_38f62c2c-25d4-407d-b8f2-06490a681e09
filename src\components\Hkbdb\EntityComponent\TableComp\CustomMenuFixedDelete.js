import React, { Fragment, useContext } from "react";

// common
import CustomSegment from "./CustomSegmentDelete";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { safeGet } from "../../../../common/codes";
import { isStringInArray } from "../commonAction";

const CustomMenuFixedDelete = ({
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    //
    const [state] = useContext(StoreContext);
    const { user, setting } = state;
    const { role } = user;
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    //
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );
    return (
        <Fragment>
            {editData.rowData
                .slice(0)
                .sort((a, b) => {
                    // sort by sorted list
                    const order = fieldPropSortedRecords.map(item =>
                        item.toUpperCase()
                    );
                    const nameA = a.propertyBindRangeStr.toUpperCase();
                    const nameB = b.propertyBindRangeStr.toUpperCase();
                    if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                    if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                    return 0;
                })
                .map(row => {
                    const { propertyBindRangeStr, rowId, values } = row;
                    const roles = safeGet(
                        fieldAttrRecords,
                        [propertyBindRangeStr, "roles"],
                        []
                    );

                    // FIXME: 也許有更好的處理方式
                    if (isStringInArray(values)) {
                        // 若為字串 (Input component)
                        row.values = [{ value: values[0], label: values[0] }];
                    }

                    if (roles.includes(role)) {
                        return (
                            <CustomSegment
                                rowIdx={rowId}
                                key={`segment-${rowId}`}
                                rowData={row}
                                editData={editData}
                                setEditData={setEditData}
                            />
                        );
                    }
                })}
        </Fragment>
    );
};

export default CustomMenuFixedDelete;
