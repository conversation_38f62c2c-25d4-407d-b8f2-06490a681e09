import { getAuthToken } from "../authentication";
import { MAX_QUERY_ITEMS } from "./config";

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;

// export type QueryOptions = {
//     db: string,
//     query: string,
//     limit: number,
//     offset?: number,
//     reasoning?: boolean,
//     url?: string
// };

function sendToServer({
    db = "hkbdb",
    query,
    offset,
    limit,
    reasoning = false,
    url = ""
}) {
    const queryParameters = {
        db: db,
        query: query,
        offset: offset > 0 ? offset : 0,
        limit: limit > MAX_QUERY_ITEMS ? MAX_QUERY_ITEMS : limit,
        reasoning
    };

    const options = {
        method: "POST",
        body: JSON.stringify(queryParameters),
        headers: {
            "content-type": "application/json"
        }
    };

    function isStoredQueryError(error) {
        return error.message.indexOf("Stored query not found") !== -1;
    }

    return new Promise((resolve, reject) => {
        appendAuthToken(options).then(() => {
            fetch(url, options)
                .then(response => {
                    response.json().then(errorJson => {
                        if (response.status !== 200) {
                            if (isStoredQueryError(errorJson)) {
                                errorJson.message = "Invalid query.";
                            }
                            reject(errorJson);
                        } else {
                            resolve(errorJson);
                        }
                    });
                })
                .catch(error => {
                    reject({
                        code: "",
                        message: "Could not make request: " + error
                    });
                });
        });
    });
}

/**
 * @deprecated
 */
export function sendQuery({ db = "hkbdb", query, offset, limit, reasoning }) {
    return sendToServer({
        db,
        query,
        offset,
        limit,
        reasoning,
        url: BACKEND_URL + "/q"
    });
}

/**
 *
 * @param db
 * @param query
 * @param offset
 * @param limit
 * @param reasoning
 * @returns {Promise<{bindings: Object[], vars: Object[]} | never>}
 */
export function sendQuery2({
    db = "hkbdb",
    query,
    offset,
    limit,
    reasoning = false
}) {
    return sendToServer({
        db,
        query,
        offset,
        limit,
        reasoning,
        url: BACKEND_URL + "/q"
    }).then(response => {
        return {
            vars: response.head.vars,
            bindings: response.results.bindings
        };
    });
}
