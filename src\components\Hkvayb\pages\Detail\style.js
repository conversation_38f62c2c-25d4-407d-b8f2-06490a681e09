import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvaby_detail: props => ({
        // margin: "56px 136px",
        padding: "56px 136px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            padding: "56px 40px"
        },
        ...props.hkvaby_detail
    }),
    hkvaby_detail_back: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "14px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.43",
        letterSpacing: "0.22px",
        textAlign: "left",
        color: "#333",
        cursor: "pointer",
        "&:hover": {
            color: "#bababc"
        },
        ...props.hkvaby_detail_back
    }),
    hkvaby_detail_category: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#b79d79",
        display: "flex",
        alignItems: "center",
        ...props.hkvaby_detail_category
    }),
    hkvaby_detail_title: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "32px",
        fontWeight: "500",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.88",
        letterSpacing: "0.51px",
        textAlign: "left",
        color: "#333",
        ...props.hkvaby_detail_title
    }),
    hkvaby_detail_content: props => ({
        margin: "44px 0 40px 0",
        ...props.hkvaby_detail_content
    })
});

export default useStyles;
