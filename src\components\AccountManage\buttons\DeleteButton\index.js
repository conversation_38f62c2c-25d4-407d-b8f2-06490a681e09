import React, { useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import { isEmpty } from "../../../../common/codes";

// api
import { deleteUser } from "../../../../api/firebase/realtimeDatabase";

const DeleteButton = ({ user }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { account } = state;
    const { changedRole } = account;
    const { uid } = changedRole;
    // button
    const [open, setOpen] = useState(false);

    const handleDelete = async () => {
        if (!isEmpty(uid)) {
            // update
            await deleteUser(uid);
            // update user role completed
            dispatch({
                type: Act.FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL,
                payload: `delete-${uid}-${new Date().getTime()}`
            });
            // alert message
            const message = {
                title: "Delete User",
                success: 1,
                error: 0,
                renderSignal: `update-user-${new Date().getTime()}`
            };
            // alert message dispatch
            dispatch({
                type: Act.DATA_MESSAGE,
                payload: message
            });
            // close modal
            setOpen(false);
        }
    };

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            trigger={<button className="ui red button">刪除</button>}
        >
            <Modal.Header>個人資料</Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description>
                    <span>確定刪除: {user.displayName}</span>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleDelete} color="green">
                    確認
                </Button>
                <Button onClick={() => setOpen(false)} color="red">
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    )
};

export default DeleteButton;
