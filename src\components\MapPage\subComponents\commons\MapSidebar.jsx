import React, { useContext, useEffect, useRef, useState } from "react";
// mui
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
//
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

// hooks
import useWindowSize from "../../../../hook/useWindowSize";
import CustomPagination from "../commons/CustomPagination/CustomPagination";
import { bs64Encode, isEmpty } from "../../../../common/codes";
import { peopleUrl } from "../../../../services/common";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import queryString from "query-string";
import { StoreContext } from "../../../../store/StoreProvider";

// this a sidebar on the right of the map
// default is hidden
// show when user click on a point on the map
// place MenuIcon on the top left corner when hidden
// place CloseIcon on the top left corner when shown

const iconStyle = {
    width: 28,
    height: 28
};
const iconButtonStyle = {
    size: "large",
    width: 52
};
const iconDisplay = {
    leftArrow: false,
    rightArrow: false
};

// GIS地圖左側選單
const MapSidebar = props => {
    const [state, _] = useContext(StoreContext);
    const { locale } = state.user;
    const { mapFilterOptions } = state.map;
    const {
        mapRef,
        timelineRef,
        pointSelected,
        variant = "trace", // Default to TraceMap variant
        isClickMapPoint,
        onClickMapPointChange
    } = props;
    const containerRef = useRef(null);
    const isTraceVariant = variant === "trace";
    const [open, setOpen] = useState(false);
    // position(top, right) for the sidebar according to the mapRef
    const [position, setPosition] = useState({ top: 0, left: 0 });
    // const [refKey, setRefKey] = useState(0); // 讓 ref 變化時觸發更新
    // size for the sidebar
    const [size, setSize] = useState({ width: 348, height: 384 });
    // context for the information of the selected point with person
    const [context, setContext] = useState(null);

    // Person selection states (only used in TraceMap variant)
    const [persons, setPersons] = useState([]);
    const [personSelected, setPersonSelected] = useState(null);
    // const [personEventIndex, setPersonEventIndex] = useState(0);

    // pages
    const pageOption = [10];
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(pageOption[0]);
    // hooks
    const winSize = useWindowSize();

    useEffect(() => {
        setCurPage(1);
    }, [totalPages]);

    useEffect(() => {
        setTotalPages(Math.ceil(context?.length / perPageNum));
    }, [context, perPageNum]);

    useEffect(() => {
        // 依據 mapRef, timelineRef 來設定 sidebar 的位置
        if (mapRef.current && timelineRef.current) {
            const {
                top,
                right,
                left,
                width,
                height
            } = mapRef.current.getBoundingClientRect();
            const {
                top: top2,
                right: right2,
                width: width2,
                height: height2
            } = timelineRef.current.getBoundingClientRect();
            // get the viewport width
            const vw = Math.max(
                document.documentElement.clientWidth || 0,
                window.innerWidth || 0
            );
            setPosition({
                top: isTraceVariant ? top + 56 : top + 12,
                left: left + iconStyle.width + 15
            });
            // setSize({
            //     width: left - iconStyle.width / 2,
            //     height: height + height2
            // });
        }
    }, [mapRef, timelineRef, winSize, isTraceVariant]);

    useEffect(() => {
        // 依據 pointSelected 來設定 sidebar 的開啟狀態
        if (pointSelected != null && isClickMapPoint) {
            setOpen(true);

            // get all the persons in the pointSelected
            const { properties } = pointSelected || {};
            const { eventList } = properties || {};

            if (Array.isArray(eventList)) {
                if (isTraceVariant) {
                    // TraceMap variant logic for person selection
                    const tmpPersons = eventList
                        .reduce((acc, event) => {
                            if (!acc.some(per => per.perId === event.perId)) {
                                acc.push({
                                    perId: event.perId,
                                    person: event.person
                                });
                            }
                            return acc;
                        }, [])
                        .sort((a, b) => a.person.localeCompare(b.person));
                    setPersons(tmpPersons);

                    if (tmpPersons.length === 1) {
                        const tmpEvent = eventList.filter(
                            event => event.perId === tmpPersons[0].perId
                        );
                        setContext(tmpEvent);
                        setPersonSelected({
                            ...tmpPersons[0]
                        });
                    } else {
                        // check if the personSelected is in the persons
                        if (personSelected != null) {
                            const tmpPerson = tmpPersons.find(
                                per => per.perId === personSelected.perId
                            );
                            if (tmpPerson) {
                                setPersonSelected(tmpPerson);
                                const tmpEvent = eventList.filter(
                                    event => event.perId === tmpPerson.perId
                                );
                                setContext(tmpEvent);
                            } else {
                                setPersonSelected(null);
                                setContext(eventList);
                            }
                        } else {
                            setPersonSelected(null);
                            setContext(eventList);
                        }
                    }
                } else {
                    // GeneralMap variant - simply use all events
                    setContext(eventList);
                }
            } else {
                if (isTraceVariant) {
                    setPersons([]);
                }
                setContext([]);
            }
        } else {
            if (isTraceVariant) {
                setPersonSelected(null);
                setPersons([]);
            }
            setContext([]);
        }
    }, [pointSelected, isTraceVariant, isClickMapPoint]);

    useEffect(() => {
        // Only for TraceMap variant
        if (isTraceVariant) {
            try {
                const { properties } = pointSelected || {};
                const { eventList } = properties || {};
                // 依據 personSelected 來設定 context
                if (personSelected != null && pointSelected != null) {
                    const tmpEvent = eventList.filter(
                        event => event.perId === personSelected.perId
                    );
                    setContext(tmpEvent);
                } else {
                    const { properties } = pointSelected || {};
                    const { eventList } = properties || {};
                    setContext(eventList);
                }
            } catch (e) {
                console.log(e);
            }
        }
    }, [personSelected, pointSelected, isTraceVariant]);

    const columns = [
        { label: ctx => ctx?.person, getValue: ctx => ctx.person },
        { label: ctx => ctx?.dateKey, getValue: ctx => ctx.date },
        { label: ctx => ctx?.endDateKey, getValue: ctx => ctx.endDate },
        { label: ctx => ctx?.addInfoDescKey, getValue: ctx => ctx.addInfoDesc },
        { label: ctx => ctx?.infoDescKey, getValue: ctx => ctx.infoDesc }
    ];

    // style for hidden sidebar with transition
    const hiddenStyle = {
        // right: `${iconButtonStyle.width / 2}px`
        width: "0px",
        height: "0px",
        minHeight: "0px"
    };
    // style for shown sidebar with transition
    const shownStyle = {
        right: `${position.right}px`,
        width: `${size.width}px`,
        height: `${size.height}px`
    };

    const handlePage = (evt, page) => {
        setCurPage(page);
    };

    const handleDDPage = (evt, data) => {
        setCurPage(data.value);
    };

    const removeStartPrefix = (id, prefix) => {
        if (id && id.startsWith(prefix)) {
            return id.replace(prefix, "");
        }
        return id;
    };

    const nameUrl = data => {
        if (isEmpty(data)) return;
        const url = peopleUrl(
            removeStartPrefix(data.perId, CLASS_PREFIX.Person),
            queryString.stringify({
                name: bs64Encode(data.person || "")
            })
        );
        return (
            <a href={url} target="_blank" rel="noreferrer">
                {data.person}
            </a>
        );
    };

    // Calculate content height based on variant
    const contentHeight = isTraceVariant
        ? `${size.height - 184}px`
        : `${size.height - 144}px`;

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.scrollTop = 0;
        }
    }, [curPage, personSelected, pointSelected]);

    useEffect(() => {
        // 當 mapFilterOptions或語系 改變時，關閉 sidebar
        setOpen(false);
        setContext([]);
    }, [mapFilterOptions, locale]);

    /**
     * 標記具有相同 ID 和 infoType 的重複項，僅針對特定類型
     * @param {Array} data - 要檢查的數據數組
     * @param {String} idKey - ID 的鍵名
     * @param {String} typeKey - 類型的鍵名
     * @returns {Array} - 標記了重複項的新數組
     */
    const markDuplicatesWithSameType = (
        data,
        idKey = "infoId",
        typeKey = "infoType"
    ) => {
        if (!Array.isArray(data) || data.length === 0) {
            return [];
        }

        // 只處理出生地點跟逝世地點
        const targetTypes = ["hasPlaceOfDeath", "hasPlaceOfBirth"];

        // 用於計數的對象
        const countMap = {};

        // 計算每個 ID+infoType 組合的出現次數，僅針對特定類型
        data.forEach((item, index) => {
            const id = item[idKey];
            const type = item[typeKey];

            // 只處理特定類型
            if (id && type && targetTypes.includes(type)) {
                const key = `${id}_${type}`;
                if (!countMap[key]) {
                    countMap[key] = {
                        count: 0,
                        indices: []
                    };
                }
                countMap[key].count += 1;
                countMap[key].indices.push(index);
            }
        });

        // 創建一個新數組，避免修改原始數據
        const newData = data.map(item => ({ ...item }));

        // 標記重複項
        Object.keys(countMap).forEach(key => {
            if (countMap[key].count > 1) {
                // 對於每個重複的組合，標記所有項目
                countMap[key].indices.forEach(index => {
                    newData[index].duplicate = "true";
                });
            }
        });

        return newData;
    };

    return (
        <Box
            sx={{
                position: "absolute",
                top: position.top,
                left: position.left,
                ...(open ? shownStyle : hiddenStyle),
                backgroundColor: "white",
                boxShadow: "0 0 10px rgba(0,0,0,0.2)",
                zIndex: "1000",
                display: "flex",
                flexDirection: "column",
                justifyContent: "flex-start",
                alignItems: "center",
                // transition: "all 0.3s",
                padding: open ? "12px" : "0",
                borderRadius: "8px"
            }}
        >
            {open ? (
                <IconButton
                    size={iconButtonStyle.size}
                    sx={{
                        position: "absolute",
                        top: "0px",
                        left: `-${iconButtonStyle.width / 2 + 10}px`,
                        cursor: "pointer",
                        backgroundColor: "#104860",
                        border: "1px solid #6C5151",
                        borderRadius: "0",
                        height: iconStyle.height,
                        width: iconStyle.width,
                        "&:hover": {
                            backgroundColor: "#104860",
                            opacity: 1
                        }
                    }}
                    onClick={() => {
                        setOpen(false);
                        // 點擊mappoint狀態改為false
                        onClickMapPointChange();
                    }}
                >
                    <CloseIcon style={{ color: "white" }} />
                </IconButton>
            ) : (
                <IconButton
                    size={iconButtonStyle.size}
                    sx={{
                        position: "absolute",
                        top: "0px",
                        left: `-${iconButtonStyle.width / 2 + 10}px`,
                        cursor: "pointer",
                        backgroundColor: "white",
                        border: "1px solid #89ACBB",
                        borderRadius: "0",
                        height: iconStyle.height,
                        width: iconStyle.width,
                        "&:hover": {
                            backgroundColor: "white",
                            opacity: 1
                        }
                    }}
                    onClick={() => setOpen(true)}
                >
                    <MenuIcon sx={{ color: "#336F89" }} />
                </IconButton>
            )}
            {/* left arrow icon button: half area of icon overlaps with sidebar */}
            {open && (
                <>
                    {pointSelected?.properties.location ? (
                        <Box
                            sx={{
                                width: "100%",
                                height: "100%"
                            }}
                        >
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                    marginBottom: 1,
                                    columnGap: 1,
                                    alignItems: "center"
                                }}
                            >
                                <Typography
                                    variant="h5"
                                    sx={{
                                        color: "rgb(16, 72, 96)",
                                        fontSize: "16px",
                                        fontWeight: "600",
                                        whiteSpace: "nowrap",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        maxWidth: "300px",
                                        "&:hover": {
                                            cursor: "pointer"
                                        }
                                    }}
                                    title={pointSelected?.properties.location} // 原生HTML tooltip
                                >
                                    {pointSelected?.properties.location}
                                </Typography>
                            </Box>

                            {/* Person selection section - only for TraceMap variant */}
                            {isTraceVariant && persons.length > 0 && (
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "flex-start",
                                        marginBottom: 1,
                                        alignItems: "center",
                                        columnGap: 2
                                    }}
                                >
                                    <Stack
                                        direction="row"
                                        spacing={1}
                                        sx={{
                                            flexWrap: "wrap"
                                        }}
                                    >
                                        {persons.map((per, idx) => (
                                            <Box
                                                key={idx}
                                                sx={{
                                                    padding: "16px",
                                                    height: "31px",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    cursor: "pointer",
                                                    backgroundColor:
                                                        personSelected?.perId ===
                                                            per.perId
                                                            ? "#104860"
                                                            : "#F3F3F3",
                                                    color:
                                                        personSelected?.perId ===
                                                            per.perId
                                                            ? "#FFFFFF"
                                                            : "#104860",
                                                    transition:
                                                        "background-color 0.1s, color 0.1s",
                                                    borderRadius: "4px"
                                                }}
                                                onClick={() => {
                                                    if (
                                                        personSelected?.perId ===
                                                        per.perId
                                                    ) {
                                                        setPersonSelected(null);
                                                    } else {
                                                        setPersonSelected(per);
                                                    }
                                                }}
                                            >
                                                <Typography
                                                    variant="body1"
                                                    sx={{ textAlign: "center" }}
                                                >
                                                    {per.person}
                                                </Typography>
                                            </Box>
                                        ))}
                                    </Stack>
                                </Box>
                            )}

                            <Box
                                sx={{
                                    overflowY: "auto",
                                    width: "100%",
                                    marginBottom: "12px",
                                    height: contentHeight,
                                    "&::-webkit-scrollbar": {
                                        width: "5px"
                                    },
                                    "&::-webkit-scrollbar-thumb": {
                                        borderRadius: "10px"
                                    },
                                    border: "1px solid #F3F3F3",
                                    padding: "4px 8px",
                                    borderRadius: "4px"
                                }}
                                ref={containerRef}
                            >
                                <Box>
                                    {Array.isArray(context) &&
                                        context.length > 0 &&
                                        markDuplicatesWithSameType(context)
                                            .slice(
                                                (curPage - 1) * perPageNum,
                                                curPage * perPageNum
                                            )
                                            .sort((a, b) => {
                                                const yearA =
                                                    a.year || a.endYear;
                                                const yearB =
                                                    b.year || b.endYear;
                                                if (yearA === yearB) {
                                                    return a.person
                                                        .toLowerCase()
                                                        .localeCompare(
                                                            b.person.toLowerCase()
                                                        );
                                                }
                                                return yearA.localeCompare(
                                                    yearB
                                                );
                                            })
                                            .map((ctx, ctxIdx) => (
                                                <Grid
                                                    key={ctxIdx}
                                                    container
                                                    columnSpacing={1}
                                                    flexDirection={"column"}
                                                    alignItems={"flex-start"}
                                                    sx={{
                                                        marginBottom: "16px",
                                                        "&:not(:last-child)": {
                                                            borderBottom:
                                                                "1px solid #F3F3F3"
                                                        },
                                                        marginLeft: "0",
                                                        paddingBottom: "8px"
                                                    }}
                                                >
                                                    {columns
                                                        .filter(
                                                            col =>
                                                                col.label(
                                                                    ctx
                                                                ) &&
                                                                col.getValue(
                                                                    ctx
                                                                )
                                                        )
                                                        .map((col, colIdx) => (
                                                            <Grid
                                                                key={colIdx}
                                                                item
                                                                xs={12}
                                                                sm={12}
                                                                md={12}
                                                            >
                                                                <Typography
                                                                    variant="subtitle1"
                                                                    sx={{
                                                                        display:
                                                                            "inline-block",
                                                                        marginRight:
                                                                            "8px",
                                                                        fontSize:
                                                                            colIdx ===
                                                                                0
                                                                                ? "14px"
                                                                                : "12px",
                                                                        color:
                                                                            colIdx ===
                                                                            0 &&
                                                                            "#4183C4",
                                                                        fontWeight:
                                                                            colIdx ===
                                                                            0 &&
                                                                            "600"
                                                                    }}
                                                                >
                                                                    {colIdx ===
                                                                        0 &&
                                                                        nameUrl(
                                                                            ctx
                                                                        )}
                                                                    {colIdx !==
                                                                        0 && (
                                                                            <>
                                                                                {col.label(
                                                                                    ctx
                                                                                )}
                                                                                ：
                                                                                {col.getValue(
                                                                                    ctx
                                                                                )}
                                                                            </>
                                                                        )}
                                                                </Typography>
                                                            </Grid>
                                                        ))}
                                                    {ctx?.duplicate && (
                                                        <Grid item xs={12}>
                                                            <Typography
                                                                variant="body2"
                                                                sx={{
                                                                    fontSize:
                                                                        "12px",
                                                                    fontStyle:
                                                                        "italic",
                                                                    color:
                                                                        "#666",
                                                                    backgroundColor:
                                                                        "#f5f5f5",
                                                                    padding:
                                                                        "4px 8px",
                                                                    borderLeft:
                                                                        "3px solid #104860",
                                                                    marginTop:
                                                                        "4px",
                                                                    marginBottom:
                                                                        "8px",
                                                                    borderRadius:
                                                                        "0 4px 4px 0"
                                                                }}
                                                            >
                                                                <FormattedMessage
                                                                    id="map.sidebar.duplicate.note"
                                                                    defaultMessage="請點擊人名以查閱不同日期的資料來源。"
                                                                />
                                                            </Typography>
                                                        </Grid>
                                                    )}
                                                </Grid>
                                            ))}
                                </Box>
                            </Box>
                            <CustomPagination
                                currentPage={curPage}
                                totalPages={totalPages}
                                handlePage={handlePage}
                                handleDDPage={handleDDPage}
                            />
                        </Box>
                    ) : (
                        <Box>
                            <Typography
                                variant="h5"
                                textAlign={"center"}
                                sx={{ marginBottom: 2 }}
                            >
                                <FormattedMessage
                                    id="map.sidebar.no.info"
                                    defaultMessage="目前沒有資訊可以顯示"
                                />
                            </Typography>
                            <Typography
                                variant="subtitle1"
                                textAlign={"center"}
                                sx={{ color: "rgb(16, 72, 96)" }}
                            >
                                <FormattedMessage
                                    id="map.sidebar.search.hint"
                                    defaultMessage="請搜尋人物並選取地圖中的地點"
                                />
                            </Typography>
                        </Box>
                    )}
                </>
            )}
        </Box>
    );
};

MapSidebar.propTypes = {
    mapRef: PropTypes.object,
    timelineRef: PropTypes.object,
    pointSelected: PropTypes.object,
    carouselRef: PropTypes.object,
    alertRef: PropTypes.object,
    personEventList: PropTypes.array,
    onClickLeftArrow: PropTypes.func,
    onClickRightArrow: PropTypes.func,
    variant: PropTypes.oneOf(["trace", "general"])
};

export default MapSidebar;
