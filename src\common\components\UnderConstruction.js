import React from "react";
import {
    Card,
    Container,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>
} from "semantic-ui-react";
import PropTypes from "prop-types";

const UnderConstruction = props => {
    const { title, cardHeader, cardDescription } = props;
    return (
        <React.Fragment>
            <Grid centered>
                <Grid.Row>
                    <Grid.Column width={10}>
                        <Header textAlign={"center"} as={"h3"}>
                            {title}
                        </Header>
                    </Grid.Column>
                </Grid.Row>
                <Grid.Row>
                    <Grid.Column width={6}>
                        <Card fluid centered>
                            <Image
                                src={"/img/data-preparation1.jpg"}
                                size={"large"}
                            />
                            <Card.Content>
                                <Card.Header>{cardHeader}</Card.Header>
                                {cardDescription && (
                                    <Card.Description>
                                        {cardDescription}
                                    </Card.Description>
                                )}
                            </Card.Content>
                        </Card>
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </React.Fragment>
    );
};

UnderConstruction.defaultProps = {
    title: "",
    cardHeader: "",
    cardDescription: ""
};

UnderConstruction.propTypes = {
    title: PropTypes.string,
    cardHeader: PropTypes.string,
    cardDescription: PropTypes.string
};

export default UnderConstruction;
