import React from "react";

// common
import { isEmpty, isNumeric } from "../../../../common/codes";

const CustomDate = ({ value, format }) => {
    //
    const date = [...`${value}`]
        .map(num => (isNumeric(num) ? num : ""))
        .join("");
    //
    const year = date.slice(0, 4) || "";
    const month = date.slice(4, 6) || "00";
    const day = date.slice(6, 8) || "00";
    //
    if (isEmpty(year)) return value;
    //
    switch (format) {
        case "yyyy":
            return year;
        case "yyyy-MM":
            return `${year}-${month}`;
        case "yyyy-MM-dd":
            return `${year}-${month}-${day}`;
    }
    //
    return value;
};

export default CustomDate;
