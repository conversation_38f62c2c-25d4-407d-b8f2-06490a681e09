import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_dualPages: props => ({
        display: "flex",
        textAlign: "justify",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexDirection: "column"
        },
        ...props.hkvayb_dualPages
    }),
    hkvayb_dualPages_left: props => ({
        width: "50%",
        borderStyle: "groove",
        padding: "10px",
        // marginRight: "8px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            width: "100%"
        },
        ...props.hkvayb_dualPages_left
    }),
    hkvayb_dualPages_right: props => ({
        width: "50%",
        borderStyle: "groove",
        padding: "10px",
        // marginLeft: "8px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            width: "100%"
        },
        ...props.hkvayb_dualPages_right
    })
});

export default useStyles;
