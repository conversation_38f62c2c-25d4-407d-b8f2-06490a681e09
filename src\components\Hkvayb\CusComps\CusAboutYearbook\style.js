import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_about_yearbook: props => ({
        padding: "80px 0 80px 0",
        display: "flex",
        margin: "0 136px",
        alignItems: "center",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            padding: "0",
            margin: "40px",
            display: "flex",
            flexDirection: "column"
        },
        ...props.hkvayb_about_yearbook
    }),
    hkvayb_about_yearbook_image: props => ({
        minWidth: "336px",
        height: "336px",
        margin: "2px 80px 2px 0",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/yearbook_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            margin: "0"
        },
        ...props.hkvayb_about_yearbook_image
    }),
    hkvayb_about_yearbook_describe: props => ({
        minHeight: "164px",
        margin: "24px 0",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#333",
        ...props.hkvayb_about_yearbook_describe
    })
});

export default useStyles;
