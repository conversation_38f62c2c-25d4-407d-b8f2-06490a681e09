import React, { useContext, useEffect, createRef, useState } from "react";
import { Icon, Label, Menu, Table } from "semantic-ui-react";

const OntoTable = ({ ontoData, myRef }) => {
    const _ontoData = new Array(100);
    for (let i = 0; i < _ontoData.length; i++) {
        _ontoData[i] = i * 2;
    }

    return (
        <div
            ref={myRef}
            style={{
                display: "flex",
                justifyContent: "center"
            }}
        >
            <Table
                celled
                fixed
                columns={3}
                color={"green"}
                style={{ width: "550px" }}
            >
                <Table.Header>
                    <Table.Row>
                        <Table.HeaderCell>Header</Table.HeaderCell>
                        <Table.HeaderCell>Header</Table.HeaderCell>
                        <Table.HeaderCell>Header</Table.HeaderCell>
                    </Table.Row>
                </Table.Header>

                <Table.Body>
                    {_ontoData.map((data, i) => (
                        <Table.Row key={Math.random() * 10000}>
                            <Table.Cell>
                                {i * 20000000000000000002000000000000000000}
                            </Table.Cell>
                            <Table.Cell>
                                {i * 20000000000000000002000000000000000000 + 1}
                            </Table.Cell>
                            <Table.Cell>
                                {i * 20000000000000002000000000000000000000 + 2}
                            </Table.Cell>
                        </Table.Row>
                    ))}
                </Table.Body>
            </Table>
        </div>
    );
};

export default OntoTable;
