import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_title: props => ({
        width: "414px",
        height: "49px",
        margin: "0 178px 23px 0",
        fontFamily: "NotoSansHK",
        fontSize: "34px",
        fontWeight: "500",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.41",
        letterSpacing: "0.54px",
        textAlign: "left",
        color: "#333",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            fontSize: "24px",
            width: "100%",
            margin: "0",
            whiteSpace: "nowrap"
        },
        ...props.hkvayb_title
    })
});

export default useStyles;
