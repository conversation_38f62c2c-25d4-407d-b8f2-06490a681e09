import { Api, readHkbdbData } from "../../../api/hkbdb/Api";

const customSort = (array, key) => {
    return array.sort(function(a, b) {
        const nameA = a[key].toLowerCase();
        const nameB = b[key].toLowerCase();
        if (nameA < nameB)
            // sort string ascending
            return -1;
        if (nameA > nameB) return 1;
        return 0; // default return value (no sorting)
    });
};

export const queryRelationOP = () => {
    const api = Api.getRelationOP();
    return new Promise((resolve, reject) => {
        readHkbdbData(api)
            .then(response => {
                let sortData = response.data;
                // 要排序的 key, 依序排序
                const sortKeys = ["op", "subPropertyOf"];
                sortKeys.forEach(key => {
                    sortData = customSort(sortData, key);
                });
                resolve(sortData);
            })
            .catch(err => {
                reject(err);
            });
    });
};
