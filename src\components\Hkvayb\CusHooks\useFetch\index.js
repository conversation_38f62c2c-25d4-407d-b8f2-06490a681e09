import { useState, useEffect } from "react";

import axios from "axios";

const useFetch = url => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");

    useEffect(() => {
        setLoading(true);
        setData([]);
        setError("");
        const source = axios.CancelToken.source();
        axios
            .get(url, { cancelToken: source.token })
            .then(res => {
                setLoading(false);
                res.data && setData(res.data || []);
            })
            .catch(() => {
                setLoading(false);
                setData([]);
                setError("An error occurred.");
            });
        return () => {
            source.cancel();
        };
    }, [url]);

    return { data, loading, error };
};

export default useFetch;
