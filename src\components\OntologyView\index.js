import React, { createRef, useState, useContext, useEffect } from "react";
import { Accordion, Grid, Container, Icon } from "semantic-ui-react";
import { ResponsiveContainer } from "../../layout/Layout";
import ontoConfig from "./config/ontoConfig";
import { CustomAccordion } from "./subComponents/subComponents";

import { StoreContext } from "../../store/StoreProvider";
import Act from "../../store/actions";
import { FormattedMessage } from "react-intl";
import DrawDisplay from "./subComponents/customDisplay/DrawDisplay";
import PropRefDisplay from "./subComponents/customDisplay/PropRefDisplay";
import OntologyDisplay from "./subComponents/customDisplay/OntologyDisplay";
import RelationOPDisplay from "./subComponents/customDisplay/RelationOPDisplay";
import ClassCategory from "./subComponents/ClassCategory";
//
import { queryRelationOP } from "./ontoUtils/ontoService";

// 調整左邊欄位要顯示的選單
const tabs = {
    default: {
        name: "Graph",
        key: "Graph-default",
        inLeftMenu: false // 是否顯示在左邊 menu
    },
    graph: {
        name: "Graph",
        key: "Graph",
        inLeftMenu: false // 是否顯示在左邊 menu
    },
    propRefs: {
        name: (
            <FormattedMessage
                id={"ontology.propertyReference"}
                defaultMessage={"Property reference"}
            />
        ),
        key: "propRefs",
        inLeftMenu: true // 是否顯示在左邊 menu
    },
    relationOP: {
        name: (
            <FormattedMessage
                id={"ontology.relationOP"}
                defaultMessage={"Relation"}
            />
        ),
        key: "relation",
        inLeftMenu: true // 是否顯示在左邊 menu
    },
    ontology: {
        name: (
            <FormattedMessage
                id={"ontology.ontology"}
                defaultMessage={"Ontology"}
            />
        ),
        key: "ontology",
        inLeftMenu: false // 是否顯示在左邊 menu
    }
};

const OntologyView = ({ ...props }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { property } = state;
    const { propertyRef, curProperties } = property;

    /* eslint-disable no-unused-vars */
    const [contextRef, setContextRef] = useState(createRef());
    const [active, setActive] = useState("Person");
    const [displayList, setDisplayList] = useState(false);
    const [curTab, setCurTab] = useState(tabs.default.name);
    const handleContextRef = cntref => setContextRef(cntref);
    // todo: 改成設定成 global
    const [relationOP, setRelaitonOP] = useState(null);

    // 建立 ref , 用於匯出 SNA image
    const exportImgRef = createRef();
    const ontoTableRef = createRef();

    const allGraphList = ontoConfig.graphList;
    const [activeIndex, setActiveIndex] = useState(0);
    const handleClick = (e, titleProps) => {
        const { index, type } = titleProps;
        const newIndex = activeIndex === index ? -1 : index;
        setActiveIndex(newIndex);
        setActive(type);
    };

    // save classNames to global
    useEffect(() => {
        dispatch({
            type: Act.ONTO_CLASS_NAMES,
            payload: allGraphList.map(info => ({
                name: info.name,
                className: info.className
            }))
        });
    }, []);

    // 將 className 轉換成 中英文 list, 用於中英文切換
    const classNameIntl18 = allGraphList.reduce((initObj, cur) => {
        initObj[cur.className] = { en: cur.className, zh: cur.name };
        return initObj;
    }, {});

    useEffect(() => {
        // 依當前選取的 className, 判斷當前的 subComponent
        const _found = allGraphList.find(info => {
            return info.className === active;
        });
    }, [active]);

    useEffect(() => {
        if (!propertyRef) return;
        const refKeys = Object.keys(propertyRef);
    }, [propertyRef]);

    useEffect(() => {
        if (!curProperties || (curProperties && curProperties.length === 0)) {
            setDisplayList(false);
        } else {
            // 當 curProperties 改變時, display list
            setDisplayList(true);
        }
    }, [curProperties]);

    // 用於控制 curPropList 的 CustomAccordion 是否要展開
    const onClickList = () => {
        setDisplayList(!displayList);
    };

    // get relation op
    useEffect(() => {
        queryRelationOP()
            .then(data => {
                setRelaitonOP(data);
            })
            .catch(err => {
                console.log(err);
            });
    }, []);

    const accContent = (
        <Accordion>
            {ontoConfig.ontoTitle.map((item, idx) => {
                const { title, type, content } = item;
                const isActive = activeIndex === idx;
                return (
                    <React.Fragment key={`react-fragment-${idx}`}>
                        <Accordion.Title
                            key={`accordion-title-${idx}`}
                            active={isActive}
                            index={idx}
                            type={type}
                            onClick={handleClick}
                        >
                            <Icon name="dropdown" />
                            {title}
                        </Accordion.Title>
                        <Accordion.Content
                            key={`accordion-content-${idx}`}
                            active={isActive}
                        >
                            {isActive && (
                                <ClassCategory
                                    active={active}
                                    setActive={setActive}
                                    infoList={content}
                                    onMenuClick={() =>
                                        setCurTab(tabs.graph.name)
                                    }
                                />
                            )}
                        </Accordion.Content>
                    </React.Fragment>
                );
            })}
        </Accordion>
    );
    return (
        <ResponsiveContainer {...props}>
            <Container id={"id_content"} style={{ paddingBottom: "100px" }}>
                <div ref={handleContextRef}>
                    <Grid divided>
                        <Grid.Row>
                            <Grid.Column width={3}>
                                {/* 左邊欄位 Class List */}
                                <div
                                    style={{
                                        position: "sticky",
                                        top: "80px",
                                        minHeight: "100px"
                                    }}
                                >
                                    {/* Accordion Content */}
                                    {accContent}
                                    <hr />

                                    {/* 左邊欄位 List : graph, property refs, ontology */}
                                    {Object.values(tabs)
                                        .filter(tab => tab.inLeftMenu)
                                        .map(ta => (
                                            <CustomAccordion
                                                key={ta.key}
                                                data={{
                                                    title: ta.name,
                                                    content: ""
                                                }}
                                                onClick={() =>
                                                    setCurTab(ta.name)
                                                }
                                                active={curTab === ta.name}
                                                // width={"200px"}
                                                useIcon={false}
                                                useContent={false}
                                            />
                                        ))}
                                </div>
                            </Grid.Column>

                            {/* 右邊顯示區 */}
                            <Grid.Column width={13}>
                                {/* display graph */}
                                {curTab === tabs.graph.name && (
                                    <DrawDisplay
                                        ref={exportImgRef}
                                        found={active}
                                    />
                                )}
                                {/* display property list */}
                                {curTab === tabs.propRefs.name && (
                                    <PropRefDisplay propertyRef={propertyRef} />
                                )}
                                {/* relation op list */}
                                {curTab === tabs.relationOP.name && (
                                    <RelationOPDisplay
                                        relationOP={relationOP}
                                    />
                                )}
                                {curTab === tabs.ontology.name && (
                                    <OntologyDisplay
                                        ontoTableRef={ontoTableRef}
                                    />
                                )}
                            </Grid.Column>
                        </Grid.Row>
                    </Grid>
                </div>
            </Container>
        </ResponsiveContainer>
    );
};

export default OntologyView;
