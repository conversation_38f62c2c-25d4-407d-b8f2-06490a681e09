import Act from "../actions";

const initState = {
    propertyObj: {},
    ontology: {},
    ontoClassNames: [],
    propertyRef: null,
    // {'ref1':{active:true, properties: [property1, property2,...]},
    // 'ref2': {active:false, properties: [property1, property2,...]},}
    curProperties: null, // { propertyRef: "", properties: [] }
    activePropRef: "", // "ref1"
    ontologyDefined: {},
    drawOntologyData: null // {"Person": {links: [], nodes: []}, "Place": {...},...}
};

const propertyReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.PROPERTY:
            return Object.assign({}, state, { propertyObj: action.payload });
        case Act.PROPERTY_CLEAN:
            return {};
        case Act.PROTEGE_GET_DATA:
            return Object.assign({}, state, { ontology: action.payload });
        case Act.PROTEGE_DEFINED_DATA_SET:
            return { ...state, ontologyDefined: action.payload };
        case Act.ONTO_PROPERTY_REF:
            return { ...state, propertyRef: action.payload };
        case Act.ONTO_CUR_PROPERTIES:
            return { ...state, curProperties: action.payload };
        case Act.ONTO_ACTIVE_PROP_REF:
            return { ...state, activePropRef: action.payload };
        case Act.DRAW_ONTO_DATA:
            return { ...state, drawOntologyData: action.payload };
        case Act.ONTO_CLASS_NAMES:
            return { ...state, ontoClassNames: action.payload };
        default:
            return state;
    }
};

export default propertyReducer;
