import React, { useContext, useState, useEffect } from "react";

import { TextField, MenuItem } from "@material-ui/core";

import { makeStyles } from "@material-ui/core/styles";

import Alert from "@material-ui/lab/Alert";

import { isEmpty } from "../../../../../../common/codes";

import { StoreContext } from "../../../../../../store/StoreProvider";

import act from "../../../../../../store/actions";

import CustomDebounce from "../../../../../../common/components/CustomDeBounce";

const useStyles = makeStyles(theme => ({
    root: {
        "& .MuiTextField-root": {
            margin: theme.spacing(1),
            width: "23.1ch"
        }
    }
}));

const alertMsg = "目前無任何「進階選項」可供選擇。";

const CusTextFields = ({ data }) => {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { reset, fields } = state.searchPage2;
    const [fieldState, setFieldState] = useState({ ...fields });

    const debInputTitle = CustomDebounce(fieldState.title, 500);
    const debInputAuthor = CustomDebounce(fieldState.author, 500);
    const debInputMechanism = CustomDebounce(fieldState.mechanism, 500);

    const handleOnChange = event => {
        const value = event.target.value;
        setFieldState({
            ...fieldState,
            [event.target.name]: value
        });
    };

    useEffect(() => {
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_FIELDS,
            payload: fieldState
        });
    }, [debInputTitle, debInputAuthor, debInputMechanism]);

    useEffect(() => {
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_FIELDS,
            payload: fieldState
        });
    }, [
        fieldState.year,
        fieldState.area,
        fieldState.media,
        fieldState.talksSymposiumType,
        fieldState.publicationType,
        fieldState.awardEventType,
        fieldState.educationType,
        fieldState.auctionType
    ]);

    useEffect(() => {
        setFieldState(prevState => ({
            ...Object.keys(prevState).reduce(
                (prevObj, key) => ({ ...prevObj, [key]: "" }),
                {}
            )
        }));
        dispatch({ type: act.SET_SEARCHPAGE_SEARCHBAR_FIELDS });
    }, [reset]);

    if (isEmpty(data)) {
        return (
            <Alert
                style={{
                    width: "100%",
                    marginLeft: "15px",
                    marginRight: "15px"
                }}
                severity="warning"
            >
                {alertMsg}
            </Alert>
        );
    }

    return (
        <form className={classes.root} noValidate autoComplete="off">
            {/* <pre>{JSON.stringify(fieldState, null, 2)}</pre> */}
            <div>
                {data &&
                    data.map((item, itemIdx) => {
                        return (
                            <TextField
                                id={`outlined-search-${itemIdx}`}
                                key={`outlined-search-${item.value}-${itemIdx}`}
                                select={item.type === "select"}
                                size={"small"}
                                label={item.label}
                                name={item.value}
                                type={item.type}
                                variant="outlined"
                                value={fieldState[item.value]}
                                onChange={handleOnChange}
                            >
                                {item &&
                                    item.options &&
                                    item.options.map(option => (
                                        <MenuItem
                                            key={option.value}
                                            value={option.value}
                                        >
                                            {option.label}
                                        </MenuItem>
                                    ))}
                            </TextField>
                        );
                    })}
            </div>
        </form>
    );
};

export default CusTextFields;
