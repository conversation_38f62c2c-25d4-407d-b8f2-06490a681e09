import React, { Fragment, useContext, useState, useEffect } from "react";

// ui
import { Message } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

const CustomAlertMessage = () => {
    //
    const [open, setOpen] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // get message
    const { title, content, type, renderSignal } = state.message;
    // ttl
    const ttl = 3 * 1000;
    //
    const CustomMessage = ({ ...rest }) => {
        const customMsgStyle = {
            // position: "fixed",
            // top: "16%",
            // left: "43.4%"
        };
        const handleClose = () => {
            setOpen(false);
        };
        return (
            <Message
                {...rest}
                style={customMsgStyle}
                header={title}
                content={content}
                onDismiss={handleClose}
            />
        );
    };
    //
    const SwitchMessage = () => {
        switch (type) {
            case "info":
                return <CustomMessage info />;
            case "warning":
                return <CustomMessage warning />;
            case "error":
                return <CustomMessage error />;
            default:
                return <CustomMessage />;
        }
    };
    useEffect(
        () => {
            if (title) {
                // open
                setOpen(true);
                // Set debouncedValue to value (passed in) after the specified delay
                const handler = setTimeout(() => {
                    // close
                    setOpen(false);
                    // clean
                    dispatch({ type: Act.MESSAGE_NOTIFICATION_CLE });
                }, ttl);
                return () => {
                    clearTimeout(handler);
                };
            }
        },
        // Only re-call effect if value changes
        [renderSignal]
    );
    //
    return <Fragment>{open ? <SwitchMessage /> : null}</Fragment>;
};

export default CustomAlertMessage;
