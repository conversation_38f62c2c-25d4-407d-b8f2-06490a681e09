import React, { useContext, useEffect, useState } from "react";

// ui
import { Accordion, Icon } from "semantic-ui-react";

// lang
import { injectIntl } from "react-intl";
import { intlMsgs } from "../../EntityComponent/entityIntlMsgs";

// custom
import CustomFixedTable from "./CustomFixedTable";
import CustomFlexibleTable from "./CustomFlexibleTable";

// type
import PropTypes from "prop-types";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { isNotEmpty } from "../../../../common/codes";

// action
import { queryOrgInfoV2, reloadInfo } from "../action";
import config from "../../../../config/config";

//
import "./Information.scss";

const handleShowAccor = (roleIn, authority) => {
    if (!roleIn || !Array.isArray(authority)) return false;
    return authority.includes(roleIn);
};

const information = ({ intl, name, permission }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { renderTarget, renderSignal } = state.information;
    const { personInformation: perInfo } = state;
    const { role: userRole } = state.user;

    // active on change
    const [activeIndex, setActiveIndex] = useState(0);
    // lang
    const { formatMessage } = intl;

    // handle click
    const handleClick = (e, titleProps) => {
        const { index } = titleProps;
        const newIndex = activeIndex === index ? -1 : index;
        setActiveIndex(newIndex);
    };

    // reload data
    useEffect(() => {
        if (
            isNotEmpty(name) &&
            isNotEmpty(renderTarget) &&
            isNotEmpty(renderSignal)
        ) {
            if (
                ["organization", "relationevent", "namenode"].includes(
                    renderTarget
                )
            ) {
                reloadInfo(name, renderTarget, dispatch);
            } else {
                queryOrgInfoV2(name, renderTarget, -1, 0, null, dispatch);
            }
        }
    }, [name, renderSignal]);

    const AccordionsNameNodes = "accordions-namenode";
    // components
    const accordions = [
        {
            id: "accordions-basic",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["basic"]),
            component: (
                <CustomFixedTable
                    data={perInfo?.organization || []}
                    loading={perInfo.fetchDataLoading}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_ORG_TYPE}
                />
            )
        },
        {
            id: AccordionsNameNodes,
            authority: config.NNIAuthority,
            title: formatMessage(intlMsgs["nameNode_org"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_NNI_TYPE}
                />
            )
        },
        {
            id: "accordions-member",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["member"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_MEM_TYPE}
                />
            )
        },
        {
            id: "accordions-publication",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["publication"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_PUB_TYPE}
                />
            )
        },
        {
            id: "accordions-article",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["article"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_ART_TYPE}
                />
            )
        },
        {
            id: "accordions-otherwork",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["otherwork"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_OTW_TYPE}
                />
            )
        },
        {
            id: "accordions-event",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["event"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_ORG_DOMAIN}
                    ontologyType={config.DEF_EVT_TYPE}
                />
            )
        }
    ];

    const filteredAccordions = config.getNNIPermission(userRole)
        ? accordions
        : accordions.filter(item => item.id !== AccordionsNameNodes);

    return (
        <div className={"information"}>
            <Accordion>
                {filteredAccordions.map((item, idx) => {
                    const { key, id, title, component, authority } = item;
                    const isActive = activeIndex === idx;
                    return (
                        handleShowAccor(userRole, authority) && (
                            <React.Fragment key={`react-fragment-${idx}`}>
                                <Accordion.Title
                                    key={`accordion-title-${key}`}
                                    active={isActive}
                                    index={idx}
                                    onClick={handleClick}
                                >
                                    <Icon name="dropdown" />
                                    {title}
                                </Accordion.Title>
                                <Accordion.Content
                                    key={`accordion-content-${key}`}
                                    active={isActive}
                                    className={`Content__${id}`}
                                >
                                    {isActive && component}
                                </Accordion.Content>
                            </React.Fragment>
                        )
                    );
                })}
            </Accordion>
        </div>
    );
};
information.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    permission: PropTypes.number
};
export default injectIntl(information);
