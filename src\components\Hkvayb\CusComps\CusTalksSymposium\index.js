import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusAlbum from "../CusAlbum";
import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const CusTalksSymposium = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };
    const venueKey = ["hasVenue", "hasVenueTmp"];
    const speakerKey = ["hasSpeaker", "hasSpeakerTmp"];
    const organizerKey = ["hasOrganizer", "hasOrganizerTmp"];

    const bilingFunc = bilingual(defObj);

    const [venueZh, venueEn] = bilingFunc(data, venueKey);
    const [speakerZh, speakerEn] = bilingFunc(data, speakerKey);
    const [organizerZh, organizerEn] = bilingFunc(data, organizerKey);

    const [locZh, locEn] = bilingFunc(data, "loc");
    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [postIdZh, postIdEn] = bilingFunc(data, "postId");
    const [remarkZh, remarkEn] = bilingFunc(data, "remark");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [endDateZh, endDateEn] = bilingFunc(data, "displayEndDate");
    const [endTimeZh, endTimeEn] = bilingFunc(data, "endTime");
    const [catTypeZh, catTypeEn] = bilingFunc(data, "catType");
    const [startDateZh, startDateEn] = bilingFunc(data, "displayStartDate");
    const [startTimeZh, startTimeEn] = bilingFunc(data, "startTime");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_essay}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`symposiums/${year}`}
                    backupPath={"symposiums"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.startDate"
                    defaultMessage="Start Date & Time : "
                />
                <CusValue {...startDateZh} />
                <CusValue {...startTimeZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.endDate"
                    defaultMessage="End Date & Time : "
                />
                <CusValue {...endDateZh} />
                <CusValue {...endTimeZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.loc"
                    defaultMessage="Location : "
                />
                <CusValue {...locZh} />
                <CusValue prefix="/" defVal="" {...locEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.catType"
                    defaultMessage="Type : "
                />
                <CusValue {...catTypeZh} />
                <CusValue prefix="/" defVal="" {...catTypeEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.venue"
                    defaultMessage="Venue : "
                />
                <CusValue {...venueZh} />
                <CusValue prefix="/" defVal="" {...venueEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.speaker"
                    defaultMessage="Speaker : "
                />
                <CusValue {...speakerZh} />
                <CusValue prefix="/" defVal="" {...speakerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.organizer"
                    defaultMessage="Organizer : "
                />
                <CusValue {...organizerZh} />
                <CusValue prefix="/" defVal="" {...organizerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.talksSymposium.remark"
                    defaultMessage="Remark : "
                />
                <CusValue {...remarkZh} />
                <CusValue prefix="/" defVal="" {...remarkEn} />
            </CusPara>
        </div>
    );
};

export default CusTalksSymposium;
