import React, { useContext, useState } from "react";
import { StoreContext } from "../../../../store/StoreProvider";
import AppBar from "@material-ui/core/AppBar";
import Toolbar from "@material-ui/core/Toolbar";
import {
    makeStyles,
    createMuiTheme,
    ThemeProvider
} from "@material-ui/core/styles";
import CloseIcon from "@material-ui/icons/Close";
import { teal, red } from "@material-ui/core/colors";
import Searchfield from "./Searchfield";
import Clearall from "./Clearall";
import Filter from "./Filter";
import Label from "./Label";
import Resultfield from "./Resultfield";
import PaginationBar from "./Pagination";
import CircularProgress from "@material-ui/core/CircularProgress";

const useStyles = makeStyles(theme => ({
    root: {
        // height: "100%",
        width: "100%",
        background: "#F0F0F0",
        position: "absolute",
        margin: "-13px 0 0 0"
    },
    card: {
        display: "flex",
        flexDirection: "column",
        minWidth: "300px",
        height: "80vh",
        maxHeight: "85vh",
        width: "80vw",
        top: "4%",
        left: "50%",
        transform: "translateX(-50%)",
        position: "absolute",
        background: "#FFFFFF",
        borderRadius: "8px",
        boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px"
    },
    appBar: {
        borderTopLeftRadius: "8px",
        borderTopRightRadius: "8px",
        marginBottom: "10px"
    },
    bar: {
        paddingTop: theme.spacing(1.5),
        display: "flex",
        flexDirection: "column",
        alignItems: "start",
        justifyContent: "space-around",
        background: "#FFFFFF",
        borderTopLeftRadius: "8px",
        borderTopRightRadius: "8px"
    },
    result: {
        flex: "auto",
        overflowY: "auto"
    },
    image: {
        width: "65%"
    },
    closeBtn: {
        fontSize: "5rem",
        position: "absolute",
        top: "20px",
        right: "20px",
        cursor: "pointer"
    }
}));
const theme = createMuiTheme({
    palette: {
        primary: teal,
        secondary: { main: red[500] }
    }
});

const SearchPage = props => {
    const classes = useStyles();
    const [state] = useContext(StoreContext);
    const { count, searchDataCount } = state.searchPage;
    const [loading, setLoading] = useState(false);
    return (
        <ThemeProvider theme={theme}>
            <div className={classes.root}>
                <div className={classes.card}>
                    <AppBar position="static" className={classes.appBar}>
                        <Toolbar className={classes.bar}>
                            <div
                                style={{
                                    width: "100%",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-evenly"
                                }}
                            >
                                <Searchfield />
                                <Clearall />
                                <Filter />
                            </div>
                            <Label />
                        </Toolbar>
                    </AppBar>
                    {loading && (
                        <div className="progressing">
                            <CircularProgress color="secondary" />
                        </div>
                    )}
                    <div className={classes.result}>
                        <Resultfield
                            loading={loading}
                            setLoading={setLoading}
                        />
                    </div>
                    {count > 0 &&
                    searchDataCount > 0 && ( // 有資料再顯示pageBar
                        <div className="pageBar">
                            <PaginationBar count={searchDataCount} />
                        </div>
                    )}
                </div>
            </div>
        </ThemeProvider>
    );
};

export default SearchPage;
