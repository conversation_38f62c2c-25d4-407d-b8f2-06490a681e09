// react
import React, { useContext, useState } from "react";
// ui
import { Button } from "semantic-ui-react";
// api
import { Api, queryHkbdbData } from "../../../../../../../api/hkbdb/Api";
// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
// services
import { removePrefix } from "../../../../../../../services/rdf";
// common
import { isEmpty } from "../../../../../../../common/codes";
// download
const fileDownload = require("react-file-download");

const CustomTSVButton = ({ open, setOpen }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryString } = state.query;
    //
    const [isLoad, setIsLoad] = useState(false);
    //
    const downloadTsv = (head, data) => {
        const DownloadTsvFileName = "generated_by_HKBDB.tsv";
        let tsvStr = "";
        head.forEach(h => {
            tsvStr += `${h}\t`;
        });
        tsvStr += "\r\n";
        data.forEach(d => {
            head.forEach(v => {
                const value = d.hasOwnProperty(v) ? d[v] : "";
                tsvStr += `${value}\t`;
            });
            tsvStr += "\r\n";
        });
        fileDownload(tsvStr, DownloadTsvFileName);
    };
    //
    const handleOnClick = async () => {
        console.log("I am TSV.");
        // set loading status
        setIsLoad(true);
        //
        if (queryString) {
            const api = Api.getQueryAndCount();
            const result = await queryHkbdbData(api, queryString, -1, 0);
            if (!isEmpty(result)) {
                const { head, data } = result;
                downloadTsv(head, data);
            }
        }
        setIsLoad(true);
        setOpen(false);
    };
    //
    return (
        <Button color="orange" onClick={handleOnClick} loading={isLoad}>
            TSV
        </Button>
    );
};

export default CustomTSVButton;
