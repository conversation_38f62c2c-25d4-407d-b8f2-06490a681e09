import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { injectIntl } from "react-intl";
import firebase from "firebase/app";
import "firebase/auth";
import StyledFirebaseAuth from "react-firebaseui/StyledFirebaseAuth";
// components

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
// config,utils
import firebaseUiConfig from "../../../../config/config-firebase-ui";
import { getFormatUser } from "../../../../common/codes";

// style

const FirebaseAuthLogin = props => {
    // props
    const { intl } = props;

    // store
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const locale = user && user.locale ? user.locale : "";
    // hooks

    // local state

    firebaseUiConfig.callbacks = {
        // eslint-disable-next-line no-unused-vars
        signInSuccessWithAuthResult: (authResult, redirectUrl) => {
            const userInfo = getFormatUser(authResult.user);
            // console.log("userInfo", userInfo);
            const { uid, displayName, email } = userInfo;
            if (uid && (displayName || email)) {
                dispatch({
                    type: Act.FIREBASE_LOGIN_USER,
                    payload: userInfo
                });
                // sign in 後不用 refresh anonymousToken
                dispatch({
                    type: Act.REFRESH_ANONYMOUS_TOKEN,
                    payload: false
                });
            }
            return false;
        }
    };

    return (
        <StyledFirebaseAuth
            uiConfig={firebaseUiConfig}
            firebaseAuth={firebase.auth()}
        />
    );
};

FirebaseAuthLogin.propTypes = {};

FirebaseAuthLogin.defaultProps = {};

export default injectIntl(FirebaseAuthLogin);
