import React, { useEffect, useState } from "react";
// mui
import Box from "@mui/material/Box";

// components
import { ResponsiveContainer } from "../../layout/Layout";
import GeneralMapIndex from "./subComponents/GeneralMapIndex.jsx";
import TraceMapIndex from "./subComponents/TraceMapIndex";
import CustomTabs from "../../common/components/Tabs/CustomTabs";
import GuideBox from "./subComponents/commons/GuideBox";
import MobileMapIndex from "./subComponents/MobileMapIndex";
import useWindowSize from "../../hook/useWindowSize.js";
import uiConfig from "../../config/config-ui.js";
import { FormattedMessage } from "react-intl";

const TABS = [
    {
        label: (
            <FormattedMessage id="map.tab.general" defaultMessage="地圖模式" />
        ),
        disabled: false,
        component: GeneralMapIndex
    },
    {
        label: (
            <FormattedMessage id="map.tab.trace" defaultMessage="行跡圖模式" />
        ),
        disabled: false,
        component: TraceMapIndex
    }
];

const MapPage = props => {
    const [tabIdx, setTabIdx] = useState(1);
    const [isMobileSize, setIsMobileSize] = useState(false);
    //
    const size = useWindowSize();
    const handleTabChange = (event, newValue) => {
        setTabIdx(newValue);
    };

    const renderTab = () => {
        const TabComponent = TABS[tabIdx].component;

        return <TabComponent />;
    };

    useEffect(() => {
        if (!size[0]) return;
        const isMobile = size[0] < uiConfig.BP_PAD_MIN_WIDTH;
        setIsMobileSize(isMobile);
    }, [size]);

    return (
        <ResponsiveContainer {...props}>
            {/* <Container sx={{ paddingBottom: "64px" }}> */}
            {/* <Container> */}
            {isMobileSize ? (
                <>
                    <MobileMapIndex />
                </>
            ) : (
                <Box className="gis-map-container">
                    <Box className="gis-map-container-header">
                        <Box className="gis-map-container-header-tab">
                            <CustomTabs
                                TABS={TABS}
                                tabIdx={tabIdx}
                                handleTabChange={handleTabChange}
                            />
                        </Box>
                        <Box className="gis-map-container-header-guide-box">
                            <GuideBox tabIdx={tabIdx} />
                        </Box>
                    </Box>
                    <Box>{renderTab()}</Box>
                </Box>
            )}
            {/* </Container> */}
        </ResponsiveContainer>
    );
};

export default MapPage;
