import React, { useState, useEffect } from 'react'
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";

function SliderPrevBtn(props) {
    const {
        className,
        style,
        onClick,
        currentSlide,
        slideCount,
        slidesToShow
    } = props;
    const [isDisabled, setIsDisabled] = useState(false);

    useEffect(() => {
        // 計算當前是否已經到最後一個完整的 slide
        if (currentSlide >= slideCount - slidesToShow) {
            setIsDisabled(true);
        } else {
            setIsDisabled(false);
        }
    }, [currentSlide, slideCount, slidesToShow]);

    return (
        <div
            className={className}
            style={{
                ...style,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                position: "absolute",
                right: "-15px",
                zIndex: 2,
                cursor: isDisabled ? "default" : "pointer",
                opacity: isDisabled ? 0.5 : 1
            }}
            onClick={!isDisabled ? onClick : undefined}
        >
            {/* <img src={iconArrowNext} alt="next" /> */}
            <KeyboardArrowLeftIcon sx={{ color: "#104860" }} />
        </div>
    );
}

export default SliderPrevBtn;
