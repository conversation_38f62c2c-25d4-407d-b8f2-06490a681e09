// import "firebase/storage";
// import firebase from "firebase/app";
// const documentDb = firebase.firestore();
//
// export function storeDocument(collection, document, id) {
//     if (id) {
//         return documentDb
//             .collection(collection)
//             .doc(id)
//             .update(document);
//     }
//     return documentDb.collection(collection).add(document);
// }
//
// export function onUserQueriesUpdated(uid, callback) {
//     return documentDb
//         .collection("storedQuery")
//         .where("author.uid", "==", uid)
//         .onSnapshot(snapshot => {
//             const docs = [];
//             snapshot.docChanges().forEach(change => {
//                 const data = change.doc.data();
//                 data.id = change.doc.id;
//                 data.changeType = change.type;
//                 docs.push(data);
//             });
//             callback(docs);
//         });
// }
//
// export function getUserQueries(uid) {
//     const storedQueriesRef = documentDb.collection("storedQuery");
//     const query = storedQueriesRef.where("author.uid", "==", uid);
//     return query
//         .get()
//         .then(result => {
//             return result.docs.map(x => {
//                 const data = x.data();
//                 data.id = x.id;
//                 return data;
//             });
//         })
//         .catch(error => {
//             return error;
//         });
// }
//
// export function getPublicQueries(uid) {
//     const storedQueriesRef = documentDb.collection("storedQuery");
//     const query = storedQueriesRef.where("private", "==", false);
//     return query
//         .get()
//         .then(result => {
//             return result.docs
//                 .map(x => x.data())
//                 .filter(x => x.author.uid !== uid);
//         })
//         .catch(error => {
//             return error;
//         });
// }
//
// export function deleteStoredQuery(id) {
//     return documentDb
//         .collection("storedQuery")
//         .doc(id)
//         .delete();
// }
