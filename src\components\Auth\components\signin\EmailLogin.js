import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
//
import { useHistory } from "react-router";
import { useDispatch } from "react-redux";
import { FormattedMessage, injectIntl } from "react-intl";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import { styled } from "@material-ui/styles";
import MailOutlineIcon from "@material-ui/icons/MailOutline";
import HttpsOutlined from "@material-ui/icons/HttpsOutlined";
// component, hooks
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Typography from "@mui/material/Typography";
import useLocaleRoute from "../../../../hook/useLocaleRoute";
// store
// config,utils
import InputPlusIcon from "../../../../common/components/input/InputPlusIcon";
import ButtonMain from "../../../../common/components/button/ButtonMain";
import { validateEmail } from "../../../../common/codes/validator";
import fireAuth from "../../../authenticate/firebase/auth/utils";
import { setCommonDialogContext } from "../../../../reduxStore/commonSlice";
import { Api } from "../../../../api/hkbdb/Api";
import { getPathById, ROUTE_ID } from "../../../../App-route";

// style

const StyledLinkDark = styled(
    "span",
    {}
)(({ theme }) => ({
    color: "#121212",
    cursor: "pointer"
}));

const StyledHint = styled(
    "span",
    {}
)(({ theme }) => ({
    color: "#ff5252"
}));

const EmailLogin = props => {
    // props
    const { intl } = props;
    // store
    const reduxDispatch = useDispatch();
    // route,intl
    const history = useHistory();
    // local state
    const [email, setEmail] = useState("");
    const [emailHint, setEmailHint] = useState("");
    const [password, setPassword] = useState("");
    const [passwordHint, setPasswordHint] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const emailValidate = validateEmail(email);
    const passwordValidate = !!password;
    const disabledSubmit = !(emailValidate && passwordValidate);

    // hooks
    const { handleLocaleRoute } = useLocaleRoute(
        Api.getLocale(),
        Api.locale_lang.LOCALE_ZH
    );

    const handleEmailChange = e => {
        const emailVal = e.target.value;
        setEmail(emailVal);
        // emailReplace()
    };
    const handlePassWordChange = e => {
        const passwordVal = e.target.value;
        setPassword(passwordVal);
    };
    const handleSubmit = () => {
        if (!disabledSubmit) {
            fireAuth.signIn({
                email,
                password,
                onSuccess: ({ user: loginUser }) => {
                    setEmailHint("");
                    setPasswordHint("");
                    const { displayName: userName } = loginUser;
                    // redirect to home page
                    history.push(handleLocaleRoute(getPathById(ROUTE_ID.Home)));
                },
                onError: ({ errorCode, errorMessage }) => {
                    if (
                        [
                            "auth/invalid-email",
                            "auth/user-not-found",
                            "auth/wrong-password",
                            "auth/user-disabled"
                        ].includes(errorCode)
                    ) {
                        setEmailHint("輸入不正確");
                        setPasswordHint("輸入不正確");
                    }
                }
            });
        }
    };

    const handleCreateAccount = () => {
        const url = handleLocaleRoute(getPathById(ROUTE_ID.Signup));
        history.push(url);
    };

    const handleDisplayPassword = e => {
        const checkedVal = e.target.checked;
        setShowPassword(checkedVal);
    };
    const handleForgetPassword = () => {
        // check email 符合格式
        if (!emailValidate) return;

        const onSuccess = () => {
            reduxDispatch(
                setCommonDialogContext({
                    openSignal: Date.now(),
                    title: "已傳送電子郵件",
                    contentText: [
                        `我們已傳送一封電子郵件到 ${email}，`,
                        `請前往信箱收信並重設密碼。`
                    ],
                    yesText: "好",
                    withFinishIcon: true
                })
            );
        };
        const onError = ({ errorCode, errorMessage }) => {
            reduxDispatch(
                setCommonDialogContext({
                    openSignal: Date.now(),
                    title: "寄送電子郵件失敗",
                    contentText: [`請確認電子郵件正確性`, errorMessage],
                    yesText: "好"
                })
            );
        };
        fireAuth.sendPasswordResetEmail({ email, onSuccess, onError });
    };

    // 所有要被focus的elements
    const eles = {
        email: {
            name: "email",
            className: "login__input--email",
            focusEle: ".login__input--email input"
        },
        password: {
            name: "password",
            className: "login__input--password",
            focusEle: ".login__input--password input"
        },
        submit: {
            name: "submit",
            className: "login__btn--submit",
            focusEle: ".login__btn--submit"
        },
        createAccount: {
            name: "createAccount",
            className: "login__btn--createAccount",
            focusEle: ".login__btn--createAccount"
        }
    };

    const [elesFocus, setElesFocus] = useState(
        Object.values(eles).map(ele => ({
            ...ele,
            active: false
        }))
    );

    // focus 事件
    const onFocus = eleName => e => {
        const thisIdx = elesFocus.findIndex(el => el.name === eleName);
        setElesFocus(list =>
            list.map((l, idx) => {
                return { ...l, active: idx === thisIdx };
            })
        );
    };

    // keyup 事件: 使用者enter可跳至下一個element
    const onKeyUp = eleName => e => {
        if (e.key === "Enter" || e.keyCode === 13) {
            const thisIdx = elesFocus.findIndex(el => el.name === eleName);
            const nextIdx = thisIdx >= elesFocus.length - 1 ? 0 : thisIdx + 1;
            const ele = document.querySelector(
                `${elesFocus[nextIdx].focusEle}`
            );
            if (ele?.focus) {
                ele.focus();
            }
        }
    };

    return (
        <Box className={"email-login"}>
            <Box mb={1} className={"email-login__form"}>
                <InputPlusIcon
                    // label="Email"
                    placeholder={intl.formatMessage({
                        id: "login.email",
                        defaultMessage: "Email"
                    })}
                    // startAdornmentIcon={<MailOutlineIcon />}
                    className={eles.email.className}
                    onChange={handleEmailChange}
                    error={!!(email && !emailValidate)}
                    onKeyUp={onKeyUp(eles.email.name)}
                    onFocus={onFocus(eles.email.name)}
                />
                {emailHint && (
                    <Box mt={1}>
                        <Typography variant="body2">
                            <StyledHint>{emailHint}</StyledHint>
                        </Typography>
                    </Box>
                )}
            </Box>
            <Box mb={1} className={"email-login__form"}>
                <InputPlusIcon
                    // label="Password"
                    placeholder={intl.formatMessage({
                        id: "login.password",
                        defaultMessage: "Password"
                    })}
                    type={showPassword ? "text" : "password"}
                    // startAdornmentIcon={<HttpsOutlined />}
                    className={eles.password.className}
                    onChange={handlePassWordChange}
                    onKeyUp={onKeyUp(eles.password.name)}
                    onFocus={onFocus(eles.password.name)}
                />
                {passwordHint && (
                    <Box mt={1}>
                        <Typography variant="body2">
                            <StyledHint>{passwordHint}</StyledHint>
                        </Typography>
                    </Box>
                )}
            </Box>
            <Box
                mb={5}
                display="flex"
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
            >
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={showPassword}
                                onChange={handleDisplayPassword}
                            />
                        }
                        label={intl.formatMessage({
                            id: "login.display.password",
                            defaultMessage: "Display password"
                        })}
                    />
                </FormGroup>

                <Typography textAlign="center" onClick={handleForgetPassword}>
                    <StyledLinkDark
                        style={{
                            color:
                                email && emailValidate ? "#121212" : "#909090",
                            cursor: emailValidate ? "pointer" : "inherit",
                            "&:hover": {
                                cursor: emailValidate ? "pointer" : "inherit"
                            }
                        }}
                    >
                        <FormattedMessage
                            id="login.forget.password"
                            defaultMessage="Forget your password?"
                        />
                    </StyledLinkDark>
                </Typography>
            </Box>
            <Box
                mb={1}
                display={"flex"}
                justifyContent={"space-between"}
                className={"email-login__buttons"}
            >
                <Button
                    variant={"contained"}
                    className={eles.submit.className}
                    fullWidth
                    disabled={disabledSubmit}
                    onClick={handleSubmit}
                    onFocus={onFocus(eles.submit.name)}
                >
                    {intl.formatMessage({
                        id: "login.submit",
                        defaultMessage: "Sign in"
                    })}
                </Button>
                <Button
                    variant={"contained"}
                    className={eles.createAccount.className}
                    fullWidth
                    disabled={false}
                    onClick={handleCreateAccount}
                    onFocus={onFocus(eles.createAccount.name)}
                >
                    {intl.formatMessage({
                        id: "login.create.account",
                        defaultMessage: "Create Account"
                    })}
                </Button>
            </Box>
        </Box>
    );
};

EmailLogin.propTypes = {};

EmailLogin.defaultProps = {};

export default injectIntl(EmailLogin);
