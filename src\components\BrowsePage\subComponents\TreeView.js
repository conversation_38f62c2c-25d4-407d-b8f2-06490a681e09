import React from "react";
import { List, Segment } from "semantic-ui-react";
import { injectIntl } from "react-intl";

import { TreeViewNode } from "./TreeViewNode";
import { rootClasses } from "../browseConfig";

const TreeView = props => {
    return (
        <Segment>
            <List verticalAlign="bottom">
                {rootClasses.map(node => (
                    <TreeViewNode
                        key={node.name}
                        node={node}
                        classPath={[node.name]}
                        onClassClicked={props.onClassClicked}
                    />
                ))}
            </List>
        </Segment>
    );
};

export default injectIntl(TreeView);
