import React, { useCallback, useContext } from "react";

// ui
import { Segment } from "semantic-ui-react";

// common
import { isEmpty } from "../../../../../common/codes";

// custom
import CustomMessage from "../../../EntityComponent/TableComp/CustomMessage";
import CustomTable from "./CustomTableFixed";
import CustomCreateModal from "../../../EntityComponent/TableComp/CustomCreateModalFixed";
import allRoles from "../../../../../App-role";
import CusSugModal from "../../../EntityComponent/TableComp/SugCreateModal";
import { StoreContext } from "../../../../../store/StoreProvider";

const typeConfig = {
    organization: {
        property: "basicProperty",
        data: "basicData",
        graph: "graph"
    }
};

const index = ({ data, loading, ontologyDomain, ontologyType }) => {
    const [state] = useContext(StoreContext);
    const { user } = state;
    // console.log("I am CustomFixedTable");
    //
    const MemoModal = useCallback(
        () =>
            !loading &&
            (user.role === allRoles.suggester ? (
                <CusSugModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            ) : (
                <CustomCreateModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            )),
        [loading, user]
    );
    // 如果內部有 subComponent 要記得用 useCallback 避免被重新喧染
    const ShowInfo = useCallback(() => {
        if (loading) {
            return <CustomMessage header="資料載入中..." />;
        } else if (isEmpty(data)) {
            return <CustomMessage header="目前結果為無資料。" />;
        } else {
            const newData = data.map(item => {
                const tmpObj = {
                    ...item,
                    property: item[typeConfig[ontologyType].property],
                    data: item[typeConfig[ontologyType].data]
                };

                delete tmpObj[typeConfig[ontologyType].property];
                delete tmpObj[typeConfig[ontologyType].data];

                return tmpObj;
            });
            return (
                <CustomTable
                    data={newData}
                    typeConfig={typeConfig}
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            );
        }
    }, [loading, JSON.stringify(data)]);
    //
    return (
        <Segment>
            <Segment size="big" basic>
                {/* show content */}
                <ShowInfo />
                {/* <CustomTable data={data} infoType={infoType} /> */}
                {/* create modal */}
                <MemoModal />
            </Segment>
        </Segment>
    );
};

export default index;
