import React, { useState, useEffect } from "react";

const useLocaleRoute = (_locale, defaultLocale) => {
    // local state
    const [localLocale, setLocalLocale] = useState(defaultLocale || "zh-hans");

    useEffect(() => {
        if (_locale) {
            setLocalLocale(_locale);
        }
    }, [_locale]);

    const handleLocaleRoute = _path =>
        _path && _path.replace(":locale", localLocale);
    return { handleLocaleRoute };
};

export default useLocaleRoute;
