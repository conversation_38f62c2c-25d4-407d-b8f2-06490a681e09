import Act from "../actions";

const initState = {
    modalLockSec: ""
};

const modalReducer = (state = initState, action) => {
    switch (action.type) {
        //
        case Act.MODAL_LOCK_SET:
            return {
                ...state,
                modalLockSec: action.payload
            };
        case Act.MODAL_LOCK_CLEAR:
            return {
                ...state,
                modalLockSec: initState.modalLockSec
            };
        //
        default:
            return state;
    }
};

export default modalReducer;
