import React, {
    Fragment,
    useContext,
    useEffect,
    useRef,
    useState
} from "react";

// ui
import { Input } from "semantic-ui-react";

// custom
import CustomDebounce from "./CustomDeBounce";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// action
import Act from "../../../../store/actions";

// common
import { isNotEmpty, safeGet } from "../../../../common/codes";

const CustomSearchInput = ({ ontologyType }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { information, personInformation } = state;
    const { searchInputs } = information;
    const infoSearchFunc = safeGet(
        personInformation,
        ["infoSearchFunc", ontologyType],
        () => { }
    );
    const searchKeyword = safeGet(searchInputs, [ontologyType], "");
    const [searchInput, setSearchInput] = useState(searchKeyword);
    const debSearchInput = CustomDebounce(searchInput, 1000);
    const prevSearchValueRef = useRef(searchKeyword);

    const handleOnChange = (event, { value }) => {
        setSearchInput(value);
    };

    useEffect(() => {
        if (prevSearchValueRef.current === searchInput) {
            return;
        }
        if (isNotEmpty(searchInput)) {
            prevSearchValueRef.current = searchInput;
            infoSearchFunc(searchInput);
        }

        dispatch({
            type: Act.INFORMATION_SEARCH_INPUTS,
            payload: {
                [ontologyType]: searchInput
            }
        });

        // searchInput 改變的時候頁碼要跳回第一頁
        dispatch({
            type: Act.INFORMATION_PAGINATIONS,
            payload: {
                [ontologyType]: {
                    localActivePage: 1,
                    searchActivePage: 1
                }
            }
        });
    }, [debSearchInput]);

    return (
        <Fragment>
            <Input
                fluid
                size="small"
                value={searchInput}
                icon="search"
                placeholder="Search..."
                onChange={handleOnChange}
            />
            <br />
        </Fragment>
    );
};

export default CustomSearchInput;
