import React, { useContext, useEffect, useState } from "react";
import { Table } from "semantic-ui-react";
import { safeGetProperty } from "../../../common/utils/safeGetProperty";
import { getUser } from "../../../../../api/firebase/realtimeDatabase";
import CustomTableLabel from "../CustomTableLabel";
import { isEmpty } from "../../../../../common/codes";
import config from "../../../../../config/config";
import transfromSugPrefix from "../../../common/utils/transfromSugPrefix";
import {
    convertPersonColName,
    convertSugTableHeader
} from "../../../common/utils/convertSugOptions";
import SugContextMenuFixed from "./SugContextMenuFixed";
import convertTableBodyVal from "../../../common/utils/convertTableBodyVal";
import { StoreContext } from "../../../../../store/StoreProvider";

const SugFixedDeleteTable = ({
    data,
    property,
    headers,
    ontologyType,
    type
}) => {
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { ontologyDefined } = property;

    const [suggesterName, setSuggesterName] = useState();
    const [headerArr, setHeaderArr] = useState([]);
    const subject = safeGetProperty(property, data.prop);

    const suggester = getUser(data.uid).then(res => {
        setSuggesterName(res?.displayName);
    });

    useEffect(() => {
        let tmpArr = [];
        if (!headers) return;
        headers.map(i => {
            const tmp = safeGetProperty(property, i.split("__")[0]);
            tmpArr.push(tmp);
        });
        setHeaderArr(tmpArr);
    }, [headers]);

    return (
        <Table celled padded style={{ width: "100%" }}>
            <Table.Header>
                <Table.Row>
                    {headerArr.map((i, idx) => (
                        <Table.HeaderCell key={idx}>{i}</Table.HeaderCell>
                    ))}
                </Table.Row>
            </Table.Header>
            <Table.Body>
                <Table.Row>
                    {type === "temporary" ? (
                        <>
                            {Object.entries(data)
                                .filter(([key]) => key !== "id") // 排除 id
                                .map(([key, value]) => {
                                    if (isEmpty(value)) return;
                                    if (
                                        key === "hasRelation" ||
                                        key === "relationRemarks__string"
                                    )
                                        return;
                                    const relationRemarksData =
                                        data?.relationRemarks__string;

                                    return (
                                        <>
                                            {/* property */}
                                            <Table.Cell>
                                                {ontologyType === "person"
                                                    ? convertPersonColName(
                                                          key,
                                                          ontologyType
                                                      )
                                                    : safeGetProperty(
                                                          property,
                                                          key.split("__")[0]
                                                      )}
                                            </Table.Cell>
                                            {/* value */}
                                            <Table.Cell>
                                                {/* {value} */}
                                                <CustomTableLabel
                                                    key={`value-label-${value}`}
                                                    value={convertTableBodyVal(
                                                        value,
                                                        key,
                                                        ontologyDefined[
                                                            ontologyType
                                                        ]
                                                    )}
                                                    color="grey"
                                                />
                                            </Table.Cell>
                                            {ontologyType === "relationevent" &&
                                                (!isEmpty(
                                                    relationRemarksData
                                                ) ? (
                                                    <Table.Cell>
                                                        {relationRemarksData}
                                                    </Table.Cell>
                                                ) : (
                                                    <Table.Cell></Table.Cell>
                                                ))}
                                            {/* uid */}
                                            <Table.Cell>
                                                <CustomTableLabel
                                                    key={`value-label-${value}`}
                                                    value={`Suggester: ${user.displayName}`}
                                                    color="orange"
                                                />
                                            </Table.Cell>
                                        </>
                                    );
                                })}
                        </>
                    ) : (
                        <>
                            <Table.Cell>{subject}</Table.Cell>
                            <Table.Cell textAlign="left">
                                {transfromSugPrefix(data?.val)}
                            </Table.Cell>
                            {data?.sheetName === "relationevent" && (
                                <Table.Cell></Table.Cell>
                            )}
                            <Table.Cell>
                                <CustomTableLabel
                                    value={`Suggester: ${suggesterName || ""}`}
                                    color="orange"
                                />
                            </Table.Cell>
                        </>
                    )}
                </Table.Row>
            </Table.Body>
        </Table>
    );
};
export default SugFixedDeleteTable;
