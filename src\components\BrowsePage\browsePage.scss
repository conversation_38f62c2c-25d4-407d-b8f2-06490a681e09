.browsePage {
  .entitySearch {
    &__input {
      height: 48px;
      border-radius: 4px;
    }
    &__select.ui.selection.dropdown {
      min-width: 9rem;
      max-width: 14rem;
      .text {
        color: #104860;
        line-height: 24px;
      }
      i {
        color: #104860;
        line-height: 24px;
      }
    }
    &__submit {
      margin-left: 8px;
      border-radius: 4px;
      background-color: #104860;
      &:hover {
        background-color: #043348;
      }
    }
    &__desc {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 10px 0 20px;
      &--text {
        cursor: pointer;
        color: #4183c4;
      }
    }
  }
}

.entitySearch{
  &__select{
    //min-width: 9rem !important;
    .text{
      div{
        color: #750f6d;
      }
    }
    .item{
      span{
        color: #104860;
      }
    }
    .visible .menu .transition{
      border: none;
    }
  }
}


@media screen and (max-width: 801px)  {
  .browsePage {
    .entitySearch {
      &__input {
        height: 48px;
        border-radius: 4px;
      }
      &__select.ui.selection.dropdown {
        min-width: 4rem;
        max-width: 8rem;
        .text {
          line-height: 24px;
        }
      }
    }
    .resultsEntity {

    }
  }
}
