# HKBDB-WEB 部署到正式站記錄

## YYYY.MM.DD

- 範例xxx - Ronald.20250115

## 2025.07.02

- 更新本次所有修改到正式站 - Johnny.20250702

## 2025.06.13

- 更新本次所有修改到正式站 - Johnny.20250613

## 2025.06.05

- 在238的/opt/hkbdb-web，將App-header.js中的map註解(後續正式站開放map的時候再解除) - Ronald.20250610

## 2025.01.10

- 範例xxx - Ronald.20250110

---

## 📝 記錄格式說明

每次從 develop 更新到 main 之後，請將YYYY.MM.DD更新成今天的日期，並新增一個日期(YYYY.MM.DD)區塊，下次更新時所需的操作請在YYYY.MM.DD新增：

```
## YYYY.MM.DD
- 操作描述 - 負責人.日期
- 操作描述 - 負責人.日期
```

- 每次的文件改動都請更新底下的最後更新

### 常見操作類型

- **Nginx 設定**：ttl修改
- **資料庫操作**：Protege更新
- **Firebase 設定**：Realtime Database、Firestore 規則、Authentication 等
- **環境變數**：API 端點、OAuth 設定等
- **API 設定**：CORS、端點變更等

---

*最後更新：2025.07.02 by Johnny*
