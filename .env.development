REACT_APP_BACKEND_URL = "http://localhost:4001/api"
#REACT_APP_API_NODE = "http://localhost:4200"
REACT_APP_API_NODE = "https://api2.daoyidh.com/hkbdb2"
REACT_APP_IMAGE_NODE = "https://api2.daoyidh.com/hkbdb2/storage"
REACT_APP_CRUD_NODE = "1"
FEATURE_HIDE_INSTANCE = "1"
REACT_APP_MODE=development

RT_COUNTER="hkbdb2"

# REACT_DB_FUSEKI: REACT_APP_API_NODE 使用的資料庫是否為 Fusekis Database
REACT_DB_FUSEKI=true

# api token
REACT_APP_AUTH_TOKEN=5uw5pm9F6s4bsH75rCr7TgLpjepgqDEtjcYNTYVm7K

# 查詢
REACT_APP_SPARQL_TOKEN="Bearer uat-secret-token-from-cuhk"

REACT_APP_SPARQL_API_ENDPOINT="https://api2.daoyidh.com/hkbdb2/aiSparqlQuery"
REACT_APP_GET_PUBLIC_SPARQL_IN_FIREBASE_ENDPOINT="https://api2.daoyidh.com/hkbdb2/advanced-query/public-queries"
REACT_APP_GET_USER_SPARQL_IN_FIREBASE_ENDPOINT="https://api2.daoyidh.com/hkbdb2/advanced-query/user-queries?uid={uid}"
REACT_APP_ADD_USER_SPARQL_IN_FIREBASE_ENDPOINT="https://api2.daoyidh.com/hkbdb2/advanced-query/insert"
REACT_APP_SPARQL_ENABLE=true
REACT_APP_GIS_ENABLE=true


