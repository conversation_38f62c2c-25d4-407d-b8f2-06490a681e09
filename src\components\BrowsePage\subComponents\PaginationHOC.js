import React, { useEffect } from "react";
import { Button, Grid, Input, Label, Pagination } from "semantic-ui-react";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";
//
import useWindowSize from "../../../hook/useWindowSize";
//
import uiConfig from "../../../config/config-ui";

const PaginationHOC = ({
    totalPages,
    displayGoToPage,
    goToPage,
    // onKeyUp,
    onSearchChange,
    inputHint,
    activePage,
    handlePaginationChange,
    // inputBtnClick,
    onEllipsisClick,
    ellipsisClickUseEffectDep
}) => {
    // hook
    const size = useWindowSize();

    useEffect(() => {
        const ellipsisClick = ({ target }) => {
            if (target.type === "ellipsisItem") {
                onEllipsisClick();
            }
        };
        document.addEventListener("click", ellipsisClick, true);

        // clean the events
        return function cleanup() {
            document.removeEventListener("click", ellipsisClick, true);
        };
    }, ellipsisClickUseEffectDep);

    // RWD for CustomPagination props
    const paginationProps = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return {};
        }
        if (size[0] <= uiConfig.BP_PAD_MIN_WIDTH) {
            return {
                ellipsisItem: null,
                firstItem: null,
                lastItem: null
            };
        }
        return {};
    };

    return (
        <React.Fragment>
            {/* 搜尋頁碼 */}
            {displayGoToPage && (
                <div style={{ marginBottom: "10px" }}>
                    {totalPages > 0 && (
                        <Label>
                            <FormattedMessage
                                id={"browse.goToPageLabel"}
                                defaultMessage={"Go to page"}
                            />

                            <Input
                                type={"number"}
                                max={totalPages}
                                min={1}
                                style={{ marginLeft: "15px", width: "100px" }}
                                value={goToPage}
                                // onKeyUp={onKeyUp}
                                // onChange={debounce(onSearchChange, 100)}
                                onChange={onSearchChange}
                                size={"mini"}
                            />
                            {/* <Button */}
                            {/*    // basic */}
                            {/*    color={"blue"} */}
                            {/*    size={"mini"} */}
                            {/*    style={{ marginLeft: "10px" }} */}
                            {/*    onClick={inputBtnClick} */}
                            {/* > */}
                            {/*    Go */}
                            {/* </Button> */}
                        </Label>
                    )}
                    <span style={{ marginLeft: "10px" }}>{inputHint}</span>
                </div>
            )}

            {/* CustomPagination */}
            <Pagination
                onPageChange={handlePaginationChange}
                activePage={parseInt(activePage)}
                totalPages={totalPages}
                {...paginationProps()}
            />
        </React.Fragment>
    );
};

PaginationHOC.defaultProps = {
    totalPages: 0,
    displayGoToPage: false,
    goToPage: 0,
    onKeyUp: null,
    onSearchChange: null,
    inputHint: "",
    activePage: 0,
    handlePaginationChange: null,
    inputBtnClick: null
};

PaginationHOC.propTypes = {
    totalPages: PropTypes.number,
    displayGoToPage: PropTypes.bool,
    goToPage: PropTypes.number,
    onKeyUp: PropTypes.func,
    onSearchChange: PropTypes.func,
    inputHint: PropTypes.string,
    activePage: PropTypes.number,
    handlePaginationChange: PropTypes.func,
    inputBtnClick: PropTypes.func
};

export default PaginationHOC;
