import React, { useContext } from "react";

import { useHistory } from "react-router-dom";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import config from "../../config";

import CusFooter from "../../CusComps/CusFooter";
import CusPrimaryHeader from "../../CusComps/CusPrimaryHeader";
import CusLineDivider from "../../CusComps/CusLineDivider";
import CusModal from "../../CusComps/CusModal";
import CusInfoMask from "../../CusComps/CusInfoMask";
import CusEssay from "../../CusComps/CusEssay";
import CusPublicIssue from "../../CusComps/CusPublicIssue";
import CusPublication from "../../CusComps/CusPublication";
import CusExhibition from "../../CusComps/CusExhibition";
import CusTalksSymposium from "../../CusComps/CusTalksSymposium";
import CusAwardEvent from "../../CusComps/CusAwardEvent";
import CusEducationEvent from "../../CusComps/CusEducationEvent";
import CusVenueInHK from "../../CusComps/CusVenueInHK";
import CusAuction from "../../CusComps/CusAuction";
import CusLoading from "../../CusComps/CusLoading";
import CusSwitch from "../../CusComps/CusSwitch";

import useFetch from "../../CusHooks/useFetch";
import useTripleMerge from "../../CusHooks/useTripleMerge";

import { bs64Encode, safeGet } from "../../../../common/codes";

import { url } from "../../../../api/hkvayb";

import { StoreContext } from "../../../../store/StoreProvider";

const coverHeaderStyle = {
    hkvayb_logo: {
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/logo_black_color_3x.png')"
    },
    hkvayb_header_right_item: {
        color: "#000"
    },
    hkvayb_header: {
        margin: "unset"
    }
};

const coverLineDividerStyle = {
    hkvayb_line_divider: {
        margin: "15.5px 0 39.5px 0"
    }
};

const coverLoadingDiv = {
    hkvayb_div: {
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        margin: "40px 0 40px 0"
    }
};

const Detail = ({ style = {}, ...props }) => {
    const {
        match: {
            params: { category, selectedId }
        }
    } = props;

    const classes = useStyles(style);
    const history = useHistory();
    const [state] = useContext(StoreContext);
    const { locale } = state.user;

    const keyword = `${bs64Encode(selectedId || "")}`;
    const apiZh = url.hkvayb.DETAILPAGE_DETAIL_ZH_INFORMATION;
    const apiEn = url.hkvayb.DETAILPAGE_DETAIL_EN_INFORMATION;
    const apiZhUrl = apiZh.replace("{keyword}", keyword);
    const apiEnUrl = apiEn.replace("{keyword}", keyword);

    const { data: dataZh, loading: loadingZh } = useFetch(apiZhUrl);
    const { data: dataEn, loading: loadingEn } = useFetch(apiEnUrl);
    const mergedDataZh = useTripleMerge(dataZh.data);
    const mergedDataEn = useTripleMerge(dataEn.data);
    const contentDataZh = safeGet(mergedDataZh, [selectedId], {});
    const contentDataEn = safeGet(mergedDataEn, [selectedId], {});
    const contentData = { zh: contentDataZh, en: contentDataEn };
    const groupedData = { ...contentData, eventId: selectedId };

    const description = safeGet(config.description, [locale, category], "");
    const categoryName = safeGet(config.category, [locale, category], "");
    const categoryImage = safeGet(config.categoryDetailImage, [category], "");

    const handleBack = () => {
        // history.goBack();
        history.push(`/${locale}/HkvaybResult`);
    };

    return (
        <div>
            <CusPrimaryHeader style={coverHeaderStyle} />
            <div
                style={{
                    backgroundImage: `url(${categoryImage})`,
                    backgroundRepeat: "no-repeat",
                    backgroundSize: "contain"
                }}
            >
                <div className={classes.hkvaby_detail}>
                    <div
                        className={classes.hkvaby_detail_back}
                        onClick={handleBack}
                    >
                        <FormattedMessage
                            id="hkvayb.search.detail.back"
                            defaultMessage="go back"
                        />
                    </div>
                    <CusLineDivider style={coverLineDividerStyle} />
                    <div className={classes.hkvaby_detail_category}>
                        {categoryName}
                        <CusModal
                            title={categoryName}
                            content={description}
                            label={<CusInfoMask />}
                        />
                    </div>
                    {loadingZh || loadingEn ? (
                        <CusLoading style={coverLoadingDiv} />
                    ) : (
                        <CusSwitch target={category}>
                            <CusEssay tg="Essay" data={groupedData} />
                            <CusPublicIssue tg="PublicIssue" data={groupedData} />
                            <CusPublication tg="Publication" data={groupedData} />
                            <CusExhibition tg="Exhibition" data={groupedData} />
                            <CusTalksSymposium tg="TalksSymposium" data={groupedData} />
                            <CusAwardEvent tg="AwardEvent" data={groupedData} />
                            <CusEducationEvent tg="EducationEvent" data={groupedData} />
                            <CusVenueInHK tg="VenueInHK" data={groupedData} />
                            <CusAuction tg="Auction" data={groupedData} />
                        </CusSwitch>
                    )}
                </div>
            </div>
            <CusFooter />
        </div>
    );
};

export default Detail;
