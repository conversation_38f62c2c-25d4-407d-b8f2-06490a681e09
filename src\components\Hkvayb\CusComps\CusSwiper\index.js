import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusCard from "../CusCard";

import useWheelHack from "../../CusHooks/useWheelHack";

const CusSwiper = ({
    data = [],
    style = {},
    width = 320,
    height = 240,
    marginRight = 24,
    ...rest
}) => {
    const classes = useStyles(style);
    const preventWheelDefault = useWheelHack();
    const divRef = React.useRef(null);
    const [maxNum, setMaxNum] = React.useState(0);
    const [slideOffset, setSlideOffset] = React.useState(0);
    const [maxDisplayNum, setMaxDisplayNum] = React.useState(0);
    const totalWidth = width + marginRight;
    const minNum = 0;

    const handleMinNum = (num, min) => (num < min ? 0 : num);
    const handleMaxNum = (num, max) => (num > max ? max : num);
    const handlePrev = () => {
        setSlideOffset(prev => handleMinNum(prev - totalWidth, minNum));
    };
    const handleNext = () => {
        setSlideOffset(prev => handleMaxNum(prev + totalWidth, maxNum));
    };
    const handleUpdateDivWidth = () => {
        if (divRef.current) {
            const tmpMaxDisplayNum = Math.trunc(
                divRef.current.offsetWidth / totalWidth
            );
            setMaxDisplayNum(tmpMaxDisplayNum);
            setMaxNum((data.length - tmpMaxDisplayNum) * totalWidth);
        }
    };

    const handleOnWheel = event => {
        preventWheelDefault();
        if (event.nativeEvent.wheelDelta > 0) {
            handlePrev();
        } else {
            handleNext();
        }
    };

    const buttons = (
        <div className={classes.hkvayb_swiper_button_group}>
            <div
                disabled={slideOffset === minNum}
                className={classes.hkvayb_swiper_prev_bottom}
                onClick={handlePrev}
            >
                <div className={classes.hkvayb_left_arrow_icon} />
                <span>
                    <FormattedMessage
                        id="hkvayb.search.swiper.tour.prev"
                        defaultMessage="prev"
                    />
                </span>
            </div>
            <div
                disabled={slideOffset >= maxNum}
                className={classes.hkvayb_swiper_next_bottom}
                onClick={handleNext}
            >
                <span>
                    <FormattedMessage
                        id="hkvayb.search.swiper.tour.next"
                        defaultMessage="next"
                    />
                </span>
                <div className={classes.hkvayb_right_arrow_icon} />
            </div>
        </div>
    );

    React.useEffect(() => {
        // init
        handleUpdateDivWidth();
        // register
        window.addEventListener("resize", handleUpdateDivWidth);
        return () => {
            // deregister
            window.removeEventListener("resize", handleUpdateDivWidth);
        };
    }, []);

    React.useEffect(() => {
        handleUpdateDivWidth();
    }, [JSON.stringify(data)]);

    return (
        <div ref={divRef}>
            <div className={classes.hkvayb_swiper} onWheel={handleOnWheel}>
                {data.map((item, index) => {
                    return (
                        <CusCard
                            {...rest}
                            key={`${item.primary}-${index}`}
                            src={item.src}
                            backupSrc={item.backupSrc}
                            primary={item.primary}
                            secondary={item.secondary}
                            category={item.category}
                            width={width}
                            height={height}
                            style={{
                                ...{
                                    hkvayb_card: {
                                        transitionDuration: "500ms",
                                        transform: `translate(-${slideOffset}px, 0)`
                                    }
                                },
                                ...style
                            }}
                        />
                    );
                })}
            </div>
            {data.length >= maxDisplayNum && buttons}
        </div>
    );
};

export default CusSwiper;
