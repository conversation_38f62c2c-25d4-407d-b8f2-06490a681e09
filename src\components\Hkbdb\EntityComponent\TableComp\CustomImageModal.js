import React, { useState } from "react";

import { Button, Image, Modal, Label } from "semantic-ui-react";

const CustomImageModal = ({ imgUrl }) => {
    //
    const [open, setOpen] = useState(false);
    //
    return (
        <Modal
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            open={open}
            trigger={
                <Image size="tiny">
                    <Label
                        color="teal"
                        content="Image"
                        icon="external alternate"
                        style={{ cursor: "pointer" }}
                    />
                </Image>
            }
        >
            <Modal.Header>Show Image</Modal.Header>
            <Modal.Content image>
                <Image size="medium" src={imgUrl} wrapped centered />
                {/* {JSON.stringify(imgUrl)} */}
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={() => setOpen(false)} positive>
                    關閉
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default CustomImageModal;
