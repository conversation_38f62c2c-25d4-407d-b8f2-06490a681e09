import React, { useEffect, useState, useContext } from "react";
import { Accordion, Container, Header, Label } from "semantic-ui-react";
import { intro, sources } from "./sourcesContent";
import { StoreContext } from "../../store/StoreProvider";
import { getDataset } from "./action";

const DatasetPage = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { source, user } = state;
    const { dataset } = source;
    const { locale } = user;
    const [panels, setPanels] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    // get dataset once from API => 統一由 App.js 呼叫
    // useEffect(() => {
    //     // get dataset and store in global state
    //     getDataset(dispatch, locale, setIsLoading);
    // }, [locale]);

    useEffect(() => {
        if (!(dataset && Array.isArray(dataset))) return;

        // dataset like this:
        // [{label: "香港古典詩文集經眼錄", dataset: "abcwhkp", description:""},
        // {}...]

        // ======= use dataset from API  ======
        const thisPanels = dataset.reduce((acc, item, idx) => {
            const accordionItem = {
                key: idx,
                title: {
                    content: (
                        <span
                            style={{
                                fontWeight: "bold",
                                fontSize: "1em",
                                color: "black"
                            }}
                        >
                            {item.label || item.dataset}
                            <Label
                                content={item.dataset}
                                style={{ float: "right" }}
                            />
                        </span>
                    )
                },
                content: {
                    content: item.description || item.label || item.dataset
                }
            };
            acc.push(accordionItem);
            return acc;
        }, []);
        setPanels(thisPanels);
    }, [dataset]);

    return (
        <Container
            text
            textAlign="justified"
            style={{ paddingBottom: "100px" }}
            className={"source-container"}
        >
            {/* <Dimmer active={isLoading} inverted> */}
            {/*    <Loader /> */}
            {/* </Dimmer> */}
            <Header as="h2">{intro.title}</Header>
            <p>{intro.content}</p>
            <Accordion panels={panels} exclusive={false} fluid styled />
        </Container>
    );
};

export default DatasetPage;
