import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_header: props => ({
        width: "100%",
        height: "92px",
        margin: "0 0 56px",
        padding: "24px 72px 24px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            justifyContent: "center",
            flexWrap: "wrap",
            height: "unset",
            margin: "0",
            padding: "24px 25px 24px"
        },
        ...props.hkvayb_header
    }),
    hkvayb_header_left: props => ({
        float: "left",
        ...props.hkvayb_header_left
    }),
    hkvayb_logo: props => ({
        width: "200px",
        height: "44px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/logo3269_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "contain",
        cursor: "pointer",
        ...props.hkvayb_logo
    }),
    hkvayb_invert_logo: props => ({
        width: "200px",
        height: "44px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/logo_black_color_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "contain",
        ...props.hkvayb_invert_logo
    }),
    hkvayb_header_right: props => ({
        float: "right",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            marginTop: "16px",
            whiteSpace: "nowrap"
        },
        ...props.hkvayb_header_right
    }),
    hkvayb_header_right_item: props => ({
        whiteSpace: "nowrap",
        // width: "32px",
        height: "24px",
        margin: "2px 32px 2px 0",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#fff",
        float: "right",
        cursor: "pointer",
        "&:hover": {
            color: "#b79d79"
        },
        ...props.hkvayb_header_right_item
    }),
    hkvayb_header_right_border_item: props => ({
        width: "40px",
        height: "28px",
        padding: "1px 11px",
        border: "solid 1px #b79d79",
        float: "right",
        whiteSpace: "nowrap",
        display: "flex",
        alignItems: "center",
        alignContent: "center",
        lineHeight: 16,
        ...props.hkvayb_header_right_border_item
    }),
    hkvayb_header_right_border_font: props => ({
        width: "16px",
        // height: "24px",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        // lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#b79d79",
        cursor: "pointer",
        "&:hover": {
            color: "rgb(183 157 121 / 60%);"
        },
        ...props.hkvayb_header_right_border_font
    })
});

export default useStyles;
