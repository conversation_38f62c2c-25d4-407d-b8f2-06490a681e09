import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, injectIntl } from "react-intl";
import {
    Breadcrumb,
    Dimmer,
    Label,
    Loader,
    Message,
    Segment,
    Dropdown,
    Divider
} from "semantic-ui-react";
import PaginationHOC from "./PaginationHOC";
import {
    loadIndividuals,
    loadIndividualsByGraph,
    countIndividuals,
    countIndividualsByGraph,
    renderLeafs
} from "./TreeViewNode";
import debounce from "lodash/debounce";
import { rootClasses } from "../browseConfig";
import "../../../style/browse.scss";
import { StoreContext } from "../../../store/StoreProvider";
import { apiParamsDefault } from "../../../api/hkbdb/Api";
import Act from "../../../store/actions";

NoIndividualsMessage.propTypes = {
    individuals: PropTypes.array
};
function NoIndividualsMessage({ individuals }) {
    if (individuals && individuals.length >= 0) {
        return null;
    }

    return (
        <Message info>
            <FormattedMessage
                id="browse.clickClass"
                defaultMessage="Select any class on the left to display individuals it contains."
            />
        </Message>
    );
}

const CustomBreadDivider = () => {
    return <Breadcrumb.Divider style={{ color: "#fff" }} icon="right angle" />;
};

const hint = {
    default: "",
    goingToPage: "搜尋中...",
    inputNumber: "請輸入頁碼",
    inputIllegal: "請輸入區間的頁碼",
    noIndividual: "該頁碼無資料，請選取其他頁碼"
};

/**
 * @params
 * user: object
 * name: string
 * path: string
 * totalIndividuals: number
 * */
const IndividualsPanel = ({ user, name, path, intl }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { source, browse } = state;
    const { dataset } = source;
    const { curGraph, filterGraphOn } = browse;

    const [localName, setLocalName] = useState("");
    // const [localPath, setLocalPath] = useState(null);
    const [individuals, setIndividuals] = useState(null);
    const [totalPages, setTotalPages] = useState(0);
    const [activePage, setActivePage] = useState(1);
    const [pageSize, setPageSize] = useState(50);
    const [loading, setLoading] = useState(false);
    const [goToPage, setGoToPage] = useState(0);
    const [displayGoToPage, setDisplayGoToPage] = useState(false);
    const [inputHint, setInputHint] = useState("");
    const [graphOption, setGraphOption] = useState([]);
    const [localTotalIndividuals, setLocalTotalIndividuals] = useState(0);
    const dropdownDefaultVal = "all";

    const loadIndividualsHelper = (name, offset) => {
        if (name) {
            // 若 curGraph 為 All , 則 search all individuals
            if (curGraph === dropdownDefaultVal) {
                // query to API
                loadIndividuals(name, offset).then(data => {
                    const individuals = data.map(i => ({
                        name: i.name,
                        type: "leaf", // node.type => 有 node, leaf 二種
                        label: [],
                        tags: i.tags,
                        toggled: false,
                        link: true
                    }));
                    if (individuals.length === 0) {
                        setInputHint(hint.noIndividual);
                    } else {
                        setInputHint(hint.default);
                    }
                    setIndividuals(individuals);
                    setLoading(false);
                });
            } else {
                if (curGraph === "") {
                    const individuals = [];
                    if (individuals.length === 0) {
                        setInputHint(hint.noIndividual);
                    } else {
                        setInputHint(hint.default);
                    }
                    setIndividuals(individuals);
                    setLoading(false);
                } else {
                    // query to API
                    loadIndividualsByGraph(
                        name,
                        offset,
                        apiParamsDefault.loadIndividuals.limit,
                        curGraph
                    ).then(data => {
                        const individuals = data.map(i => ({
                            name: i.name,
                            type: "leaf", // node.type => 有 node, leaf 二種
                            label: [],
                            tags: i.tags,
                            toggled: false,
                            link: true
                        }));
                        if (individuals.length === 0) {
                            setInputHint(hint.noIndividual);
                        } else {
                            setInputHint(hint.default);
                        }
                        setIndividuals(individuals);
                        setLoading(false);
                    });
                }
            }
        }
    };

    // 切換頁碼時，query 該類別實體清單
    const handlePaginationChange = (e, { activePage }) => {
        const offset = activePage === 1 ? 0 : (activePage - 1) * pageSize;
        setLoading(true);
        setActivePage(activePage);
        setGoToPage(activePage);

        // hidden gotoPage input
        setDisplayGoToPage(false);
        loadIndividualsHelper(localName, offset);
    };

    // debounce search query
    // 搜尋頁碼
    const delayedSearchChange = debounce(eventData => {
        if (eventData === "") {
            setInputHint(hint.inputNumber);
            return;
        }
        setActivePage(eventData);
        if (parseInt(eventData) > totalPages || parseInt(eventData) <= 0) {
            setInputHint(hint.inputIllegal);
            return;
        }
        setLoading(true);
        setInputHint(hint.goingToPage);

        // hidden gotoPage input
        setDisplayGoToPage(false);

        const offset = eventData === 0 ? eventData : eventData * pageSize;
        loadIndividualsHelper(name, offset, pageSize);
    }, 800);

    // 當 頁碼 input 變更時
    const onSearchChange = (event, data) => {
        // input 變更時，立即更新 goToPage value
        setGoToPage(data.value);

        // 但是延遲 query individuals
        delayedSearchChange(data.value);
    };

    // 選擇 dataset
    const handleDropdownChange = (e, data) => {
        dispatch({
            type: Act.SET_CUR_GRAPH,
            payload: data.value
        });
    };

    // 將 activePge 調整至 1 的狀況：
    // 1.類別變更,
    // 2.curGraph 變更
    // 3.filterGraphOn 變更
    useEffect(() => {
        setActivePage(1);
    }, [name, curGraph, filterGraphOn]);

    // 重新計算 totalIndividuals
    useEffect(() => {
        if (!name || name === "") {
            setLocalTotalIndividuals(0);
        } else {
            setLoading(true);

            if (curGraph === dropdownDefaultVal) {
                countIndividuals(name).then(count => {
                    setLocalTotalIndividuals(count);
                });
            } else {
                if (curGraph === "") {
                    setLocalTotalIndividuals(0);
                } else {
                    countIndividualsByGraph(name, curGraph).then(count => {
                        setLocalTotalIndividuals(count);
                    });
                }
            }
        }
    }, [name, curGraph]);

    // 當選取其他類別時，則重新計算頁碼總數
    useEffect(() => {
        if (name !== localName) {
            setLocalName(name);
            setLoading(true);
        }
        setTotalPages(Math.ceil(localTotalIndividuals / pageSize));
    }, [name, localTotalIndividuals]);

    // 重新 query individuals 的狀況：
    // 1. 類別變更(Person, Organization)
    // 2. urGraph  變更
    // 3. filterGraphOn 變更
    // 4. activePage(目前頁碼) 變更
    useEffect(() => {
        // 同步變更 頁碼input(goToPage)
        setGoToPage(activePage);

        const offset = activePage === 0 ? 0 : (activePage - 1) * pageSize;
        loadIndividualsHelper(localName, offset);
    }, [localName, activePage, curGraph, filterGraphOn]);

    useEffect(() => {
        const ellipsisClick = ({ target }) => {
            if (target.type === "ellipsisItem") {
                setDisplayGoToPage(true);
            }
        };
        document.addEventListener("click", ellipsisClick, true);

        // clean the events
        return function cleanup() {
            document.removeEventListener("click", ellipsisClick, true);
        };
    }, [name, activePage]);

    // 加入 all option (含語系切換)
    const addAllOption = options => {
        if (!(options && Array.isArray(options))) return;
        const allOption = {
            key: "all",
            text: intl.formatMessage({
                id: "browse.searchAllGraph",
                defaultMessage: "Search All"
            }),
            value: "all"
        };
        const optionsFilter = options.filter(ot => ot.value !== "all");
        return [allOption].concat(optionsFilter);
    };

    // 初始化 curGraph
    useEffect(() => {
        dispatch({
            type: Act.SET_CUR_GRAPH,
            payload: dropdownDefaultVal
        });
    }, []);

    // 取得 dataset => 轉成 過濾 dataset 的 dropdown option 可接受的格式
    // 語系變更時, search all 選項也會變更
    useEffect(() => {
        // dataset like:
        // [{label: "香港文學資料庫", lang: "zh", dataset: "hklit"}]
        if (!(dataset && Array.isArray(dataset))) return;
        const option = dataset.map(dt => ({
            key: dt.dataset,
            // todo: 語系切換時, dataset 的 label(rdfs:label) 要重新抓
            text: dt.label || dt.dataset,
            value: dt.dataset
        }));
        if (!(option && Array.isArray(option))) return;
        const allOption = {
            key: "all",
            text: (
                <FormattedMessage
                    id={"browse.searchAllGraph"}
                    defaultMessage={"Search All"}
                />
            ),
            // text: intl.formatMessage({
            //     id: "browse.searchAllGraph",
            //     defaultMessage: "Search All"
            // }),
            value: "all"
        };
        const optionsFilter = option.filter(ot => ot.value !== "all");
        const newOpts = [allOption].concat(optionsFilter);

        // 加入 all option
        // const _option = addAllOption(option);
        setGraphOption(newOpts);
    }, [dataset, intl]);

    return (
        <Segment className={"individual-panel-entity"}>
            <Dimmer active={loading} inverted>
                <Loader size="mini">
                    <FormattedMessage
                        id="browse.loadingIndividuals"
                        defaultMessage="Loading individuals..."
                    />
                </Loader>
            </Dimmer>

            <Label color="blue" ribbon size="large">
                <Breadcrumb size={"large"}>
                    <Breadcrumb.Section>
                        <FormattedMessage
                            id="browse.individual"
                            defaultMessage="Individuals"
                        />
                    </Breadcrumb.Section>
                    {name && name === rootClasses[0].name && (
                        <React.Fragment>
                            <CustomBreadDivider />
                            <Breadcrumb.Section>
                                <FormattedMessage
                                    id="browse.entityClassPerson"
                                    defaultMessage={"Person"}
                                />
                            </Breadcrumb.Section>
                        </React.Fragment>
                    )}
                    {name && name === rootClasses[1].name && (
                        <React.Fragment>
                            <CustomBreadDivider />
                            <Breadcrumb.Section>
                                <FormattedMessage
                                    id="browse.entityClassOrganization"
                                    defaultMessage={"Organization"}
                                />
                            </Breadcrumb.Section>
                        </React.Fragment>
                    )}
                </Breadcrumb>
            </Label>

            {/* <NoIndividualsMessage individuals={individuals} /> */}
            {/* <IndividualsBreadcrumb path={path} loading={loading} /> */}
            <Divider hidden style={{ marginTop: "5px", marginBottom: "5px" }} />

            {/* Pagination with 搜尋頁碼 */}
            <PaginationHOC
                totalPages={totalPages}
                activePage={activePage}
                // displayGoToPage={displayGoToPage}
                displayGoToPage // always displayed
                goToPage={goToPage}
                // onKeyUp={onKeyUp}
                onSearchChange={onSearchChange}
                inputHint={inputHint}
                // inputBtnClick={send}
                handlePaginationChange={handlePaginationChange}
                onEllipsisClick={() => setDisplayGoToPage(true)}
                ellipsisClickUseEffectDep={[name, activePage]}
            />

            <Divider hidden />
            <div className="" style={{ display: "flex", alignItems: "center" }}>
                <Label
                    size={"large"}
                    style={{ whiteSpace: "nowrap", textAlign: "center" }}
                >
                    <FormattedMessage
                        id="browse.filterDatasetLabel"
                        defaultMessage={"Dataset"}
                    />
                </Label>
                <Dropdown
                    // placeholder="Select Dataset"
                    selection
                    value={curGraph}
                    options={graphOption}
                    onChange={handleDropdownChange}
                    style={{ minWidth: "200px" }}
                    fluid
                />
            </div>

            {/* entity list in segment with scroll bar */}
            <Segment className="individual-panel-entity-list">
                {renderLeafs(individuals, path, {})}
            </Segment>
        </Segment>
    );
};

export default injectIntl(IndividualsPanel);
