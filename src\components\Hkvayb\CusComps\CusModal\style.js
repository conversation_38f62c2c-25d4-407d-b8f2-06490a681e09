import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_div: props => ({
        display: "flex",
        justifyContent: "space-evenly",
        alignItems: "center",
        width: "auto",
        "@media screen and (max-width: 600px)": {
            overflowY: "scroll",
            height: "500px",
            flexWrap: "wrap"
        },
        ...props.hkvayb_div
    }),
    hkvayb_div_img_only: props => ({
        width: "auto",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexWrap: "wrap",
        ...props.hkvayb_div_img_only
    }),
    hkvayb_desc: props => ({
        marginLeft: "40px",
        "@media screen and (min-width: 500px)": {
            height: "420px",
            overflowY: "auto"
        },
        "@media screen and (max-width: 500px)": {
            flexWrap: "wrap"
        },
        ...props.hkvayb_desc
    }),
    hkvayb_info_title: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "20px",
        fontWeight: "600",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.6",
        textDecorationLine: "underline",
        letterSpacing: "0.32px",
        textAlign: "left",
        color: "#333",
        margin: "10px 0 ",
        marginRight: "15px",
        ...props.hkvayb_info_title
    })
});

export default useStyles;
