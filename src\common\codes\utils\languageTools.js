import { Api } from "../../../api/hkbdb/Api";

export const isSupportedLang = lang => {
    return Object.values(Api.locale_lang).some(value => value === lang);
};
/* [檢查-語系]本系統是否支援該語系 並回傳語系 */
export const checkLanguageWithLanguage = language => {
    return isSupportedLang(language)
        ? language
        : Api.locale_lang.LOCALE_DEFAULT;
};

/* [檢查-路徑]本系統是否支援該語系 並回傳語系 */
export const checkLanguageWithPaths = pathname => {
    const supportLanguages = Object.values(Api.locale_lang);
    const currentPathnames = pathname.split("/"); // 將網址分割
    let language = Api.locale_lang.LOCALE_DEFAULT; // 預設第一個語系
    // 逐一檢查語系
    for (let i = 0; i < supportLanguages.length; i += 1) {
        if (currentPathnames.includes(supportLanguages[i])) {
            language = supportLanguages[i];
            break;
        }
    }
    return language;
};

/* 變更語系 */
export const changeLang = ({ history, currentLanguage, nextLanguage }) => {
    const {
        location: { pathname, search }
    } = history;

    /* 檢查網址中是否有語系存在 */
    if (currentLanguage && pathname.startsWith(`/${currentLanguage}`)) {
        /* 存在 => 取代原有語系 */
        history.push({
            pathname: pathname.replace(
                `/${currentLanguage}`,
                `/${checkLanguageWithLanguage(nextLanguage)}`
            ),
            search
        });
    } else {
        /* 不存在 => 推入瀏覽器預設語系 */
        const browserLanguage = (navigator.languages
            ? navigator.languages[0]
            : navigator.language || navigator.userLanguage
        ).toLowerCase();

        // console.log('eeeeee', checkLanguageWithLanguage(browserLanguage));
        history.push({
            pathname: `/${checkLanguageWithLanguage(browserLanguage)}`
        });
    }
};
