import React from "react";

import TextField from "@mui/material/TextField";

import useStyles from "./style";

const CusInput = ({ value, placeholder, style = {}, ...rest }) => {
    const classes = useStyles(style);
    return (
        <TextField
            {...rest}
            value={value}
            className={classes.hkvayb_input}
            label={placeholder}
            variant="filled"
        />
    );
};

export default CusInput;
