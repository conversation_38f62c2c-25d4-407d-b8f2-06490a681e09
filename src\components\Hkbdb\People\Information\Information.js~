import React, { useCallback, useContext, useEffect, useState } from "react";

// ui
import { Accordion, Icon } from "semantic-ui-react";

// lang
import { injectIntl } from "react-intl";
import { intlMsgs } from "../../EntityComponent/entityIntlMsgs";

// custom
import CustomFixedTable from "./CustomFixedTable";
import CustomFlexibleTable from "./CustomFlexibleTable";

// type
import PropTypes from "prop-types";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { isNotEmpty, isEmpty } from "../../../../common/codes";
import fetchSugDataBySheet from "../../common/utils/fetchSugDataBySheet";

// action
import { queryPersonInfoV2, reloadPersonInfo } from "../action";

//
import config from "../../../../config/config";

//
import "./Information.scss";
import { bs64DecodeId } from "../../../../common/codes/jenaHelper";
import Act from "../../../../store/actions";
import allRoles from "../../../../App-role";

const handleShowAccor = (role, authority) => {
    if (!role || !Array.isArray(authority)) return false;
    return authority.includes(role);
};

const information = ({ intl, name, permission }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const {
        renderTarget,
        renderSignal,
        personId,
        curOpenTab
    } = state.information;
    const { personInformation: perInfo, user } = state;
    const { resetSuggestionsTable } = perInfo;
    const { role: userRole, uid: userUid } = state.user;
    // active on change
    const [activeIndex, setActiveIndex] = useState(0);
    // lang
    const { formatMessage } = intl;
    // handle click
    const handleClick = async (e, titleProps, sheetName) => {
        const { index } = titleProps;
        const newIndex = activeIndex === index ? -1 : index;
        setActiveIndex(newIndex);
        // suggester fetch corresponding sheet data
        const data = await fetchSugDataBySheet(
            state.user,
            titleProps,
            personId,
            sheetName
        );

        if (!isEmpty(data)) {
            const filterData = data.filter(i => i.uid === userUid);

            dispatch({
                type: Act.SET_SUGGESTINFO,
                payload: filterData
            });
        } else {
            dispatch({
                type: Act.SET_SUGGESTINFO,
                payload: {}
            });
        }
        dispatch({
            type: Act.INFORMATION_CURRENTLY_OPEN_TAB,
            payload: sheetName
        });
    };
    // reload data
    useEffect(() => {
        if (
            isNotEmpty(name) &&
            isNotEmpty(renderTarget) &&
            isNotEmpty(renderSignal)
        ) {
            if (
                ["person", "relationevent", "namenode"].includes(renderTarget)
            ) {
                reloadPersonInfo(name, renderTarget, dispatch);
            } else {
                queryPersonInfoV2(name, renderTarget, -1, 0, null, dispatch);
            }
        }
    }, [name, renderSignal]);

    useEffect(() => {
        console.log("outside_curOpenTab", curOpenTab);
        // 初次渲染抓基本資料
        const getSugData = async tmpCurOpenTab => {
            // suggester fetch corresponding sheet data
            const data = await fetchSugDataBySheet(
                state.user,
                { active: false },
                personId,
                tmpCurOpenTab
            );

            if (!isEmpty(data)) {
                const filterData = data.filter(i => i.uid === userUid);

                dispatch({
                    type: Act.SET_SUGGESTINFO,
                    payload: filterData
                });
            } else {
                dispatch({
                    type: Act.SET_SUGGESTINFO,
                    payload: {}
                });
            }

            // user.role === allRoles.suggester
            //     ? dispatch({
            //           type: Act.SET_IS_LOADING_SUG_DATA,
            //           payload: true
            //       })
            //     : dispatch({
            //           type: Act.SET_IS_LOADING_SUG_DATA,
            //           payload: false
            //       });
        };
        getSugData(curOpenTab);
    }, [personId, state.user, renderSignal, curOpenTab]);

    const AccordionsNameNodes = "accordions-namenode";
    // components
    const accordions = [
        {
            id: "accordions-basic",
            // todo: authority config store on firebase
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["basic"]),
            component: (
                <CustomFixedTable
                    data={perInfo?.person || []}
                    loading={perInfo.fetchDataLoading}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_PER_TYPE}
                />
            ),
            sheetName: config.DEF_PER_TYPE
        },
        {
            id: AccordionsNameNodes,
            authority: config.NNIAuthority,
            title: formatMessage(intlMsgs["nameNode"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_NNI_TYPE}
                />
            ),
            sheetName: config.DEF_NNI_TYPE
        },
        {
            id: "accordions-education",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["education"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_EDUEVT_TYPE}
                />
            ),
            sheetName: config.DEF_EDUEVT_TYPE
        },
        {
            id: "accordions-employment",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["employment"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_EMPEVT_TYPE}
                />
            ),
            sheetName: config.DEF_EMPEVT_TYPE
        },
        {
            id: "accordions-publication",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["publication"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_PUB_TYPE}
                />
            ),
            sheetName: config.DEF_PUB_TYPE
        },
        {
            id: "accordions-article",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["article"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_ART_TYPE}
                    name={name}
                />
            ),
            sheetName: config.DEF_ART_TYPE
        },
        {
            id: "accordions-otherwork",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["otherwork"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_OTW_TYPE}
                />
            ),
            sheetName: config.DEF_OTW_TYPE
        },
        {
            id: "accordions-organization",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["membership"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_ORGEVT_TYPE}
                />
            ),
            sheetName: config.DEF_ORGEVT_TYPE
        },
        {
            id: "accordions-relation",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["relation"]),
            component: (
                <CustomFixedTable
                    data={perInfo?.relationevent || []}
                    loading={perInfo.fetchDataLoading}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_RELEVT_TYPE}
                />
                // <CustomFlexibleTable
                //     info={perInfo}
                //     ontologyDomain="person"
                //     ontologyType="relationevent"
                // />
            ),
            sheetName: config.DEF_RELEVT_TYPE
        },
        {
            id: "accordions-event",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["event"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_EVT_TYPE}
                />
            ),
            sheetName: config.DEF_EVT_TYPE
        },
        {
            id: "accordions-award",
            authority: config.basicAuthority,
            title: formatMessage(intlMsgs["award"]),
            component: (
                <CustomFlexibleTable
                    info={perInfo}
                    ontologyDomain={config.DEF_PER_DOMAIN}
                    ontologyType={config.DEF_AWDEVT_TYPE}
                />
            ),
            sheetName: config.DEF_AWDEVT_TYPE
        }
    ];

    const filteredAccordions = config.getNNIPermission(userRole)
        ? accordions
        : accordions.filter(item => item.id !== AccordionsNameNodes);

    // useEffect(() => {
    //     const resetAll = () => {
    //         setActiveIndex(0);
    //         dispatch({
    //             type: Act.INFORMATION_CURRENTLY_OPEN_TAB,
    //             payload: "person"
    //         });
    //     };
    //     resetAll();
    // }, [resetSuggestionsTable]);

    return (
        <div className={"information"}>
            <Accordion>
                {filteredAccordions.map((item, idx) => {
                    const {
                        key,
                        id,
                        title,
                        component,
                        authority,
                        sheetName
                    } = item;
                    const isActive = activeIndex === idx;

                    return (
                        handleShowAccor(userRole, authority) && (
                            <React.Fragment key={`react-fragment-${idx}`}>
                                <Accordion.Title
                                    key={`accordion-title-${key}`}
                                    active={isActive}
                                    index={idx}
                                    onClick={(evt, props) =>
                                        handleClick(evt, props, sheetName)
                                    }
                                >
                                    <Icon name="dropdown" />
                                    {title}
                                </Accordion.Title>
                                <Accordion.Content
                                    key={`accordion-content-${key}`}
                                    active={isActive}
                                    className={`Content__${id}`}
                                >
                                    {isActive && component}
                                </Accordion.Content>
                            </React.Fragment>
                        )
                    );
                })}
            </Accordion>
        </div>
    );
};
information.propTypes = {
    intl: PropTypes.objectOf(PropTypes.any).isRequired,
    permission: PropTypes.number
};
export default injectIntl(information);
