//body {
//  overflow: hidden;
//}
.App {
  &-title {
    justify-content: center;
  }

  &-subtitle {
    font-weight: 100;
  }
}

.App {
  text-align: center;
}

.App-logo {
  animation: App-logo-spin infinite 20s linear;
  height: 80px;
}

.App-intro {
  font-size: large;
}

@keyframes App-logo-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}


.configs {
  li {
    margin: 15px 0px;
  }
}

code {
  border-radius: 3px;
  padding: 2px 10px;
  font-size: 17px;
  margin-right: 10px;
  background-color: rgba(222, 222, 222, 0.34);
  border: 1px solid #cccccc;
  color: #868686;
}

.command {
    margin: 40px 0px 50px 0;
}

.description {
  padding: 20px;
}

.readmeLink {
  font-size: 18px;
}

#main-loader-wrapper {
  position: fixed;
  width: 100%;
  height:100%;
  display: flex;
  align-items: center;
  top: 0;
}

.main-loader {
  display: flex;
  margin: 0 auto;
}

.table-data-sources {
  border-collapse: collapse;
  border: 1pt solid black;
  td {
    border: 1pt solid black;
  }
}

.header-with-image {
  border-radius: 2pt;
  text-shadow: 1pt 1pt rgba(0, 0, 0, 0.17);
}

.home-header {
  height: 300px;
  //background:
  //        linear-gradient(
  //                        rgba(255, 255, 255, 0.5),
  //                        rgba(255, 255, 255, 0.7),
  //                        rgba(255, 255, 255, 0.7),
  //                        rgba(255, 255, 255, 0.3)
  //        ),
  //        url("/img/img_202102/HKBDB_V7_bg.png") !important;
  background-size: cover !important;
  background-position: center center;
  border: #868686 1px solid;

  //opacity: 0;  // 有 side effect,不要使用
  transition: opacity 1s ease-in-out;
  &__show {
    opacity: 1;
  }
}



.home-header-small {
  height: 60px;
  //background:
  //        linear-gradient(
  //                        rgba(255, 255, 255, 0.5),
  //                        rgba(255, 255, 255, 0.7),
  //                        rgba(255, 255, 255, 0.7),
  //                        rgba(255, 255, 255, 0.3)
  //        ),
  //        url("/img/img_202102/HKBDB_mobile_v3_BG.png") !important;
  background-size: cover !important;
  background-position: center center;

  //opacity: 0;  // 有 side effect,不要使用
  transition: opacity 1s ease-in-out;
  &__show {
    opacity: 1;
  }
}

.non-home-header {
  height: 100px;
  //background:
  //        linear-gradient(
  //                        rgba(255, 255, 255, 0.5),
  //                        rgba(255, 255, 255, 0.5),
  //        ),
  //        url("/img/img_202102/HKBDB_V7_banner.png") !important;
  background-size: cover !important;
  background-position: center center;
  border: #868686 1px solid;
}

.non-home-header-mobile {
  height: 60px;
  //background:
  //        linear-gradient(
  //                        rgba(255, 255, 255, 0.7),
  //                        rgba(255, 255, 255, 0.7),
  //        ),
  //        url("/img/img_202102/HKBDB_mobile_v3_banner.png") !important;
  background-size: cover !important;
  background-position: center center;
  border: #868686 1px solid;
}

.tree-view-node-toggle:hover {
  cursor: pointer;
}

.header {
  visibility: visible !important;
}

.stored-query-text {
  white-space: pre-wrap;
  border: 1pt solid rgba(0, 0, 0, 0.1);
  font-family: "Courier New", "Courier 10 Pitch", Monospaced, monospace;
}

.home-footer {
  margin: 0 !important;
  z-index: 999;
}


// container width
.ui.container:not(.source-container) {
  min-width: 85%;
}

.ui.container.source-container {
  min-width: 60%;
}

// 登入button樣式
.right.item{
  .ui.secondary.button{
    color: #104860;
    background-color: transparent;
    //border: 1px solid #89ACBB;
  }
}

// header的hover效果
.right.item {
  .ui.inverted.secondary.button{
    border: none;
    background-color: transparent !important;
    box-shadow: none !important;
    span:hover{
      transition: font-weight 0.3s ease;
      font-weight: 700 !important;
    }
  }
  .ui.inverted.secondary.button:hover{
    background-color: transparent !important;
}
}

//header內容上下對齊
.desktop{
  &__menu{
    .ui.secondary.pointing.menu .item{
      align-self: center;
    }
    .ui.secondary.pointing.menu .header.item{
      align-self: flex-end;
    }
  }
}

