import React, { useState, useEffect } from "react";
import { Button, Placeholder, Segment } from "semantic-ui-react";
import { FormattedMessage, injectIntl } from "react-intl";
import classNames from "classnames";
//
import CustomDebounce from "./CustomDeBounce";
import EntityBodyComp from "./EntityBody";

//

export const ResultsEntity = ({
    results,
    className, // root ele 的 className
    classType, // e.g.Person or Organization
    showSearchMessage,
    loading,
    searchValue,
    useShowBtn: useShowBtn = true,
    EntityBody = EntityBodyComp // 若沒有提供 則使用預設 component
}) => {
    const [showResults, setShowResults] = useState(true);
    const [preSearchVal, setPreSearchVal] = useState(null);
    // 重新包裝一下 preSearchVal, 當 preSearchVal 不再發生變化時
    const debPreSearchVal = CustomDebounce(preSearchVal, 1500);

    const onClickBtn = () => {
        setShowResults(!showResults);
    };

    useEffect(() => {
        setShowResults(true);
    }, [results]);

    useEffect(() => {
        setPreSearchVal(searchValue);
    }, [searchValue]);

    if (loading) {
        return (
            <div className={"resultsEntity__placeholder"}>
                <Placeholder>
                    <Placeholder.Line />
                    <Placeholder.Line />
                </Placeholder>
            </div>
        );
    }
    if (!(results && results.bindings)) return null;

    return (
        <Segment className={classNames("resultsEntity", className)}>
            {showSearchMessage && (
                <div
                    style={{ display: "flex", justifyContent: "space-between" }}
                >
                    {/* 找得到結果 */}
                    {results.bindings.length > 0 && (
                        <React.Fragment>
                            <div
                                style={{
                                    fontSize: "0.9em"
                                }}
                            >
                                <FormattedMessage
                                    id="search.found-result-pre"
                                    defaultMessage="Display"
                                />
                                <span> {` ${results.bindings.length} `} </span>
                                <FormattedMessage
                                    id="search.found-result-post"
                                    defaultMessage="results"
                                />
                                {" ( "}
                                <span>{results.durationSS || "  "}</span>
                                {"  "}
                                <FormattedMessage
                                    id="search.found-result-search-seconds"
                                    defaultMessage="seconds"
                                />
                                {"  ) "}
                            </div>
                            {useShowBtn && (
                                <Button
                                    color={showResults ? null : "blue"}
                                    onClick={onClickBtn}
                                    className={"resultsEntity__showBtn"}
                                >
                                    {showResults && (
                                        <FormattedMessage
                                            id="search.btn-hide"
                                            defaultMessage="hide"
                                        />
                                    )}
                                    {!showResults && (
                                        <FormattedMessage
                                            id="search.btn-show"
                                            defaultMessage="show"
                                        />
                                    )}
                                </Button>
                            )}
                        </React.Fragment>
                    )}

                    {/* 找不到結果 */}
                    {results.bindings.length === 0 && (
                        <div
                            style={{
                                fontSize: "0.9em"
                            }}
                        >
                            <FormattedMessage
                                id="search.not-found-pre"
                                defaultMessage="Your search - "
                            />
                            <span style={{ fontWeight: "bold" }}>
                                {/* {preSearchVal} */}
                                {debPreSearchVal}
                            </span>
                            <FormattedMessage
                                id="search.not-found-post"
                                defaultMessage=" - did not match any instances."
                            />
                        </div>
                    )}
                </div>
            )}
            {showResults && EntityBody && (
                <EntityBody bindings={results.bindings} classType={classType} />
            )}
        </Segment>
    );
};

export default injectIntl(ResultsEntity);
