import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_pagination: props => ({
        "& .MuiPaginationItem-root": {
            width: "48px",
            height: "48px",
            borderRadius: "unset",
            "&.Mui-selected": {
                // color: "#b79d79",
                color: "#b79d79",
                backgroundColor: "#333333"
            },
            "&:hover": {
                color: "#fff",
                backgroundColor: "rgba(0,0,0,0.25)"
            },
            // eslint-disable-next-line
            ["@media screen and (max-width: 600px)"]: {
                width: "20px",
                height: "35px"
            }
        },
        "& .MuiPagination-ul": {
            // eslint-disable-next-line
            ["@media screen and (max-width: 600px)"]: {
                display: "flex",
                flexWrap: "nowrap"
            }
        },
        ...props.hkvayb_pagination
    }),
    hkvayb_pagination_group: props => ({
        marginTop: "39.5px",
        display: "flex",
        justifyContent: "flex-end",
        alignItems: "center",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexWrap: "wrap",
            justifyContent: "center"
        },
        ...props.hkvayb_pagination_group
    }),
    hkvayb_pagination_dropdown_group: props => ({
        display: "flex",
        justifyContent: "flex-end",
        alignItems: "center",
        marginLeft: "77px",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            marginLeft: "0"
        },
        ...props.hkvayb_pagination_dropdown_group
    })
});

export default useStyles;
