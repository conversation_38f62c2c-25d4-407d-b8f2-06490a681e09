import React, { useContext } from "react";

// custom
import CustomInput from "./CustomInputEditSelect";
import CustomDateInput from "./CustomDateInputCreated";
import CustomDropdown from "./CustomDropdownEditSelect";

// ui
import { Container, Segment, Header, Label, Divider } from "semantic-ui-react";

// common
import {
    cvtDatasetLocale,
    getProperty,
    isEmpty
} from "../../../../common/codes";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import CustomEditCoordinatesModal from "./CustomEditCoordinatesModal";

const customSelectedPropDataSegment = ({
    rowIdx,
    graph,
    eventId,
    editData,
    setEditData,
    selectedProp,
    ontologyType
}) => {
    //
    const {
        property: propName,
        range: propRange,
        value: propertyBindRangeStr
    } = selectedProp;
    //
    const [state] = useContext(StoreContext);
    const { property: propData, source } = state;
    const { dataset: globalDataset } = source;
    //
    const dividerStyle = {
        margin: ".5rem 0"
    };
    const segmentStyle = () => {
        if (editData.createdRowIds.includes(rowIdx)) {
            return {
                // backgroundColor: "#f8ffff"
                backgroundColor: "#fcfff5"
            };
        } else {
            return {};
        }
    };
    //
    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(propData?.propertyObj)
            ? _property
            : getProperty(_property, propData.propertyObj);
    };

    const title = safeGetProperty(propName);
    //
    const showForm = type => {
        switch (type) {
            case "Place":
            case "Organization":
                return (
                    <CustomEditCoordinatesModal
                        title={title}
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        setEditData={setEditData}
                        ontologyType={ontologyType}
                        propRange={type}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "string":
            case "float":
                return (
                    <CustomInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            case "DateEvent":
                return (
                    <CustomDateInput
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyType={ontologyType}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
            default:
                return (
                    <CustomDropdown
                        rowIdx={rowIdx}
                        graph={graph}
                        eventId={eventId}
                        editData={editData}
                        setEditData={setEditData}
                        ontologyType={ontologyType}
                        propRange={propRange}
                        propertyBindRangeStr={propertyBindRangeStr}
                    />
                );
        }
    };
    //
    return (
        <Segment style={segmentStyle()}>
            <Header as="h5">{safeGetProperty(propName)}</Header>
            <Container>{showForm(propRange)}</Container>
            <Divider hidden style={dividerStyle} />
            <Label color="orange">
                {cvtDatasetLocale(graph, globalDataset)}
            </Label>
        </Segment>
    );
};

export default customSelectedPropDataSegment;
