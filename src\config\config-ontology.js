// graph for 香港中文大學人名資料庫
export const AUDA_HKLIT_GRAPH = "auda_hklit";

export const CLASS_PREFIX = {
    Person: "PER",
    Organization: "ORG"
};

export const CLASS_NAME = {
    Person: "Person",
    Organization: "Organization"
};

const RelationEventType = "RelationEvent";
const NameNodeType = "NameNode";
const DateEventType = "DateEvent";

// instanceRandomID 為需要建立 random ID 的 class 列表
const INSTANCE_RAND_ID = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: "AWEEVT", eventType: "AwardEvent" },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "OTW", eventType: "OtherWork" },
    // 以 localNameId 為 ID
    { prefix: "NNI", eventType: NameNodeType }
];

export const EVT_PREFIX = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: "RELEVT", eventType: RelationEventType },
    { prefix: "AWEEVT", eventType: "AwardEvent" },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: "NNI", eventType: NameNodeType }
];

const VIRTUAL_EVT_PREFIX = [{ prefix: "MEMEVT", eventType: "memberEvent" }];

// instance id 需要 uriEncode 的 SPARQL classType
export const URI_ENCODE_TYPE = [
    { prefix: "PER", eventType: "Person" },
    { prefix: "PLA", eventType: "Place" },
    { prefix: "RELEVT", eventType: RelationEventType },
    { prefix: "EVT", eventType: "Event" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "ADP", eventType: "AcademicDiscipline" },
    { prefix: "ADG", eventType: "AcademicDegree" },
    { prefix: "NTN", eventType: "Nation" },
    { prefix: "PRV", eventType: "Province" },
    { prefix: "CTY", eventType: "City" },
    { prefix: "TNS", eventType: "Township" },
    { prefix: "CNT", eventType: "Continent" },
    { prefix: "VNE", eventType: "Venue" }
];

// 需要建立 nameNode 的 SPARQL classType
export const CREATE_NNI_TYPE = [
    { prefix: "PER", eventType: "Person" },
    { prefix: "ORG", eventType: "Organization" }
];

export const ALL_CLASS = [
    { prefix: "PER", eventType: "Person" },
    { prefix: "PLA", eventType: "Place" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "DAE", eventType: DateEventType },
    { prefix: "OTW", eventType: "OtherWork" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "ADP", eventType: "AcademicDiscipline" },
    { prefix: "ADG", eventType: "AcademicDegree" },
    { prefix: "WEB", eventType: "WebConfig" }
].concat(EVT_PREFIX, VIRTUAL_EVT_PREFIX);
