import React from "react";
import { TextField } from "@mui/material";
import { FormattedMessage } from "react-intl";
import PropTypes from "prop-types";

const YearInput = ({
    id,
    type,
    value,
    onChange,
    min,
    max
}) => {
    const messageMap = {
        start: { id: "map.start.time", defaultMessage: "開始時間" },
        end: { id: "map.end.time", defaultMessage: "結束時間" },
      };
    return (
        <TextField
            required
            id={id}
            label={
                <FormattedMessage
                {...messageMap[type]}
                />
            }
            defaultValue={value}
            type="number"
            size="small"
            value={value}
            sx={{
                marginRight: "12px",
                maxWidth: "160px",
                "& label": { color: "#89ACBB" },
                color: "#104860",
                "& .MuiInputBase-root": { height: 42 },
                "& .MuiInputBase-input": { height: "100%" }
            }}
            onChange={e => {
                const newYear = parseInt(e.target.value, 10);
                onChange(newYear);
            }}
            inputProps={{
                min,
                max
            }}
        />
    );
};

YearInput.propTypes = {
    id: PropTypes.string.isRequired,
    messageId: PropTypes.string.isRequired,
    defaultMessage: PropTypes.string.isRequired,
    value: PropTypes.number.isRequired,
    onChange: PropTypes.func.isRequired,
    min: PropTypes.number.isRequired,
    max: PropTypes.number.isRequired
};

export default YearInput;