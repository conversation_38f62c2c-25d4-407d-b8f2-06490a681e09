import { Icon, Label, List } from "semantic-ui-react";
import React, {
    Component,
    useState,
    useEffect,
    useContext,
    useMemo
} from "react";
// import PropTypes from "prop-types";
import { peopleUrl, orgUrl } from "../../../services/common";
import { rootClasses } from "../browseConfig";
import { StoreContext } from "../../../store/StoreProvider";

// api
import {
    readHkbdbData,
    apiSwithcer,
    varSwitcher,
    apiParamsDefault
} from "../../../api/hkbdb/Api";

import { genRandomKey, cvtDatasetLocale } from "../../../common/codes";
import { FormattedMessage } from "react-intl";
import { useHistory } from "react-router-dom";
import useWindowSize from "../../../hook/useWindowSize";
import uiConfig from "../../../config/config-ui";

// type NodeData = {
//     name: string,
//     type: string,
//     tags: string[],
//     label: Object,
//     toggled: boolean,
//     link: boolean
// };

// use recursion function to render 階層圖
// <TreeViewNode /> includes renderLeafs()
export const renderLeafs = (nodes, parentClassPath, props) => {
    const classPath = (type, name) =>
        type === "node" ? parentClassPath.concat([name]) : parentClassPath;

    return (
        <List>
            {nodes &&
                nodes.map((node, i) => (
                    <TreeViewNode
                        key={`${node.name}-${genRandomKey(10)}`}
                        node={node}
                        classPath={classPath(node.type, node.name)}
                        onClassClicked={props.onClassClicked}
                    />
                ))}
        </List>
    );
};

export const renderNodeIcon = (node, hasChildren) => {
    if (node.type === "node") {
        if (!hasChildren) {
            return "";
        }
        if (node.toggled) {
            return "down caret";
        }
        return "right caret";
    }
    return "";
};

export const renderNodeHeaderAs = (node, classPath) => {
    if (!node.link) {
        return null;
    }
    if (node.type === "node") {
        return "a";
    }
    if (
        classPath &&
        (classPath.includes("Person") || classPath.includes("Organization"))
    ) {
        return "a";
    }
    return null;
};

export const renderTags = tags => {
    // hook
    const size = useWindowSize();
    //
    let _tags = [];
    if (!tags) return null;
    if (tags && !Array.isArray(tags)) {
        _tags = [tags];
    } else {
        _tags = tags;
    }

    const isMobile = () => {
        if (!(Array.isArray(size) && size.length >= 2)) {
            return false;
        }
        return size[0] <= uiConfig.BP_PAD_MIN_WIDTH;
    };

    if (_tags.length < 1) return null;
    // if (!tags || tags.length < 1) {
    //     return null;
    // }
    return (
        <List.Content floated="right" style={{ marginBottom: "8px" }}>
            <div
                style={{
                    display: "flex",
                    flexDirection: isMobile() ? "column" : "row",
                    columnGap: "5px",
                    rowGap: "5px"
                }}
            >
                {_tags.map(tag => (
                    <Label key={`${tag}-${genRandomKey(10)}`}>{tag}</Label>
                ))}
            </div>
        </List.Content>
    );
};

// export function countSubClasses(name) {
//     return countSubClassesOfType(name).then(res =>
//         parseInt(res.bindings[0].count.value, 10)
//     );
// }

// count individuals
export function countIndividuals(name) {
    if (!name) return;
    const api = apiSwithcer[name].count;
    const vars = varSwitcher[name].count[0];
    return readHkbdbData(api).then(res => {
        if (!res.data[0]) return 0;
        return parseInt(res.data[0][vars], 10);
    });
}

// count individuals filtered by graph(資料集)
export function countIndividualsByGraph(name, graph) {
    if (!name) return;
    const api = apiSwithcer[name].countByGraph.replace("{graph}", graph);
    const vars = varSwitcher[name].count[0];
    return readHkbdbData(api).then(res => {
        return parseInt(res.data[0][vars], 10);
    });
}

// 取得實體清單
export function loadIndividuals(
    name,
    offset = apiParamsDefault.loadIndividuals.offset,
    limit = apiParamsDefault.loadIndividuals.limit
) {
    if (!name) return;
    const api = apiSwithcer[name].list
        .replace("{offset}", offset)
        .replace("{limit}", limit);
    const vars = varSwitcher[name].list;
    return readHkbdbData(api).then(res => {
        if (res && res.data && Array.isArray(res.data)) {
            // [{g: "hklit",
            // personName: "(cannot open the o"},{},...]
            const obj = {};
            res.data.forEach(d => {
                if (!obj[d[vars[0]]]) {
                    obj[d[vars[0]]] = {
                        name: d[vars[0]],
                        tags: [d[vars[1]]]
                    };
                } else {
                    obj[d[vars[0]]].tags.push(d[vars[1]]);
                }
            });
            return Object.values(obj);
        }
        return [];
    });
}

// 取得實體清單 filtered by graph(資料集)
export function loadIndividualsByGraph(
    name,
    offset = apiParamsDefault.loadIndividuals.offset,
    limit = apiParamsDefault.loadIndividuals.limit,
    graph
) {
    if (!name) return;
    const api = apiSwithcer[name].listByGraph
        .replace("{offset}", offset)
        .replace("{limit}", limit)
        .replace("{graph}", graph);

    const vars = varSwitcher[name].list;
    return readHkbdbData(api).then(res => {
        if (res && res.data) {
            return res.data.map(d => ({
                ...d,
                name: d[vars[0]],
                tags: d[vars[1]]
            }));
        }
        return [];
    });
}

// export function countTmpOrgSubClasses(name) {
//     return countTmpOrgSubClassesOfType(name).then(res =>
//         parseInt(res.bindings[0].count.value, 10)
//     );
// }
//
// export function countTmpOrgIndividuals(name) {
//     return countTmpOrgIndividualsOfType(name).then(res =>
//         parseInt(res.bindings[0].count.value, 10)
//     );
// }

/**
 *
 * @returns {NodeData}
 */
export function resultsToNodes(items, nodeType) {
    const uniqueClasses = items.reduce((acc, cls) => {
        const name = cls.s.value.split("#")[1];
        const db = cls.g.value.split("/").pop();
        if (!name) {
            return acc;
        }
        if (acc[name]) {
            acc[name].tags.add(db);
        } else {
            acc[name] = {
                name: name,
                type: nodeType,
                label: [],
                tags: new Set([db]),
                toggled: false,
                link: true
            };
        }
        return acc;
    }, {});

    return Object.values(uniqueClasses).map(cls => {
        cls.tags = Array.from(cls.tags);
        cls.tags.sort();
        return cls;
    });
}

// function loadData(name) {
//     // #FIXME#, Request from Tana, to make Organization layer like,
//     // Organization -- SocietalSector -- ...
//     let subClassesPromise = null;
//     if (name === "Organization") {
//         subClassesPromise = getTmpOrgSubClassesWithDbOf(name).then(
//             response => response.bindings
//         );
//     } else {
//         subClassesPromise = getSubClassesWithDbOf(name).then(
//             response => response.bindings
//         );
//     }
//
//     return subClassesPromise
//         .then(res => {
//             return {
//                 classes: resultsToNodes(res, "node")
//             };
//         })
//         .catch(error => {
//             console.error(error);
//             throw error;
//         });
// }

// type NodeState = {
//     individualsCount: number,
//     subClassCount: number,
//     node: NodeData,
//     classPath: string[],
//     children: NodeData[] | null
// };
//
// type NodeProps = {
//     node: NodeData,
//     classPath: string[]
// };

export const TreeViewNode = props => {
    // route
    const history = useHistory();
    //
    const [state, dispatch] = useContext(StoreContext);
    const { browse, source } = state;
    const { curGraph, filterGraphOn } = browse;

    // 資料集中英文名稱
    // [{ dataset: "abcwhkp", label: "《香港古典詩文集經眼錄》", lang: "zh" },{}]
    const { dataset: globalDataset } = source;

    const [individualsCount, setIndividualsCount] = useState(0);
    const [subClassCount, setSubClassCount] = useState(0);
    const [node, setNode] = useState(props.node);
    const [children, setChildren] = useState(null);
    const [classPath, setClassPath] = useState(props.classPath);
    const [childrenCount, setChildrenCount] = useState(0);

    const toggleNode = () => {
        const _node = Object.assign({}, node);
        _node.toggled = !_node.toggled;
        setNode(_node);
    };

    // const onToggle = () => {
    //     if (!node.link) {
    //         return;
    //     }
    //     if (node.toggled) {
    //         toggleNode();
    //     } else {
    //         if (!children) {
    //             loadData(node.name).then(res => {
    //                 const _node = Object.assign({}, node);
    //                 _node.toggled = true;
    //                 setNode(_node);
    //                 setChildren(res.classes);
    //             });
    //         } else {
    //             const _node = Object.assign({}, node);
    //             _node.toggled = true;
    //             setNode(_node);
    //         }
    //     }
    // };

    const onClick = (type, path, name, link) => {
        if (!link) {
            return;
        }

        // display persons or orgs on indiidualsPanel when click on node of left panel
        if (type !== "leaf") {
            props.onClassClicked(name, path, individualsCount);
            return;
        }

        let url;
        // link to people page
        if (path[0] === "Person") {
            url = peopleUrl(name);
        }
        // link to organizaiton page
        else if (path[0] === "Organization") {
            url = orgUrl(name);
        } else {
            url = `/entity/${path[0]}/${name}`;
        }
        // 在原tab開啟
        history.push(url);

        // 另開分頁
        // window.open(url, "_blank", "noopener,noreferrer");
    };

    // 建立 mounted 以確認當前 component mounted 狀態
    const mounted = useMemo(() => ({ current: true }), []);

    useEffect(() => {
        return () => {
            mounted.current = false;
        };
    }, [mounted]);

    useEffect(() => {
        if (!node) return;
        // node.type => 有 node, leaf 二種
        if (node.type === "node") {
            if (node.name === "Organization") {
                // Organization -- SocietalSector -- ...

                // countTmpOrgSubClasses(node.name).then(count => {
                //     this.setState({
                //         subClassCount: count
                //     });
                // });
                // countTmpOrgIndividuals(node.name).then(count => {
                //     this.setState({
                //         individualsCount: count
                //     });
                // });

                countIndividuals(node.name).then(count => {
                    // 若 mounted 時, 才 setIndividualsCount, 否則會造成 memory leak
                    if (mounted.current) setIndividualsCount(count);
                });
            } else if (node.name === "Person") {
                // countSubClasses(node.name).then(count => {
                //     this.setState({
                //         subClassCount: count
                //     });
                // });

                countIndividuals(node.name).then(count => {
                    // 若 mounted 時, 才 setIndividualsCount, 否則會造成 memory leak
                    if (mounted.current) setIndividualsCount(count);
                });
            }
        }
    }, [node.name, curGraph, filterGraphOn]);

    useEffect(() => {
        setChildrenCount(individualsCount + subClassCount);
    }, [individualsCount, subClassCount]);

    return (
        <List.Item key={node.name}>
            {/* tags: 資料集名稱 */}
            {node.type === "leaf"
                ? renderTags(cvtDatasetLocale(node.tags, globalDataset))
                : null}
            {childrenCount ? (
                <Icon
                    name={renderNodeIcon(node, childrenCount > 0)}
                    className="tree-view-node-toggle"
                    // onClick={this.onToggle.bind(this)}
                    size="large"
                />
            ) : (
                <Icon
                    name={renderNodeIcon(node, childrenCount > 0)}
                    size="large"
                />
            )}

            <List.Content style={{ width: "100%" }}>
                <List.Header
                    as={renderNodeHeaderAs(node, classPath)}
                    onClick={() =>
                        onClick(node.type, classPath, node.name, node.link)
                    }
                >
                    {node && node.name === rootClasses[0].name && (
                        <FormattedMessage
                            id="browse.classPerson"
                            defaultMessage="Person"
                        />
                    )}
                    {node && node.name === rootClasses[1].name && (
                        <FormattedMessage
                            id="browse.classOrganization"
                            defaultMessage="Organization"
                        />
                    )}
                    {node &&
                        node.name !== rootClasses[0].name &&
                        node.name !== rootClasses[1].name && (
                            <React.Fragment>{node.name}</React.Fragment>
                        )}
                    {node.type === "node" ? (
                        <span>
                            {/* TODO: 暫時用不到 subClassCount */}
                            {/* <Label size="mini" circular> */}
                            {/*   Classes{" "} */}
                            {/*   <Label.Detail> */}
                            {/*       {this.state.subClassCount} */}
                            {/*   </Label.Detail> */}
                            {/* </Label> */}
                            <Label
                                size="mini"
                                circular
                                style={{ marginLeft: "10px" }}
                            >
                                <FormattedMessage
                                    id="browse.individualsCount"
                                    defaultMessage="Individuals"
                                />{" "}
                                <Label.Detail>{individualsCount}</Label.Detail>
                            </Label>
                        </span>
                    ) : null}
                </List.Header>

                {/* TODO: 暫時不用展開第 2 階層 */}
                {node.link && node.toggled
                    ? renderLeafs(children, classPath, props)
                    : null}
            </List.Content>
        </List.Item>
    );
};
