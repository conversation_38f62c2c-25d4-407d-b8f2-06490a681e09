import React from "react";
import { Placeholder } from "semantic-ui-react";
import CustomAvatar from "../../../common/components/avatar";
import useStyles from "./style/pageStyle";

const AvatarHOC = ({ personImgSrc, type, onClick }) => {
    // style
    const classes = useStyles();
    return (
        <div onClick={onClick}>
            {!personImgSrc && personImgSrc !== "" && (
                <Placeholder className={classes.avatarPlaceholder}>
                    <Placeholder.Image />
                </Placeholder>
            )}
            {personImgSrc === "" && (
                <CustomAvatar.Normal
                    className={classes.avatarRoot}
                    src={personImgSrc}
                    type={type}
                />
            )}
            {personImgSrc && (
                <CustomAvatar.Normal
                    className={`${classes.avatarRoot} ${classes.avatarCursor}`}
                    src={personImgSrc}
                    type={type}
                />
            )}
        </div>
    );
};

export default AvatarHOC;
