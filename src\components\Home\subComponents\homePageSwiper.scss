.swiper-button-prev, .swiper-button-next {
  &:after {
    display: none;
    color: #fff;
  }
  .MuiAvatar-root {
    width: 56px;
    height: 56px;
    opacity: 1;
    background-color: #fff;
    .MuiSvgIcon-root {
      color: rgba(0, 0, 0, 0.54);
      font-size: 44px;
    }
  }
  &.swiper-button-disabled {
    //opacity: 1;
    cursor: pointer;
    pointer-events: auto;
  }
}

.swiper-button-prev {
  left: 25px;
}

.swiper-button-next {
  right: 25px;
}

.swiper-card {
  width: 200px;
  height: 147px;
  background-position: center;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &__text {
    margin-bottom: 20px;
    &--title {
      z-index: 50;
      text-align: center;
      font-size: 30px;
      color: #fff;
      font-weight: 500;
      margin-top: 15px;
      margin-bottom: 4px;
    }
    &--subtitle {
      z-index: 50;
      text-align: center;
      font-size: 16px;
      color: #fff;
      font-weight: normal;
    }
  }
}

@mixin literature-swiper($height,$flex-direction,$align-items){
  width: 300px;
  height: $height;
  background-position: center;
  background-size: cover;
  display: flex;
  flex-direction:$flex-direction ;
  align-items: $align-items;
  justify-content: center;
  cursor: pointer;
}

.literature-swiper-card{
  @include literature-swiper(380px,column,center);
}

.region-literature-swiper-card{
  @include literature-swiper(280px,row,flex-end);
}
