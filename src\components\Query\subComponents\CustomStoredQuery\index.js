// react
import React from "react";

// ui
import { Container, Divider } from "semantic-ui-react";

// custom
import CustomSearch from "./CustomSearch";
import CustomAccordion from "./CustomAccordion";
import CustomModal from "./CustomModal";

const CustomStoredQuery = ({ setTextareaValue }) => {
    return (
        <Container>
            <CustomSearch />
            <Divider />
            <CustomModal setTextareaValue={setTextareaValue} />
            <CustomAccordion />
        </Container>
    );
};

export default CustomStoredQuery;
