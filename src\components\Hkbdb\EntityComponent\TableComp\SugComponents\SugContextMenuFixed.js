import React, { useContext, useState, useEffect } from "react";
import { Men<PERSON>, Button, Popup, Icon } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isNotEmpty, isTrue, safeGet } from "../../../../../common/codes";
import { bs64EncodeId } from "../../../../../common/codes/jenaHelper";
import authority from "../../../../../App-authority";
import { FormattedMessage } from "react-intl";
import SugFixedDeleteModal from "./SugFixedDeleteModal";
import allRoles from "../../../../../App-role";

const SugContextMenuFixed = ({
    data,
    ontologyDomain,
    ontologyType,
    headers,
    handleDelete
}) => {
    const [state] = useContext(StoreContext);
    const { personId } = state.information;
    const { uid, role } = state.user;
    const [popupOpen, setPopupOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);

    const handleReFormatData = () => {
        const eventId = safeGet(data, ["eventId"], "");
        const range = safeGet(data, ["range"], "");
        const property = safeGet(data, ["property"], "");
        // const remark = safeGet(data, ["remark"], "");
        const remark = safeGet(data, ["relationRemarks"], "");
        const values = safeGet(data, ["values"], []).sort();
        const dataset = safeGet(data, ["dataset"], []);
        const propertyBindRangeStr = `${property}__${range}`;

        let rowData = dataset.map(graph => ({
            srcId: personId,
            eventId: "",
            property,
            propertyBindRangeStr,
            range,
            remark,
            values,
            graph
        }));

        if (isNotEmpty(remark)) {
            const graph = safeGet(dataset, [0], "");
            if (isNotEmpty(graph)) {
                rowData = [
                    ...rowData,
                    {
                        srcId: "",
                        eventId: bs64EncodeId(eventId),
                        property: "relationRemarks",
                        propertyBindRangeStr: `relationRemarks__string`,
                        range: "string",
                        values: [remark],
                        graph
                    }
                ];
            }
        }

        return {
            // init and reformat data
            rowData: rowData.map((item, idx) => ({ ...item, rowId: idx })),
            // record changed data
            changedData: {},
            updatedRowIds: [],
            isUpdated: false,
            // record deleted data
            deletedData: [],
            deletedRowIds: [],
            isDeleted: false,
            //
            createdData: {},
            createdRowIds: [],
            isCreated: false,
            // edit menu
            // ontology: ontology || [],
            selectedProperties: []
        };
    };

    const [editData, setEditData] = useState(handleReFormatData);

    useEffect(() => {
        setEditData(handleReFormatData());
        // todo deps中使用JSON方法對於資料量少的情況下很有用，但資料量大則會很耗資源，未來優化項目
    }, [JSON.stringify(data)]);

    const handleItemClick = (e, { name }) => {
        switch (name) {
            case "edit":
                // open editModal
                setEditModalOpen(true);
                break;
            case "delete":
                // open deleteModal
                setDeleteModalOpen(true);
                break;
            default:
                break;
        }
        // close popup
        setPopupOpen(false);
    };
    const handleClose = () => {
        setPopupOpen(false);
    };

    const PopupButton = (
        <Button
            icon="ellipsis vertical"
            size="mini"
            // style={{ backgroundColor: "#f9fafb" }}
            onClick={() => setPopupOpen(true)}
        />
    );

    if (
        isTrue(process.env.REACT_APP_CRUD_NODE) &&
        isNotEmpty(uid) &&
        authority.People_Information.includes(role)
    ) {
        return (
            <React.Fragment>
                <Popup
                    flowing
                    hoverable
                    hideOnScroll
                    trigger={PopupButton}
                    open={popupOpen}
                    style={{ zIndex: "1" }}
                    onClose={handleClose}
                >
                    <Menu size="mini" vertical>
                        {/* 不開放suggester使用 */}
                        <Menu.Item
                            name="delete"
                            onClick={handleItemClick}
                            // disabled={isDisabled}
                        >
                            <Icon name="delete" />
                            <FormattedMessage
                                id={"people.Information.menu.delete"}
                                defaultMessage={"Delete"}
                            />
                        </Menu.Item>
                    </Menu>
                </Popup>
                {role === allRoles.suggester && deleteModalOpen && (
                    <SugFixedDeleteModal
                        open={deleteModalOpen}
                        setOpen={setDeleteModalOpen}
                        data={data}
                        ontologyType={ontologyType}
                        headers={headers}
                        handleDelete={handleDelete}
                        type="temporary"
                    />
                )}
            </React.Fragment>
        );
    } else {
        return null;
    }
};

export default SugContextMenuFixed;
