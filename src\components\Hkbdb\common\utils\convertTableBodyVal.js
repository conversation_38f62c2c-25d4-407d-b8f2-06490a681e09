import { bs64Decode } from "../../../../common/codes";

const { GV } = require("../../../../common/codes/globalVars");
const {
    findPropertyRange,
    convertDateEvent2
} = require("../../../../common/codes");

/**
 * @description 根據資料型別("range")做畫面字串對應顯示處理
 * "range" => 從propArr === ontologyDefined[ontologyType] 找 property名稱
 * */
const convertTableBodyVal = (val, property, propArr) => {
    if (!val) {
        return val;
    }

    const checkIsArray = () => {
        let res;
        // 針對如地點有多個value的情況下處理
        if (Array.isArray(val)) {
            const joinStr = val.join(",");
            if (joinStr.includes("PLA")) {
                const decodeArray = val.map(v => decodeURI(v.substring(3)));
                res = decodeArray.join(",");
            } else if (joinStr.includes("PER")) {
                const decodeArray = val.map(v => {
                    const vs = v.split("PER");
                    const xx = decodeURI(vs[1]);
                    return xx;
                });
                const yyy = decodeArray.join(",");
                res = yyy;
            } else {
                res = val.join(",");
            }
        }
        return res;
    };

    // 找property被定義的型別
    const range = findPropertyRange(property, propArr);

    switch (range) {
        case GV.PERSON:
            return Array.isArray(val)
                ? checkIsArray()
                : decodeURI(val.split("PER")[1]);
        case GV.DATE_EVENT:
            if (Array.isArray(val)) {
                return checkIsArray();
            }
            if (val.startsWith(val)) {
                return convertDateEvent2(val);
            }
            // suggestions改為暫存後，property是DateEvent的值會是字串
            return val;
        default:
            if (Array.isArray(val)) {
                return checkIsArray();
            }
            return val.includes("PLA") ? decodeURI(val.substring(3)) : val;
    }
};

export default convertTableBodyVal;
