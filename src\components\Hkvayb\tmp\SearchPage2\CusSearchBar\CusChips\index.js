import React, { useContext, useState, useEffect } from "react";

import { makeStyles } from "@material-ui/core/styles";

import Chip from "@material-ui/core/Chip";

import Alert from "@material-ui/lab/Alert";

import { isEmpty } from "../../../../../../common/codes";

import { StoreContext } from "../../../../../../store/StoreProvider";

import act from "../../../../../../store/actions";

const useStyles = makeStyles(theme => ({
    root: {
        display: "flex",
        justifyContent: "center",
        flexWrap: "wrap",
        listStyle: "none",
        padding: theme.spacing(0.5),
        margin: 0
    },
    chip: {
        margin: theme.spacing(0.5)
    },
    clickedChip: {
        color: "#009688",
        border: "2px solid #009688",
        margin: theme.spacing(0.5)
    }
}));

const alertMsg = "目前無任何「選項」可供選擇。";

const CusChips = ({ data }) => {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { reset } = state.searchPage2;

    const [chipData, setChipData] = useState(() => {
        if (isEmpty(data)) {
            return [];
        }
        return [
            { key: -1, label: "選擇全部", clicked: true },
            ...data.map((item, index) => ({
                key: index,
                label: item.label,
                value: item.value,
                clicked: true
            }))
        ];
    });

    const handleOnClick = selectedChip => () => {
        setChipData(chips => {
            return chips.map(chip => {
                if (chip.key === selectedChip.key) {
                    return {
                        ...chip,
                        clicked: !chip.clicked
                    };
                }
                return chip;
            });
        });
    };

    const handleOnClickAll = selectedChip => () => {
        if (!selectedChip.clicked) {
            setChipData(chips =>
                chips.map(chip => ({
                    ...chip,
                    clicked: true
                }))
            );
        } else {
            setChipData(chips =>
                chips.map(chip => ({
                    ...chip,
                    clicked: false
                }))
            );
        }
    };

    useEffect(() => {
        const selectedTypes = chipData.reduce((prevArr, chip) => {
            if (chip.key !== -1 && chip.clicked) {
                return [...prevArr, chip.value];
            }
            return prevArr;
        }, []);
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_TYPES,
            payload: selectedTypes
        });
    }, [chipData]);

    useEffect(() => {
        // 初始化設定
        setChipData(chips =>
            chips.map(chip => ({
                ...chip,
                clicked: true
            }))
        );
    }, [reset]);

    if (isEmpty(data)) {
        return (
            <Alert
                style={{
                    width: "100%",
                    marginLeft: "15px",
                    marginRight: "15px"
                }}
                severity="warning"
            >
                {alertMsg}
            </Alert>
        );
    }

    return (
        <div component="ul" className={classes.root}>
            {chipData &&
                chipData.map(data => {
                    let color, variant, chipClass;
                    if (data.clicked) {
                        color = "primary";
                        variant = undefined;
                        variant = "outlined";
                        chipClass = classes.clickedChip;
                    } else {
                        color = "default";
                        variant = "outlined";
                        chipClass = classes.chip;
                    }
                    return (
                        <li key={data.key}>
                            <Chip
                                clickable={true}
                                color={color}
                                label={data.label}
                                className={chipClass}
                                onClick={
                                    data.key === -1
                                        ? handleOnClickAll(data)
                                        : handleOnClick(data)
                                }
                                variant={variant}
                            />
                        </li>
                    );
                })}
        </div>
    );
};

export default CusChips;
