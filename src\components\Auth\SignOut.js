import React, { useContext, useEffect } from "react";

// firebase
import firebase from "firebase/app";
import "firebase/auth";
import { Redirect } from "react-router";

// store
import { StoreContext } from "../../store/StoreProvider";
import Act from "../../store/actions";

const SignOut = () => {
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user, main } = state;
    const { production } = main;
    const locale = user && user.locale ? user.locale : "";

    useEffect(() => {
        if (isLogin) {
            // SignOut firebase
            firebase
                .auth()
                .signOut()
                .then(() => {
                    // Sign-out successful.
                    // clean user data
                    dispatch({ type: Act.FIREBASE_LOGOUT_USER });
                    // clean localStorage
                    localStorage.removeItem("isLogin");

                    // refresh anonymous token
                    // sign out 後要 refresh anonymousToken
                    dispatch({
                        type: Act.REFRESH_ANONYMOUS_TOKEN,
                        payload: true
                    });
                })
                .catch(error => {
                    // An error happened.
                    console.log(error);
                });
        } else {
            // console.log('do nothing');
        }
    }, []);

    // eslint-disable-next-line react/jsx-filename-extension
    // redirect to home
    // fixme: redirect to "/en" => layout broken
    // return <Redirect to={`/${locale}`} />;
    return <Redirect to="/" />;
};

export default SignOut;
