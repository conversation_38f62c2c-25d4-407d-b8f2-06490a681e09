import React from "react";

import useStyles from "./style";

import CusLoading from "../CusLoading";

import noImage from "../../image/no_image.png";

const CusImage = ({
    src = "",
    backupSrc = "",
    alt = "",
    style = {},
    ...rest
}) => {
    const classes = useStyles(style);
    let errorTimes = 0;
    let errorFlag = true;

    const [loading, setLoading] = React.useState(true);

    const handleOnError = event => {
        if (errorFlag) {
            if (errorTimes < 1) {
                ++errorTimes;
                event.target.src = backupSrc;
            } else {
                errorFlag = false;
                event.target.src = noImage;
            }
        }
    };

    return (
        <div className={classes.hkvayb_image}>
            {loading && (
                <CusLoading
                    style={{
                        hkvayb_div: {
                            ...style
                        }
                    }}
                />
            )}
            <img
                {...rest}
                className={classes.hkvayb_div}
                style={style}
                alt={alt}
                src={src}
                loading="lazy"
                onError={handleOnError}
                onLoad={() => setLoading(false)}
            />
        </div>
    );
};

export default CusImage;
