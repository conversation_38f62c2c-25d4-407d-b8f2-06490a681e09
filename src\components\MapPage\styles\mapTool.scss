
/*********** map tool css style ***********/
.maps-tool {
  display: flex;
  justify-content: flex-start;
  z-index: 2000;

  /**/
  &__toggle-btn {
    width: 40px;
    height: 40px;
    border-radius: 5px;
    border: #bfbfbf solid 2px;
    text-align: center;
    padding: 7px 5px;
    background: #fff;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &--iconbtn {}
  }

  & .tool-legend__groups {
    order: -1;
    width: 180px;
    visibility: visible;
    padding: 16px 24px;
    opacity: 1.0;
    height: auto !important;
    flex-direction: column;
    background: #fff;
    border: #a6a6a6 solid 1px;
    border-radius: 5px;
    /*width: 0px;*/
    transition: all 0.3s linear;
    margin-right: 10px;
  }

  /* hidden */
  & .tool-legend__groups-_hidden {
    visibility: hidden;
    padding: 0;
    opacity: 0;
    height: 0 !important;
  }

  /**/
  & .tool-group {
    display: flex;
    flex-direction: column;
    border-bottom: #a6a6a6 solid 1px;
    padding: 4px 0 8px 0;
    h5 {
      margin-top: 12px;
      margin-bottom: 12px;
    }
    /**/
    .option-item {
      //margin-bottom: 15px;
    }
  }

  /**/
  & .legend-group {
    display: flex;
    flex-direction: column;
    padding: 4px 0 8px 0;
    h5 {
      margin-top: 12px;
      margin-bottom: 12px;
    }
    /**/
    .point-marker-palette {
      display: flex;
      flex-direction: column;
      border: #a6a6a6 1px solid;
      border-radius: 5px;
      width: 100px;
      padding: 5px;
      background: #fff;
      box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.2);
      margin-top: 15px;
      /**/
      .palette-item {
        display: flex;
        flex-direction: row;
      }
      /**/
      .color-block-container {
        display: flex;
        align-items: center;
        /**/
        .color-block {
          width: 10px;
          height: 10px;
          margin-right: 5px;
        }
      }
    }
  }

}







