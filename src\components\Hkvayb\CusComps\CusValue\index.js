import React from "react";

import useStyles from "./style";

import CusValueLink from "../CusValueLink";

import { isShow, isNotEmpty } from "../../../../common/codes";

const CusValue = ({ value, type, prefix = "", defVal = "-", style = {} }) => {
    const classes = useStyles(style);
    switch (type && type.toLowerCase()) {
        case "person":
        case "organization":
            return <CusValueLink value={value} type={type} />;
        default:
            return (
                <div className={classes.hkvayb_div}>
                    {isNotEmpty(value) && prefix}
                    {isNotEmpty(prefix) && isNotEmpty(value) && (
                        <div className={classes.hkvayb_div} />
                    )}
                    {isShow(value, defVal)}
                </div>
            );
    }
};

export default CusValue;
