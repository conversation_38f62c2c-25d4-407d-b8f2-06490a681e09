// react
import React, { useState, useContext, useEffect } from "react";

// ui
import { Modal, Button, Icon, Form, TextArea, Radio } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// firebase api
import cloudStorage from "../../../../../../api/firebase/cloudFirestore/Api";

// common
import { isEmpty } from "../../../../../../common/codes";

// import
import authority from "../../../../../../App-authority";
import { isPermitting } from "../../../../../../App-header";
import { FormattedMessage, injectIntl } from "react-intl";

const CustomSaveButton = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // get query from state
    const { user, query } = state;
    const { uid, displayName, email, role } = user;
    const { queryString, selectedQueryId } = query;
    // modal switch
    const [open, setOpen] = useState(false);
    const [titleEn, setTitleEn] = useState("");
    const [titleZh, setTitleZh] = useState("");
    const [isPrivate, setIsPrivate] = useState(false);
    // handle close modal
    const handleClose = () => {
        // back to default
        setOpen(false);
        setTitleEn("");
        setTitleZh("");
        setIsPrivate(false);
        // clean query Id
        dispatch({ type: Act.QUERY_SELECTED_QUERY_ID_CEL });
    };
    // handle open modal
    const handleOpen = () => {
        if (isEmpty(selectedQueryId)) {
            setTitleEn("");
            setTitleZh("");
            setIsPrivate(false);
        }
        setOpen(true);
    };
    // handle TitleEn Change
    const handleTitleEnChange = (event, { value }) => {
        setTitleEn(value);
    };
    // handle TitleZh Change
    const handleTitleZhChange = (event, { value }) => {
        setTitleZh(value);
    };
    // handle isPrivate Change
    const handleIsPrivate = () => {
        setIsPrivate(!isPrivate);
    };
    // handleSave
    const handleSave = async () => {
        // check params
        if (isEmpty(uid)) {
            console.error("handleSave param error: uid not exist.");
            return;
        }
        if (isEmpty(queryString)) {
            console.error("handleSave param error: queryString not exist.");
            return;
        }
        if (isEmpty(titleEn) && isEmpty(titleZh)) {
            console.error("handleSave param error: title not exist.");
            return;
        }
        // doc params
        const doc = {
            query: queryString,
            author: {
                uid,
                displayName,
                email
            },
            title: {
                en: titleEn,
                zh: titleZh
            },
            private: isPrivate,
            timestamp: new Date().getTime()
        };
        // insert data
        const success = await cloudStorage.insertQuery(doc);
        if (success) {
            // update queryReducer state
            dispatch({
                type: Act.QUERY_RELOAD_RENDER_SIGNAL_SET,
                payload: `reload-queries-${new Date().getTime()}`
            });
            // clean query Id
            // dispatch({ type: Act.QUERY_SELECTED_QUERY_ID_CEL });
            // done & close
            handleClose();
        } else {
            console.log("insert query undone");
        }
    };
    //
    return (
        role &&
        authority.Query_advance &&
        isPermitting(role, authority.Query_advance) && (
            <Modal
                open={open}
                onClose={handleClose}
                onOpen={handleOpen}
                trigger={
                    <Button size="tiny" disabled={isEmpty(queryString)}>
                        <span>
                            <FormattedMessage
                                id="custom.saveButton"
                                defaultMessage="Save"
                            />
                        </span>
                    </Button>
                }
            >
                <Modal.Header>
                    <FormattedMessage
                        id="query.store.save.title"
                        defaultMessage="Stored query"
                    />
                </Modal.Header>
                <Modal.Content image scrolling>
                    <Modal.Description>
                        <Form>
                            <Form.Field>
                                <label>
                                    <FormattedMessage
                                        id="query.store.save.query.en.title"
                                        defaultMessage="Query title (en)"
                                    />
                                </label>
                                <TextArea
                                    placeholder="Query title in English"
                                    onChange={handleTitleEnChange}
                                    value={titleEn}
                                />
                            </Form.Field>
                            <Form.Field>
                                <label>
                                    <FormattedMessage
                                        id="query.store.save.query.zh.title"
                                        defaultMessage="Query title (zh)"
                                    />
                                </label>
                                <TextArea
                                    placeholder="Query title in Chinese"
                                    onChange={handleTitleZhChange}
                                    value={titleZh}
                                />
                            </Form.Field>
                            <Form.Field>
                                <label>
                                    <FormattedMessage
                                        id="query.store.save.private"
                                        defaultMessage="Private"
                                    />
                                </label>
                                <Radio
                                    toggle
                                    // label="Private"
                                    checked={isPrivate}
                                    onChange={handleIsPrivate}
                                />
                            </Form.Field>
                        </Form>
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    <Button onClick={handleClose} primary>
                        <Icon name="close" />{" "}
                        <FormattedMessage
                            id="custom.cancelButton"
                            defaultMessage="Cancel"
                        />
                    </Button>
                    <Button color="green" onClick={handleSave}>
                        <Icon name="checkmark" />
                        <span>
                            <FormattedMessage
                                id="custom.saveButton"
                                defaultMessage="Save"
                            />
                        </span>
                    </Button>
                </Modal.Actions>
            </Modal>
        )
    );
};

export default injectIntl(CustomSaveButton);
