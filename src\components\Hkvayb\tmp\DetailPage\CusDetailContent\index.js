import React from "react";

// history
import { useHistory } from "react-router-dom";

// material ui
import { Typography, Button } from "@material-ui/core";

import ArrowBackIcon from "@material-ui/icons/ArrowBack";

// material style
import { makeStyles } from "@material-ui/core/styles";

// common code
import { safeGet } from "../../../../../common/codes";

const useStyles = makeStyles(theme => ({
    button: {
        margin: theme.spacing(1)
    }
}));

const CusClassTpg = ({ data }) => {
    const classes = useStyles();
    const history = useHistory();

    const handleOnClick = () => {
        history.push("/zh-hans/hkvaybTest");
    };

    return (
        <React.Fragment>
            <br />
            <Typography key={`999`} variant="h3" gutterBottom>
                {safeGet(data, ["awardTitle"], []).join("、")}
            </Typography>
            <br />
            {data &&
                Object.keys(data).map((key, keyIdx) => {
                    const content = safeGet(data, [key], []).join("、");
                    return (
                        <div key={`div-${keyIdx}`}>
                            <Typography
                                key={`${keyIdx}`}
                                variant={"h6"}
                                gutterBottom
                            >
                                {`${key}: ${content}`}
                            </Typography>
                            <br />
                        </div>
                    );
                })}
            <br />
            <Button
                variant="contained"
                color="default"
                className={classes.button}
                onClick={handleOnClick}
            >
                <ArrowBackIcon fontSize="large" />
            </Button>
        </React.Fragment>
    );
};

export default CusClassTpg;
