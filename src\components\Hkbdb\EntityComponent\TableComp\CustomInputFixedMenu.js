import React, { useEffect, useState } from "react";

// ui
import { Input, TextArea } from "semantic-ui-react";

// common comp
import CustomDebounce from "./CustomDeBounce";

// common func
import { safeGet, isNotEmpty } from "../../../../common/codes";
import { convertToStringArray } from "../commonAction";
import config from "../../../../config/config";

const CustomInput = ({
    rowIdx,
    graph,
    eventId,
    propertyBindRangeStr,
    editData,
    setEditData,
    defaultValue,
    ontologyType
}) => {
    // Input 的 value 必需存字串或字串陣列
    const arrayValues = convertToStringArray(defaultValue);
    const newDefaultValue = arrayValues.join("\n");

    const [inputValue, setInputValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debInputValue = CustomDebounce(inputValue, 500);

    const handleUpdateState = () => {
        const changedData = {
            // 注意: 這裡 rowIdx 會自動被轉為 string，object' key 只允許字串內容
            // 後續使用 rowIdx 時要注意
            [rowIdx]: {
                graph,
                srcId:
                    safeGet(editData, ["rowData", rowIdx, "eventId"], "") ||
                    eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: [debInputValue]
            }
        };

        // update useState
        if (isNotEmpty(debInputValue)) {
            // update editData state
            setEditData(prevEditData => {
                return {
                    ...prevEditData,
                    changedData: {
                        // old changed items
                        ...prevEditData.changedData,
                        // new changed item
                        ...changedData
                    }
                };
            });
        } else {
            if (isNotEmpty(editData.changedData)) {
                setEditData(prevEditData => {
                    //
                    const {
                        [rowIdx]: _,
                        ...restChangedData
                    } = prevEditData.changedData;
                    //
                    return {
                        ...prevEditData,
                        changedData: {
                            ...restChangedData
                        }
                    };
                });
            }
        }
    };

    const handleChange = (event, { value }) => {
        setInputValue(value);
    };

    useEffect(() => {
        if (newDefaultValue !== debInputValue) {
            handleUpdateState();
        }
    }, [debInputValue]);

    // 多語系
    if (config.MULTIPLE_VALUES.indexOf(propertyBindRangeStr) > -1) {
        return (
            <TextArea
                type="text"
                onChange={handleChange}
                defaultValue={newDefaultValue}
                disabled={editData.isUpdated || editData.isCreated}
            />
        );
    }
    return (
        <Input
            fluid
            type="text"
            onChange={handleChange}
            defaultValue={newDefaultValue}
            disabled={editData.isUpdated || editData.isCreated}
        />
    );
};

export default CustomInput;
