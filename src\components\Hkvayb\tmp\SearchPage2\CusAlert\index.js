import React, { useState, useContext, useEffect } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { Alert, AlertTitle } from "@material-ui/lab";

import { Collapse, IconButton } from "@material-ui/core";

import CloseIcon from "@material-ui/icons/Close";

import { StoreContext } from "../../../../../store/StoreProvider";

import act from "../../../../../store/actions";

import { isEmpty } from "../../../../../common/codes";

const useStyles = makeStyles(theme => ({
    root: {
        width: "100%",
        "& > * + *": {
            marginTop: theme.spacing(2)
        }
    }
}));

const CusAlert = () => {
    const classes = useStyles();
    const [open, setOpen] = useState(true);
    const [state, dispatch] = useContext(StoreContext);
    const { error } = state.searchPage2;
    const { type, message } = error; // error | warning | info | success

    useEffect(() => {
        const handler = setTimeout(() => {
            setOpen(false);
        }, 5 * 1000);
        return () => {
            clearTimeout(handler);
            setOpen(false);
            dispatch({ type: act.CLE_SEARCHPAGE_SEARCHBAR_ERROR });
        };
    }, []);

    if (isEmpty(type) && isEmpty(message)) {
        return null;
    }

    return (
        <div className={classes.root}>
            <Collapse in={open}>
                <Alert
                    severity={`${type}`.toLowerCase()}
                    action={
                        <IconButton
                            aria-label="close"
                            color="inherit"
                            size="small"
                            onClick={() => {
                                setOpen(false);
                            }}
                        >
                            <CloseIcon fontSize="inherit" />
                        </IconButton>
                    }
                >
                    <AlertTitle>{type}</AlertTitle>
                    {message}
                </Alert>
            </Collapse>
        </div>
    );
};

export default CusAlert;
