// react
import React from "react";

// ui
import { Container } from "semantic-ui-react";

// custom
import CustomPagination from "./CustomPagination";
import CustomInputDropdown from "./CustomInputDropdown";

const index = () => {
    //
    const customSpanStyle = {
        padding: "1em"
    };
    const customVirtualDropDownStyle = {
        padding: "3.3em"
    };
    //
    return (
        <Container>
            <CustomPagination />
            <span style={customSpanStyle} />
            <CustomInputDropdown />
            {/*
                span 只是用來排版的
                這是因為 semantic-ui 的 Input 和 DropDown 組合應有用個問題，
                (CustomInputDropdown.js) 在頁面上只會顯示 Input 的寬度而不包含 DropDown，
                e.g.
                假設: input(width: 10) + dropdown(width: 10) = ?
                理想答案: width 應為 20
                實際答案: width 只有 input 的 10
                所以做一個虛擬的 html 補一下 (customVirtualDropDownStyle)
            */}
            <span style={customVirtualDropDownStyle} />
        </Container>
    );
};

export default index;
