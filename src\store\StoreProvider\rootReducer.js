// combine
import combineReducer from "./combineReducer";

// reducer
import userReducer from "../reducers/userReducer";
import personInfoReducer from "../reducers/personInfoReducer";
import orgReducer from "../reducers/organizationReducer";
import propertyReducer from "../reducers/propertyReducer";
import mainReducer from "../reducers/mainReducer";
import sourceReducer from "../reducers/sourceReducer";
import accountReducer from "../reducers/accountReducer";
import settingReducer from "../reducers/settingReducer";
import browseReducer from "../reducers/browseReducer";
import queryReducer from "../reducers/queryReducer";
import messageReducer from "../reducers/messageReducer";
import backendReducer from "../reducers/backendReducer";
import informationReducer from "../reducers/informationReducer";
import searchPageReducer from "../reducers/searchPageReducer";
import searchPage2Reducer from "../reducers/searchPage2Reducer";
import hkvaybSearchReducer from "../reducers/hkvaybSearchReducer";
import modalReducer from "../reducers/modalReducer";
import fbReducer from "../../../src/api/firebase/firebaseReducer";
import mapTimelineReducer from "../reducers/mapTimelineReducer";
import mapReducer from "../reducers/mapReducer";

const reducers = combineReducer({
    personInformation: personInfoReducer,
    orgInfo: orgReducer,
    user: userReducer,
    property: propertyReducer,
    main: mainReducer,
    source: sourceReducer,
    account: accountReducer,
    setting: settingReducer,
    browse: browseReducer,
    query: queryReducer,
    message: messageReducer,
    backend: backendReducer,
    information: informationReducer,
    searchPage: searchPageReducer,
    searchPage2: searchPage2Reducer,
    hkvaybSearch: hkvaybSearchReducer,
    modal: modalReducer,
    fbReducer,
    mapTime: mapTimelineReducer,
    map: mapReducer
});

export default reducers;
