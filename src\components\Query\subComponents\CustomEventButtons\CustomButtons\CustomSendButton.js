// react
import React, { useContext, useState, useEffect } from "react";

// ui
import { Button } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// api
import { Api, queryHkbdbData } from "../../../../../api/hkbdb/Api";

// common
import { isEmpty } from "../../../../../common/codes";
import { FormattedMessage, injectIntl } from "react-intl";

const CustonSendButton = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryString, pagination } = state.query;
    const { activePage, limit } = pagination;
    const queryOffset = (activePage - 1) * limit;
    //
    const [isLoad, setIsLoad] = useState(false);
    //
    const isReadOnlyQuery = query => {
        const prohibitedList = ["delete", "insert"];
        if (isEmpty(query)) {
            return false;
        } else {
            let isReadOnly = true;
            prohibitedList.forEach(item => {
                if (query.search(item) !== -1) {
                    isReadOnly = false;
                }
            });
            return isReadOnly;
        }
    };
    // query data
    const queryData = async () => {
        setIsLoad(true);
        //
        if (queryString) {
            // check query
            if (isReadOnlyQuery(queryString)) {
                const result = await queryHkbdbData(
                    Api.getQueryAndCount(),
                    queryString,
                    limit,
                    queryOffset,
                    // 搜尋時 20 秒 timeout
                    20000
                );
                // save result to state
                if (!isEmpty(result)) {
                    dispatch({
                        type: Act.QUERY_RESULT_SET,
                        payload: result
                    });
                }
            } else {
                // call alert message
                dispatch({
                    type: Act.MESSAGE_NOTIFICATION_SET,
                    payload: {
                        title: "Read-Only Error",
                        type: "error",
                        content: "query string can't contain update or delete.",
                        renderSignal: new Date().getTime()
                    }
                });
            }
        }
        setIsLoad(false);
    };
    // handel send query
    const handleSendQuery = async () => {
        queryData();
        // 搜尋完，page 回到第一頁
        dispatch({
            type: Act.QUERY_PAGINATION_ACTIVE_PAGE_SET,
            payload: 1
        });
    };
    //
    useEffect(() => {
        queryData();
    }, [activePage, limit]);
    //
    return (
        <Button
            color="blue"
            floated="right"
            size="tiny"
            onClick={handleSendQuery}
            disabled={isEmpty(queryString)}
            loading={isLoad}
            style={{ marginBottom: "15px" }}
        >
            <FormattedMessage id="custom.sendButton" defaultMessage="Send" />
        </Button>
    );
};

export default injectIntl(CustonSendButton);
