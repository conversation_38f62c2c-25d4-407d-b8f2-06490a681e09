import React, { useEffect, useState, useContext, useCallback } from "react";
import "../../../../style/searchPage.css";
import { makeStyles } from "@material-ui/core/styles";
import Link from "@material-ui/core/Link";
import List from "@material-ui/core/List";
import ListItem from "@material-ui/core/ListItem";
import Divider from "@material-ui/core/Divider";
import Typography from "@material-ui/core/Typography";
import { StoreContext } from "../../../../store/StoreProvider";
import act from "../../../../store/actions";
import SearchNumber from "./SearchNumber";
import { promiseList } from "../../../../api/hkvayb/api";

import { useHistory } from "react-router-dom";

const config = require("../../../../config/config");

const useStyles = makeStyles(theme => ({
    root: {
        width: "100%",
        height: "100%"
    },
    heading: {
        fontSize: theme.typography.pxToRem(20),
        flexBasis: "40%",
        flexShrink: 0,
        display: "flex",
        alignItems: "center"
    },
    secondaryHeading: {
        fontSize: theme.typography.pxToRem(10),
        color: theme.palette.text.secondary,
        display: "flex",
        alignItems: "center"
    },
    topic: {
        fontSize: 30,
        fontFamily: "Lato"
    }
}));

const Resultfield = props => {
    const [state, dispatch] = useContext(StoreContext);
    const { label, isSearch, keyWord, count, searchDataCount, labelTranslate, pageNumber, duration } = state.searchPage;
    let lastPage = Math.floor(searchDataCount / 5) + 1; // 最後一頁
    const classes = useStyles();
    const [resultData, setResultData] = useState([]); // 抓到資料的主題

    useEffect(() => { // 搜尋資料有變動時，關掉loading動畫
        props.setLoading(false);
    }, [resultData]);

    useEffect(() => {
        // get filter info
        const keysArray = Object.keys(label);
        const valuesArray = Object.values(label);
        let countResultNum = 0;
        dispatch({ // 設定搜尋資料總比數
            type: act.SET_SEARCHDATACOUNT,
            payload: countResultNum
        });
        if (isSearch) {
            // 有做搜尋動作再fetch result
            let topicArr = valuesArray.map((element, index) => {
                if(element === true){
                    return keysArray[index];
                }
            }).filter((element) => {
                return element !== undefined;
            });
            let tmpDuration = 0;
            promiseList(topicArr, keyWord).then((res) => { // 處理抓到的資料
                let resData = res.map((item) => {
                    tmpDuration = Object.values(item)[0].durationSS > tmpDuration ? Object.values(item)[0].durationSS: tmpDuration;
                    return { [Object.keys(item)]: Object.values(item)[0].data.data }}); // 取出axios裡面的data
                setResultData(resData);
                dispatch({ // 設定duration
                    type: act.SET_DURATION,
                    payload: tmpDuration
                });
                for(let i = 0; i < resData.length; i++){
                    countResultNum += Object.values(resData[i])[0].length;
                }
                dispatch({ // 設定搜尋資料總比數
                    type: act.SET_SEARCHDATACOUNT,
                    payload: countResultNum
                });
            }).catch((err) => {console.log(err);}); // 回傳promise List結果

            dispatch({
                // 有做搜尋動作，回到第一頁
                type: act.SET_PAGENUMBER,
                payload: 1
            });

            props.setLoading(true);
            if (count === 0) { // 沒有選擇搜尋主題，不顯示搜尋動畫
                props.setLoading(false);
            }
        }
    }, [isSearch]);

    const ResultList = props => {
        const history = useHistory();
        // const handleOnClick = useCallback(() => history.push('/zh-hans/hkvayb/detail/AWEEVT3090'), [history]);
        const { item } = props;
        let resultList = [];
        let keyIndex = -1; // 紀錄每筆dataObj的號碼
        if (count >= 1) {
            for (let i = 0; i < item.length; i++) {
                // console.log(Object.keys(item[i]), Object.values(item[i]));
                let tempArr = Object.values(item[i])[0].map((dataObj) => {
                    keyIndex++;
                    if (pageNumber < lastPage) {
                        if (keyIndex >= 5 * (pageNumber - 1) && keyIndex < 5 * pageNumber) {
                            return (
                                <List key={keyIndex}>
                                    <ListItem>
                                        <div className="listItem">
                                            <div className="content">
                                                <Typography variant="h5">
                                                    {/* 有中文title先顯示，沒有中文title再顯示英文 */}
                                                    {dataObj["title_zh"] || dataObj["title_en"]}
                                                </Typography>
                                                {Object.keys(dataObj).indexOf("year") >= 0 && (
                                                    <Typography className="year" variant="subtitle1" gutterBottom={true}>
                                                        年份:{" "}{dataObj["year"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("address") >= 0 && (
                                                    <Typography className="address" variant="subtitle1" gutterBottom={true}>
                                                        地址:{" "}{dataObj["address"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("essId") >= 0 && (
                                                    <Typography className="essId" variant="subtitle1" gutterBottom={true}>
                                                        essId:{" "}{dataObj["essId"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("author_per_zh") >= 0 && (
                                                    <Typography className="author_per_zh" variant="subtitle1" gutterBottom={true}>
                                                        作者:{" "}{dataObj["author_per_zh"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("cat_zh") >= 0 && (
                                                    <Typography className="cat_zh" variant="subtitle1" gutterBottom={true}>
                                                        類別:{" "}{dataObj["cat_zh"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("start_date") >= 0 && (
                                                    <Typography className="start_date" variant="subtitle1" gutterBottom={true}>
                                                        start_date:{" "}{dataObj["start_date"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("end_date") >= 0 && (
                                                    <Typography className="end_date" variant="subtitle1" gutterBottom={true}>
                                                        end_date:{" "}{dataObj["end_date"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("media_zh") >= 0 && (
                                                    <Typography className="media_zh" variant="subtitle1" gutterBottom={true}>
                                                        media_zh:{" "}{dataObj["media_zh"]}
                                                    </Typography>)}
                                            </div>
                                            <div className="topic">
                                                <Typography className={classes.topic}>
                                                    { labelTranslate[Object.keys(item[i])] }
                                                </Typography>
                                            </div>
                                        </div>
                                    </ListItem>
                                    <Divider />
                                </List>
                            );
                        }
                    } else {
                        if (keyIndex >= 5 * (pageNumber - 1) && keyIndex < 5 * (pageNumber - 1) + (searchDataCount % 5)) {
                            return (
                                <List key={keyIndex}>
                                    <ListItem>
                                        <div className="listItem">
                                            <div className="content">
                                                <Typography variant="h5">
                                                    {/* 有中文title先顯示，沒有中文title再顯示英文 */}
                                                    {dataObj["title_zh"] || dataObj["title_en"]}
                                                </Typography>
                                                {Object.keys(dataObj).indexOf("year") >= 0 && (
                                                    <Typography className="year" variant="subtitle1" gutterBottom={true}>
                                                        年份:{" "}{dataObj["year"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("address") >= 0 && (
                                                    <Typography className="address" variant="subtitle1" gutterBottom={true}>
                                                        地址:{" "}{dataObj["address"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("essId") >= 0 && (
                                                    <Typography className="essId" variant="subtitle1" gutterBottom={true}>
                                                        essId:{" "}{dataObj["essId"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("author_per_zh") >= 0 && (
                                                    <Typography className="author_per_zh" variant="subtitle1" gutterBottom={true}>
                                                        作者:{" "}{dataObj["author_per_zh"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("cat_zh") >= 0 && (
                                                    <Typography className="cat_zh" variant="subtitle1" gutterBottom={true}>
                                                        類別:{" "}{dataObj["cat_zh"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("start_date") >= 0 && (
                                                    <Typography className="start_date" variant="subtitle1" gutterBottom={true}>
                                                        start_date:{" "}{dataObj["start_date"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("end_date") >= 0 && (
                                                    <Typography className="end_date" variant="subtitle1" gutterBottom={true}>
                                                        end_date:{" "}{dataObj["end_date"]}
                                                    </Typography>)}
                                                {Object.keys(dataObj).indexOf("media_zh") >= 0 && (
                                                    <Typography className="media_zh" variant="subtitle1" gutterBottom={true}>
                                                        media_zh:{" "}{dataObj["media_zh"]}
                                                    </Typography>)}
                                            </div>
                                            <div className="topic">
                                                <Typography className={classes.topic}>
                                                    { labelTranslate[Object.keys(item[i])] }
                                                </Typography>
                                            </div>
                                        </div>
                                    </ListItem>
                                    <Divider />
                                </List>
                            );
                        }
                    }
                }).filter((element) => { return element !== undefined});
                resultList = [...resultList, ...tempArr];
            }

            if(resultList.length === 0){
                return <div className="noData">No Result</div>;
            }
            return <div>{resultList}</div>;
        } else {
            return <div className="noData">Please choose one topic at least</div>;
        }
    };
    return (
        <div className={classes.root}>
            {searchDataCount > 0 && ( // 有資料再顯示Search data Number
                <SearchNumber
                    className="showSearchNum"
                    searchDataCount={searchDataCount}
                    duration={duration}
                />
            )}
            {isSearch && !props.loading && <ResultList item={resultData}/>}
        </div>
    );
};
export default Resultfield;