import React from "react";
import PropTypes from "prop-types";
import Avatar from "@material-ui/core/Avatar";
import FaceIcon from "@material-ui/icons/Face";
import DomainIcon from "@material-ui/icons/Domain";
import peopleAvatar from "../../../images/avatar/peopleAvatar/peopleAvatar.svg";
import peopleAvatarLight from "../../../images/avatar/peopleAvatar/peopleAvatar_light.svg";
import orgAvatar from "../../../images/avatar/orgAvatar/orgAvatar.svg";

const iconList = {
    person: { name: "person", src: peopleAvatarLight, icon: FaceIcon },
    organization: { name: "organization", src: orgAvatar, icon: DomainIcon }
};

const NormalAvatar = ({ className, onClick, src, type, data }) => {
    const handleAvatarClick = e => {
        if (onClick) onClick(e, data);
    };
    return (
        <Avatar
            onClick={handleAvatarClick}
            className={className}
            src={src || iconList[type].src}
        />
    );
};

NormalAvatar.defaultProps = {
    className: "",
    onClick: null,
    src: "",
    type: iconList.person.name,
    data: {}
};

NormalAvatar.propTypes = {
    className: PropTypes.string,
    onClick: PropTypes.func,
    src: PropTypes.string,
    type: PropTypes.string,
    data: PropTypes.objectOf(PropTypes.any)
};

export default NormalAvatar;
