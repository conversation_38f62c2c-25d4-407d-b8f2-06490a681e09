.email-signup {
  &__stepper {
    .MuiStepper-root {
      .MuiStep-root {
        .MuiStepLabel-root {
          .MuiSvgIcon-root {
            &.MuiSvgIcon-root {
              color: #750f6d;
            }
          }
        }
      }
    }
  }

  &__form {
    .signup__input {
      width: 100%;
      height: 48px;
      border-radius: 4px;
      border: solid 0.5px rgba(34, 36, 38, 0.15);
      background-color: #fff;

      .MuiOutlinedInput-input {
        width: 100%;
      }

      min-width: 350px;
    }
  }

  &__privacyAgreeInfo {
    strong {
      font-weight: 900;
    }
  }

  .MuiFormGroup-root {
    .MuiFormControlLabel-root {
      .MuiCheckbox-root {
        &.Mui-checked {
          color: #750f6d;
        }
      }
    }
  }

  &__buttons {
    .signup__btn {
      &--next {
        width: 100%;
        height: 48px;
        padding: 12px 57.5px;
        border-radius: 4px;
        background-color: #411111;
        white-space: nowrap;

        &:hover {
          background-color: #411111;
        }
      }

      &--back {
        width: 100%;
        height: 48px;
        padding: 12px 74px;
        border-radius: 4px;
        background-color: #750f6d;
        white-space: nowrap;

        &:hover {
          background-color: #750f6d;
        }
      }

      &--backToHome {
        width: 100%;
        height: 48px;
        padding: 12px 57.5px;
        border-radius: 4px;
        background-color: #411111;
        white-space: nowrap;

        &:hover {
          background-color: #411111;
        }
      }
    }
  }
}