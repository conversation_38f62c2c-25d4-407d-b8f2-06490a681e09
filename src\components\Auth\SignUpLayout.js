import React, { useContext } from "react";

import { Grid, Box } from "@mui/material";
//
// component
import CustomSignUpUI from "./components/signup/CustomSignUpUI";
import ConfirmModalHoc from "../../common/components/ConfirmModalHoc";
// config
import { Redirect } from "react-router";
import { injectIntl } from "react-intl";
import { StoreContext } from "../../store/StoreProvider";

// commons code
import { ResponsiveContainer } from "../../layout/Layout";
import role from "../../App-role";
import "./SignUpLayout.scss";

const SignUpLayout = props => {
    const { intl } = props;
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const locale = user && user.locale ? user.locale : "";

    return user.isAnonymous || user.role === role.anonymous ? (
        // eslint-disable-next-line react/jsx-filename-extension
        <ResponsiveContainer {...props}>
            <Grid container justifyContent="center">
                <Grid item>
                    <Box my={1}>
                        <CustomSignUpUI />
                        <ConfirmModalHoc />
                    </Box>
                </Grid>
            </Grid>
        </ResponsiveContainer>
    ) : (
        <Redirect to={`/${locale}`} />
    );
};

export default injectIntl(SignUpLayout);
