import { getProperty, isEmpty, safeGet } from "../../../../common/codes";
import allRoles from "../../../../App-role";

const getFieldInfo = (tmpArr, ontologyDomain, ontologyType) =>
    safeGet(tmpArr, [ontologyDomain, ontologyType], []);

const safeGetProperty = (_property, property) => {
    if (isEmpty(_property)) {
        return "";
    }
    return isEmpty(property?.propertyObj)
        ? _property
        : getProperty(_property, property.propertyObj);
};

/** 根據角色取得對應的可編輯欄位 */
/** 只用於Suggestion */
const getSugPropOptions = (
    ontologyData,
    ontologyDomain,
    ontologyType,
    fieldAttr,
    fieldProp,
    property,
    role = allRoles.anonymous
) => {
    const fieldAttrRecords = getFieldInfo(
        fieldAttr,
        ontologyDomain,
        ontologyType
    );

    const fieldPropSortedRecords = getFieldInfo(
        fieldProp?.sortedRecords,
        ontologyDomain,
        ontologyType
    );

    const lookupRepeatedProp = ontologyData.reduce((prevObj, item) => {
        const propName = item.property;
        prevObj[propName] = ++prevObj[propName] || 0;
        return prevObj;
    }, {});

    //
    return (
        ontologyData
            // sort 會改變內容，先用 slice 複製內容並回傳
            .slice(0)
            .map(item => {
                //
                const {
                    property: propName,
                    propertyBindRangeStr: propBindRangeStr,
                    range: propRange
                } = item;
                //
                const isRepeated = lookupRepeatedProp[propName] >= 1;
                //
                const newLabel = isRepeated
                    ? `${safeGetProperty(propName, property)} (${propRange})`
                    : safeGetProperty(propName, property);
                //
                return {
                    label: newLabel,
                    value: propBindRangeStr,
                    property: propName,
                    range: propRange
                };
            })
            // 過濾掉與 event 相關的 property
            .filter(item => {
                // Special case: DateEvent 放行
                if (item.range === "DateEvent") {
                    return true;
                }
                // flags i 表示不分大小寫
                const reg = new RegExp("event", "i");
                // 把 item.type 當變數，確保即使是 undefined 也可以視為字串
                return `${item.range}`.search(reg) === -1;
            })
            // 依照角色設定顯示相對應的 property (每個角色可以看見的 property 各不同)
            .filter(item => {
                // 如果遇到 graph 則跳過，不須作任何更動
                if (item.property === "graph") return true;
                // 從 firebase 拿到的 roles 來比對目前的角色
                return safeGet(
                    fieldAttrRecords,
                    [item.value, "roles"],
                    []
                ).includes(role);
            })
            .sort((a, b) => {
                const order = fieldPropSortedRecords.map(item =>
                    item.toUpperCase()
                );
                const nameA = a.value.toUpperCase();
                const nameB = b.value.toUpperCase();
                if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                return 0;
            })
    );
};

export default getSugPropOptions;
