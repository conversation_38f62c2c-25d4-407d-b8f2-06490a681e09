import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_search_background: props => ({
        width: "100%",
        minHeight: "474px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/search_background_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        ...props.hkvayb_search_background
    }),
    hkvayb_search_grid: props => ({
        // minWidth: "1280px",
        // margin: "0 80px 0 80px",
        height: "inherit",
        paddingBottom: "56px",
        ...props.hkvayb_search_grid
    })
});

export default useStyles;
