import React, { Fragment, useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Modal, Grid, Segment } from "semantic-ui-react";

// lang
import { FormattedMessage, injectIntl } from "react-intl";

// custom
import CustomModalContent from "./CustomModalContentFlex";
import CustomAlertMessage from "./CustomAlertMessage";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { isEmpty, isNotEmpty, isTrue, safeGet } from "../../../../common/codes";

// api
import { Api, createHkbdbData, doRestCreate } from "../../../../api/hkbdb/Api";

// auth
import authority from "../../../../App-authority";
import config from "../../../../config/config";
import Act from "../../../../store/actions";
import { SPECIAL_HEADER } from "../../Organization/action";
import allRoles from "../../../../App-role";
import base64url from "base64url";
import axios from "axios";
import { HAS_COORDS_TYPE } from "../constants";

// 組織排除的 property
const removeOrgOntologyData = [
    "hasInterviewer",
    "hasDescribedTarget",
    "hasContributor"
];
//

const CustomCreateModal = ({ ontologyDomain, ontologyType, intl }) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { property, source, information, user } = state;
    const { personInformation: perInfo } = state;
    const { memberInvert } = perInfo;
    const { ontologyDefined, propertyObj } = property;
    const { dataset } = source;
    const { personId, ontologyOneOfThemData } = information;
    const { uid, role } = user;
    // modal 開啟的狀態統一註冊,避免開啟modal後無預警關閉
    const { modalLockSec } = state.modal;
    //
    const infoReloadFunc = safeGet(
        perInfo,
        ["infoReloadFunc", ontologyType],
        () => {}
    );
    //
    const [isLoading, setIsLoading] = useState(() => false);
    //
    const [createData, setCreateData] = useState(() => {
        //
        const graphs = dataset.map(item => item.dataset);
        //
        const ontology = safeGet(ontologyDefined, [ontologyType], []);
        //
        let oneOfThem = safeGet(
            ontologyOneOfThemData,
            [`${ontologyType}`.toLowerCase()],
            []
        );
        //
        if (ontologyDomain === "organization") {
            const result = oneOfThem.filter(
                prop => removeOrgOntologyData.indexOf(prop) === -1
            );

            oneOfThem = result;
        }
        //
        return {
            graphs,
            selectedProperties: [],
            willCreatedData: { srcId: personId },
            propertyObj,
            ontology,
            ontologyType,
            ontologyDefined,
            ontologyOneOfThemData: oneOfThem,
            isCreated: false,
            perInfo
        };
    });
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const createLocationLabel = async (graph, locType, locId) => {
        const encodedGraph = base64url.encode(graph);

        const graphLocations = await axios.get(
            locType === "Place"
                ? Api.getGraphPlace(encodedGraph)
                : Api.getGraphOrganization(encodedGraph)
        );

        const locationList = graphLocations.data.data;

        const locationMap = new Map(
            locationList.map(item => [item.id, item.label])
        );

        const hasId = locationMap.has(locId);
        const label = locationMap.get(locId);
        const needToCreate = !hasId || !label || !label.trim();

        if (needToCreate) {
            const location = locId.replace(/^(ORG|PLA)/, "");
            const locLabel = decodeURIComponent(location);
            const srcId = `${
                locType === "Place" ? "PLA" : "ORG"
            }${base64url.encode(location)}`;

            const createEntry = {
                graph,
                srcId,
                classType: locType,
                value: {
                    [locType === "Place" ? "label_Place" : "bestKnownName"]: [
                        locLabel
                    ]
                }
            };

            await createHkbdbData(Api.restfulHKBDB(), createEntry);
        }
    };
    //
    const handleCreate = async () => {
        //
        setIsLoading(true);
        //
        if (!isEmpty(personId)) {
            let entry = createData.willCreatedData;

            const { graph, value } = createData.willCreatedData;
            const graphStr = Array.isArray(graph) ? graph[0] : graph;

            for (const key of Object.keys(value)) {
                const [, propRange] = key.split("__");

                if (HAS_COORDS_TYPE.includes(propRange)) {
                    for (const locId of value[key]) {
                        await createLocationLabel(graphStr, propRange, locId);
                    }
                }
            }

            //
            // 針對組織 member 「新增」做特別處理
            if (ontologyType === "member") {
                //
                const name = entry.value?.[SPECIAL_HEADER];
                //
                let memberType = entry.classType;
                //
                const memberValue = Object.keys(entry.value).reduce(
                    (acc, curKey) => {
                        // 特定 key 和值為空時不做處理，直接回傳
                        if (
                            curKey === SPECIAL_HEADER ||
                            isEmpty(entry.value[curKey])
                        )
                            return acc;
                        //
                        if (memberInvert[curKey]) {
                            memberType = memberInvert[curKey].classtype;
                            acc[memberInvert[curKey].property] = name;
                        }

                        acc[curKey] = entry.value[curKey];
                        return acc;
                    },
                    {}
                );
                //
                entry.classType = memberType;
                entry.value = memberValue;
            }

            const tmpValueKeyArr = Object.keys(entry?.value);
            // 檢查是否有 label 沒有則補上空 label
            if (
                tmpValueKeyArr.filter(
                    tmpPropKey => tmpPropKey.indexOf("label_") === -1
                ) &&
                ontologyType !== "member"
            ) {
                entry.value = {
                    [`label_${ontologyType}__string`]: "",
                    ...entry.value // 擺第二個如果原本就有值的話以最後一個為準再覆蓋回來
                };
            }

            // 多語系
            Object.keys(entry.value).forEach(valKey => {
                if (config.MULTIPLE_VALUES.indexOf(valKey) > -1) {
                    entry.value[valKey] = entry.value[valKey].split("\n");
                }
            });

            const result = await doRestCreate(user, entry);
            // get result
            if (!isEmpty(result)) {
                //
                setCreateData(prevData => ({
                    ...prevData,
                    isCreated: true
                }));
                //
                setAlertMsg(prevMsg => ({
                    ...prevMsg,
                    title: intl.formatMessage({
                        id: "alertMsg.createModalFlex.title.success",
                        defaultMessage: `Create successfully ...`
                    }),
                    type: "success",
                    content: intl.formatMessage({
                        id: "alertMsg.createModalFlex.content.success",
                        defaultMessage: `Create new property successfully ...`
                    }),
                    renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
                }));
            }
            // result error
            else {
                setAlertMsg(prevMsg => ({
                    ...prevMsg,
                    title: intl.formatMessage({
                        id: "alertMsg.createModalFlex.title.Error",
                        defaultMessage: `Create error ...`
                    }),
                    type: "error",
                    content: intl.formatMessage({
                        id: "alertMsg.createModalFlex.content.Error",
                        defaultMessage: `Create new property error ...`
                    }),
                    renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
                }));
            }
        }
        //
        setIsLoading(false);
    };

    const handleClose = () => {
        setCreateData(prevData => ({
            ...prevData,
            selectedProperties: [],
            willCreatedData: { srcId: personId },
            isCreated: false
        }));
        // close
        dispatch({
            type: Act.MODAL_LOCK_CLEAR
        });
        // setOpen(false);
    };

    const handleCancel = () => {
        handleClose();
        if (createData.isCreated) {
            // 通知 API 重新去讀取
            infoReloadFunc();
        }
    };

    //
    const SwitchButton = () => {
        //
        if (createData.isCreated) {
            return (
                <Button onClick={handleCancel} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            //
            const {
                srcId,
                graph,
                ...restWillCreatedData
            } = createData.willCreatedData;
            //
            const isEmptyData = isEmpty(restWillCreatedData);
            //
            const requiredArr = createData.selectedProperties.reduce(
                (prevObj, item) => {
                    if (item.hasOwnProperty("required")) {
                        return [...prevObj, item.value];
                    }
                    return prevObj;
                },
                []
            );
            //
            const selectedGraph = safeGet(
                createData.willCreatedData,
                ["graph"],
                ""
            );
            //
            const selectProperties = safeGet(
                createData,
                ["selectedProperties"],
                []
            );
            //
            const oneOfThemData = safeGet(
                createData,
                ["ontologyOneOfThemData"],
                []
            );
            //
            const entryValue = safeGet(
                createData.willCreatedData,
                ["value"],
                {}
            );
            //
            const isNotSelectedGraph = isEmpty(selectedGraph);
            //
            const isNotDoneRequired =
                requiredArr
                    .map(prop => {
                        if (isNotEmpty(safeGet(entryValue, [prop], []))) {
                            return prop;
                        }
                    })
                    .filter(item => item !== undefined).length !==
                requiredArr.length;
            //
            const isNotSelectedOneOfThem =
                selectProperties.findIndex(item =>
                    oneOfThemData
                        .map(value => value.toLowerCase())
                        .includes(safeGet(item, ["property"], "").toLowerCase())
                ) === -1;
            // button's disabled 為 true 才能 disabled，因此所有項目都需要反轉
            // const isDisabled =
            //     // 必填清單存在的話
            //     requiredArr.length >= 1
            //         ? !isDoneRequired || !isSelectedGraph
            //         : !isNotEmptyData || !isSelectedGraph;
            //
            let isDisabled = false;
            // 如果還未選擇 dataset，則 true
            if (isNotSelectedGraph) {
                isDisabled = true;
            }
            // 任何欄位都不存在時，則 true
            else if (isEmptyData) {
                isDisabled = true;
            }
            // 必填清單存在時，且欄位尚未完全填完，則 true
            else if (isNotEmpty(requiredArr) && isNotDoneRequired) {
                isDisabled = true;
            }
            // 最少要填一項欄位清單存在時，如果連一項都沒選，則 true
            else
                isDisabled =
                    isNotEmpty(oneOfThemData) && isNotSelectedOneOfThem;
            //
            return (
                <Fragment>
                    <Button
                        color="green"
                        loading={isLoading}
                        disabled={isDisabled}
                        onClick={handleCreate}
                    >
                        <FormattedMessage
                            id={"people.Information.button.confirm"}
                            defaultMessage={"Confirm"}
                        />
                    </Button>
                    <Button color="red" onClick={handleCancel}>
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };
    //
    if (
        !isTrue(process.env.REACT_APP_CRUD_NODE) ||
        !isNotEmpty(uid) ||
        !authority.People_Information.includes(role)
    ) {
        return null;
    }
    //
    return (
        <Segment basic textAlign="center">
            <Grid>
                <Grid.Column width={5}>
                    <Divider />
                </Grid.Column>
                <Grid.Column width={6}>
                    <Modal
                        // open={open}
                        // modal 開啟的狀態統一註冊,避免開啟modal後無預警關閉
                        open={!!modalLockSec}
                        onClose={handleCancel}
                        onOpen={() => {
                            // setOpen(true)
                            dispatch({
                                type: Act.MODAL_LOCK_SET,
                                payload: `${ontologyDomain}-${ontologyType}`
                            });
                        }}
                        trigger={
                            <Button
                                basic
                                size="small"
                                content={
                                    <FormattedMessage
                                        id="people.Information.createProperty"
                                        defaultMessage="Create new Property"
                                    />
                                }
                                icon="add"
                                labelPosition="left"
                            />
                        }
                    >
                        {/* <pre>{JSON.stringify(createData.willCreatedData, null, 2)}</pre> */}
                        <Modal.Header>
                            <FormattedMessage
                                id="people.Information.createProperty"
                                defaultMessage="Create new Property"
                            />
                        </Modal.Header>
                        <Modal.Content image scrolling>
                            <Modal.Description style={{ width: "100%" }}>
                                <CustomAlertMessage
                                    alertMsg={alertMsg}
                                    setAlertMsg={setAlertMsg}
                                />
                                {/* show content */}
                                <CustomModalContent
                                    createData={createData}
                                    setCreateData={setCreateData}
                                    ontologyDomain={ontologyDomain}
                                    ontologyType={ontologyType}
                                />
                            </Modal.Description>
                        </Modal.Content>
                        <Modal.Actions>
                            <SwitchButton />
                        </Modal.Actions>
                    </Modal>
                </Grid.Column>
                <Grid.Column width={5}>
                    <Divider />
                </Grid.Column>
            </Grid>
        </Segment>
    );
};

export default injectIntl(CustomCreateModal);
