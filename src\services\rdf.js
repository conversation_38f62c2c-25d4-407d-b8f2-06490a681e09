import * as fp from "../fp";

export const namespace = {
    graph: "http://hkbdb.lib.cuhk.edu.hk/",
    myURI: "http://hkbdb.lib.cuhk.edu.hk#",
    hkbdb: "http://hkbdb.lib.cuhk.edu.hk/v1#",
    rdf: "http://www.w3.org/2000/01/rdf-schema#",
    rdfs: "http://www.w3.org/2000/01/rdf-schema#",
    xsd: "http://www.w3.org/2001/XMLSchema#",
    owl: "http://www.w3.org/2002/07/owl#"
};

const Namespace = prefix => literal => `${prefix}${literal}`;

export const graph = Namespace(namespace.graph);
export const rdf = Namespace(namespace.rdf);
export const rdfs = Namespace(namespace.rdfs);
export const owl = Namespace(namespace.owl);
export const xsd = Namespace(namespace.xsd);
export const hkbdb = Namespace(namespace.hkbdb);

export function removePrefix(iri) {
    const prefix = findPrefix(iri);
    if (!prefix) {
        return iri;
    }
    return iri.replace(new RegExp(prefix, "g"), "");
}

function findPrefix(iri) {
    return Object.keys(namespace).reduce((acc, key) => {
        const prefix = namespace[key];
        if (iri.indexOf(prefix) === 0) {
            return prefix;
        }
        return acc;
    }, null);
}

export const getLiteralType = fp.compose(
    fp.head,
    fp.keys,
    fp.omit(["type", "value"])
);
