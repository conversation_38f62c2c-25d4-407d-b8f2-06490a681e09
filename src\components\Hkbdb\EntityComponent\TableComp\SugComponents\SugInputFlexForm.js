import React, { useContext, useEffect, useState } from "react";

// ui
import { Label, Input, Icon, TextArea, Form } from "semantic-ui-react";

// common comp
import CustomDebounce from "../CustomDeBounce";
import CustomList from "../CustomListFlex";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isNotEmpty } from "../../../../../common/codes";
import Role from "../../../../../App-role";
import config from "../../../../../config/config";

// common func

const CustomInput = ({ property, createData, setCreateData }) => {
    const [state] = useContext(StoreContext);
    const { role } = state.user;
    //
    const { ontologyType } = createData;
    // { label: "常見名稱", value: "nnBestKnownName__string",
    // property: "nnBestKnownName", range: "string" }
    const { value: propertyName, label, required } = property;
    //
    const [selectedValue, setSelectedValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSelectedValue = CustomDebounce(selectedValue, 200);
    //
    const { perInfo } = createData;

    const handleUpdateState = () => {
        // 加上isNotEmpty(debSelectedValue)的判斷，會導致刪除的input在新增的時候會被新增
        // if (isNotEmpty(propertyName) && isNotEmpty(debSelectedValue)) {
        if (isNotEmpty(propertyName)) {
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyName]: debSelectedValue
                    }
                }
            }));
        } else {
            console.log(
                "CustomInput param error, propertyName: ",
                propertyName,
                "debSelectedValue:",
                debSelectedValue
            );
        }
    };
    //
    const handleChange = (event, { value }) => {
        // if (isNotEmpty(value)) {
        setSelectedValue(value);
        // }
    };
    //
    useEffect(() => {
        handleUpdateState();
    }, [debSelectedValue]);

    // 多語系
    if (config.MULTIPLE_VALUES.indexOf(propertyName) > -1) {
        return (
            <Form>
                <Label>
                    {label}
                    {required && (
                        <Icon
                            style={{
                                marginLeft: "1.5em"
                            }}
                            // color="red"
                            // name="asterisk"
                            // size="mini"
                        />
                    )}
                </Label>
                <TextArea
                    style={{ marginTop: "1em" }}
                    type="text"
                    onChange={handleChange}
                    defaultValue={debSelectedValue}
                />
            </Form>
        );
    }

    return (
        <>
            <Input
                fluid
                labelPosition="left"
                type="text"
                onChange={handleChange}
            >
                <Label>
                    {label}
                    {required && (
                        <Icon
                            style={{ marginLeft: "1.5em" }}
                            color="red"
                            name="asterisk"
                            size="mini"
                        />
                    )}
                </Label>
                <input
                    placeholder={
                        (role === Role.suggester &&
                            propertyName === "comment__string" &&
                            "請提供參考資料及其他相關資訊") ||
                        (role === Role.suggester &&
                            propertyName === "name__string" &&
                            "請提供其他名稱") ||
                        (role === Role.suggester &&
                            propertyName === "relationRemarks__string" &&
                            "請提供相關人際關係詳情")
                    }
                />
            </Input>
            <CustomList
                perInfo={perInfo?.infoData || {}}
                propertyName={propertyName}
            />
        </>
    );
};

export default CustomInput;
