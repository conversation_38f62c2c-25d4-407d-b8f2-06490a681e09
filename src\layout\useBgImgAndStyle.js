import { Api } from "../api/hkbdb/Api";
import { useEffect, useState } from "react";

//
import useWindowSize from "../hook/useWindowSize";
import collectionConfig from "../components/CollectionPage/collectionConfig";

const useBgImgAndStyle = ({ imageToken, mobile, location }) => {
    const [bgImgUrl, setBgImgUrl] = useState("");
    const [bgStyle, setBgStyle] = useState("");
    const size = useWindowSize();

    //
    const getSegmentClassName = () => {
        if (mobile) {
            return "home-header-small";
        } else if (isHomePage(location)) {
            return "home-header";
        } else {
            return "non-home-header";
        }
    };

    // 依據 RWD 調整 background image
    // 若可以取得 firestore 的 image, 則使用
    // 若否，則使用 local 的 image
    useEffect(() => {
        const getFallbackImg = (obj, keys = [], defaultImgPath = "") => {
            let imgPath = { ...(obj || {}) };
            keys.forEach(key => {
                if (typeof imgPath === "object" && key in imgPath) {
                    imgPath = imgPath[key];
                }
            });
            return (
                (imgPath && typeof imgPath === "string" && imgPath) ||
                defaultImgPath
            );
        };
        const desktopBGfallback = "/img/img_202102/HKBDB_V7_bg.png";
        const desktopBG = getFallbackImg(
            imageToken,
            ["desktop", "background"],
            ""
        );
        const desktopBannerFallback = "/img/img_202102/HKBDB_V7_banner.png";
        const desktopBanner = getFallbackImg(
            imageToken,
            ["desktop", "banner"],
            desktopBannerFallback
        );
        const mobilBGFallback = "/img/img_202102/HKBDB_mobile_v3_BG.png";
        const mobilBG = getFallbackImg(
            imageToken,
            ["mobile", "background"],
            ""
        );
        const mobileBannerFallback =
            "/img/img_202102/HKBDB_mobile_v3_banner.png";
        const mobileBanner = getFallbackImg(
            imageToken,
            ["mobile", "banner"],
            mobileBannerFallback
        );
        let _bgImgUrl = "";
        if (mobile) {
            if (isHomePage(location)) {
                _bgImgUrl = mobilBG;
            } else {
                _bgImgUrl = mobileBanner;
            }
        } else {
            if (isHomePage(location)) {
                _bgImgUrl = desktopBG;
            } else {
                _bgImgUrl = desktopBanner;
            }
        }
        if (_bgImgUrl && _bgImgUrl !== bgImgUrl) setBgImgUrl(_bgImgUrl);
    }, [imageToken.desktop, imageToken.mobile]);

    // handle bgStyle
    useEffect(() => {
        let _bgStyle = "";
        if (mobile && isHomePage(location)) {
            _bgStyle = `url(${bgImgUrl})`;
            // _bgStyle = `linear-gradient( rgba(255, 255, 255, 0.5),
            //     rgba(255, 255, 255, 0.7),
            //     rgba(255, 255, 255, 0.7),
            //     rgba(255, 255, 255, 0.3)),
            //     url(${bgImgUrl})`;
        } else if (!isHomePage(location)) {
            _bgStyle = `url(${bgImgUrl})`;
            // _bgStyle = `linear-gradient( rgba(255, 255, 255, 0.7),
            //     rgba(255, 255, 255, 0.7)),
            //     url(${bgImgUrl})`;
        } else {
            _bgStyle = `url(${bgImgUrl})`;
            // _bgStyle = `linear-gradient( rgba(255, 255, 255, 0.5),
            //     rgba(255, 255, 255, 0.7),
            //     rgba(255, 255, 255, 0.7),
            //     rgba(255, 255, 255, 0.3)),
            //     url(${bgImgUrl})`;
        }
        if (_bgStyle !== bgStyle) setBgStyle(_bgStyle);
    }, [bgImgUrl]);

    const isHomePage = name => {
        const locale = Api.getLocale();
        return (
            name === "/" ||
            !name ||
            name === "/home" ||
            name === `/${locale}` ||
            name === `/${locale}/focusPoint` ||
            name === `/${locale}/hotSearch` ||
            name ===
                `/${locale}/collection/${collectionConfig.manuScript.pathName}` ||
            name ===
                `/${locale}/collection/${collectionConfig.featuredPub.pathName}`
        );
    };

    return { bgImgUrl, bgStyle, getSegmentClassName, isHomePage };
};

export default useBgImgAndStyle;
