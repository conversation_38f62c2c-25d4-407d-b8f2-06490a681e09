import Act from "../actions";

const initState = {
    title: "",
    content: "",
    type: "",
    renderSignal: "",
    ttl: 3 * 1000
};

const messageReducer = (state = initState, action) => {
    switch (action.type) {
        // queryString
        case Act.MESSAGE_NOTIFICATION_SET:
            return { ...state, ...action.payload };
        case Act.MESSAGE_NOTIFICATION_CLE:
            return { ...initState, renderSignal: state.renderSignal };
        default:
            return state;
    }
};

export default messageReducer;
