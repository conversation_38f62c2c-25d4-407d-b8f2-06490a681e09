import React from "react";

import Collapse from "@mui/material/Collapse";

import useStyles from "./style";

const CusCollapse = ({ children, collapsedSize = 0, style = {} }) => {
    const classes = useStyles(style);
    const [checked, setChecked] = React.useState(false);

    const handleClick = () => {
        setChecked(prev => !prev);
    };

    return (
        <div className={classes.hkvayb_collapse}>
            <Collapse in={checked} collapsedSize={collapsedSize}>
                <div className={classes.hkvayb_divider} />
                {children}
            </Collapse>
            <div className={classes.hkvayb_div} onClick={handleClick}>
                <img
                    className={classes.hkvayb_downArrow}
                    src={
                        checked
                            ? "https://fs-root.daoyidh.com/hkvayb/desktop/down_arrow_3x.png"
                            : "https://fs-root.daoyidh.com/hkvayb/desktop/up_arrow_3x.png"
                    }
                />
            </div>
        </div>
    );
};

export default CusCollapse;
