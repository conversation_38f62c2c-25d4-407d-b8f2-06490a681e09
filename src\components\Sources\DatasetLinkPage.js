import React, { Component } from "react";
import { Container } from "semantic-ui-react";
import { injectIntl } from "react-intl";

function DatasetLinkPage({ link }) {
    const getLinkPage = link => {
        switch (link) {
            case "":
                return "";
            default:
                break;
        }
    };

    const getError = () => {
        return <h1>Page is not exist.</h1>;
    };

    return (
        <Container text textAlign="justified">
            {link ? getLinkPage(link) : this.getError}
        </Container>
    );
}

export default injectIntl(DatasetLinkPage);
