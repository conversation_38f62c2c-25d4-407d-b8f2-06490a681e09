import React, { useContext, useState, useEffect } from "react";

// ui
import Select, { createFilter } from "react-select";

// custom component
import <PERSON>ValueRemove from "./MultiValueRemove";

// common function
import { getProperty, isEmpty, safeGet } from "../../../../common/codes";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// custom
import MenuList from "./MenuList";

const CustomPropertyDropdown = ({
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    // console.log(JSON.stringify(createData.ontology, null, 2));
    //
    const [state, dispatch] = useContext(StoreContext);
    const { user, property, setting, information } = state;
    const { role } = user;
    const { fieldSetting } = setting;
    const { ontologyOneOfThemData } = information;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    //
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );
    //
    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(property?.propertyObj)
            ? _property
            : getProperty(_property, property.propertyObj);
    };
    //
    const [createState, setCreateState] = useState(() => {
        const options = [];
        //
        return {
            //
            isDisabled: isEmpty(options),
            // dropdown 呈現載入狀態
            isLoading: false,
            // dropdown 所有選項
            options: options,
            // 目前所選擇的選項
            value: options
        };
    });
    //
    const handleChange = async (options, action) => {
        // console.log("action: ", action);
        // safe options
        options = isEmpty(options) ? [] : options;
        // console.log(options);
        // update dropdown value
        setCreateState(prevState => ({ ...prevState, value: options }));
        // action 有很多 event 要分別處理
        const { removedValue: selectedValue, option: removedValue } = action;
        //
        if (!isEmpty(selectedValue)) {
            // add selectedProperties
            setEditData(prevData => ({
                ...prevData,
                selectedProperties: [
                    ...prevData.selectedProperties,
                    selectedValue
                ]
            }));
        }
        //
        if (!isEmpty(removedValue)) {
            const labelValue = removedValue.value;
            // remove selectedProperties
            setEditData(prevData => ({
                ...prevData,
                selectedProperties: [
                    // 移除選擇的項目
                    ...prevData.selectedProperties.filter(
                        item => item.value !== labelValue
                    )
                ]
            }));
        }
    };
    //
    const customStyles = {
        container: styles => ({
            ...styles
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles
        })
    };
    const customPlacement = rowId => (rowId >= 4 ? "top" : "bottom");
    //
    useEffect(() => {
        //
        const oneOfThem = safeGet(
            ontologyOneOfThemData,
            [`${ontologyType}`.toLowerCase()],
            []
        );
        //
        let ontologyData = safeGet(editData, ["ontology"], []);
        // console.log("ontologyType", ontologyType);
        if (ontologyType === "member") {
            ontologyData = safeGet(editData, ["ontology"], []).filter(
                item => !oneOfThem.includes(item.property)
            );
        }
        //
        const lookupRepeatedProp = ontologyData.reduce((prevObj, item) => {
            const propName = item.property;
            prevObj[propName] = ++prevObj[propName] || 0;
            return prevObj;
        }, {});
        //
        let newOptions = ontologyData
            // sort 會改變內容，先用 slice 複製內容並回傳
            .slice(0)
            .map(item => {
                //
                const {
                    property: propName,
                    propertyBindRangeStr: propBindRangeStr,
                    range: propRange
                } = item;
                //
                const isRepeated = lookupRepeatedProp[propName] >= 1;
                //
                const newLabel = isRepeated
                    ? `${safeGetProperty(propName)} (${propRange})`
                    : safeGetProperty(propName);
                //
                return {
                    label: newLabel,
                    value: propBindRangeStr,
                    property: propName,
                    range: propRange
                };
            })
            // 過濾掉與 event 相關的 property
            .filter(item => {
                // Special case: DateEvent 放行
                if (item.range === "DateEvent") {
                    return true;
                }
                // flags i 表示不分大小寫
                const reg = new RegExp("event", "i");
                // 把 item.type 當變數，確保即使是 undefined 也可以視為字串
                return `${item.range}`.search(reg) === -1;
            })
            // 依照角色設定顯示相對應的 property (每個角色可以看見的 property 各不同)
            .filter(item => {
                // 如果遇到 graph 則跳過，不須作任何更動
                if (item.property === "graph") return true;
                // 從 firebase 拿到的 roles 來比對目前的角色
                return safeGet(
                    fieldAttrRecords,
                    [item.value, "roles"],
                    []
                ).includes(role);
            })
            .filter(item => {
                // 如果遇到 graph 則跳過，不須作任何更動
                if (item.property === "graph") return true;
                // 從 firebase 拿到的 required 判斷是否是必填欄位，
                // 如果是必填欄位則｢不會出現在'選擇項目'的選單中」
                const isRequried = safeGet(
                    fieldAttrRecords,
                    [item.value, "required"],
                    false
                );

                /*
                 *  如果是選單就先塞入清單中
                 *  memberName 是供「組織成員」顯示的欄位，在 ontology 中沒有定義
                 *  因此在編輯跟刪除時不需要顯示該欄位
                 */
                // if (isRequried && item.property !== "memberName") {
                if (isRequried && item.property !== "memberName") {
                    item.required = true;
                    //
                    setEditData(prevData => ({
                        ...prevData,
                        selectedProperties: [
                            item,
                            ...prevData.selectedProperties
                        ]
                    }));
                }
                //
                return !isRequried; // dropdown 只會顯示 true 項目，所以要反轉一次，以免出現在選單中
            })
            // 依照設定的內容作排序
            .sort((a, b) => {
                const order = fieldPropSortedRecords.map(item =>
                    item.toUpperCase()
                );
                const nameA = a.value.toUpperCase();
                const nameB = b.value.toUpperCase();
                if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
                if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
                return 0;
            });
        //
        setCreateState({
            //
            isDisabled: isEmpty(newOptions),
            // dropdown 呈現載入狀態
            isLoading: false,
            // dropdown 所有選項
            options: newOptions,
            // 目前所選擇的選項
            value: newOptions
        });
    }, [editData.ontology]);
    //
    return (
        <Select
            isMulti
            isClearable
            styles={customStyles}
            isDisabled={
                editData.isCreated ||
                editData.isUpdated ||
                createState.isLoading ||
                createState.isDisabled
            }
            isLoading={createState.isLoading}
            options={createState.options}
            value={createState.value}
            onChange={handleChange}
            components={{ MenuList, MultiValueRemove }}
            menuPlacement={customPlacement()}
            filterOption={createFilter({ ignoreAccents: false })}
        />
    );
};

export default CustomPropertyDropdown;
