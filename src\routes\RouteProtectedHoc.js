import React, { useContext } from "react";
import { Route, Redirect } from "react-router-dom";
import { bool, any, object } from "prop-types";

// store
import { StoreContext } from "../store/StoreProvider";

// common
import { isEmpty } from "../common/codes";

const RouteProtectedHoc = ({ component: Component, ...rest }) => {
    // handle page refresh go to home if user in other page and in login status
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));

    // eslint-disable-next-line no-unused-vars
    const [state, _] = useContext(StoreContext);
    const { user, main } = state;
    const { webStyle } = main;

    if (isLogin || !isEmpty(user)) {
        return (
            <Route
                {...rest}
                render={props => (
                    <Component
                        {...props}
                        user={user}
                        webStyle={webStyle}
                        history={props.history}
                        location={props.location.pathname}
                    />
                )}
            />
        );
    }
    // fixme: pathname
    return <Redirect to={"/:locale?"} />;
};

RouteProtectedHoc.propTypes = {
    component: any,
    isLoggedIn: bool,
    rest: object,
    props: object
};

export default RouteProtectedHoc;
