import React, { Component, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, injectIntl } from "react-intl";
import {
    <PERSON>readcrumb,
    Dimmer,
    Label,
    Loader,
    Message,
    Segment,
    Icon,
    Menu,
    Button,
    PaginationItem,
    Input
} from "semantic-ui-react";

const BrowsePagination = ({
    onPageChange,
    activePage,
    totalPages,
    onActivePageChange
}) => {
    const [pageInputOpen, setPageInputOpen] = useState(true);
    // console.log('totalPages', totalPages)
    if (totalPages === 0) {
        return null;
    }

    const onEllipsisItemClick = () => {
        console.log("pageInputOpen", pageInputOpen);
        setPageInputOpen(!pageInputOpen);
    };

    return (
        <div>
            {pageInputOpen && (
                <Label basic color="blue" pointing={"below"}>
                    頁碼
                    <Input
                        type={"number"}
                        max={totalPages}
                        min={1}
                        style={{ marginLeft: "10px", width: "100px" }}
                    />
                    <Button
                        color={"blue"}
                        size={"mini"}
                        style={{ marginLeft: "10px" }}
                    >
                        Go
                    </Button>
                </Label>
            )}
            <Menu>
                <PaginationItem type={"firstItem"}>
                    <Icon name="angle double left" />
                </PaginationItem>
                <PaginationItem type={"prevItem"}>
                    <Icon name="angle left" />
                </PaginationItem>
                <PaginationItem type={"prevItem"}>1</PaginationItem>
                <PaginationItem type={"prevItem"}>2</PaginationItem>

                <PaginationItem
                    type={"ellipsisItem"}
                    onClick={() => {
                        setPageInputOpen(true);
                    }}
                >
                    <Icon name="ellipsis horizontal" />
                </PaginationItem>

                <PaginationItem type={"nextItem"}>
                    <Icon name="angle right" />
                </PaginationItem>
                <PaginationItem type={"lastItem"}>
                    <Icon name="angle double right" />
                </PaginationItem>
            </Menu>
        </div>
    );
};

export default BrowsePagination;
