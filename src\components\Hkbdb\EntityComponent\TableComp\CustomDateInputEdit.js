import React, { useEffect, useState } from "react";

// ui
import { Input } from "semantic-ui-react";

// common
import { isEmpty, isNotEmpty, isNumeric } from "../../../../common/codes";

// custom
import CustomDebounce from "./CustomDeBounce";
import { convertToStringArray } from "../commonAction";

const CustomDateInputEdit = ({
    rowIdx,
    graph,
    eventId,
    propertyBindRangeStr,
    editData,
    setEditData,
    defaultValue,
    ontologyType
}) => {
    const arrayValues = convertToStringArray(defaultValue);
    const newDefaultValue = arrayValues.join("\n");

    const [inputDate, setInputDate] = useState(() => newDefaultValue);
    //
    const debInputDate = CustomDebounce(inputDate, 500);
    //
    const handleChange = (event, { value }) => {
        //
        const newValue = value.replaceAll("-", "");
        //
        if (isEmpty(newValue)) {
            setInputDate("");
            return;
        }
        // console.log(value);
        if (isNotEmpty(newValue) && isNumeric(newValue)) {
            //
            const yyyy = newValue.slice(0, 4);
            const MM = newValue.slice(4, 6);
            const dd = newValue.slice(6, 8);
            //
            let dataFormatValue = "";
            //
            if (isNotEmpty(yyyy)) {
                const currYear = new Date().getFullYear();
                if (yyyy > currYear) {
                    dataFormatValue += currYear;
                } else {
                    dataFormatValue += yyyy;
                }
            }
            if (isNotEmpty(MM)) {
                if (MM > 12) {
                    dataFormatValue += "-12";
                } else if (MM < 0) {
                    dataFormatValue += "-00";
                } else {
                    dataFormatValue += `-${MM}`;
                }
            }
            if (isNotEmpty(dd)) {
                const date = new Date();
                const daysInMonth = new Date(
                    date.getFullYear(),
                    date.getMonth() + 1,
                    0
                ).getDate();
                //
                if (dd > daysInMonth) {
                    dataFormatValue += `-${daysInMonth}`;
                } else if (dd < 0) {
                    dataFormatValue += "-00";
                } else {
                    dataFormatValue += `-${dd}`;
                }
            }
            //
            setInputDate(dataFormatValue);
        }
    };
    //
    const handleUpdateState = () => {
        console.log("I am handleUpdateState");
        //
        const changedData = {
            // 注意: 這裡 rowIdx 會自動被轉為 string，object' key 只允許字串內容
            // 後續使用 rowIdx 時要注意
            [rowIdx]: {
                graph,
                srcId: eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: [debInputDate]
            }
        };
        // update useState
        if (isNotEmpty(debInputDate)) {
            // update editData state
            setEditData(prevEditData => {
                return {
                    ...prevEditData,
                    changedData: {
                        // old changed items
                        ...prevEditData.changedData,
                        // new changed item
                        ...changedData
                    }
                };
            });
        } else {
            setEditData(prevEditData => {
                //
                const {
                    [rowIdx]: _,
                    ...restChangedData
                } = prevEditData.changedData;
                //
                return {
                    ...prevEditData,
                    changedData: {
                        ...restChangedData
                    }
                };
            });
        }
    };
    //
    useEffect(() => {
        if (newDefaultValue !== debInputDate) {
            handleUpdateState();
        }
    }, [debInputDate]);
    return (
        <Input
            fluid
            type="text"
            value={inputDate}
            onChange={handleChange}
            disabled={editData.isUpdated || editData.isCreated}
            placeholder="yyyy-MM-dd"
        />
    );
};

export default CustomDateInputEdit;
