import React, { useEffect, useState } from "react";
import { Segment, Table, Icon } from "semantic-ui-react";
import { injectIntl } from "react-intl";
import { ResponsiveContainer } from "../../layout/Layout";
import { getAcademicEventsListener } from "../services/authentication";

// type item = {
//     conference/workshop: string,
//     presentation/paper: string,
//     presenters: string[],
//     period: string
// };

const loadContent = academicEvents => {
    if (!academicEvents) {
        return null;
    }

    const findIconSetting = academicEvents.find(item => {
        return item.hasOwnProperty("icon");
    });

    const icons = findIconSetting["icon"];
    return academicEvents.map((item, idx) => {
        const type = item.type.charAt(0).toUpperCase() + item.type.slice(1);
        const iconName = icons[item.type].name;
        const iconColor = icons[item.type].color;
        return (
            <Table.Row key={idx}>
                <Table.Cell width={2} textAlign="center">
                    <Icon name={iconName} color={iconColor} />
                    {type}
                </Table.Cell>
                <Table.Cell width={4}>
                    <p>
                        <b>{item.presentation || item.paper}</b>
                    </p>
                </Table.Cell>
                <Table.Cell width={4}>
                    <p>
                        <i>{item.conference || item.workshop}</i>
                    </p>
                </Table.Cell>
                <Table.Cell width={2}>
                    {item.presenters.map((p, pi) => {
                        return <p key={pi}>{p}</p>;
                    })}
                </Table.Cell>
                <Table.Cell width={2}>
                    <p>{item.period}</p>
                </Table.Cell>
            </Table.Row>
        );
    });
};

const AcademicEventsPage = props => {
    const [academicEvents, setAcademicEvents] = useState(null);

    useEffect(() => {
        getAcademicEventsListener(setAcademicEvents);
    }, []);

    return (
        <ResponsiveContainer {...props}>
            <Segment
                vertical
                style={{
                    width: "80vw",
                    marginRight: "10vw",
                    marginLeft: "10vw"
                }}
            >
                <Table striped size="small">
                    <Table.Body>{loadContent(academicEvents)}</Table.Body>
                </Table>
            </Segment>
        </ResponsiveContainer>
    );
};

export default injectIntl(AcademicEventsPage);
