import React, { useContext, useEffect } from "react";
import { ResponsiveContainer } from "../../layout/Layout";
import { Container } from "semantic-ui-react";
import SwaggerUI from "swagger-ui-react";
import "swagger-ui-react/swagger-ui.css";
import { StoreContext } from "../../store/StoreProvider";

import swaggerAdmin from "./swaggerAdmin.json";
import swaggerDeveloper from "./swaggerDeveloper.json";
import swaggerEditor from "./swaggerEditor.json";
import swaggerReader from "./swaggerReader.json";
import swaggerAnonymous from "./swaggerAnonymous.json";
import Act from "../../store/actions";
import "../../style/swaggerAPI.css";
import { encodeUrl } from "../../api/hkbdb/Api";

const SwaggerAPI = props => {
    const [state, dispatch] = useContext(StoreContext);
    const { user, main } = state;
    const { swgJSON, swgJSONfile } = main;
    const { role, token: globalToken } = user;

    useEffect(() => {
        switch (role) {
            case "admin":
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerAdmin
                });
                break;
            case "developer":
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerDeveloper
                });
                break;
            case "editor":
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerEditor
                });
                break;
            case "reader":
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerReader
                });
                break;
            case "anonymous":
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerAnonymous
                });
                break;
            default:
                dispatch({
                    type: Act.SET_SWG_JSON_FILE,
                    payload: swaggerAnonymous
                });
                break;
        }
    }, [role]);

    const reqInterceptor = req => {
        // add token to req.headers
        const token = JSON.parse(localStorage.getItem("token")) || globalToken;
        req.headers.Authorization = `${token}`;
        // encode query string (except limit and offset)

        // SwaggerUI 的 req.url 已經 encodeURIComponent, 所以必須 先decodeURIComponent 再base64url.encode
        req.url = encodeUrl(req.url, true);
        return req;
    };

    return (
        <ResponsiveContainer {...props}>
            <Container style={{ paddingBottom: "100px" }}>
                {swgJSONfile && (
                    <SwaggerUI
                        spec={swgJSONfile}
                        defaultModelsExpandDepth={3}
                        defaultModelExpandDepth={3}
                        syntaxHighlight={{ activate: true, theme: "obsidian" }}
                        requestInterceptor={reqInterceptor}
                    />
                )}
            </Container>
        </ResponsiveContainer>
    );
};
// /
export default SwaggerAPI;
