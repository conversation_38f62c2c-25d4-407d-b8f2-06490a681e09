import React from "react";

import CusDiamDivider from "../CusDiamDivider";
import CusPrimaryHeader from "../CusPrimaryHeader";

import useStyles from "./style";

const coverHeaderStyle = {
    hkvayb_logo: {
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/logo_black_color_3x.png')"
    },
    hkvayb_header_right_item: {
        color: "#000"
    },
    hkvayb_header: {
        margin: "unset"
    }
};

const CusSecondaryHeader = ({ label, children, style = {} }) => {
    const classes = useStyles(style);
    return (
        <div>
            <div className={classes.hkvayb_header_grid}>
                <CusPrimaryHeader style={coverHeaderStyle} />
            </div>
            <div className={classes.hkvayb_header_background}>
                <div className={classes.hkvayb_header_grid}>
                    <div className={classes.hkvayb_header_title}>{label}</div>
                    <CusDiamDivider />
                    {children}
                </div>
            </div>
        </div>
    );
};

export default CusSecondaryHeader;
