import React, { useEffect, useMemo, useState } from "react";
import Box from "@mui/material/Box";
import { ResponsiveContainer } from "../../../layout/Layout";
// import Typography from "@mui/material/Typography";
import { fetchMapGuideContent } from "../fetchData";
import { convert2HtmlEntities } from "../../../common/components/Markdown2React/mdUtils";
import { isEmpty } from "../../../common/codes";
import { Api } from "../../../api/hkbdb/Api";
import "../styles/guide.scss";
import { FormattedMessage } from "react-intl";

const Guide = props => {
    const locale = Api.getLocale();

    const [content, setContent] = useState({ zhContent: "", enContent: "" });

    const getContent = useMemo(() => {
        return locale === "en" ? content.enContent : content.zhContent;
    }, [locale, content]);

    useEffect(() => {
        try {
            fetchMapGuideContent().then(res => {
                const { data } = res;
                const zhData = data.find(item => item.lang === "zh");
                const enData = data.find(item => item.lang === "en");
                setContent({
                    zhContent:
                        (zhData?.content || "")
                            .replace(
                                "<p>###地圖模式###</p>",
                                "<h2 id='mapMode'>地圖模式</h2>"
                            )
                            .replace(
                                "<p>###行跡圖模式###</p>",
                                "<h2 id='trackingMode'>行跡圖模式</h2>"
                            ) ?? "",
                    enContent:
                        (enData?.content || "")
                            .replace(
                                "<p>###Map Mode###</p>",
                                "<h2 id='mapMode'>Map Mode</h2>"
                            )
                            .replace(
                                "<p>###Tracking Mode###</p>",
                                "<h2 id='trackingMode'>Tracking Mode</h2>"
                            ) ?? ""
                });
            });
        } catch (e) {
            console.log("Error fetchMapGuideContent: ", e);
        }
    }, []);

    const scrollToElement = id => {
        const element = document.getElementById(id);
        const navbarHeight = 72;
        if (element) {
            const offsetTop = element.offsetTop - navbarHeight;
            window.scrollTo({
                top: offsetTop,
                behavior: "smooth"
            });
        }
    };

    useEffect(() => {
        const queryParams = new URLSearchParams(window.location.search);
        const mode = queryParams.get("mode");
        if (mode) {
            scrollToElement(mode);
        }
    }, [content]);

    return (
        <ResponsiveContainer {...props}>
            {/* <Container sx={{ paddingBottom: "64px" }}> */}
            {/* <Container> */}
            <Box sx={{ padding: "0 72px 40px 72px" }}>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "flex-start",
                        marginBottom: "32px",
                        flexDirection: "column"
                    }}
                    className="gisUserGuide"
                >
                    <h1>
                        <FormattedMessage
                            id="map.info.gis.title"
                            defaultMessage="GIS User Guide"
                        />
                    </h1>
                    {!isEmpty(content.zhContent) &&
                        convert2HtmlEntities(getContent)}
                    {/* <Box */}
                    {/*    sx={{ */}
                    {/*        // display: "flex", */}
                    {/*        // justifyContent: "flex-start", */}
                    {/*        // alignItems: "center" */}
                    {/*        // gap: "0.5rem", */}
                    {/*        // cursor: "pointer" */}
                    {/*        mb: "2rem" */}
                    {/*    }} */}
                    {/* > */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "24px", */}
                    {/*            color: "#104860", */}
                    {/*            fontWeight: "600" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        GIS使用說明 */}
                    {/*    </Typography> */}
                    {/* </Box> */}
                    {/* <Box> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "16px", */}
                    {/*            color: "#000000", */}
                    {/*            fontWeight: "600", */}
                    {/*            marginBottom: "1rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        地圖模式 */}
                    {/*    </Typography> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "14px", */}
                    {/*            color: "#000000", */}
                    {/*            fontWeight: "600", */}
                    {/*            marginBottom: "0.5rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        副標題樣式 */}
                    {/*    </Typography> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "14px", */}
                    {/*            color: "#000000", */}
                    {/*            marginBottom: "0.3rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        內文 */}
                    {/*    </Typography> */}
                    {/*    <img src={example} alt="example" /> */}
                    {/* </Box> */}
                    {/* <Box> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "16px", */}
                    {/*            color: "#000000", */}
                    {/*            fontWeight: "600", */}
                    {/*            marginBottom: "1rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        行跡圖模式 */}
                    {/*    </Typography> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "14px", */}
                    {/*            color: "#000000", */}
                    {/*            fontWeight: "600", */}
                    {/*            marginBottom: "0.5rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        副標題樣式 */}
                    {/*    </Typography> */}
                    {/*    <Typography */}
                    {/*        style={{ */}
                    {/*            fontSize: "14px", */}
                    {/*            color: "#000000", */}
                    {/*            marginBottom: "0.3rem" */}
                    {/*        }} */}
                    {/*    > */}
                    {/*        內文 */}
                    {/*    </Typography> */}
                    {/*    <img src={example} alt="example" /> */}
                    {/* </Box> */}
                </Box>
            </Box>
        </ResponsiveContainer>
    );
};

export default Guide;
