import React, { useContext, useState, useEffect } from "react";

import { makeStyles } from "@material-ui/core/styles";

import { InputBase } from "@material-ui/core";

import { StoreContext } from "../../../../../../store/StoreProvider";

import act from "../../../../../../store/actions";

import CustomDebounce from "../../../../../../common/components/CustomDeBounce";

const useStyles = makeStyles(theme => ({
    input: {
        marginLeft: theme.spacing(1),
        paddingLeft: theme.spacing(2),
        flex: 1
    }
}));

const CusInput = () => {
    const classes = useStyles();
    const [state, dispatch] = useContext(StoreContext);
    const { reset } = state.searchPage2;
    const [inputValue, setInputValue] = useState("");
    const debInputValue = CustomDebounce(inputValue, 500);

    const handleOnChange = event => {
        const value = event.target.value;
        setInputValue(value);
    };

    useEffect(() => {
        dispatch({
            type: act.SET_SEARCHPAGE_SEARCHBAR_KEYWORD,
            payload: debInputValue
        });
    }, [debInputValue]);

    useEffect(() => {
        setInputValue("");
    }, [reset]);

    return (
        <InputBase
            className={classes.input}
            placeholder="請輸入關鍵字"
            value={inputValue}
            onChange={handleOnChange}
        />
    );
};

export default CusInput;
