import React from "react";
import CoordinatesModal from "@daoyi/coordinates-editor-modal";
import deleteIcon from "../../../../images/delete-icon.svg";

const CustomCoordinatesModal = ({
    open,
    setOpen,
    allCoordinates,
    locations,
    title,
    isLoading,
    getGoogleCoord,
    handleChangeCoords
}) => (
    <CoordinatesModal
        open={open}
        setOpen={isOpen => setOpen(isOpen)}
        coordinateOptions={allCoordinates}
        locations={locations}
        title={title}
        isLoading={isLoading}
        getCoordinates={getGoogleCoord}
        onClick={data => handleChangeCoords(data)}
        chipSx={{
            backgroundColor: "#DCDCDC",
            color: "#525252",
            borderRadius: "2px",
            fontSize: "12px",
            fontWeight: 400,
            height: "auto",
            paddingTop: "2px",
            paddingBottom: "2px"
        }}
        triggerSx={{
            borderRadius: "0px 4px 4px 0px",
            border: "1px solid rgba(34, 36, 38, 0.15)"
        }}
        modalSx={{
            width: "50%",
            height: "100%",
            left: 0,
            transform: "translate(0, -50%)",
            borderRadius: "0 12px 12px 0"
        }}
        titleSx={{
            backgroundColor: "#FAFAFA",
            borderBottom: "1px solid #EEEEEE",
            color: "#000000"
        }}
        addCoordinatesIcon={false}
        addCoordinateBtnSx={{
            backgroundColor: "#104860",
            color: "white",
            padding: "8px 16px",
            "&:hover": {
                backgroundColor: "rgb(51,111,137)"
            }
        }}
        tableHeadSx={{ backgroundColor: "#FAFAFA" }}
        tableHeadCellSx={{ color: "#000000" }}
        deleteIcon={<img src={deleteIcon} alt="delete" />}
        mapInfoSx={{
            backgroundColor: "#F3F3F3",
            color: "black",
            border: "0.5px solid #3D3D3D"
        }}
        mapInfoIconSx={{ color: "#3D3D3D" }}
        panelSx={{ gap: "8px" }}
        cancelBtnSx={{
            backgroundColor: "#104860",
            color: "white",
            "&:hover": {
                backgroundColor: "rgb(51,111,137)"
            }
        }}
        saveBtnSx={{
            backgroundColor: "#104860",
            color: "white",
            "&:hover": {
                backgroundColor: "rgb(51,111,137)"
            }
        }}
    />
);

export default CustomCoordinatesModal;
