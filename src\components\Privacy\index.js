import React, { useContext, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import { FormattedMessage, injectIntl } from "react-intl";
import { ResponsiveContainer } from "../../layout/Layout";
import service from "./service";
import { StoreContext } from "../../store/StoreProvider";
import Md2React from "../../common/components/Markdown2React";
import FadeIn from "react-fade-in";

const simpleLocale = locale => {
    const defaultLocale = "en";
    if (!locale || typeof locale !== "string") return defaultLocale;
    return locale.indexOf("zh") >= 0 ? "zh" : defaultLocale;
};

const PrivacyPage = props => {
    // store
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    // local
    const [webConfig, setWebConfig] = useState(null);
    const [pageData, setPageData] = useState(null);
    const [loading, setLoading] = useState(false);
    const bindId = "privacyPolicy";

    const fetchWebconfig = async () => {
        try {
            const apiData = await service.getWebConfig();
            setWebConfig(apiData);
            const curPageData = apiData
                .filter(item => item?.bindId.indexOf(bindId) >= 0)
                .filter(item => item.lang === simpleLocale(locale));
            setPageData(curPageData);
        } catch (err) {
            // console.log(err)
        }
    };

    useEffect(() => {
        setLoading(!webConfig);
    }, [webConfig]);

    useEffect(() => {
        fetchWebconfig();
    }, []);

    // 語系變更時, 依語系變更內容
    useEffect(() => {
        if (!Array.isArray(webConfig)) return;
        const filterData = webConfig
            .filter(item => item?.bindId.indexOf(bindId) >= 0)
            .filter(item => item.lang === simpleLocale(locale));
        setPageData(filterData);
    }, [locale]);

    return (
        <ResponsiveContainer {...props}>
            <Container textAlign="justified">
                <FadeIn transitionDuration={1000}>
                    <div
                        style={{
                            marginTop: "50px",
                            marginBottom: "80px",
                            padding: "0 100px"
                        }}
                    >
                        <Md2React.Normal
                            content={pageData && pageData[0].content}
                        />
                    </div>
                </FadeIn>

                <Divider hidden />
            </Container>
        </ResponsiveContainer>
    );
};

export default injectIntl(PrivacyPage);
