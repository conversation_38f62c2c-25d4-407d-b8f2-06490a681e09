import React from "react";

import useStyles from "./style";

import { isNotEmpty, safeGet } from "../../../../common/codes";

const CusDualPages = ({ children, left, right, style = {} }) => {
    const classes = useStyles(style);
    if (isNotEmpty(children)) {
        const [leftComp, rightComp] = children;
        return (
            <div className={classes.hkvayb_dualPages}>
                {isNotEmpty(safeGet(leftComp, ["props", "value"])) && (
                    <div className={classes.hkvayb_dualPages_left}>
                        {leftComp}
                    </div>
                )}
                {isNotEmpty(safeGet(rightComp, ["props", "value"])) && (
                    <div className={classes.hkvayb_dualPages_right}>
                        {rightComp}
                    </div>
                )}
            </div>
        );
    }
    return (
        <div className={classes.hkvayb_dualPages}>
            <div className={classes.hkvayb_dualPages_left}>{left}</div>
            <div className={classes.hkvayb_dualPages_right}>{right}</div>
        </div>
    );
};

export default CusDualPages;
