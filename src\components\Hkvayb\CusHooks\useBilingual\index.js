import React from "react";

import config from "../../config";

import { isNotEmpty, safeGet } from "../../../../common/codes";

const useBilingual = (object, locale) => {
    const [zhObject, setZhObject] = React.useState({});
    const [enObject, setEnObject] = React.useState({});

    const commaSymbol = config.symbol.comma;
    const spaceSymbol = config.symbol.space;

    const getTransform = (targetLabel, category) => {
        console.log(targetLabel, category);
        const zhOptions = safeGet(
            config.searchBoxOptions,
            [config.languages.zhHans, category],
            []
        );
        const enOptions = safeGet(
            config.searchBoxOptions,
            [config.languages.en, category],
            []
        );
        if (config.languages.zhHans === locale) {
            const zhOption = zhOptions.find(option => option.label === targetLabel);
            const zhValue = safeGet(zhOption, ["value"], "");
            const enOption = enOptions.find(option => option.value === zhValue);
            return safeGet(enOption, ["label"], "");
        }
        const enOption = enOptions.find(option => option.label === targetLabel);
        const enValue = safeGet(enOption, ["value"], "");
        const zhOption = zhOptions.find(option => option.value === enValue);
        return safeGet(zhOption, ["label"], "");
    };

    React.useEffect(() => {
        if (isNotEmpty(object)) {
            setZhObject(
                Object.keys(object).reduce((prevState, categoryKey) => {
                    const tmpValue = safeGet(object, [categoryKey], "");
                    const tmpArrValue = tmpValue.split(commaSymbol);
                    if (isNotEmpty(tmpArrValue)) {
                        return {
                            ...prevState,
                            [categoryKey]: tmpArrValue
                                .map(label => getTransform(label, categoryKey))
                                .join(commaSymbol)
                        };
                    }
                    return {
                        ...prevState,
                        [categoryKey]: spaceSymbol
                    };
                }, {})
            );
        }
    }, [JSON.stringify(object)]);

    return [zhObject, enObject];
};

export default useBilingual;
