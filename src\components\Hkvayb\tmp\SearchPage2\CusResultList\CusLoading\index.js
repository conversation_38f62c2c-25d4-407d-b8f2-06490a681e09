import React from "react";
import { makeStyles } from "@material-ui/core/styles";
// import { CircularProgress } from "@material-ui/core";
import { CircularProgress, Typography } from "@material-ui/core";

const useStyles = makeStyles(theme => ({
    root: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        margin: "1em"
    }
}));

const CusLoading = () => {
    const classes = useStyles();

    return (
        <div className={classes.root}>
            <CircularProgress />
            <br />
            <Typography>Loading ...</Typography>
        </div>
    );
};

export default CusLoading;
