import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_content: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#333",
        display: "flex",
        flexDirection: "column",
        ...props.hkvayb_content
    }),
    hkvayb_div: props => ({
        ...props.hkvayb_div
    })
});

export default useStyles;
