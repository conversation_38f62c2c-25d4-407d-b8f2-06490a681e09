import React, { useState, useEffect, useContext } from "react";
import {
    Button,
    Container,
    Menu,
    Segment,
    Visibility
} from "semantic-ui-react";
import RightMenu from "./RightMenu";
import { AppTitle } from "./AppTitle";
import LanguageSwitcher from "./LanguageSwitcher";
import CreateMenuItems from "./CreateMenuItems";
import { menus } from "../App-header";
import { StoreContext } from "../store/StoreProvider";
import { isProductionDb } from "../common/codes";
import { FormattedMessage } from "react-intl";
import useBgImgAndStyle from "./useBgImgAndStyle";

// type State = {
//     activeItem: string | null,
//     fixedForced: boolean
// };
//
// type DesktopMenuProps = {
//     permission: number,
//     location: string,
//     mobile: boolean,
//     header: null,
//     onLocaleChanged: Function,
//     intl: Object,
//     children?: Array<any>
// };

const DesktopMenu = ({ intl, user, location, mobile, header, webStyle }) => {
    const [state] = useContext(StoreContext);
    const { main } = state;
    const { imageToken, database } = main;

    const { permission, isAnonymous } = user;
    const [fixedForced, setFixedForced] = useState(false);
    const [activeItem, setActiveItem] = useState(null);
    const [localBgStyle, setLocalBgStyle] = useState("");
    const [isScrolled, setIsScrolled] = useState(false);

    const hideFixedMenu = () => setFixedForced(false);
    const showFixedMenu = () => setFixedForced(true);
    const onMenuItemClick = (e, { name }) => {
        setActiveItem(name);
    };
    // hook
    const { bgImgUrl, bgStyle, isHomePage } = useBgImgAndStyle({
        imageToken,
        mobile,
        location
    });

    useEffect(() => {
        if (!activeItem) {
            setActiveItem(location);
        }
    }, [location]);

    let fixed;
    // set fixed: false whether in home page or other page
    if (isHomePage(location)) {
        fixed = fixedForced;
    } else {
        fixed = fixedForced;
    }

    // let segmentHeight = fixed ? 60 : 300;
    let segmentHeight = isHomePage(location) ? "calc(100vh - 50px)" : "5vh";

    useEffect(() => {
        setLocalBgStyle(bgStyle);
        setTimeout(() => {
            ["home-header", "home-header-small"].forEach(name => {
                const selectorName = `.${name}`;
                const ele = document.querySelector(selectorName);
                if (ele) {
                    ele.classList.add(`${name}__show`);
                }
            });
        }, 1000);
    }, [bgImgUrl, bgStyle]);

    const getSegmentClassName = () => {
        if (isHomePage(location)) {
            return "home-header";
        } else {
            return "non-home-header";
        }
    };

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY >= 110) {
                setIsScrolled(true);
            } else {
                setIsScrolled(false);
            }
        };
        window.addEventListener("scroll", handleScroll);

        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    return (
        <Visibility
            once={false}
            onBottomPassed={showFixedMenu}
            onBottomPassedReverse={hideFixedMenu}
            className="desktop__menu"
        >
            <Segment
                inverted
                className={getSegmentClassName()}
                textAlign="center"
                style={{
                    overflowY:
                        getSegmentClassName() === "home-header" && "scroll",
                    minHeight: segmentHeight,
                    padding: "1em 0em",
                    marginBottom: isHomePage(location) ? "0" : "2em",
                    backgroundColor: "transparent",
                    background: localBgStyle
                }}
                vertical
            >
                <Menu
                    fixed={fixed ? "top" : null}
                    pointing={!fixed}
                    // secondary={!fixed}
                    secondary
                    style={{
                        backgroundColor: isProductionDb(database)
                            ? webStyle.header.bg.color
                            : isScrolled // 偵測頁面Y軸移動，動態調整背景顏色
                            ? "white"
                            : "rgba(248, 231, 28, 0)",
                        borderBottom: "0"
                    }}
                    size="large"
                >
                    <Container
                        style={{
                            height: "60px",
                            zIndex: "1000"
                        }}
                    >
                        {/* logo */}
                        <AppTitle
                            fixed={fixed}
                            onClick={onMenuItemClick}
                            mobile={mobile}
                            webStyle={webStyle}
                            style={{ alignSelf: "flex-end" }}
                        />
                        {/* 是否顯示測試站 */}
                        {!isProductionDb(database) ? (
                            <Menu.Item
                                position="right"
                                style={{ alignSelf: "center" }}
                            >
                                <Button
                                    color={"red"}
                                    size={"huge"}
                                    disabled
                                    inverted
                                >
                                    <FormattedMessage
                                        id={"menu.isProductionSite"}
                                        defaultMessage={"Testing Site"}
                                    />
                                </Button>
                            </Menu.Item>
                        ) : (
                            <>
                                {/* 防止header跑版，故設定螢幕寬度小於1100px不顯示 */}
                                {window.innerWidth > 1100 && (
                                    <Menu.Item
                                        position="right"
                                        style={{ alignSelf: "center" }}
                                    >
                                        <Button disabled inverted></Button>
                                    </Menu.Item>
                                )}
                            </>
                        )}
                        {/* navbar left items */}
                        <CreateMenuItems
                            intl={intl}
                            permission={permission}
                            activeItem={activeItem}
                            onMenuItemClick={onMenuItemClick}
                            mobile={mobile}
                            webStyle={webStyle}
                            menuItems={menus.menuLeft}
                        />
                        <RightMenu
                            intl={intl}
                            fixed={fixed}
                            isAnonymous={isAnonymous}
                            permission={permission}
                            location={location}
                            onClick={onMenuItemClick}
                            webStyle={webStyle}
                        />
                        <LanguageSwitcher />
                    </Container>
                </Menu>
                {header && header({ mobile, webStyle })}
            </Segment>
        </Visibility>
    );
};

export default DesktopMenu;
