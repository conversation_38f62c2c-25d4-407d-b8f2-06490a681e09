import React, { useState, useEffect, useRef } from "react";

// leaflet
import {
    MapContainer,
    LayersControl,
    TileLayer,
    FeatureGroup,
    ZoomControl,
    useMap
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import "../../styles/myMap.scss";
import L from "leaflet";
// utils
// eslint-disable-next-line import/no-extraneous-dependencies
import isArray from "lodash/isArray";
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
// eslint-disable-next-line import/no-extraneous-dependencies
import useSupercluster from "use-supercluster";

// types
// eslint-disable-next-line import/no-extraneous-dependencies
// import { type BBox } from "geojson";
// import {
//   type ICluster,
//   type IPoint,
// } from "@/app/[lang]/activity/replus2023/subComponent/TypeScriptProps.tsx";

// component
import HkbdbMapMarkerPopup from "../commons/HkbdbMapMarkerPopup.jsx";
import ClusterMarker from "../commons/mapCore/ClusterMarker.jsx";
import PointMarker from "../commons/mapCore/PointMarker.jsx";
import PointsLine from "../commons/mapCore/PointsLine";
import EasyCircleMarker from "../commons/mapCore/EasyCircleMarker";

// config
import {
    mapLatLongDefault,
    dfTileLayerInfo,
    zoomDefault
    // boundsDefault,
    // type PaletteKey,
} from "../commons/config.js";

// helpers
// eslint-disable-next-line import/named
import {
    // getCenter,
    clusterMap,
    clusterReduce,
    updateMap,
    createMarker,
    combinePointsByCoordinates,
    isInHongKong
} from "../commons/MapHelper.jsx";
// TODO:style
// import "./myMap.scss";
// eslint-disable-next-line import/order
import { fetchPointData } from "../../fetchData.js";
import PropTypes from "prop-types";
import EasyMapIcon, {
    EasyMapIconOptions
} from "../commons/mapCore/EasyMapIcon";

// 預設座標常數
const DEFAULT_COORDINATES = {
    longitude: mapLatLongDefault.hongKong[1], // 114.1694
    latitude: mapLatLongDefault.hongKong[0] // 22.3193
};

// 座標驗證和修正函數
const validateAndFixCoordinates = (longitude, latitude, context = "") => {
    let validLongitude = longitude;
    let validLatitude = latitude;

    if (longitude == null || isNaN(longitude)) {
        validLongitude = DEFAULT_COORDINATES.longitude;
        console.warn(`Invalid longitude${context}, using default:`, {
            original: longitude,
            default: validLongitude
        });
    }

    if (latitude == null || isNaN(latitude)) {
        validLatitude = DEFAULT_COORDINATES.latitude;
        console.warn(`Invalid latitude${context}, using default:`, {
            original: latitude,
            default: validLatitude
        });
    }

    return [validLongitude, validLatitude];
};

// const anchor: L.Layer | null = null;

// interface MyMapProps {
//   onPointChange?: (
//     _cluster: IPoint | ICluster,
//     ref?: React.RefObject<L.Marker<any>>,
//   ) => void;
//   points: IPoint[];
//   onReady?: () => void;
//   onCreated?: (_map: L.Map) => void;
//   paletteStyle?: PaletteKey;
//   style?: CSSProperties;
//   usingCluster?: boolean;
// }

const ListenMapCreation = props => {
    const { onMapCreated } = props;
    const map = useMap();
    const [listenCount, setListenCount] = useState(0);

    useEffect(() => {
        if (map != null && listenCount === 0) {
            // console.log("map", map);
            setListenCount(listenCount + 1);
            onMapCreated(map);
        }
    }, [map]);

    return null;
};

// 選定 marker
const MarkerDef = EasyCircleMarker;
// const MarkerDef = EasyPointMarker

/**
 * @typedef {Object} ILine
 * @property {string} id
 * @property {string} srcPoint
 * @property {string} dstPoint
 * @property {string} srcYear
 * @property {string} dstYear
 * @property {{color:string;weight:number;dashArray:string}} style
 *
 */

/**
 * @typedef {Object} TraceMapProps
 * @property {(cluster: IPoint | ICluster, ref?: React.RefObject<L.Marker<any>>) => void} onPointChange
 * @property {IPoint[]} points
 * @property {ILine[]} lines
 * @property {() => void} onReady
 * @property {(_map: L.Map) => void} onCreated
 * @property {string} paletteStyle
 * @property {CSSProperties} style
 * @property {boolean} usingCluster
 *
 *
 * @param {TraceMapProps} props
 * @returns {React.JSX.Element}
 * @constructor
 */
const GenaralMap = props => {
    const {
        onPointChange,
        points,
        lines = [],
        onReady,
        onCreated,
        paletteStyle,
        style,
        usingCluster = true,
        usingPopup = false,
        usingMarkerWhenTarget = true,
        refreshDataTs,
        pointSelected,
        onClickMapPointChange
    } = props;

    const [mapCenter, setMapCenter] = useState(mapLatLongDefault.hongKong); // map center coordinate
    const [zoom, setZoom] = useState(
        window.screen.width > 767
            ? zoomDefault.desktopInit
            : zoomDefault.mobileInit
    ); // zoom level
    const [bounds, setBounds] = useState(null); // map
    // const [map, setMap] = useState<null | L.Map>(null);
    const [map, setMap] = useState(null);
    //
    const [mapPoints, setMapPoints] = useState([]);

    const clearAllMarkers = () => {
        if (map != null) {
            map.eachLayer(layer => {
                if (layer instanceof L.Marker) {
                    const markerClass = layer.options.icon?.options?.className;
                    if (markerClass === EasyMapIconOptions.className) {
                        map.removeLayer(layer);
                    }
                }
            });
        }
    };

    useEffect(() => {
        // console.log("points", points);
        if (!(points && points.length >= 0)) return;
        //
        setMapPoints(points);
        // setMapCenter(getCenter(points));
        // TODO: 暫以香港為中心
        setMapCenter(mapLatLongDefault.hongKong);
        // set zoom level
        setZoom(window.screen.width > 767 ? 4 : 3);
    }, [points]);

    useEffect(() => {
        // 當 refreshDataTs 變動時, 重新清除所有 marker
        // clear all markers
        clearAllMarkers();
    }, [refreshDataTs]);

    useEffect(() => {
        // TODO: 依據 pointSelected, 移動到該點
        //
    }, [pointSelected]);

    useEffect(() => {
        if (map != null) {
            // 以 points 的平均值為中心
            // map.setView(
            //   L.latLng({ lat: mapCenter[0], lng: mapCenter[1] }),
            //   map.getZoom(),
            //   {
            //     animate: true,
            //   },
            // );
            // TODO: 暫以香港為中心
            map.setView(mapLatLongDefault.hongKong, map.getZoom(), {
                animate: true
            });
        }
    }, [mapCenter, map]);

    // set clusters data for generating points and clusters
    // 當 points or bounds or zoom 變動時, 重新計算 clusters
    const { clusters, supercluster } = useSupercluster({
        points: points ?? [],
        // 提供有效的 bounds，不依賴於地圖的 bounds
        bounds: bounds || [
            [0, 0], // 西南角
            [80, 180] // 東北角 (覆蓋大部分北半球)
        ],
        zoom: zoom || 4, // 提供一個默認的 zoom 值
        options: {
            radius: 50,
            maxZoom: 8,
            minPoints: 3,
            map: clusterMap,
            reduce: clusterReduce
        }
    });

    // 在 useEffect 中，當 points 變動時，強制更新 bounds 和 zoom
    useEffect(() => {
        // 黨點超過兩個時, 不需要重新計算 bounds 和 zoom, 避免無法zoom
        if (points && points.length >= 2) return;

        // 設置 mapPoints
        setMapPoints(points);

        // 設置地圖中心
        setMapCenter(mapLatLongDefault.hongKong);

        // 設置 zoom level
        const newZoom = window.screen.width > 767 ? 4 : 3;
        setZoom(newZoom);

        // 設置一個固定的 bounds，確保 useSupercluster 能夠運作
        // 暫定的bounds
        const worldBounds = [
            [0, 0], // 西南角
            [80, 180] // 東北角 (覆蓋大部分北半球)
        ];
        setBounds(worldBounds);

        // 如果地圖已經初始化，設置視圖
        if (map) {
            map.setView(mapLatLongDefault.hongKong, newZoom, {
                animate: true
            });
        }
    }, [points, map]);

    // 當地圖被建立
    // FIXME: react-leaflet v4 以上, MapContainer 不再支援 onCreated
    // const onMapCreated = (mapInstance: L.Map): void => {
    //   setMap(mapInstance);
    //   if (isFunction(onCreated)) onCreated(mapInstance);
    // };

    // 當地圖準備好
    const onMapReady = () => {
        // do something when map ready
        if (isFunction(onReady)) {
            onReady();
        }
    };

    // 當 cluster click
    const handleClusterClick = cluster => {
        try {
            // every cluster point has coordinates
            const [longitude, latitude] = cluster.geometry.coordinates;

            // 處理無效座標，提供預設值
            const [validLongitude, validLatitude] = validateAndFixCoordinates(
                longitude,
                latitude,
                " in cluster"
            );

            // zoom level in when click
            const expansionZoom = Math.min(
                supercluster.getClusterExpansionZoom(cluster.id),
                15
            );
            if (map != null) {
                map.setView(
                    L.latLng({ lat: validLatitude, lng: validLongitude }),
                    expansionZoom,
                    {
                        animate: true
                    }
                );
            }
        } catch (e) {
            console.error(e);
        }
    };

    // 當 point click
    const handlePointClick = async (cluster, markerRef) => {
        try {
            // every cluster point has coordinates
            const [longitude, latitude] = cluster.geometry.coordinates;

            // 處理無效座標，提供預設值
            let validLongitude = longitude;
            let validLatitude = latitude;

            if (longitude == null || isNaN(longitude)) {
                validLongitude = DEFAULT_COORDINATES.longitude;
                console.warn("Invalid longitude in point, using default:", {
                    original: longitude,
                    default: validLongitude
                });
            }

            if (latitude == null || isNaN(latitude)) {
                validLatitude = DEFAULT_COORDINATES.latitude;
                console.warn("Invalid latitude in point, using default:", {
                    original: latitude,
                    default: validLatitude
                });
            }

            onClickMapPointChange();
            if (map != null) {
                // remove all the markers in map
                clearAllMarkers();
                // 移到正中間
                map.setView(
                    L.latLng({ lat: validLatitude, lng: validLongitude }),
                    zoom,
                    {
                        animate: true
                    }
                );

                // create marker on map according to cluster
                createMarker(map, [validLongitude, validLatitude]);
            }
            // 觸發父層事件
            if (onPointChange) onPointChange(cluster, markerRef);

            // TODO: 做到客製化,由父層來 fetch 所需的資料
            const findPoint = mapPoints.find(mps => mps.id === cluster.id);
            // 如果搜尋過, 就不要再搜尋,降低流量
            if (findPoint?.properties.alreadyFetch) return;

            // 先設定 properties.isFetching = true
            setMapPoints(mps =>
                mps.map(mp => {
                    if (mp.id === cluster.id) {
                        return {
                            ...mp,
                            properties: {
                                ...mp.properties,
                                isFetching: true
                            }
                        };
                    }
                    return mp;
                })
            );

            // 取得某個國家的使用者:包含 handler 及 upstream
            const pointData = await fetchPointData(cluster.id);
            // console.log("pointData", pointData);

            // 更新 mapPoints 狀態
            setMapPoints(mps =>
                mps.map(mp => {
                    if (mp.id === cluster.id) {
                        return {
                            ...mp,
                            properties: {
                                ...mp.properties,
                                alreadyFetch: true,
                                isFetching: false // 改為 false
                                // upstreamCount: pointData?.upstreamCount ?? 0,
                                // handlerCount: pointData?.handlerCount ?? 0
                            }
                        };
                    }
                    return mp;
                })
            );
        } catch (e) {
            console.error(e);
        }
    };

    const handlePointPopupOpen = async (cluster, markerRef) => {
        // console.log("[handlePointPopupOpen]click", cluster.id);
        //
        if (isFunction(onPointChange)) onPointChange(cluster, markerRef);
    };

    const handlePointPopupClose = async (cluster, markerRef) => {
        //
        if (isFunction(onPointChange)) onPointChange(null);
    };

    const isMobileDevice = () => {
        /* Storing user's device details in a variable */
        const details = navigator.userAgent;

        /* Creating a regular expression
    containing some mobile devices keywords
    to search it in details string */
        const regexp = /android|iphone|kindle|ipad/i;

        /* Using test() method to search regexp in details
    it returns boolean value */
        return regexp.test(details);
    };

    const handlePointTooltipOpen = cluster => {
        // console.log('isMobileDevice', isMobileDevice());
        // 因為 ios 及部分手機作業系統 於 click 第一次時, 會指向 tooltip 事件,
        // 第二次 click 時, 才會指向 click 事件, 所以在這邊多判斷, 若為 行動載具,則同時觸發 onPointChange
        if (isMobileDevice() && handlePointClick)
            void handlePointClick(cluster);
    };

    const handleClusterTooltipOpen = cluster => {
        // console.log('isMobileDevice', isMobileDevice());
        // 因為 ios 及部分手機作業系統 於 click 第一次時, 會指向 tooltip 事件,
        // 第二次 click 時, 才會指向 click 事件, 所以在這邊多判斷, 若為 行動載具,則同時觸發 onPointChange
        if (isMobileDevice() && handleClusterClick) handleClusterClick(cluster);
    };

    const handlePopupOpen = () => {
        //
    };

    useEffect(() => {
        if (map) {
            // set event listener for map
            map.on("moveend", updateMap(map, { setBounds, setZoom }));
            map.on("click", () => null);
            map.on("zoomend", () => {});
        }
    }, [map]);

    const mapRef = useRef();

    useEffect(() => {
        if (mapRef.current) {
            setMap(mapRef.current);
            if (isFunction(onCreated)) onCreated(mapRef.current);
        }
    }, [mapRef]);

    return (
        <MapContainer
            /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
            // @ts-expect-error
            ref={mapRef}
            center={mapCenter}
            zoom={zoom}
            zoomControl={false}
            minZoom={zoomDefault.min}
            scrollWheelZoom
            preferCanvas
            whenReady={onMapReady}
            style={style ?? { height: "100%", width: "100%" }}
            // maxBounds={boundsDefault.max}
        >
            <ListenMapCreation
                onMapCreated={thisMap => {
                    setMap(thisMap);
                }}
            />
            {/* zoom in & zoom out control */}
            <ZoomControl position="bottomright" />
            {/* base map layer */}
            <LayersControl position="bottomright">
                {isArray(dfTileLayerInfo) &&
                    dfTileLayerInfo
                        .filter(o => o.display)
                        .map((layer, idx) => (
                            <LayersControl.BaseLayer
                                key={layer.name}
                                name={layer.name}
                                checked={layer.checked}
                            >
                                <TileLayer
                                    key={layer.name}
                                    attribution={layer.attribution}
                                    url={layer.url}
                                    {...(layer.subdomains
                                        ? { subdomains: layer.subdomains }
                                        : {})}
                                    {...(layer.maxZoom
                                        ? { maxZoom: layer.maxZoom }
                                        : {})}
                                    {...(layer.apikey
                                        ? { apikey: layer.apikey }
                                        : {})}
                                    noWrap
                                />
                            </LayersControl.BaseLayer>
                        ))}
                {/* <FeatureGroup>
                    {lines.map((line, lineIdx) => {
                        return (
                            <PointsLine
                                key={line.id}
                                srcPoint={line.srcPoint}
                                dstPoint={line.dstPoint}
                                srcYear={line.srcYear}
                                dstYear={line.dstYear}
                                srcLocation={line.srcLocation}
                                dstLocation={line.dstLocation}
                                style={line.style}
                                addArrowHead={line.addArrowHead}
                            />
                        );
                    })}
                </FeatureGroup> */}
                <LayersControl.Overlay name="Marker" checked>
                    <FeatureGroup>
                        {!usingCluster &&
                            mapPoints.map((point, pointIdx) => {
                                //
                                const [
                                    longitude,
                                    latitude
                                ] = point.geometry.coordinates;
                                //
                                const commonProps = {
                                    clusterIndex: point.id,
                                    cluster: point,
                                    latitude,
                                    longitude,
                                    pointCount: 1,
                                    clusterPoints: points,
                                    paletteStyle
                                };

                                // 檢查是否只有一個點在香港內
                                const isInHongKongArea = isInHongKong(
                                    latitude,
                                    longitude
                                );
                                const isOnlyOnePointInHongKong =
                                    isInHongKongArea &&
                                    mapPoints.filter(p => {
                                        const [
                                            lng,
                                            lat
                                        ] = p.geometry.coordinates;
                                        return isInHongKong(lat, lng);
                                    }).length === 1;

                                return (
                                    <MarkerDef
                                        key={point.id}
                                        {...commonProps}
                                        onClick={handlePointClick}
                                        onTooltipOpen={handleClusterTooltipOpen}
                                        onPopupOpen={handlePointPopupOpen}
                                        onPopupClose={handlePointPopupClose}
                                        // showTooltip={
                                        //     !!point?.properties?.location
                                        // }
                                        pointIconClassName="asdfads"
                                        showPopup={false}
                                        showTooltip={true}
                                        popupElement={
                                            usingPopup && (
                                                <HkbdbMapMarkerPopup
                                                    point={point}
                                                />
                                            )
                                        }
                                        eventList={point?.properties?.eventList}
                                        type="General"
                                        isShowingHongKongTextBelow={
                                            isOnlyOnePointInHongKong
                                        }
                                    />
                                );
                            })}
                        {usingCluster &&
                            clusters.map((cluster, clusterIndex) => {
                                // every cluster point has coordinates
                                const [
                                    longitude,
                                    latitude
                                ] = cluster.geometry.coordinates;
                                // the point may be either a cluster or a crime point
                                const {
                                    cluster: isCluster,
                                    point_count: pointCount
                                } = cluster.properties;
                                // if isCluster is true, render cluster
                                const commonProps = {
                                    key: clusterIndex.toString(),
                                    clusterIndex,
                                    cluster,
                                    latitude,
                                    longitude,
                                    pointCount,
                                    clusterPoints: points,
                                    paletteStyle
                                };

                                // 檢查是否只有一個點在香港內
                                const isInHongKongArea = isInHongKong(
                                    latitude,
                                    longitude
                                );
                                const isOnlyOnePointInHongKong =
                                    isInHongKongArea &&
                                    clusters.filter(c => {
                                        const [
                                            lng,
                                            lat
                                        ] = c.geometry.coordinates;
                                        return isInHongKong(lat, lng);
                                    }).length === 1;

                                if (isCluster) {
                                    return (
                                        // eslint-disable-next-line react/jsx-key
                                        <ClusterMarker
                                            {...commonProps}
                                            onClick={handleClusterClick}
                                            onTooltipOpen={
                                                handleClusterTooltipOpen
                                            }
                                            // showTooltip={
                                            // !!cluster?.properties?.location
                                            // }
                                            showTooltip={true}
                                            clusterIconClassName={`cluster-marker-icon-${paletteStyle}`}
                                        />
                                    );
                                }
                                // if isCluster is false, render point
                                return (
                                    // eslint-disable-next-line react/jsx-key
                                    // <PointMarker
                                    //     {...commonProps}
                                    //     onClick={handlePointClick}
                                    //     onPopupOpen={handlePopupOpen}
                                    //     onTooltipOpen={handlePointTooltipOpen}
                                    //     showPopup={false}
                                    //     showTooltip={
                                    //         !!cluster?.properties?.location
                                    //     }
                                    //     pointIconClassName={`point-marker-icon-${paletteStyle}`}
                                    //     popupElement={
                                    //         usingPopup && (
                                    //             <HkbdbMapMarkerPopup
                                    //                 point={cluster}
                                    //             />
                                    //         )
                                    //     }
                                    // />
                                    <MarkerDef
                                        key={cluster.id}
                                        {...commonProps}
                                        onClick={handlePointClick}
                                        onTooltipOpen={handleClusterTooltipOpen}
                                        onPopupOpen={handlePointPopupOpen}
                                        onPopupClose={handlePointPopupClose}
                                        // showTooltip={
                                        //     !!point?.properties?.location
                                        // }
                                        pointIconClassName="asdfads"
                                        showPopup={false}
                                        showTooltip={true}
                                        popupElement={
                                            usingPopup && (
                                                <HkbdbMapMarkerPopup
                                                    point={cluster}
                                                />
                                            )
                                        }
                                        eventList={
                                            cluster?.properties?.eventList
                                        }
                                        type="General"
                                        isShowingHongKongTextBelow={
                                            isOnlyOnePointInHongKong
                                        }
                                    />
                                );
                            })}
                    </FeatureGroup>
                </LayersControl.Overlay>
            </LayersControl>
        </MapContainer>
    );
};

// interface MyMapProps {
//   onPointChange?: (
//     _cluster: IPoint | ICluster,
//     ref?: React.RefObject<L.Marker<any>>,
//   ) => void;
//   points: IPoint[];
//   onReady?: () => void;
//   onCreated?: (_map: L.Map) => void;
//   paletteStyle?: PaletteKey;
//   style?: CSSProperties;
//   usingCluster?: boolean;
// }

GenaralMap.propTypes = {
    points: PropTypes.array,
    lines: PropTypes.array,
    onPointChange: PropTypes.func,
    onReady: PropTypes.func,
    onCreated: PropTypes.func,
    paletteStyle: PropTypes.string,
    style: PropTypes.object,
    usingCluster: PropTypes.bool,
    usingPopup: PropTypes.bool,
    usingMarkerWhenTarget: PropTypes.bool,
    refreshDataTs: PropTypes.number,
    pointSelected: PropTypes.object
};

export default GenaralMap;
