import static com.kms.katalon.core.checkpoint.CheckpointFactory.findCheckpoint
import static com.kms.katalon.core.testcase.TestCaseFactory.findTestCase
import static com.kms.katalon.core.testdata.TestDataFactory.findTestData
import static com.kms.katalon.core.testobject.ObjectRepository.findTestObject
import static com.kms.katalon.core.testobject.ObjectRepository.findWindowsObject
import com.kms.katalon.core.checkpoint.Checkpoint as Checkpoint
import com.kms.katalon.core.cucumber.keyword.CucumberBuiltinKeywords as CucumberKW
import com.kms.katalon.core.mobile.keyword.MobileBuiltInKeywords as Mobile
import com.kms.katalon.core.model.FailureHandling as FailureHandling
import com.kms.katalon.core.testcase.TestCase as TestCase
import com.kms.katalon.core.testdata.TestData as TestData
import com.kms.katalon.core.testng.keyword.TestNGBuiltinKeywords as TestNGKW
import com.kms.katalon.core.testobject.TestObject as TestObject
import com.kms.katalon.core.webservice.keyword.WSBuiltInKeywords as WS
import com.kms.katalon.core.webui.keyword.WebUiBuiltInKeywords as WebUI
import com.kms.katalon.core.windows.keyword.WindowsBuiltinKeywords as Windows
import internal.GlobalVariable as GlobalVariable
import org.openqa.selenium.Keys as Keys

WebUI.openBrowser('')

WebUI.navigateToUrl('https://hkbdb2.daoyidh.com/zh-hans/map')

WebUI.click(findTestObject('Object Repository/Page_HKBDB/人物按鈕_小思'))
WebUI.click(findTestObject('Object Repository/Page_HKBDB/人物按鈕_西西'))
WebUI.click(findTestObject('Object Repository/Page_HKBDB/人物按鈕_金庸'))

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_內容 (小思)'), 10)
WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_內容 (西西)'), 10)
WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖右上_線條圖例_內容 (金庸)'), 10)

WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖層級_65 (香港)'))
WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖層級_64 (香港)'))
WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖層級_63 (香港)'))
WebUI.click(findTestObject('Object Repository/Page_HKBDB/地圖'))

WebUI.waitForElementVisible(findTestObject('Object Repository/Page_HKBDB/地圖左上_地點資訊_視窗'), 10)

WebUI.dragAndDropByOffset(findTestObject('Page_HKBDB/開始時間_拖曳點'), 200, 0)
WebUI.dragAndDropByOffset(findTestObject('Page_HKBDB/結束時間_拖曳點'), -200, 0)

WebUI.closeBrowser()