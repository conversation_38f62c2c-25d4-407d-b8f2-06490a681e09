import React, { useContext, useEffect, useState } from "react";

// react select
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable/dist/react-select.esm";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomDebounce from "./CustomDeBounce";

// common
import { isEmpty, isNotEmpty, safeGet } from "../../../../common/codes";

// common comp
import MenuList from "./MenuList";
import MenuListFooter from "./MenuListFooter";

// api
import { Api, doRestCreate, readHkbdbData } from "../../../../api/hkbdb/Api";
import { fetchOptionList } from "../commonAction";

// store
import { StoreContext } from "../../../../store/StoreProvider";

const CustomDropdown = ({
    rowIdx,
    graph,
    eventId,
    editData,
    setEditData,
    ontologyType,
    propRange,
    propertyBindRangeStr
}) => {
    //
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { ontologyDefined } = state.property;
    //
    const [inputValue, setInputValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debInputValue = CustomDebounce(inputValue, 1000);
    //
    const [optionState, setOptionState] = useState(() => {
        return {
            isLoading: false,
            options: [],
            value: [],
            addTimes: 1,
            limit: 10
        };
    });
    //
    const handleInputChange = value => {
        setInputValue(value);
    };
    //
    const handleChange = selectValue => {
        //
        selectValue = isEmpty(selectValue) ? [] : selectValue;
        // keep input value when it changed
        setOptionState(prevCreateState => ({
            ...prevCreateState,
            value: selectValue
        }));
        // change content
        const createdData = {
            // 注意: 這裡 rowIdx 會自動被轉為 string，object' key 只允許字串內容
            // 後續使用 rowIdx 時要注意
            [rowIdx]: {
                graph,
                srcId: eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: selectValue
            }
        };
        // update useState
        if (isNotEmpty(safeGet(createdData, [rowIdx, "values"], []))) {
            // update editData state
            setEditData(prevEditData => {
                return {
                    ...prevEditData,
                    createdData: {
                        // old changed items
                        ...prevEditData.createdData,
                        // new changed item
                        ...createdData
                    }
                };
            });
        } else {
            setEditData(prevEditData => {
                //
                const {
                    [rowIdx]: _,
                    ...restCreatedData
                } = prevEditData.createdData;
                //
                return {
                    ...prevEditData,
                    createdData: {
                        ...restCreatedData
                    }
                };
            });
        }
    };
    //
    const handleCreate = async inputValue => {
        let objValue = {};
        //
        const foundClassName = Object.keys(ontologyDefined).find(
            key =>
                `${key}`.toLowerCase().indexOf(`${propRange}`.toLowerCase()) !==
                -1
        );
        //
        if (isNotEmpty(foundClassName)) {
            //
            const targetData = safeGet(ontologyDefined, [foundClassName], []);
            //
            const foundLabel = targetData.find(
                item => item.property === "label"
            );
            //
            const foundBestknownName = targetData.find(
                item => item.property === "bestKnownName"
            );
            //
            if (isNotEmpty(foundLabel)) {
                objValue = {
                    ...objValue,
                    [`label_${propRange}`]: [inputValue]
                };
            }
            //
            if (isNotEmpty(foundBestknownName)) {
                objValue = {
                    ...objValue,
                    bestKnownName: [inputValue]
                };
            }
            // label 或 bestKnownName 都不存在則預設使用 label
            if (isEmpty(foundLabel) && isEmpty(foundBestknownName)) {
                objValue = {
                    ...objValue,
                    [`label_${propRange}`]: [inputValue]
                };
            }
        } else {
            // 連類別都找不到的話，預設就用 label
            objValue = {
                ...objValue,
                [`label_${propRange}`]: [inputValue]
            };
        }
        //
        const createdData = {
            graph,
            srcId: "not_needed",
            classType: propRange,
            value: objValue
        };
        // let createdData = {
        //     graph,
        //     srcId: "not_needed",
        //     classType: range,
        //     value: objValue
        // };
        //
        // const createdData = {
        //     graph,
        //     srcId: eventId,
        //     classType: ontologyType,
        //     propertyBindRangeStr,
        //     values: [inputValue]
        // };
        //
        if (!isEmpty(createdData)) {
            // open isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: true
            }));
            // call create api
            const result = await doRestCreate(user, createdData);
            //
            // console.log(result);
            if (result.state) {
                //
                const apiStr = Api.findIdByValue()
                    .replace("{class}", propRange)
                    .replace("{keyword}", inputValue);
                //
                const result = await readHkbdbData(apiStr);
                //
                const objId = safeGet(result, ["data", 0, "value"], "");
                // console.log("objId", objId);
                //
                if (isNotEmpty(objId)) {
                    // add new option
                    const newOption = {
                        label: inputValue,
                        value: objId
                    };
                    // update editData state
                    setEditData(prevEditData => ({
                        ...prevEditData,
                        createdData: {
                            // old changedData records
                            ...prevEditData.createdData,
                            // new ChangedData record
                            ...{
                                [rowIdx]: {
                                    graph,
                                    srcId: eventId,
                                    classType: ontologyType,
                                    propertyBindRangeStr,
                                    values: [...optionState.value, newOption]
                                }
                            }
                        }
                    }));
                    // update state
                    setOptionState(prevCreateState => ({
                        ...prevCreateState,
                        options: [...prevCreateState.options, newOption],
                        value: [...prevCreateState.value, newOption]
                    }));
                } else {
                    console.log("handleCreate failed to add item:", inputValue);
                }
                // console.log(result);
            }
            // close isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: false
            }));
        }
        // console.log(createdData);
    };
    //
    const customStyles = {
        container: styles => ({
            ...styles
            // margin: "-9px",
            // maxWidth: "500px"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            // borderRadius: "unset",
            backgroundColor: controlColor
        })
    };
    //
    const addOption = () => {
        //
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(propRange, debInputValue, limitValue, 30 * 1000)
            .then(res => {
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                setOptionState(prevState => ({
                    ...prevState,
                    options: newData,
                    addTimes: prevState.addTimes + 1,
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    options: []
                }));
            });
    };
    //
    useEffect(() => {
        //
        if (isEmpty(propRange)) {
            return;
        }
        // open dropdown Loading
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(propRange, debInputValue, limitValue, 30 * 1000)
            .then(res => {
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                setOptionState(prevState => ({
                    ...prevState,
                    options: newData,
                    addTimes: prevState.addTimes + 1,
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    options: [],
                    isLoading: false
                }));
            });
    }, [debInputValue, propRange]);
    //
    return (
        <CreatableSelect
            isMulti
            isClearable
            styles={customStyles}
            isDisabled={editData.isUpdated || editData.isCreated}
            isLoading={optionState.isLoading}
            options={optionState.options}
            value={optionState.value}
            onChange={handleChange}
            onInputChange={handleInputChange}
            onCreateOption={handleCreate}
            components={{
                MenuList,
                MenuListFooter: (
                    <MenuListFooter
                        options={optionState.options}
                        onClick={addOption}
                    />
                )
            }}
            placeholder={
                <FormattedMessage
                    id={"people.Information.dropDown.placeholder"}
                    defaultMessage={"Select..."}
                />
            }
            filterOption={createFilter({ ignoreAccents: false })}
        />
    );
};

export default CustomDropdown;
