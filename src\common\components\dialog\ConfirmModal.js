import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
//
import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import IconButton from "@mui/material/IconButton";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import CheckCircleIcon from "@material-ui/icons/CheckCircle";
import ButtonMain from "../button/ButtonMain";
//
import { isFunction } from "../../codes";

const ConfirmModal = ({
    title,
    contentText,
    openSignal,
    onClose,
    onYes,
    onNo,
    yesText,
    noText,
    withFinishIcon
}) => {
    const [open, setOpen] = useState(false);

    const handleClose = () => {
        setOpen(false);
        if (onClose && isFunction(onClose)) onClose();
    };

    useEffect(() => {
        if (openSignal) {
            setOpen(true);
        }
    }, [openSignal]);

    const handleYes = () => {
        if (onYes && isFunction(onYes)) onYes();
        handleClose();
    };

    const handleNo = () => {
        if (onNo && isFunction(onNo)) onNo();
        handleClose();
    };

    const getContentText = () => {
        const safeContentText = Array.isArray(contentText)
            ? contentText
            : [contentText];
        return safeContentText.map((text, idx) => (
            <DialogContentText
                key={idx.toString()}
                id="alert-dialog-description"
            >
                {text}
            </DialogContentText>
        ));
    };

    const getDialogActionsSx = () => {
        if (yesText && noText) {
            return {
                "&.MuiDialogActions-root": {
                    display: "flex",
                    justifyContent: "center"
                }
            };
        }
        return {};
    };

    return (
        <div>
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                sx={{
                    "&.MuiDialog-root": {
                        "& .MuiDialog-container": {
                            "& .MuiPaper-root": {
                                padding: "18px 36px"
                            }
                        }
                    }
                }}
            >
                {withFinishIcon && (
                    <Box display="flex" justifyContent="center">
                        <IconButton
                            color="primary"
                            size="large"
                            sx={{ color: "#4183c4" }}
                        >
                            <CheckCircleIcon fontSize="large" />
                        </IconButton>
                    </Box>
                )}
                {title && (
                    <DialogTitle
                        id="alert-dialog-title"
                        sx={{ textAlign: "center" }}
                    >
                        {title}
                    </DialogTitle>
                )}
                <DialogContent>
                    <Box
                        display="flex"
                        flexDirection="column"
                        justifyContent="center"
                        sx={{ textAlign: "center" }}
                    >
                        {getContentText()}
                    </Box>
                </DialogContent>
                {(yesText || noText) && (
                    <DialogActions
                        sx={{
                            ...getDialogActionsSx()
                        }}
                    >
                        {((yesText && !noText) || (!yesText && noText)) && (
                            <ButtonMain
                                buttonProps={{
                                    text: yesText,
                                    fullWidth: true,
                                    onClick: handleYes,
                                    variant: "contained"
                                }}
                            />
                        )}
                        {yesText && noText && (
                            <Box display="flex" justifyContent="space-between">
                                <Box mx={1}>
                                    <ButtonMain
                                        buttonProps={{
                                            text: yesText,
                                            sx: {
                                                minWidth: 92
                                            },
                                            fullWidth: true,
                                            onClick: handleYes,
                                            variant: "contained"
                                        }}
                                    />
                                </Box>

                                <Box mx={1}>
                                    <ButtonMain
                                        buttonProps={{
                                            text: noText,
                                            sx: {
                                                minWidth: 92
                                            },
                                            onClick: handleNo,
                                            variant: "outlined"
                                        }}
                                    />
                                </Box>
                            </Box>
                        )}
                    </DialogActions>
                )}
            </Dialog>
        </div>
    );
};

ConfirmModal.propTypes = {
    title: PropTypes.string,
    contentText: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.arrayOf(PropTypes.string)
    ]),
    openSignal: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.object,
        PropTypes.number
    ]),
    onClose: PropTypes.func,
    onYes: PropTypes.func,
    onNo: PropTypes.func,
    yesText: PropTypes.string,
    noText: PropTypes.string,
    withFinishIcon: PropTypes.bool
};

ConfirmModal.defaultProps = {
    title: null,
    contentText: null,
    openSignal: false,
    onClose: () => null,
    onYes: () => null,
    onNo: () => null,
    yesText: null,
    noText: null,
    withFinishIcon: false
};

export default ConfirmModal;
