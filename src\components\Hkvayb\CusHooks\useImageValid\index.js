import React from "react";

const useImageValid = urls => {
    const [validImages, setValidImages] = React.useState([]);

    React.useEffect(() => {
        urls.forEach(item => {
            const { src, backupSrc } = item;
            const imageArr = [src, backupSrc];

            imageArr.forEach(url => {
                const img = new Image();
                img.src = url;

                if (img.complete) {
                    //
                } else {
                    img.onload = () => {
                        setValidImages(prevState => [...prevState, item]);
                    };
                    img.onerror = () => {
                        //
                    };
                }
            });
        });
    }, [JSON.stringify(urls)]);

    return validImages;
};

export default useImageValid;
