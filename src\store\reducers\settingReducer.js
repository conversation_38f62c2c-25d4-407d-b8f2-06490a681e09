import Act from "../actions";

const initState = {
    changedRole: {},
    willRemoveUser: "",
    renderSignal: "",
    // message
    message: { title: "", type: "", success: 0, error: 0, renderSignal: "" },
    // userSettings
    tableSortedRecords: { information: {} },
    fieldSetting: {},
    // email config
    emailConfig: {},
    // signup config
    signuplConfig: {} // {email-domain:{allowable:[]},privacyPolicy:{zh:"",en:""},termsOfService:{en:"",zh:""}}
};

const settingReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.FIREBASE_USER_ROLE_CHANGED:
            return { ...state, changedRole: action.payload };
        case Act.SETTINGS_SORTED_HEADER_SET:
            return { ...state, tableSortedRecords: action.payload };
        case Act.SETTINGS_FIELD_SETTING_SET:
            return { ...state, fieldSetting: action.payload };
        case Act.SETTINGS_EMAIL_CONFIG:
            return { ...state, emailConfig: action.payload };
        case Act.SETTINGS_SIGNUP_CONFIG:
            return { ...state, signuplConfig: action.payload };
        default:
            return state;
    }
};

export default settingReducer;
