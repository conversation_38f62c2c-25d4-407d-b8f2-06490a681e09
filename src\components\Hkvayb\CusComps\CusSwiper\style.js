import { makeStyles } from "@material-ui/styles";

const buttomStyle = {
    margin: "0 0 4px",
    fontFamily: "NotoSansHK",
    fontSize: "14px",
    fontWeight: "normal",
    fontStretch: "normal",
    fontStyle: "normal",
    lineHeight: "1.43",
    letterSpacing: "0.22px",
    textAlign: "left",
    color: "#333",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    ":hover": {
        color: "#b79d79"
    },
    "&[disabled]": {
        pointerEvents: "none",
        opacity: "0.7"
    }
};

const useStyles = makeStyles({
    root: {},
    hkvayb_swiper: props => ({
        display: "flex",
        overflow: "hidden",
        ...props.hkvayb_swiper
    }),
    hkvayb_left_arrow_icon: props => ({
        width: "24px",
        height: "24px",
        backgroundImage: `url("https://fs-root.daoyidh.com/hkvayb/desktop/left_arrow_mask_3x.png")`,
        backgroundSize: "cover",
        ...props.hkvayb_left_arrow_icon
    }),
    hkvayb_right_arrow_icon: props => ({
        width: "24px",
        height: "24px",
        backgroundImage: `url("https://fs-root.daoyidh.com/hkvayb/desktop/right_arrow_mask_3x.png")`,
        backgroundSize: "cover",
        ...props.hkvayb_right_arrow_icon
    }),
    hkvayb_swiper_button_group: props => ({
        height: "24px",
        marginTop: "15.5px",
        display: "flex",
        justifyContent: "space-between",
        ...props.hkvayb_swiper_button_group
    }),
    hkvayb_swiper_prev_bottom: props => ({
        ...buttomStyle,
        ...props.hkvayb_swiper_prev_bottom
    }),
    hkvayb_swiper_next_bottom: props => ({
        ...buttomStyle,
        ...props.hkvayb_swiper_next_bottom
    })
});

export default useStyles;
