import React from "react";

import { isEmpty, safeGet } from "../../../../common/codes";

const useTripleMerge = triples => {
    const [data, setData] = React.useState({});
    React.useEffect(() => {
        if (triples) {
            const mergedData = triples.reduce((prevObj, item) => {
                const { eventId, property, value_zh: value, classType } = item;
                if (isEmpty(prevObj)) {
                    return {
                        [eventId]: {
                            [property]: {
                                type: classType,
                                value
                            }
                        }
                    };
                } else {
                    return {
                        ...prevObj,
                        [eventId]: {
                            ...safeGet(prevObj, [eventId], {}),
                            [property]: {
                                type: classType,
                                value: [
                                    ...safeGet(
                                        prevObj,
                                        [eventId, property, "value"],
                                        []
                                    ),
                                    value
                                ]
                            }
                        }
                    };
                }
            }, {});
            setData(mergedData);
        }
    }, [JSON.stringify(triples)]);
    return data;
};

export default useTripleMerge;
