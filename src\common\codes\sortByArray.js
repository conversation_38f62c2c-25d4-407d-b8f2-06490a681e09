import { sortedByStroke } from "twchar";

const sortByArray = (inputArr, keyArr) => {
    if (!inputArr || inputArr.length === 0) {
        return [];
    }

    const keyElse = "@KeyElse";
    const resObj = { [keyElse]: [] };

    inputArr.forEach(item => {
        for (const key in keyArr) {
            if (Object.keys(item).indexOf(key) > -1) {
                // include
                if (Object.keys(resObj).indexOf(key) > -1) {
                    resObj[key].append(item);
                } else {
                    resObj[key] = [item];
                }
                return;
            }
        }
        resObj[keyElse].append(item);
    });

    // sort
    let sortedArr = [];
    Object.keys(resObj).forEach(key => {
        const keyArr = sortedByStroke(resObj[key], [key]);
        sortedArr = sortedArr.concat(keyArr);
    });
    return sortedArr;
};

export { sortByArray };
