// react
import React, { useContext } from "react";

// ui
import { Table, Icon } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../common/codes";

const CustomTableCell = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    // get query from state
    const { queryResult } = state.query;
    const { head } = queryResult;
    //
    const TableCell = () => {
        if (isEmpty(head)) {
            return ["s", "p", "o"].map(head => {
                return (
                    <Table.HeaderCell
                        key={`table-header-${head}`}
                        textAlign="center"
                    >
                        {head}
                    </Table.HeaderCell>
                );
            });
        } else {
            return head.map(head => {
                return (
                    <Table.HeaderCell key={`table-header-${head}`}>
                        {head}
                    </Table.HeaderCell>
                );
            });
        }
    };
    //
    return <TableCell />;
};

export default CustomTableCell;
