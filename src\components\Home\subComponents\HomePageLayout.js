import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
    Segment,
    Grid,
    Header,
    Image,
    Container,
    List,
    Divider,
    Card
} from "semantic-ui-react";
import { getHomePageCount } from "../services/sparql/templates/queries";
import NumberFormat from "react-number-format";
import UnderConstruction from "../../../common/components/UnderConstruction";

export const HomePageLayout = () => {
    const [peopleCount, setPeopleCount] = useState(19372);
    const [organizationCount, setOrganizationCount] = useState(45620);
    const [positionCount, setPositionCount] = useState(1379);
    useEffect(() => {
        getHomePageCount()
            .then(response => {
                setPeopleCount(response.bindings[0].peopleCount.value);
                setPositionCount(response.bindings[0].positionCount.value);
                setOrganizationCount(response.bindings[0].orgCount.value);
            })
            .catch(() => {
                setPeopleCount(19372);
                setPositionCount(1379);
                setOrganizationCount(45620);
            });
    }, []);
    return (
        <div>
            <Segment style={{ padding: "4em 0em" }} vertical>
                <Container textAlign="justified">
                    <h2>Introduction</h2>
                    <Divider />
                    <Divider hidden />
                    <UnderConstruction
                        title={
                            "Here is the introduction about The Hong Kong Biographical Database"
                        }
                        cardHeader={"資料準備中"}
                        cardDescription={""}
                    />
                </Container>
            </Segment>

            <Segment
                inverted
                vertical
                style={{ padding: "5em 0em", backgroundColor: "#000000" }}
            >
                <Container>
                    <Grid divided centered inverted stackable>
                        <Grid.Row style={{ paddingBottom: "0em" }}>
                            <Grid.Column width={3} />
                            <Grid.Column width={3}>
                                <Header
                                    textAlign={"center"}
                                    inverted
                                    as="h4"
                                    content="Contact"
                                />
                            </Grid.Column>
                            <Grid.Column width={3}>
                                <Header
                                    textAlign={"center"}
                                    inverted
                                    as="h4"
                                    content="Sponsored by"
                                />
                            </Grid.Column>
                        </Grid.Row>
                        <Grid.Row style={{ paddingTop: "0em" }}>
                            <Grid.Column width={3} verticalAlign={"bottom"}>
                                <a
                                    target="view_window"
                                    href="http://www.orient.cas.cz/"
                                >
                                    {/* <Image src="img/OI_ang_1_NEW_CMYK.jpeg" /> */}
                                </a>
                            </Grid.Column>
                            <Grid.Column
                                width={3}
                                verticalAlign={"bottom"}
                                style={{ paddingBottom: "1.5em" }}
                            >
                                <List link inverted verticalAlign={"bottom"}>
                                    <List.Item as="a">
                                        {/* {"email"} */}
                                    </List.Item>
                                </List>
                            </Grid.Column>
                            <Grid.Column width={3} verticalAlign={"bottom"}>
                                {/* <Image src="img/GACR-logo.jpeg" /> */}
                            </Grid.Column>
                        </Grid.Row>
                    </Grid>
                </Container>
            </Segment>
        </div>
    );
};
