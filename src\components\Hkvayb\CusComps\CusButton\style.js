import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_buttom_basic: props => ({
        width: "15%",
        height: "48px",
        backgroundColor: "#b79d79",
        border: 0,
        color: "#ffffff",
        cursor: "pointer",
        fontSize: "16px",
        fontFamily: "NotoSansHK",
        "&:hover": {
            backgroundColor: "#d7c0a1"
        },
        "&:active": {
            backgroundColor: "#b79d79"
        },
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            width: "100%"
        },
        ...props.hkvayb_buttom_basic
    }),
    hkvayb_buttom_invert: props => ({
        width: "15%",
        height: "48px",
        backgroundColor: "#ffffff",
        border: 0,
        color: "#b79d79",
        cursor: "pointer",
        fontSize: "16px",
        fontFamily: "NotoSansHK",
        "&:hover": {
            color: "#ffffff",
            backgroundColor: "#d7c0a1"
        },
        "&:active": {
            backgroundColor: "#b79d79"
        },
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            width: "100%",
            backgroundColor: "rgb(239 239 239)"
        },
        ...props.hkvayb_buttom_invert
    }),
    hkvayb_buttom_border_invert: props => ({
        width: "15%",
        height: "48px",
        backgroundColor: "#ffffff",
        border: "solid 1px #b79d79",
        color: "#b79d79",
        cursor: "pointer",
        fontSize: "16px",
        fontFamily: "NotoSansHK",
        "&:hover": {
            color: "#ffffff",
            backgroundColor: "#d7c0a1"
        },
        "&:active": {
            backgroundColor: "#b79d79"
        },
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            width: "100%",
            whiteSpace: "nowrap"
        },
        ...props.hkvayb_buttom_border_invert
    })
});

export default useStyles;
