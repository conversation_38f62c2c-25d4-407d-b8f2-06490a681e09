import React from "react";

// semantic ui
import { Container } from "semantic-ui-react";

// RWD Layout
import { ResponsiveContainer } from "../../../../layout/Layout";

// custom component
import SearchPage from "./SearchPage";

const index = props => {
    return (
        <ResponsiveContainer {...props}>
            <Container style={{ paddingBottom: "100px", width: "100%" }}>
                <SearchPage />
            </Container>
        </ResponsiveContainer>
    );
};

export default index;
