import React from "react";

import useStyles from "./style";

import CusBr from "../CusBr";
import CusPara from "../CusPara";
import CusPdfLink from "../CusPdfLink";
import CusMarkdown from "../CusMarkdown";
import CusDualPages from "../CusDualPages";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const coverButtonLeftStyle = {
    hkvayb_buttom_border_invert: {
        width: "100%"
    }
};

const coverButtonRightStyle = {
    hkvayb_buttom_border_invert: {
        width: "100%"
    }
};

const coverBrStyle = {
    hkvayb_br: {
        padding: "10px"
    }
};

const CusPublicIssue = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const defObj = { type: null, value: [] };

    const bilingFunc = bilingual(defObj);
    const [descZh, descEn] = bilingFunc(data, "desc");
    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [pdfLinkZh, pdfLinkEn] = bilingFunc(data, "pdfLink");

    return (
        <div className={classes.hkvayb_essay}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusDualPages>
                    <CusMarkdown value={descZh.value} />
                    <CusMarkdown value={descEn.value} />
                </CusDualPages>
            </CusPara>
            <CusPara>
                <div>
                    <CusPdfLink
                        label="閱讀全文"
                        style={coverButtonLeftStyle}
                        value={pdfLinkZh.value}
                        path={`pdf+public+issue`}
                        // path={`pdf+public+issue/${year}`}
                    />
                </div>
                <CusBr style={coverBrStyle} />
                <div>
                    <CusPdfLink
                        label="Read the full articles"
                        style={coverButtonRightStyle}
                        value={pdfLinkEn.value}
                        path={`pdf+public+issue`}
                        // path={`pdf+public+issue/${year}`}
                    />
                </div>
            </CusPara>
        </div>
    );
};

export default CusPublicIssue;
