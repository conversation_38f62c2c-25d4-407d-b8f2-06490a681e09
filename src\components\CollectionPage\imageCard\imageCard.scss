.ImageCard {
  transition: box-shadow ease-in-out 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0px 0px 30px 0px rgba(149, 157, 165, 0.2);
  }

  &__avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    //background: #f7f1ea;
  }


  &__title {
    padding: 12px;
    color: #104860;
    //background: whites
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;

    p{
      font-weight: 400;
      padding: 5px;
    }
  }

  &__info {
    color: #797979;
    font-size: 12px;
    line-height: 18px;
  }


  &--slick {
    padding: 10px;
    width: 260px;
    background-color: white;
    box-shadow: 0px 0px 10px 0px rgba(149, 157, 165, 0.2);

    .ImageCard {
      &__avatar {
        width: 240px;
        height: 240px;
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .ImageCard{
    &--slick {
      width: 148px;
      padding: 8px;
      .ImageCard {
        &__avatar {
          width: 132px;
          height: 132px;
        }
      }
    }
  }
}