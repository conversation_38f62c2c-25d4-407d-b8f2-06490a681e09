const person = {
    hasBirthDate: "出生日期",
    hasDeathDate: "逝世日期",
    gender: "性別",
    hasNationality: "國籍",
    hasNativePlace: "籍貫",
    hasPlaceOfBirth: "出生地點",
    hasPlaceOfDeath: "逝世地點",
    hasPlaceOfBuried: "埋葬地點",
    deathCause: "死因",
    comment: "備註",
    graph: "資料來源",

    basicProperty: "項目",
    basicData: "內容",

    hasBirthDate__DateEvent: "出生日期",
    hasDeathDate__DateEvent: "逝世日期",
    gender__string: "性別",
    hasNationality__Place: "國籍",
    hasNativePlace__Place: "籍貫",
    hasPlaceOfBirth__Place: "出生地點",
    hasPlaceOfDeath__Place: "逝世地點",
    hasPlaceOfBuried__Place: "埋葬地點",
    deathCause__string: "死因",
    comment__string: "備註"
};

const namenode = {
    originalName__string: "中文本名",
    penName__string: "筆名",
    name__string: "其他名稱",
    comment__string: "備註",
    graph: "資料來源"
};

const educationevent = {
    hasStartDate__DateEvent: "開始日期",
    hasEndDate__DateEvent: "結束日期",
    hasEducatedAt__Organization: "就讀於／學校",
    hasAcademicDegree__AcademicDegree: "學歷",
    hasPlace__Place: "地點",
    comment__string: "備註",
    graph: "資料來源"
};

const employmentevent = {
    hasStartDate__DateEvent: "開始日期",
    hasEndDate__DateEvent: "結束日期",
    employmentType__string: "工作類型",
    hasEmployedAt__Organization: "工作機構",
    jobTitle__string: "職稱",
    hasPlace__Place: "工作地點",
    penName__string: "筆名",
    column__string: "專欄標題",
    hasColumnist__Person: "專欄合寫者",
    hasAlumni__Person: "引薦者",
    comment__string: "備註",
    graph: "資料來源"
};

const publication = {
    hasAuthor__Person: "作者及合寫者",
    hasEditor__Person: "編者",
    hasTranslator__Person: "譯者",
    penName__string: "筆名",
    label_publication__string: "著作名稱",
    hasPlaceOfPublication__Place: "出版地點",
    hasPublisher__Organization: "出版社",
    hasInceptionDate__DateEvent: "出版日期",
    hasGenre__string: "著作文類",
    language__string: "著作語言",
    hasBookPrefacer__Person: "撰序者",
    hasInterviewer__Person: "訪問者",
    hasRespondent__Person: "受訪者",
    isEditionOrTranslationOf__WrittenWork: "原著或版本資料",
    // 顯示Publication而不是WrittenWork
    isEditionOrTranslationOf__Publication: "原著或版本資料",
    comment__string: "備註",
    graph: "資料來源"
};

const article = {
    hasAuthor__Person: "作者",
    hasTranslator__Person: "譯者",
    creatorNote__string: "刊登名稱",
    label_article__string: "文章名稱",
    hasPublishedIn__Publication: "刊物名稱",
    hasInceptionDate__DateEvent: "發表日期",
    hasGenre__string: "文類",
    language__string: "作品語言",
    page__string: "刊登頁碼",
    hasInterviewer__Person: "訪問者",
    hasRespondent__Person: "受訪者",
    comment__string: "備註",
    graph: "資料來源"
};

const otherwork = {
    hasRelatedPerson__Person: "作者及合作者",
    hasRelatedOrganization__Organization: "相關組織",
    label_otherwork__string: "作品名稱",
    hasStartDate__DateEvent: "發表日期",
    otherWorkType__string: "作品類型",
    comment__string: "備註",
    graph: "資料來源"
};

const organizationevent = {
    hasFounded__Organization: "創立組織名稱",
    hasParticipant__Organization: "參與組織名稱",
    jobTitle__string: "職稱",
    hasPlace__Place: "組織地點",
    organizationType__string: "組織類型",
    hasStartDate__DateEvent: "開始日期",
    hasEndDate__DateEvent: "退出日期",
    comment__string: "備註",
    graph: "資料來源"
};

const relationevent = {
    hasFriendship__Person: "朋友",
    hasYaji__Person: "雅集",
    hasClassmate__Person: "同學",
    hasStudent__Person: "學生",
    hasTeacher__Person: "老師",
    hasKinship__Person: "親屬",
    hasSon__Person: "兒子",
    hasDaughter__Person: "女兒",
    hasWife__Person: "妻子",
    hasSpouse__Person: "丈夫",
    hasBrother__Person: "兄弟",
    hasSister__Person: "姊妹",
    hasNephew__Person: "姪子",
    hasMotherGrandSon__Person: "外孫",
    hasGrandSon__Person: "孫",
    hasDescendants__Person: "後代",
    hasGrandFather__Person: "祖父",
    hasGrandMother__Person: "祖母",
    hasMaternalGrandfather__Person: "外祖父",
    hasMaternalGrandmother__Person: "外祖母",
    hasBoss__Person: "上司",
    hasSubordinateWas__Person: "下屬",
    hasDaughterInLaw__Person: "媳婦",
    hasCousin__Person: "堂兄弟",
    hasInLaw__Person: "姻親",
    hasIntroducer__Person: "介紹人",
    isRelationOf__Person: "其他關係",
    relationRemarks__string: "關係備註",
    graph: "資料來源"
};

const event = {
    hasStartDate__DateEvent: "開始日期",
    hasEndDate__DateEvent: "結束日期",
    label_event__string: "事件",
    hasRelatedPerson__Person: "相關人物",
    hasRelatedOrganization__Organization: "相關組織",
    participationIdentity__string: "參與身份",
    hasEventPlace__Place: "地點",
    comment__string: "備註",
    graph: "資料來源"
};

const awardevent = {
    hasStartDate__DateEvent: "獲獎日期",
    wasConferredBy__Organization: "頒發機構",
    awardTitle__string: "獎項名稱",
    hasPlace__Place: "頒發地點",
    comment__string: "備註",
    hasAwardedForWork__Publication: "得獎著作",
    hasAwardedForWork__Article: "得獎文章",
    graph: "資料來源"
};

// FIXME: Organization 頁面需要 check
const organization = {
    graph: "資料來源"
};

const member = {
    graph: "資料來源"
};

function getLabel(dataType) {
    switch (dataType) {
        case "person":
            return person;

        case "namenode":
            return namenode;

        case "educationevent":
            return educationevent;

        case "employmentevent":
            return employmentevent;

        case "publication":
            return publication;

        case "article":
            return article;

        case "otherwork":
            return otherwork;

        case "organizationevent":
            return organizationevent;

        case "relationevent":
            return relationevent;

        case "event":
            return event;

        case "awardevent":
            return awardevent;

        case "organization":
            return organization;

        case "member":
            return member;

        default:
            throw new Error(`Invalid data type: ${dataType}`);
    }
}

const convertPersonColName = (colName, type) => {
    // 調整名稱(label)及排序
    const swt = getLabel(type);
    return swt[colName];
};

// 用於一般的people(非suggester)
const convertNormalColName = (colName, type, item) => {
    // 調整名稱(label)及排序
    const swt = getLabel(type);
    if (Object.keys(swt).includes(colName)) {
        item["label"] = swt[colName];
    }

    return item;
};

// 用於非suggester的項目名稱
const convertNormalColHeaderName = (colName, type, ogVal) => {
    const swt = getLabel(type);

    if (Object.keys(swt).includes(colName)) {
        return swt[colName];
    }

    return ogVal;
};

const convertSugOptions = (sugOptions, type) => {
    // 調整名稱(label)及排序
    const swt = getLabel(type);
    return sugOptions
        .sort((a, b) => {
            return (
                Object.keys(swt).indexOf(a.value) -
                Object.keys(swt).indexOf(b.value)
            );
        })
        .map(item => {
            return {
                ...item,
                label: swt[item.value]
            };
        });
};

const convertSugTableHeader = (header, type) => {
    const swt = getLabel(type);
    const result = [];
    header
        .sort((a, b) => {
            return Object.keys(swt).indexOf(a) - Object.keys(swt).indexOf(b);
        })
        .forEach(item => {
            result[item] = swt[item];
        });
    return result;
};

export {
    convertSugOptions,
    convertSugTableHeader,
    convertPersonColName,
    convertNormalColName,
    convertNormalColHeaderName
};
