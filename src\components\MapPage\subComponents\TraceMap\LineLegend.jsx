import React, { useEffect, useState } from "react";
// mui
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import LegendToggleIcon from "@mui/icons-material/LegendToggle";
//
import PropTypes from "prop-types";

// hooks
import useWindowSize from "../../../../hook/useWindowSize";
import { basicOptions } from "../commons/mapCore/PointsLine";
import { FormattedMessage } from "react-intl";

const LineLegendStyle = {
    width: 125,
    height: 150
};

const iconButtonStyle = {
    size: "small",
    width: 28,
    height: 28
};

/**
 * types for groupPallet
 * {perId:string; group:object[]; style: {color:string; weight:number; dashArray:string}; person:string}[]
 *
 * for exampale:
 * [
 *     {
 *         "perId": "PER%E9%87%91%E5%BA%B8",
 *         "group": [
 *             {
 *                 "perId": "PER%E9%87%91%E5%BA%B8",
 *                 "person": "金庸",
 *                 "placeId": "PLA%E6%B5%99%E6%B1%9F%E7%9C%81%E6%B5%B7%E5%AF%A7%E7%B8%A3%E8%A2%81%E8%8A%B1%E9%8E%AE",
 *                 "lat": "30.411076",
 *                 "long": "120.781114",
 *                 "place": "浙江省海寧縣袁花鎮",
 *                 "placeKey": "出生地點",
 *                 "dateKey": "出生日期",
 *                 "year": "1924",
 *                 "month": "0",
 *                 "day": "0",
 *                 "infoId": "PER%E9%87%91%E5%BA%B8",
 *                 "infoType": "hasPlaceOfBirth",
 *                 "date": "1924"
 *             },...
 *         ],
 *         "style": {
 *             "color": "#FF4500",
 *             "weight": 2,
 *             "dashArray": "5, 5"
 *         },
 *         "person": "金庸"
 *     }
 * ]
 */

/**
 *
 * @param props
 * @returns {Element}
 * @constructor
 */
const LineLegend = props => {
    const { mapRef, groupPallet } = props;

    const [open, setOpen] = useState(true);
    // position(top, right) for the sidebar according to the mapRef
    const [position, setPosition] = useState({ top: 0, right: 0 });

    // hooks
    const winSize = useWindowSize();

    useEffect(() => {
        if (mapRef.current) {
            const {
                top,
                right,
                width,
                height
            } = mapRef.current.getBoundingClientRect();
            // get the viewport width
            const vw = Math.max(
                document.documentElement.clientWidth || 0,
                window.innerWidth || 0
            );
            setPosition({
                top: top + 56,
                right: vw - right + iconButtonStyle.width + 18
            });
        }
    }, [mapRef, winSize]);

    const hiddenStyle = {
        width: "0px",
        height: "0px",
        minHeight: "0px"
    };
    const shownStyle = {
        width: LineLegendStyle.width,
        height: "auto",
        minHeight: LineLegendStyle.height
    };

    return (
        <Box
            sx={{
                position: "absolute",
                top: position.top,
                right: position.right,
                zIndex: 1200,
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                justifyContent: "flex-start",
                ...(open ? shownStyle : hiddenStyle),
                backgroundColor: "rgba(255, 255, 255)",
                borderRadius: "4px",
                paddingY: open ? "8px" : "0",
                paddingX: open ? "12px" : "0"
                // boxShadow: "0 0 10px rgba(0, 0, 0, 0.5)",
                // transition: "all 0.5s"
            }}
        >
            <IconButton
                size={iconButtonStyle.size}
                sx={{
                    position: "absolute",
                    top: "0px",
                    right: `-36px`,
                    cursor: "pointer",
                    backgroundColor: "white",
                    border: "1px solid #89ACBB",
                    borderRadius: "4px",
                    height: iconButtonStyle.height,
                    width: iconButtonStyle.width,
                    "&:hover": {
                        backgroundColor: "white",
                        opacity: 1
                    }
                }}
                onClick={() => setOpen(!open)}
            >
                <LegendToggleIcon sx={{ color: "#336F89" }} />
            </IconButton>
            {open && (
                <Box sx={{ width: "100%" }}>
                    <Typography
                        variant="subtitle1"
                        textAlign={"left"}
                        sx={{
                            marginBottom: 0.5,
                            fontSize: "14px",
                            color: "#104860",
                            fontWeight: "600"
                        }}
                    >
                        <FormattedMessage
                            id="map.legend"
                            defaultMessage="線條圖例"
                        />
                    </Typography>
                    <Box>
                        {groupPallet.map((group, index) => {
                            return (
                                <Grid
                                    container
                                    key={index}
                                    justifyContent={"flex-start"}
                                >
                                    <Grid item xs={6}>
                                        <Typography
                                            variant="body2"
                                            sx={{ fontSize: "12px" }}
                                        >
                                            {group.person}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                        {/*  create svg line according to group?.style  */}
                                        <svg width={50} height={10}>
                                            <line
                                                x1="0"
                                                y1="0"
                                                x2="100"
                                                y2="0"
                                                style={{
                                                    ...basicOptions,
                                                    stroke: group?.style.color,
                                                    strokeWidth:
                                                        group?.style.weight * 2,
                                                    strokeDasharray:
                                                        group?.style.dashArray
                                                }}
                                            />
                                        </svg>
                                    </Grid>
                                </Grid>
                            );
                        })}
                    </Box>
                </Box>
            )}
        </Box>
    );
};

LineLegend.propTypes = {
    mapRef: PropTypes.object,
    groupPallet: PropTypes.arrayOf(PropTypes.object)
};

export default LineLegend;
