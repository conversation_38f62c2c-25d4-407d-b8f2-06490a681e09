import { handleNameSpellCheck } from "../../../common/codes";

/**
 * 顯示實體的名稱,有幾種狀況:
 * 1.id 與 name 一樣,則顯示 name
 * 2.id 與 name 不同,則顯示 id(常見名稱:name)
 * 3.沒有 name, 僅顯示 id
 *
 * @param id {string} 實體 id(移除 prefix)
 * @param name {string} query string 的 name (經過 decode)
 * @param locale {string} 語系
 * @returns {*|string}
 */
export const displayInstanceName = (id, name, locale) => {
    if (!name) return id;
    const label = locale.startsWith("zh") ? "常見名稱" : "Best Known Name";
    return id === handleNameSpellCheck(name || "")
        ? name
        : `${id} (${label}:${name})`;
};

/**
 * remove prefix form id
 *
 * @param {string} id. e.g. PER金庸
 * @param {string} prefix. e.g. PER
 * @returns {string} . e.g. 金庸
 */
export const removeStartPrefix = (id, prefix) => {
    if (id && id.startsWith(prefix)) {
        return id.replace(prefix, "");
    }
    return id;
};
