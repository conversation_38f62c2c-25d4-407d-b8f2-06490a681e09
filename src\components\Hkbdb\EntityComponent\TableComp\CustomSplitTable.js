import CustomAlertMessage from "./CustomAlertMessage";
import { Button, Dropdown, Input, Label, Segment } from "semantic-ui-react";
import { FormattedMessage } from "react-intl";
import ReactSelect, { createFilter } from "react-select";
import MenuList from "./MenuList";
import config from "../../../../config/config";
import React, { useContext, useEffect, useState } from "react";
import { StoreContext } from "../../../../store/StoreProvider";
import CustomDebounce from "../../../../common/components/CustomDeBounce";
import { bs64Encode, isEmpty } from "../../../../common/codes";
import { removeStartPrefix } from "../helper";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import { fetchOptionList } from "../commonAction";
import CreatableSelect from "react-select/creatable";
import { value } from "lodash/seq";

const TEXT_PLACEHOLDER = "請選擇";
const typeOptions = [
    {
        text: (
            <FormattedMessage
                id={"people.Information.label.person"}
                defaultMessage={"Person"}
            />
        ),
        value: config.DEF_PER_RANGE
    },
    {
        text: (
            <FormattedMessage
                id={"people.Information.split.label.organization"}
                defaultMessage={"Organization"}
            />
        ),
        value: config.DEF_ORG_RANGE
    }
];
//
const customPlusButtonStyle = {
    marginLeft: ".7em",
    marginTop: ".6em",
    marginBottom: ".6em",
    padding: ".5em"
};
//
const customDropDownStyle = {
    marginRight: ".8em",
    width: "20%"
};
//
const customStyles = {
    container: styles => ({
        ...styles,
        width: "100%"
    }),
    control: (styles, { selectProps: { controlColor } }) => ({
        ...styles,
        borderTopLeftRadius: "0",
        borderBottomLeftRadius: "0",
        borderLeftColor: "transparent",
        border: "1px solid rgba(34,36,38,.15)"
    })
};
//
const inputStyle = {
    marginTop: "1.5em",
    marginBottom: "1.5em"
};
const defaultValue = typeOptions[0].value;
//
const CustomSplitTable = ({
    id,
    // entitySetting,
    mergeState,
    setMergeState,
    // valYesNo,
    alertMsg,
    setAlertMsg
}) => {
    const [searchValue, setSearchValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSearchValue = CustomDebounce(searchValue, 500);
    // 預設值為 Person
    const [rowData, setRowData] = useState([{ type: defaultValue, value: [] }]);
    //
    const customPlacement = rowId => (rowId >= 4 ? "top" : "bottom");
    //
    useEffect(() => {
        setMergeState(prevState => ({
            ...prevState,
            data: rowData
        }));
    }, [rowData]);

    // 當搜尋內容改變的時候 rerender
    useEffect(() => {
        if (isEmpty(debSearchValue)) {
            return;
        }
        // open dropdown Loading
        setMergeState(prevState => ({
            ...prevState,
            isLoading: true
        }));

        // 找出目前 focus 哪一個 type 和 idx
        const tmpListType = rowData.find(row => row.focused)?.type || "Person";
        const focusedIdx = rowData.findIndex(row => row.focused) || 0;

        // 根據 Type 取出選單資料
        fetchOptionList(tmpListType, debSearchValue, 100, 30 * 1000)
            .then(res => {
                setMergeState(prevState => ({
                    ...prevState,
                    optionsForIdx: focusedIdx,
                    options: res.data
                        .map(o => ({
                            ...o,
                            _label: o.label,
                            label: `${removeStartPrefix(
                                o.value,
                                CLASS_PREFIX[tmpListType]
                            )} (常見名稱:${o.label})`
                        }))
                        .filter(
                            o => o.value !== `${CLASS_PREFIX[tmpListType]}${id}`
                        ),
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setMergeState(prevState => ({
                    ...prevState,
                    options: [],
                    isLoading: false
                }));
            });
    }, [debSearchValue]);
    //
    const handleChange = idx => async options => {
        // e.g. options = [ {label: "三水", value: "PLA三水"}, ... ]
        const rowValue = isEmpty(options) ? [] : options;
        // update dropdown value
        // setMergeState(prevState => ({ ...prevState, value: options }));

        setRowData(prev =>
            prev.map((row, i) => {
                if (i === idx) {
                    return {
                        ...row,
                        value: rowValue
                    };
                }
                return row;
            })
        );
    };
    //
    const handleTypeChange = idx => (e, { value }) => {
        setRowData(prev =>
            prev.map((row, i) => {
                if (i === idx) {
                    return {
                        ...row,
                        type: value,
                        // 在更改 type 的時候把 value 的值清空
                        value: []
                    };
                }
                return row;
            })
        );
    };
    //
    const handleInputChange = idx => value => {
        setRowData(prev =>
            prev.map((row, i) => {
                if (i === idx) {
                    return {
                        ...row,
                        focused: true
                    };
                }
                return {
                    ...row,
                    focused: false
                };
            })
        );
        setSearchValue(value);
    };
    //
    return (
        <Segment.Group>
            {rowData.map((dt, idx) => (
                <Segment key={idx.toString()}>
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    <Input
                        style={inputStyle}
                        fluid
                        labelPosition="left"
                        type="text"
                        loading={mergeState.isLoading}
                    >
                        <Dropdown
                            onChange={handleTypeChange(idx)}
                            fluid
                            selection
                            style={customDropDownStyle}
                            placeholder={TEXT_PLACEHOLDER}
                            options={typeOptions}
                            defaultValue={defaultValue}
                        />
                        <Label>
                            <FormattedMessage
                                id={"information.split.label"}
                                defaultMessage={`Name`}
                            />
                        </Label>
                        <CreatableSelect
                            isClearable
                            placeholder={
                                <FormattedMessage
                                    id={
                                        "people.Information.split.to.placeholder"
                                    }
                                    defaultMessage={`Please type the target name`}
                                />
                            }
                            isLoading={mergeState.isLoading}
                            options={
                                mergeState?.optionsForIdx === idx
                                    ? mergeState.options
                                    : []
                            }
                            value={dt.value}
                            styles={customStyles}
                            onChange={handleChange(idx)}
                            onInputChange={handleInputChange(idx)}
                            components={{ MenuList }}
                            menuPlacement={customPlacement()}
                            filterOption={createFilter({
                                ignoreAccents: false
                            })}
                        />
                        {idx === rowData.length - 1 && (
                            <Button
                                onClick={() => {
                                    setRowData(prev =>
                                        prev.concat([
                                            { type: defaultValue, value: [] }
                                        ])
                                    );
                                }}
                                circular
                                size="mini"
                                color="green"
                                icon="plus"
                                style={customPlusButtonStyle}
                            />
                        )}
                    </Input>
                </Segment>
            ))}
        </Segment.Group>
    );
};

export default CustomSplitTable;
