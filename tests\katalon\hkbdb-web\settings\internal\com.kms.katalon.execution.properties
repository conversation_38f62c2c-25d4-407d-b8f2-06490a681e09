execution.default.selectingCapturedObjectSelectorMethod="XPATH"
report.videoRecorderOption="{\\"enable\\"\:false,\\"useBrowserRecorder\\"\:true,\\"videoFormat\\"\:\\"AVI\\",\\"videoQuality\\"\:\\"LOW\\",\\"recordAllTestCases\\"\:false,\\"allowedRecordIfFailed\\"\:true,\\"allowedRecordIfPassed\\"\:false}"
execution.logTestSteps=true
execution.hideHostName=false
execution.default.timeout=30
execution.default.openReportAfterExecuting=false
execution.default.quitDriversAfterExecutingTestCase=false
execution.default.quitDriversAfterExecutingTestSuite=true
report.takeScreenshotOption=true
report.takeScreenshotSettings="{\\"enable\\"\:true}"
report.videoRecorderSettings="{\\"enable\\"\:true,\\"useBrowserRecorder\\"\:false,\\"videoFormat\\"\:\\"AVI\\",\\"videoQuality\\"\:\\"MEDIUM\\",\\"recordAllTestCases\\"\:true}"
