import queryString from "query-string";
import base64url from "base64url";
import axios from "axios";

let baseUrl = "https://api.daoyidh.com/hkbdb/zh-hans/hkvayb/search/";
let keywordStr = "/1.0?limit=-1&offset=0&keyword=";

const excEncodeQueryParam = ["limit", "offset"];

const encodeQueryStr = queryStr => {
    // queryString will parse '+' as a space. So, we have to set {decode: false}.
    // https://github.com/sindresorhus/query-string/issues/305
    const queryObj = queryString.parse(queryStr, { decode: false });

    let encodeQueryStr = [];
    Object.keys(queryObj).forEach(qo => {
        let val = queryObj[qo];
        if (excEncodeQueryParam.indexOf(qo) < 0) {
            val = base64url.encode(val);
            queryObj[qo] = val;
        }
        encodeQueryStr.push(`${qo}=${val}`);
    });
    return encodeQueryStr.join("&");
};

export const encodeUrl = apiStr => {
    const preApiStr = apiStr.split("?")[0];
    const queryStr = apiStr.split("?")[1];

    if (!queryStr) return apiStr;

    const queryStrEncode = encodeQueryStr(queryStr);

    return `${preApiStr}?${queryStrEncode}`;
};

async function promiseList(topicArr = [], keyword = "") {
    // multiple promise return at one time
    // keyword = "";
    // topicArr = ["essay", "publicIssue", "auction"];
    try {
        let searchTopic = topicArr.map(topic => {
            // 儲存所有API url的array
            return baseUrl + topic + keywordStr + keyword;
        });
        const promises = searchTopic.map((topicUrl, index) => {
            return new Promise((resolve, reject) => {
                let encodeTopicUrl = encodeUrl(topicUrl);
                // resolve(fetch(encodeTopicUrl).then((data) => {return data.json()}));
                resolve(
                    axios.get(encodeTopicUrl).then(data => {
                        return data;
                    })
                );
            });
        });
        let result = await Promise.all(promises);

        let resultData = result.reduce((tempArr, item, index) => {
            return [...tempArr, { [topicArr[index]]: item }];
        }, []);

        return resultData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
}

export { promiseList };
