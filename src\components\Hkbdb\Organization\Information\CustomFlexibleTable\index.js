import React, { useCallback, useContext } from "react";

// ui
import { Segment } from "semantic-ui-react";
import { injectIntl } from "react-intl";
// common
import { isEmpty, safeGet } from "../../../../../common/codes";

// custom
import CustomTable from "./CustomTableFlex";
import CustomMessage from "../../../EntityComponent/TableComp/CustomMessage";
import CustomLoading from "../../../EntityComponent/TableComp/CustomLoading";
import CustomCreateModal from "../../../EntityComponent/TableComp/CustomCreateModalFlex";
import CustomSearchInput from "../../../EntityComponent/TableComp/CustomSearchInput";

import { StoreContext } from "../../../../../store/StoreProvider";
import allRoles from "../../../../../App-role";
import CusSugModal from "../../../EntityComponent/TableComp/SugCreateModal";

const index = ({ info, ontologyDomain, ontologyType, intl }) => {
    //
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { searchInputs } = state.information;
    const keyword = safeGet(searchInputs, [ontologyType], "");
    const dataType = keyword ? "infoSearchData" : "infoData";
    const pageType = keyword ? "infoSearchPage" : "infoPage";
    const loadType = keyword ? "infoSearchLoad" : "infoLoad";
    //
    const data = safeGet(info, [dataType, ontologyType], []);
    const page = safeGet(info, [pageType, ontologyType], []);
    const load = safeGet(info, [loadType, ontologyType], false);
    //
    const MemoLoading = useCallback(() => <CustomLoading />, []);
    //
    const MemoNoDataMsg = useCallback(
        () => (
            <CustomMessage
                header={intl.formatMessage({
                    id: "customMsg.noData.title",
                    defaultMessage: `The current result is no data.`
                })}
            />
        ),
        []
    );
    //
    const MemoTable = useCallback(
        () => (
            <CustomTable
                data={data}
                page={page}
                ontologyDomain={ontologyDomain}
                ontologyType={ontologyType}
            />
        ),
        [JSON.stringify(data)]
    );
    //
    const MemoModal = useCallback(
        () =>
            !load &&
            (user.role === allRoles.suggester ? (
                <CusSugModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            ) : (
                <CustomCreateModal
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            )),
        [load]
    );

    const ShowInfo = ({ component: Component, ...rest }) => {
        return (
            <Segment>
                <Segment size={"big"} basic>
                    <CustomSearchInput ontologyType={ontologyType} />
                    {/* show content */}
                    <Component {...rest} />
                    {/* create modal */}
                    <MemoModal />
                </Segment>
            </Segment>
        );
    };
    //
    if (load) {
        return <ShowInfo component={MemoLoading} />;
    }
    //
    else if (isEmpty(data)) {
        return <ShowInfo component={MemoNoDataMsg} />;
    }
    //
    else {
        return <ShowInfo component={MemoTable} />;
    }
};

export default injectIntl(index);
