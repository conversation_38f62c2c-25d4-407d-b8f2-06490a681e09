import { isEmpty } from "../../../../common/codes";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import { decodeURIComponentSafe } from "../../../../common/codes/jenaHelper";
import _ from "lodash";

/**
 * 使用於處理 step1 及 step2 query 各自取得的 sna data
 *
 * @param SNAData {*[]}
 * @param centerName {string} 中心點的 label
 * @param centerId {string} 中心點的 id
 * @param step {number|string} 1 or 2
 * @returns {{nodes: [], orgList: [], links: []}}
 */
export const processSNADataSlim = (SNAData, centerName, centerId, step = 1) => {
    const snaData = {
        nodes: [],
        links: [],
        orgList: []
    };
    const isEmpty = obj => Object.keys(obj || {}).length === 0;
    const checkNodeList = {};
    const checkLinkList = {};

    const TYPE_COLOR_SYMBOL = {
        center: {
            color: "red",
            symbolType: "circle",
            type: "center"
        },
        per: {
            color: "green",
            symbolType: "circle",
            // type: "PER",
            type: CLASS_PREFIX.Person
        },
        org: {
            color: "black",
            symbolType: "square",
            // type: "ORG",
            type: CLASS_PREFIX.Organization
        }
    };

    const createNode = (type, name, id) => {
        return {
            ...Object.assign({}, TYPE_COLOR_SYMBOL[type]),
            // id: name || "",
            // 注意用來當作 <svg> 中的各種元素的 id 之合法字元包含A-Z、a-z、0-9、_、中文字
            // 不可以使用 %[]+= 等符號
            // 透過 decodeURIComponent 置換不合法字元
            id: decodeURIComponentSafe(id || ""),
            _id: id,
            label: name
        };
    };
    // 不管是 step 1 或 step 2，都要把 target node 放進去，標記 red color
    checkLinkList[centerId] = Object.assign({});
    snaData.nodes.push(createNode("center", centerName, centerId));
    checkNodeList[centerId] = true;

    // dstId: "ORG香港中文大學"
    // dstName: "香港中文大學"
    // dstType: "org"
    // relation: "Educated At"
    // srcId: "PER古蒼梧"
    // srcName: "古蒼梧"
    // srcType: "per"

    SNAData.forEach(sd => {
        // check srcName
        if (!checkNodeList[sd.srcId] && sd.srcType && sd.srcName) {
            snaData.nodes.push(createNode(sd.srcType, sd.srcName, sd.srcId));
            checkNodeList[sd.srcId] = true;
            // if (sd.srcType === "org") snaData.orgList.push(sd.srcName);
            if (sd.srcType === "org")
                snaData.orgList.push(decodeURIComponentSafe(sd.srcId));
        }
        // check dstName
        if (!checkNodeList[sd.dstId] && sd.dstType && sd.dstName) {
            snaData.nodes.push(createNode(sd.dstType, sd.dstName, sd.dstId));
            checkNodeList[sd.dstId] = true;
            // if (sd.dstType === "org") snaData.orgList.push(sd.dstName);
            if (sd.dstType === "org")
                snaData.orgList.push(decodeURIComponentSafe(sd.dstId));
        }

        // add link
        if (sd.relation && sd.srcId && sd.dstId) {
            snaData.links.push({
                label: sd.relation || "",
                source: decodeURIComponentSafe(sd.srcId),
                target: decodeURIComponentSafe(sd.dstId)
            });
        }
        if (!checkLinkList[`${sd.srcId}-${sd.dstId}`]) {
            checkLinkList[`${sd.srcId}-${sd.dstId}`] = true;
        }
    });
    // 若 link 的 source 與 target 相同時, 將 link label 串接起來
    snaData.links = Object.values(
        snaData.links.reduce((acc, cur) => {
            const find = acc[`${cur.source}-${cur.target}`];
            if (find) {
                acc[`${cur.source}-${cur.target}`] = {
                    ...find,
                    // label: `${find.label}, ${cur.label}`
                    label: `${find.label}, ${cur.label}`
                };
            } else {
                acc[`${cur.source}-${cur.target}`] = {
                    ...cur
                };
            }
            return acc;
        }, {})
    );
    return snaData;
};

/**
 * check SNA data 是否具備該有的 keys
 *
 * @param snaData
 * @returns {boolean}
 */
export const checkProperty = snaData => {
    if (isEmpty(snaData)) return false;
    const keys = Object.keys(snaData);
    const keyRequired = ["nodes", "links", "orgList"];
    let keyEnough = true;
    keyRequired.forEach(kr => {
        if (keys.indexOf(kr) < 0) keyEnough = false;
    });
    return keyEnough;
};

/**
 * 結合原本的 sna data 及 新的 sna data
 * 可擴展至 step3, 但須修改程式碼為 3 個參數
 * sna data like:
 * {
 *   nodes: [],
 *   links: []
 * }
 *
 * @param originSnaData
 * @param newSnaData
 * @returns {{nodes: *, links: *}}
 */
export const combineSnaData = (originSnaData, newSnaData) => {
    // fixme: 重複的 node, link, 要過濾掉, 不然會造成 react 相同的 key
    if (!checkProperty(originSnaData) && !checkProperty(newSnaData))
        return {
            nodes: [],
            links: [],
            orgList: []
        };
    if (checkProperty(originSnaData) && !checkProperty(newSnaData)) {
        return originSnaData;
    }
    if (!checkProperty(originSnaData) && checkProperty(newSnaData)) {
        return newSnaData;
    }
    return {
        nodes: originSnaData.nodes.concat(newSnaData.nodes),
        links: originSnaData.links.concat(newSnaData.links),
        orgList: originSnaData.orgList.concat(newSnaData.orgList)
    };
};

/**
 * use property value to find property key in propertyObj
 *
 * @param property
 * @param propertyObj
 * @returns {string|null}
 */
export const findPropertyKey = (property, propertyObj) => {
    let _propertyKey = "";
    const langKeys = Object.keys(propertyObj);
    langKeys.forEach(lk => {
        Object.keys(propertyObj[lk]).forEach(po => {
            if (po === property) {
                _propertyKey = po;
            }
            if (propertyObj[lk][po] === property) {
                _propertyKey = po;
            }
        });
    });
    return _propertyKey !== "" ? _propertyKey : null;
};

const LOCALE_LIST = {
    en: {
        name: "en",
        list: ["en"]
    },
    zh: {
        name: "zh",
        list: ["zh", "zh-hant"]
    }
};

/**
 * 將 property 轉換成 目前語系的 property
 *
 * @param property => 傳進來的 property 可能是 zh, en. 為 property 使用 "," 串接
 * @param propertyObj =>
 * {en: {propertyKey: propertyName, ...}, zh: {{propertyKey: propertyName, ...}}}
 * @param locale
 * @returns {*}
 */
export const cvtPropertyOnLocale = (property, propertyObj, locale) => {
    const _propertyObj = _.cloneDeep(propertyObj);
    if (!(property && _propertyObj && _propertyObj.en && _propertyObj.zh))
        return property;
    const lang =
        locale && locale.startsWith(LOCALE_LIST.zh.name)
            ? LOCALE_LIST.zh.name
            : LOCALE_LIST.en.name;

    const propList = (property || "").split(",").map(st => st.trim());
    const propLabelList = propList.map(prop => {
        // use property(zh, en) to find property en name
        // 若未在 protege 建立的 property 則找不到對應的 propertyKey
        const propertyKey = findPropertyKey(prop, _propertyObj);

        if (!propertyKey) {
            return prop;
        }
        // return property;
        return _propertyObj[lang] && _propertyObj[lang][propertyKey]
            ? _propertyObj[lang][propertyKey]
            : prop;
    });
    return Array.from(new Set(propLabelList)).join(", ");
};

/**
 * 依據 locale 轉換 SNA data
 *
 * @param data: SNA data
 * [
 * {dstId: "ORG香港中文大學",
    dstName: "香港中文大學",
    dstType: "org",
    relation: "hasEducatedAt",
    srcId: "PER古蒼梧",
    srcName: "古蒼梧",
    srcType: "per"}
 * ]
 *
 * @param propertyObj
 * @param locale
 * @returns {*}
 */
export const cvtSNAdataByLocale = (data, propertyObj, locale) => {
    // eslint-disable-next-line no-undef
    const _propertyObj = _.cloneDeep(propertyObj);
    if (!(data && Array.isArray(data) && data.length > 0)) return data;
    if (!(_propertyObj && _propertyObj.en && _propertyObj.zh)) return data;

    data.forEach((dt, idx) => {
        if (isEmpty(dt)) return;
        const keys = Object.keys(dt);
        keys.forEach(key => {
            const property = cvtPropertyOnLocale(dt[key], _propertyObj, locale);
            if (property) {
                data[idx][key] = property;
            }
        });
    });

    return data;
};
