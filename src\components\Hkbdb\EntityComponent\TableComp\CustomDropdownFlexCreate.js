import React, { useState, useEffect, useContext } from "react";

// ui
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";

// ui
import { Label, Input, Icon } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomDebounce from "./CustomDeBounce";
import MenuList from "./MenuList";
import MenuListFooter from "./MenuListFooter";

// common
import {
    bs64Decode,
    isEmpty,
    isNotEmpty,
    safeGet
} from "../../../../common/codes";

// api
import { Api, doRestCreate, readHkbdbData } from "../../../../api/hkbdb/Api";
import { fetchOptionList, getNextNameId } from "../commonAction";

// nameId, nameNode
import { createNameNode } from "../nameNodeLib";

// config
import { AUDA_HKLIT_GRAPH } from "../../../../config/config-ontology";

// helper
import {
    bs64EncodeId,
    doCreateNNI,
    doUriEncode
} from "../../../../common/codes/jenaHelper";
import { StoreContext } from "../../../../store/StoreProvider";
import allRoles from "../../../../App-role";

const CustomDropdown = ({
    property,
    createData,
    setCreateData,
    createGraphData
}) => {
    const [state] = useContext(StoreContext);
    const { user } = state;
    //
    const [searchValue, setSearchValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSearchValue = CustomDebounce(searchValue, 1000);
    //
    const { ontologyType, ontologyDefined, willCreatedData } = createData;
    const { range, value: propertyBindRangeStr, label, required } = property;
    //
    const [optionState, setOptionState] = useState(() => {
        return {
            isLoading: false,
            isAddDefaultAuthor: false,
            options: [],
            value: [],
            addTimes: 1,
            limit: 10
        };
    });
    //
    const customStyles = {
        container: styles => ({
            ...styles,
            width: "100%"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            borderTopLeftRadius: "0",
            borderBottomLeftRadius: "0",
            borderLeftColor: "transparent",
            border: "1px solid rgba(34,36,38,.15)"
            // borderRadius: "unset"
        })
    };
    //
    const handleInputChange = value => {
        setSearchValue(value);
    };
    //
    const handleChange = async options => {
        //
        options = isEmpty(options) ? [] : options;
        //
        setOptionState(prevState => ({ ...prevState, value: options }));
        //
        const joinedValue = options.map(_option => _option.value);

        // 選擇已存在的 Publication 進行新增，
        // 則 srcId 必須換為 Publication 的 id, label 為書名
        // Article, OtherWork 亦同
        const classArr = ["publication", "article", "otherwork"];
        //
        if (
            classArr.indexOf(ontologyType) >= 0 &&
            propertyBindRangeStr === "label"
        ) {
            // update selected value
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    // srcId: bs64EncodeId(joinedValue[0]),
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyBindRangeStr]: options.map(
                            _option => _option.label
                        )
                    }
                }
            }));
            return;
        }

        // update selected value
        setCreateData(prevData => ({
            ...prevData,
            willCreatedData: {
                ...prevData.willCreatedData,
                classType: ontologyType,
                // srcId: personId,
                value: {
                    ...prevData.willCreatedData.value,
                    [propertyBindRangeStr]: joinedValue
                }
            }
        }));
    };
    //
    const handleCreate = async inputValue => {
        //
        let objValue = {};
        //
        const foundClassName = Object.keys(ontologyDefined).find(
            key =>
                `${key}`.toLowerCase().indexOf(`${range}`.toLowerCase()) !== -1
        );
        //
        let srcId;
        //
        if (isNotEmpty(foundClassName)) {
            //
            const targetData = safeGet(ontologyDefined, [foundClassName], []);
            //
            const foundLabel = targetData.find(
                item => item.property === "label"
            );
            //
            const foundBestknownName = targetData.find(
                item => item.property === "bestKnownName"
            );
            //
            if (isNotEmpty(foundLabel)) {
                objValue = {
                    ...objValue,
                    [`label_${range}`]: [inputValue]
                };
            }
            //
            if (isNotEmpty(foundBestknownName)) {
                // 若要建立 bestKnowName 時, 會順便建立 nameId 及 nameNode
                objValue = {
                    ...objValue,
                    bestKnownName: [inputValue]
                };
                //
                const createNNIObj = doCreateNNI(range);
                const { eventType, prefix } = createNNIObj || {};
                if (eventType && prefix) {
                    const nextNameId = await getNextNameId(eventType);
                    srcId = bs64EncodeId(`${prefix}${inputValue}`);
                    if (nextNameId) {
                        // create nameNode
                        const addNameNodeRes = await createNameNode(
                            srcId,
                            nextNameId,
                            AUDA_HKLIT_GRAPH,
                            inputValue,
                            user
                        );
                        objValue = {
                            ...objValue,
                            nameId: nextNameId
                            // nameNode id 已經在 createNameNode() fetch API 後自動產生並建立 person 與 nameNode 的連結
                            // 這邊就不用再帶 hasNameId__NameNode
                            // hasNameId__NameNode: createNameNodeId(nextNameId)
                        };
                    }
                }
            }
            // label 或 bestKnownName 都不存在則預設使用 label
            if (isEmpty(foundLabel) && isEmpty(foundBestknownName)) {
                objValue = {
                    ...objValue,
                    [`label_${range}`]: [inputValue]
                };
            }
        } else {
            // 連類別都找不到的話，預設就用 label
            objValue = {
                ...objValue,
                [`label_${range}`]: [inputValue]
            };
        }
        //
        const doUriEncodeObj = doUriEncode(range);
        const { eventType, prefix } = doUriEncodeObj || {};
        if (eventType && prefix) {
            srcId = bs64EncodeId(`${prefix}${inputValue}`);
        }
        //
        let createdData = {
            graph: AUDA_HKLIT_GRAPH,
            srcId: srcId || "not_needed",
            classType: range,
            value: objValue
        };
        //
        if (!isEmpty(createdData)) {
            // open isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: true
            }));
            // call create api
            const result = await doRestCreate(user, createdData);
            //
            // console.log(result);
            if (result.state) {
                //
                const apiStr = Api.findIdByValue()
                    .replace("{class}", range)
                    .replace("{keyword}", inputValue);
                //
                const result = await readHkbdbData(apiStr);
                //
                const objId = safeGet(result, ["data", 0, "value"], "");
                //
                if (isNotEmpty(objId)) {
                    // add new option
                    const newOption = {
                        label: inputValue,
                        value: objId
                    };
                    // update editData state
                    setCreateData(prevData => ({
                        ...prevData,
                        willCreatedData: {
                            ...prevData.willCreatedData,
                            classType: ontologyType,
                            // srcId: bs64EncodeId(joinedValue[0]),
                            value: {
                                ...prevData.willCreatedData.value,
                                [propertyBindRangeStr]: [
                                    ...safeGet(
                                        prevData.willCreatedData,
                                        ["value", propertyBindRangeStr],
                                        []
                                    ),
                                    objId
                                ]
                            }
                        }
                    }));
                    // update state
                    setOptionState(prevCreateState => ({
                        ...prevCreateState,
                        options: [newOption, ...prevCreateState.options],
                        value: [newOption, ...prevCreateState.value]
                    }));
                } else {
                    console.log("handleCreate failed to add item:", inputValue);
                }
            }
            // close isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: false
            }));
        }
        // console.log(createdData);
    };
    //
    const addOption = () => {
        //
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(range, debSearchValue, limitValue, 30 * 1000)
            .then(res => {
                //
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debSearchValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debSearchValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                //
                setOptionState(prevState => ({
                    ...prevState,
                    options: newData,
                    addTimes: prevState.addTimes + 1,
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    isLoading: false
                }));
            });
    };
    //
    useEffect(() => {
        //
        if (isEmpty(range)) {
            return;
        }
        // open dropdown Loading
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(range, debSearchValue, limitValue, 30 * 1000)
            .then(res => {
                //
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debSearchValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debSearchValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                //
                const srcId = safeGet(
                    createData,
                    ["willCreatedData", "srcId"],
                    ""
                );
                let defaultData;
                const isInOneOfThem =
                    safeGet(createData, ["ontologyOneOfThemData"], []).indexOf(
                        property?.property
                    ) !== -1;
                if (isInOneOfThem) {
                    if (srcId) {
                        const _prefix = srcId.slice(0, 3);
                        const _perName = bs64Decode(srcId.slice(3));
                        const _label = `${decodeURI(_perName)}(default)`;
                        const _value = `${_prefix}${_perName}`;
                        defaultData = {
                            label: _label,
                            value: _value
                        };
                    } else {
                        defaultData = {};
                    }
                } else {
                    defaultData = {};
                }
                //
                if (!optionState.isAddDefaultAuthor && defaultData?.value) {
                    // update options of dropdown
                    setOptionState(prevState => ({
                        ...prevState,
                        value: [defaultData, ...prevState.value],
                        options: [defaultData, ...newData],
                        addTimes: prevState.addTimes + 1,
                        isLoading: false,
                        isAddDefaultAuthor: true
                    }));
                    // update willCreateData to change the default option of dropdown
                    setCreateData(prevData => ({
                        ...prevData,
                        willCreatedData: {
                            ...prevData.willCreatedData,
                            classType: ontologyType,
                            // srcId: bs64EncodeId(joinedValue[0]),
                            value: {
                                ...prevData.willCreatedData.value,
                                [propertyBindRangeStr]: [defaultData.value]
                            }
                        }
                    }));
                } else {
                    // update options of dropdown
                    setOptionState(prevState => ({
                        ...prevState,
                        options: newData,
                        addTimes: prevState.addTimes + 1,
                        isLoading: false
                    }));
                }
            })
            .catch(() => {
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    options: [],
                    isLoading: false
                }));
            });
    }, [debSearchValue, range]);
    //
    const customPlacement = rowId => (rowId >= 4 ? "top" : "bottom");
    //
    return (
        <Input fluid labelPosition="left" type="text" onChange={handleChange}>
            <Label>
                {label}
                {required && (
                    <Icon
                        style={{ marginLeft: "1.5em" }}
                        color="red"
                        name="asterisk"
                        size="mini"
                    />
                )}
            </Label>
            <CreatableSelect
                isMulti
                isClearable
                styles={customStyles}
                isLoading={optionState.isLoading}
                options={optionState.options}
                value={optionState.value}
                onChange={handleChange}
                onInputChange={handleInputChange}
                onCreateOption={handleCreate}
                components={{
                    MenuList,
                    MenuListFooter: (
                        <MenuListFooter
                            options={optionState.options}
                            onClick={addOption}
                        />
                    )
                }}
                getOptionLabel={option => {
                    const { label } = option || {};
                    return label.length > 130
                        ? label.slice(0, 130) + "..."
                        : label;
                }}
                placeholder={
                    <FormattedMessage
                        id={"people.Information.dropDown.placeholder"}
                        defaultMessage={"Select..."}
                    />
                }
                menuPlacement={customPlacement()}
                filterOption={createFilter({ ignoreAccents: false })}
            />
        </Input>
    );
};

export default CustomDropdown;
