import React, { useContext, useEffect, useMemo, useState } from "react";
import { v4 } from "uuid";

// ui
import { Label, Popup, Table } from "semantic-ui-react";
import CircularProgress from "@mui/material/CircularProgress";

// route
import { Link } from "react-router-dom";

// custom
import CustomDate from "../../../EntityComponent/TableComp/CustomDate";
import CustomPagination from "../../../EntityComponent/TableComp/CustomPagination";
import CustomContentMenu from "../../../EntityComponent/TableComp/CustomContextMenuFlex";
import CustomMessage from "../../../EntityComponent/TableComp/CustomMessage";
import CustomIsLoading from "../../../EntityComponent/TableComp/CustomIsLoading";
import CustomImageModal from "../../../EntityComponent/TableComp/CustomImageModalDimmer";
import SugFlexTableBody from "../../../EntityComponent/TableComp/SugComponents/SugFlexTableBody";

// common
import {
    safeGet,
    isEmpty,
    isNumeric,
    isNotEmpty,
    getProperty,
    cvtDatasetLocale,
    convertDateEventToFormalDate
} from "../../../../../common/codes";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// global
import { GV } from "../../../../../common/codes/globalVars";
import { mergeArrayByKeys } from "../../../../../common/codes/mergeArray";
import { sortedByStrokeAdv, ORDER } from "twchar";
import authority from "../../../../../App-authority";
import ROLE from "../../../../../App-role";
import {
    convertNormalColHeaderName,
    convertSugTableHeader
} from "../../../common/utils/convertSugOptions";

const SortHeaderNumber = 2;
const sortLen = 12;

const customDivStyle = {
    maxHeight: "350px",
    overflow: "auto",
    borderTop: "1px solid rgba(34,36,38,.1)",
    borderBottom: "1px solid rgba(34,36,38,.1)",
    borderRadius: ".28571429rem .28571429rem .28571429rem .28571429rem"
};
const customEditMenuStyle = {
    width: "48px"
};
const fixTableHeaderStyle = {
    position: "sticky",
    top: "0",
    zIndex: "998"
};
const customTableCellStyle = {
    minWidth: "150px"
};
const tableStyle = {
    borderTop: "unset",
    borderBottom: "unset"
};
const fixColumnWidthStyle = {
    whiteSpace: "nowrap",
    maxWidth: "250px",
    overflow: "hidden",
    textOverflow: "ellipsis",
    margin: "0 auto"
};

const CustomTableFlex = ({ data, page, ontologyDomain, ontologyType }) => {
    const [state] = useContext(StoreContext);
    const {
        user,
        setting,
        property,
        source,
        information,
        personInformation
    } = state;
    const { role } = user;
    const { fieldSetting } = setting;
    const { attribute: fieldAttr, property: fieldProp } = fieldSetting;
    const { ontologyDefined } = property;
    const { dataset: globalDataset } = source;
    const { isLoading, searchInputs, paginations } = information;
    const {
        nameNode,
        headers: head,
        suggestInfo,
        tempUpdateSuggestInfo
    } = personInformation;
    const { globalMaxRows } = paginations;

    const localActivePage = safeGet(
        paginations,
        [ontologyType, "localActivePage"],
        1
    );

    const searchActivePage = safeGet(
        paginations,
        [ontologyType, "searchActivePage"],
        1
    );

    const searchKeyword = safeGet(searchInputs, [ontologyType], "");
    const fieldAttrRecords = safeGet(
        fieldAttr,
        [ontologyDomain, ontologyType],
        []
    );
    const fieldPropSortedRecords = safeGet(
        fieldProp?.sortedRecords,
        [ontologyDomain, ontologyType],
        []
    );

    const initData = () => {
        const slicedData = data.slice(0).sort((a, b) => {
            // 抓出帶有 DataEvent 的 header
            // firstDateProp: hasStartDate__DateEvent
            let firstDateProp = Object.keys(a).find(prop => {
                // prop:: hasEmployedAt__Organization
                prop.toLowerCase().includes("DateEvent".toLowerCase());
            });
            if (isEmpty(firstDateProp)) {
                firstDateProp = Object.keys(b).find(prop =>
                    prop.toLowerCase().includes("DateEvent".toLowerCase())
                );
            }
            const tmpA = safeGet(a, [firstDateProp, 0, "label"], "").slice(
                0,
                4
            );
            const tmpB = safeGet(b, [firstDateProp, 0, "label"], "").slice(
                0,
                4
            );

            let dateA = isNumeric(tmpA) ? parseInt(tmpA) : 0;
            let dateB = isNumeric(tmpB) ? parseInt(tmpB) : 0;

            return dateB - dateA;
        });

        const keyToMerged = "graph";
        const mergedKey = "graphIds";
        const mergedByKey = mergeArrayByKeys(
            slicedData,
            [keyToMerged],
            mergedKey
        );

        // 把 merged 的值, 覆蓋原本的值
        const merged = mergedByKey.map(itm => {
            itm[keyToMerged] = itm[mergedKey].map(m => m[keyToMerged]);
            delete itm[mergedKey];
            return itm;
        });

        return merged;
    };

    const [newData, setNewData] = useState(initData);
    const [headers, setHeaders] = useState(() => {
        // reduce all key...
        const originArr = [
            ...new Set(
                newData.reduce((keys, item) => {
                    Object.keys(item).forEach(key => {
                        keys.push(key);
                    });
                    return keys;
                }, [])
            )
        ];

        const fieldAttrRecordsKeys = Object.keys(fieldAttrRecords);
        // sort...
        const mergedArr = [
            // 依照 fieldPropSortedRecords 做排序
            ...new Set([
                ...fieldPropSortedRecords,
                ...fieldPropSortedRecords.filter(prop =>
                    originArr.includes(prop)
                ),
                ...fieldAttrRecordsKeys,
                ...originArr
            ])
        ];

        if (isEmpty(fieldAttrRecordsKeys)) return [];
        // filter...
        const newHeader = mergedArr
            .filter(prop => !(prop || "").startsWith(GV.SYSTEM_PREFIX))
            .filter(prop => {
                if (prop === "graph") return true;
                const _roles = safeGet(fieldAttrRecords, [prop, "roles"], []);
                return _roles.includes(role);
            });
        if (!newHeader.includes("graph")) {
            newHeader.push("graph");
        }
        return newHeader;
    });

    if (isEmpty(headers)) {
        return <CustomMessage header="目前結果為無資料。" />;
    }

    const [dupHeaders, setDupHeaders] = useState(() => {
        const dupHeaderObj = headers.reduce((dictObj, header) => {
            const [oriHeaderName, headerRange] = header.split("__");
            dictObj[oriHeaderName]
                ? dictObj[oriHeaderName]++
                : (dictObj[oriHeaderName] = 1);
            return dictObj;
        }, {});
        return Object.keys(dupHeaderObj).reduce((dupArr, oriHeaderName) => {
            const count = dupHeaderObj[oriHeaderName];
            if (count > 1) {
                return [...dupArr, oriHeaderName];
            }
            return dupArr;
        }, []);
    });

    const safeGetProperty = _property => {
        if (isEmpty(_property)) {
            return "";
        }
        return isEmpty(property?.propertyObj)
            ? _property
            : getProperty(_property, property.propertyObj);
    };

    const popupLongString = (newValue, width = 18) => {
        return (
            <Popup
                content={newValue}
                trigger={
                    <div
                        style={{
                            ...fixColumnWidthStyle,
                            ...{ maxWidth: `${width}rem` }
                        }}
                    >
                        {newValue}
                    </div>
                }
                hoverable
                position="top center"
            />
        );
    };

    const isAllowedDisplay = (headers, rowData) => {
        const acc = headers.reduce((prevObj, header) => {
            if (`${header}`.toLowerCase() === "graph") return prevObj;
            if (isNotEmpty(rowData[header])) return prevObj + 1;
            return prevObj;
        }, 0);
        return acc >= 1;
    };

    const handleShowHeader = (header, dupHeaders) => {
        const headerArr = header.split("__");
        const [oriHeader, headerRange] = headerArr;

        if (dupHeaders.includes(oriHeader)) {
            return `${safeGetProperty(oriHeader)}(${headerRange})`;
        }

        return safeGetProperty(oriHeader);
    };

    // 取得排序後的array
    const sortArray = (sortHeader, data) => {
        let localHeader;
        // namenode 為特例，需另外處理排序
        if (ontologyType === "namenode") {
            localHeader = [
                "nnBestKnownName__string",
                "originalName__string",
                "zi__string",
                "hao__string",
                "penName__string",
                "name__string"
            ];
        } else {
            // 抓取要排序的欄位
            localHeader = sortHeader.slice(0, SortHeaderNumber);
        }
        // 取出type
        const HeaderType = [];
        localHeader.forEach(header => {
            const headerRange = header.split("__")[1];
            HeaderType.push(headerRange);
        });

        // 回傳排序後陣列
        return sortedByStrokeAdv({
            inputArr: data,
            keyArr: localHeader,
            getValFunc: safeGet,
            sortLen: sortLen,
            order: HeaderType.map(n =>
                n === "DateEvent" ? ORDER.ASC : ORDER.DES
            )
        });
    };

    const handleShowValue = (header, rowData) => {
        let value;
        const item = safeGet(rowData, [header], []);
        if (Array.isArray(item)) {
            value = item
                .sort((a, b) => {
                    let nameA = safeGet(a, ["label"], "").toUpperCase();
                    let nameB = safeGet(b, ["label"], "").toUpperCase();
                    if (nameA < nameB) {
                        return -1;
                    }
                    if (nameA > nameB) {
                        return 1;
                    }
                    return 0;
                })
                .reduce((prevObj, item) => {
                    const { label } = item;
                    if (isNotEmpty(label)) {
                        return [prevObj, label]
                            .filter(label => isNotEmpty(label))
                            .join(", ");
                    }
                    return prevObj;
                }, "");
        } else {
            value = item;
        }

        if (isEmpty(value)) {
            const [oriHeader, headerRange] = header.split("__");
            value = rowData[oriHeader];
        }

        const headerArr = header.split("__");
        const oriHeader = headerArr[0];
        // 轉換日期為易讀模式
        const newValue = convertDateEventToFormalDate(
            value,
            oriHeader,
            ontologyDefined[ontologyType]
        );
        if (isEmpty(newValue)) return value;
        switch (oriHeader) {
            case "graph":
                return newValue.map(val => (
                    <Label color="orange" key={v4()}>
                        {cvtDatasetLocale(val, globalDataset)}
                    </Label>
                ));
            case "IMGportrait":
                return (
                    <CustomImageModal
                        key={`image-modal-${newValue}`}
                        imgUrl={newValue}
                    />
                );
        }

        if (oriHeader === "label_article") {
            const publishedInStr = safeGet(
                rowData,
                ["hasPublishedIn__Publication", 0, "label"],
                ""
            );
            const basicUrl = "https://hklit.lib.cuhk.edu.hk/explore/#/search";
            const model = "advanced";
            const publishYear = "%5B1891%2C2020%5D";
            const publishedIn = publishedInStr
                .replace("《", "")
                .replace("》", "");
            const externalLink = `${basicUrl}/?mode=${model}&publish_year_=${publishYear}&title="${newValue}"&source="${publishedIn}"`;
            return (
                <Link
                    to={{ pathname: externalLink }}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    {newValue}
                </Link>
            );
        }
        // width
        const attrSetting = fieldAttrRecords[header];
        if (isNotEmpty(attrSetting)) {
            if (attrSetting.hasOwnProperty("width")) {
                return popupLongString(newValue, attrSetting.width);
            }
        }
        // date
        const isDateEvent =
            safeGet(ontologyDefined, [ontologyType], []).find(
                item => item?.property === oriHeader
            ).range === "DateEvent";
        if (isDateEvent) {
            return <CustomDate value={newValue} format={"yyyy"} />;
        }

        return newValue;
    };

    const TableHeader = (
        <Table.Row textAlign="center">
            <Table.HeaderCell
                style={{
                    ...fixTableHeaderStyle,
                    ...customEditMenuStyle
                }}
            >
                {" "}
            </Table.HeaderCell>
            {user.role === ROLE.suggester
                ? Object.entries(
                    convertSugTableHeader(headers, ontologyType)
                ).map(([key, value]) => {
                    return (
                        <Table.HeaderCell
                            key={`table-header-cell-${key}`}
                            style={fixTableHeaderStyle}
                        >
                            {value}
                        </Table.HeaderCell>
                    );
                })
                : headers.map((header, headerId) => {
                    return (
                        <Table.HeaderCell
                            key={`table-header-cell-${headerId}`}
                            style={fixTableHeaderStyle}
                        >
                            {convertNormalColHeaderName(
                                header,
                                ontologyType,
                                handleShowHeader(header, dupHeaders)
                            )}
                        </Table.HeaderCell>
                    );
                })}
        </Table.Row>
    );

    // sortData 排序後的資料
    const sortData = sortArray(headers, newData);
    const totalPages = Math.ceil(page / globalMaxRows);
    const activePage = isEmpty(searchKeyword)
        ? localActivePage
        : searchActivePage;

    const startIndex = globalMaxRows * (activePage - 1);
    const endIndex = activePage * globalMaxRows;

    const processedData = sortData
        .map((row, rowId) => {
            if (isAllowedDisplay(headers, row)) {
                return {
                    ...row,
                    key: `table-row-${rowId}`,
                    cells: headers.map((header, headerId) => ({
                        key: `table-row-cell-${headerId}`,
                        value: handleShowValue(header, row)
                    }))
                };
            }
            return null;
        })
        .filter(row => row !== null);

    const slicedData = processedData.slice(startIndex, endIndex);

    const TableBody = slicedData.map(row => (
        <Table.Row key={row.key} textAlign="center">
            <Table.Cell key={`${row.key}-edit`}>
                {authority.EditButton.includes(user.role) && (
                    <CustomContentMenu
                        rowData={row}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                )}
            </Table.Cell>
            {row.cells.map(cell => (
                <Table.Cell key={cell.key} style={customTableCellStyle}>
                    {cell.value}
                </Table.Cell>
            ))}
        </Table.Row>
    ));

    const sugFlexStartIndex = Math.max(0, startIndex - processedData.length);
    const sugFlexEndIndex = Math.max(0, endIndex - processedData.length);

    if (
        isEmpty(TableBody) &&
        isEmpty(suggestInfo) &&
        isEmpty(tempUpdateSuggestInfo)
    ) {
        return (
            <div>
                <CustomMessage header="目前結果為無資料。" />
                <br />
                <CustomPagination
                    totalPages={totalPages}
                    ontologyType={ontologyType}
                />
            </div>
        );
    }

    if (isLoading) {
        return <CustomIsLoading />;
    }

    return (
        <div>
            <div style={customDivStyle} className={"CustomTableFlex__wrapper"}>
                <Table
                    singleLine
                    style={tableStyle}
                    className={"CustomTableFlex__wrapper--table"}
                >
                    <Table.Header
                        className={"CustomTableFlex__wrapper--table--head"}
                    >
                        {TableHeader}
                    </Table.Header>
                    <Table.Body
                        className={"CustomTableFlex__wrapper--table--body"}
                    >
                        {TableBody}
                        {/* {user.role === ROLE.suggester && */}
                        {/*    !isEmpty(suggestInfo) && ( */}
                        {/*        <SugFlexTableBody */}
                        {/*            headers={headers} */}
                        {/*            ontologyType={ontologyType} */}
                        {/*            ontologyDomain={ontologyDomain} */}
                        {/*            startIndex={sugFlexStartIndex} */}
                        {/*            endIndex={sugFlexEndIndex} */}
                        {/*        /> */}
                        {/*    )} */}

                        {/* 因suggestions改為暫存，所以suggestInfo會為{}，因而移除判斷 */}
                        {user.role === ROLE.suggester && (
                            <SugFlexTableBody
                                headers={headers}
                                ontologyType={ontologyType}
                                ontologyDomain={ontologyDomain}
                                startIndex={sugFlexStartIndex}
                                endIndex={sugFlexEndIndex}
                            />
                        )}
                    </Table.Body>
                </Table>
            </div>
            <br />
            <CustomPagination
                totalPages={totalPages}
                ontologyType={ontologyType}
            />
        </div>
    );
};

export default CustomTableFlex;
