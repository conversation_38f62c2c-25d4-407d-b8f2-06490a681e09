import React, { useContext, useState, useEffect } from "react";
// import { signOut } from "../services/authentication";
import { Dropdown, Icon, Menu } from "semantic-ui-react";
import { StoreContext } from "../store/StoreProvider";
import { useHistory } from "react-router-dom";
import { menus, isPermitting } from "../App-header";
import { Api } from "../api/hkbdb/Api";
import { FormattedHTMLMessage, FormattedMessage } from "react-intl";

const userAvatarTrigger = (
    <span>
        <Icon
            circular
            // color="DarkBlue"
            name="user"
            style={{
                boxShadow: "none",
                border: "1px solid #DAE9EF",
                color: "#104860"
            }}
        />
    </span>
);

export const UserAvatar = ({ intl }) => {
    const [state, _] = useContext(StoreContext);
    const { user, backend } = state;
    const { locale, role } = user;
    const { backendWeb } = backend;

    //
    const history = useHistory();
    const [localLocale, setLocalLocale] = useState(
        Api.locale_lang.LOCALE_DEFAULT
    );
    const [options, setOptions] = useState([]);

    useEffect(() => {
        if (locale) {
            setLocalLocale(locale);
        }
    }, [locale]);

    useEffect(() => {
        const _options = menus.menuRight
            .filter(mr => mr.public && mr.authority.includes(role))
            .map(menu => ({
                key: menu.id,
                text: menu.label,
                value: menu.label,
                icon: menu.icon,
                path: menu.path,
                public: menu.public,
                authority: menu.authority,
                labelIntl18: menu.labelIntl18
            }));
        setOptions(_options);
    }, [role]);

    const onDropdownItemClick = (e, data) => {
        const path = options.filter(ot => ot.value === data.value)[0].path;
        if (data.value === "Management system") {
            window.open(backendWeb.url, "_blank", "noopener,noreferrer");
        } else {
            history.push(`${path.replace(":locale", localLocale)}`);
        }
    };

    return (
        <Menu.Item position="right">
            <span
                style={{
                    paddingRight: "5px",
                    fontSize: "1em",
                    color: "#104860"
                }}
            >
                {user.displayName || ""}
            </span>
            <Dropdown trigger={userAvatarTrigger} pointing="top left">
                <Dropdown.Menu>
                    {options &&
                        options.map(ot => {
                            if (isPermitting(role, ot.authority) && ot.public) {
                                return (
                                    <Dropdown.Item
                                        key={ot.key}
                                        value={ot.value}
                                        onClick={onDropdownItemClick}
                                        icon={ot.icon}
                                        text={ot.labelIntl18}
                                    />
                                );
                            } else {
                                return null;
                            }
                        })}
                </Dropdown.Menu>
            </Dropdown>
        </Menu.Item>
    );
};
