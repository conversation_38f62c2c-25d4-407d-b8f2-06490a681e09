import React, { useEffect, useState } from "react";
// mui
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import SignalCellularAltIcon from "@mui/icons-material/SignalCellularAlt";
import Typography from "@mui/material/Typography";
//
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

// hooks
import useWindowSize from "../../../../../hook/useWindowSize";
import CheckboxList from "../../commons/CheckboxList";

const LineLegendStyle = {
    width: 140,
    height: 150
};

const iconButtonStyle = {
    size: "small",
    width: 28,
    height: 28
};

/**
 * types for groupPallet
 * {perId:string; group:object[]; style: {color:string; weight:number; dashArray:string}; person:string}[]
 *
 * for exampale:
 * [
 *     {
 *         "perId": "PER%E9%87%91%E5%BA%B8",
 *         "group": [
 *             {
 *                 "perId": "PER%E9%87%91%E5%BA%B8",
 *                 "person": "金庸",
 *                 "placeId": "PLA%E6%B5%99%E6%B1%9F%E7%9C%81%E6%B5%B7%E5%AF%A7%E7%B8%A3%E8%A2%81%E8%8A%B1%E9%8E%AE",
 *                 "lat": "30.411076",
 *                 "long": "120.781114",
 *                 "place": "浙江省海寧縣袁花鎮",
 *                 "placeKey": "出生地點",
 *                 "dateKey": "出生日期",
 *                 "year": "1924",
 *                 "month": "0",
 *                 "day": "0",
 *                 "infoId": "PER%E9%87%91%E5%BA%B8",
 *                 "infoType": "hasPlaceOfBirth",
 *                 "date": "1924"
 *             },...
 *         ],
 *         "style": {
 *             "color": "#FF4500",
 *             "weight": 2,
 *             "dashArray": "5, 5"
 *         },
 *         "person": "金庸"
 *     }
 * ]
 */

/**
 *
 * @param props
 * @returns {Element}
 * @constructor
 */
const FilterSidebar = props => {
    const { mapRef, groupPallet } = props;

    const [open, setOpen] = useState(true);
    // position(top, right) for the sidebar according to the mapRef
    const [position, setPosition] = useState({ top: 0, right: 0 });
    // hooks
    const winSize = useWindowSize();

    useEffect(() => {
        // console.log(mapRef);
        if (mapRef.current) {
            const {
                top,
                right,
                width,
                height
            } = mapRef.current.getBoundingClientRect();
            // get the viewport width
            const vw = Math.max(
                document.documentElement.clientWidth || 0,
                window.innerWidth || 0
            );
            setPosition({
                top: top + 12,
                right: vw - right + iconButtonStyle.width + 18
            });
        }
    }, [mapRef, winSize]);

    const hiddenStyle = {
        width: "0px",
        height: "0px",
        minHeight: "0px"
    };
    const shownStyle = {
        width: LineLegendStyle.width,
        height: "auto",
        minHeight: LineLegendStyle.height
    };

    return (
        <Box
            sx={{
                position: "absolute",
                top: position.top,
                right: position.right,
                zIndex: 1200,
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                justifyContent: "flex-start",
                ...(open ? shownStyle : hiddenStyle),
                backgroundColor: "rgba(255, 255, 255)",
                borderRadius: "4px",
                paddingY: open ? "8px" : "0",
                paddingX: open ? "12px" : "0"
                // boxShadow: "0 0 10px rgba(0, 0, 0, 0.5)",
                // transition: "all 0.5s"
            }}
        >
            <IconButton
                size={iconButtonStyle.size}
                sx={{
                    position: "absolute",
                    top: "0px",
                    right: `-36px`,
                    cursor: "pointer",
                    backgroundColor: "white",
                    border: "1px solid #89ACBB",
                    borderRadius: "4px",
                    height: iconButtonStyle.height,
                    width: iconButtonStyle.width,
                    "&:hover": {
                        backgroundColor: "white",
                        opacity: 1
                    }
                }}
                onClick={() => setOpen(!open)}
            >
                {/* <LegendToggleIcon /> */}
                <SignalCellularAltIcon sx={{ color: "#336F89" }} />
            </IconButton>
            {open && (
                <Box sx={{ width: "100%" }}>
                    <Typography
                        variant="subtitle1"
                        textAlign={"left"}
                        sx={{
                            marginBottom: 0.5,
                            fontSize: "14px",
                            color: "#104860",
                            fontWeight: "600"
                        }}
                    >
                        <FormattedMessage
                            id="map.filter.heaeder"
                            defaultMessage="篩選條件"
                        />
                    </Typography>
                    <Typography
                        variant="subtitle1"
                        textAlign={"left"}
                        sx={{
                            marginBottom: 0.5,
                            fontSize: "12px",
                            color: "#000000",
                            fontWeight: "400"
                        }}
                    >
                        <FormattedMessage
                            id="map.filter.context"
                            defaultMessage="請至少勾選一項。"
                        />
                    </Typography>
                    {/* 篩選條件項目 */}
                    <CheckboxList />
                </Box>
            )}
        </Box>
    );
};

FilterSidebar.propTypes = {
    mapRef: PropTypes.object,
    groupPallet: PropTypes.arrayOf(PropTypes.object)
};

export default FilterSidebar;
