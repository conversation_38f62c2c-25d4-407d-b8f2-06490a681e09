// react
import React, { Fragment, useContext } from "react";

// ui
import { Pagination } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

const CustomPagination = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryResult, pagination } = state.query;
    const { total } = queryResult;
    const { activePage, limit } = pagination;
    //
    const handlePageChange = (event, { activePage }) => {
        dispatch({
            type: Act.QUERY_PAGINATION_ACTIVE_PAGE_SET,
            payload: activePage
        });
    };
    //
    return (
        <Pagination
            size="small"
            boundaryRange={0}
            activePage={activePage}
            totalPages={total ? Math.ceil(total / limit) : 1}
            onPageChange={handlePageChange}
        />
    );
};

export default CustomPagination;
