import React, { useContext, useEffect } from "react";
import {
    getDatabase,
    getDataset,
    getPropertDefined,
    queryPropertyMap
} from "./components/Hkbdb/People/action";
import { StoreContext } from "./store/StoreProvider";

const ApiDataLayer = ({ location, history }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { locale, token: globalToken } = user;

    useEffect(() => {
        // 取得db資料:區分正式站db or 測試站db
        getDatabase(dispatch);
    }, []);

    useEffect(() => {
        // 先抓 propery map table
        queryPropertyMap(dispatch);

        // get propery defined
        getPropertDefined(dispatch);
    }, [globalToken]);

    useEffect(() => {
        // get all dataset when locale change
        getDataset(dispatch, locale);
    }, [locale]);

    return null;
};

export default ApiDataLayer;
