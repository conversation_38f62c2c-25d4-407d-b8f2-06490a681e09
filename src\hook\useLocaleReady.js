import React, { useContext, useEffect, useState } from "react";
import { Api } from "../api/hkbdb/Api";
import { StoreContext } from "../store/StoreProvider";

const useLocaleReady = () => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    // local state
    const [localLocale, setLocalLocale] = useState(locale);
    const [localeReady, setLocaleReady] = useState(false);

    useEffect(() => {
        /* FIXME: 或許把 locale 變成個人的設定 */
        // 在 react-intl 中只有 zh，並無 zh-hans
        const intlLocale = locale.startsWith("zh") ? "zh" : locale;
        setLocalLocale(intlLocale);
        Api.setLocale(locale);
        setLocaleReady(true);
    }, [locale]);

    return { localLocale, localeReady };
};

export default useLocaleReady;
