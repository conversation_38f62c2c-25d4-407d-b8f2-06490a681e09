import { isEmpty } from "../../../../common/codes";

const transfromSugPrefix = inputString => {
    if (isEmpty(inputString)) return null;
    try {
        const inputArray = Array.isArray(inputString)
            ? inputString
            : inputString.split(",");

        const decodedArray = inputArray.map(item => {
            let decodedString = item;
            const prefixesToRemove = [
                "ORG",
                "ADF",
                "DAE_",
                "PLA",
                "PER",
                "ADG"
            ];

            prefixesToRemove.forEach(prefix => {
                if (decodedString.startsWith(prefix)) {
                    decodedString = decodedString.slice(prefix.length);
                }
            });

            return decodeURIComponent(decodedString);
        });

        return decodedArray.join(",");
    } catch (error) {
        console.error("Error decoding string:", error);
        return null;
    }
};

export default transfromSugPrefix;
