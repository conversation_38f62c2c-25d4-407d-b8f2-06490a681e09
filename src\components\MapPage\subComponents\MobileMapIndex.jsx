import React from 'react';
import { Box, Typography } from '@mui/material';
import { FormattedMessage } from "react-intl";
import '../styles/myMap.scss'

const MobileMapIndex = () => {
    return (
        <Box className='gis-map-container-mobile'>
            <Box className='gis-map-container-mobile-title'>
                <FormattedMessage
                    id="map.mobile.hint.header"
                    defaultMessage="GIS僅限電腦使用"
                />
            </Box>
            <Box className='gis-map-container-mobile-content'>
                <Typography>
                    <FormattedMessage
                        id="map.mobile.hint.content"
                        defaultMessage="為提供您最佳體驗，GIS功能僅支援電腦版。"
                    />
                </Typography>
                <Typography>
                    <FormattedMessage
                        id="map.mobile.hint.sub.content"
                        defaultMessage="請使用電腦開啟此頁面，以獲得完整的操作體驗。"
                    />
                </Typography>
            </Box>
        </Box>
    )
};

export default MobileMapIndex;
