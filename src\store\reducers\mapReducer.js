import Act from "../actions";
import { FormattedMessage } from "react-intl";
import React from "react";
import { mapFilterOptions } from "../../components/MapPage/subComponents/commons/config";

const initState = {
    /** for map page */
    mapFilterOptions: {
        hasPlaceOfBirth: {
            status: true,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.birth.place"
                    defaultMessage="出生地點"
                />
            ),
            color: mapFilterOptions.hasPlaceOfBirth
        },
        EducationEvent: {
            status: false,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.educational.institution"
                    defaultMessage="教育單位"
                />
            ),
            color: mapFilterOptions.EducationEvent
        },
        EmploymentEvent: {
            status: false,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.work.place"
                    defaultMessage="工作地點"
                />
            ),
            color: mapFilterOptions.EmploymentEvent
        },
        Award: {
            status: false,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.award.location"
                    defaultMessage="獲獎地點"
                />
            ),
            color: mapFilterOptions.Award
        },
        Event: {
            status: false,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.event.location"
                    defaultMessage="事件地點"
                />
            ),
            color: mapFilterOptions.Event
        },
        hasPlaceOfDeath: {
            status: false,
            label: (
                <FormattedMessage
                    id="map.filter.sidebar.death.place"
                    defaultMessage="逝世地點"
                />
            ),
            color: mapFilterOptions.hasPlaceOfDeath
        }
    },
    traceMapPersonSelected: [],
    generalMapFilterSelected: []
};

const mapReducer = (state = initState, action) => {
    switch (action.type) {
        /** for map page */
        case Act.SET_MAP_FILTER_OPTIONS:
            return {
                ...state,
                mapFilterOptions: action.payload
            };

        case Act.SET_TRACE_MAP_PERSON_SELECTED:
            return {
                ...state,
                traceMapPersonSelected: action.payload
            };

        case Act.SET_GENERAL_MAP_FILTER_SELECTED:
            return {
                ...state,
                generalMapFilterSelected: action.payload
            };

        default:
            return state;
    }
};

export default mapReducer;
