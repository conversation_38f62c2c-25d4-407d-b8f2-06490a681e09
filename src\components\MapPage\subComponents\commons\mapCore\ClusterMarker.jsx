/* eslint-disable no-unused-vars, react/prop-types */
import React, { useRef } from "react";
//
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-leaflet";
// eslint-disable-next-line import/no-extraneous-dependencies
import isFunction from "lodash/isFunction";
import L from "leaflet";
//
import classNames from "classnames";
// import { type ICluster } from "@/app/[lang]/activity/replus2023/subComponent/TypeScriptProps.tsx";
import sliceByLimit from "../../../utils/sliceByLimit.js";
import { isInHongKong, isNearHongKong } from "../MapHelper.jsx";
import {
    dfPointMarkerPalette,
    mapLatLongDefault
    // type PaletteKey,
} from "../config.js";
import "../../../styles/myMap.scss";

// marker icon background color generator
const markerIconColor = (count, paletteStyle) => {
    const pointPalette =
        paletteStyle && paletteStyle in dfPointMarkerPalette
            ? dfPointMarkerPalette[paletteStyle]
            : dfPointMarkerPalette.light;
    const colorFound = pointPalette.find(
        pp => count >= pp.min && count <= pp.max
    );
    if (colorFound != null) {
        return colorFound.color;
    }
    return pointPalette[0].color;
};

const MAX_WIDTH = 55;
const safeWidth = (width, maxWidth) => (width > maxWidth ? maxWidth : width);

// generate cluster icon
const clusterIcons = {};

// interface FetchClusterIconProps {
//     count: number;
//     size: number;
//     paletteStyle?: PaletteKey;
//     className: string;
// }

const fetchClusterIcon = ({
    count,
    size,
    paletteStyle,
    className,
    isHongKong
}) => {
    const limitWidth = safeWidth(size, MAX_WIDTH);
    const clusterColor = markerIconColor(count, paletteStyle);
    const iconHtml = `
        <div class="${classNames(
        "cluster-marker-icon-wrapper"
    )}" style="position: relative; left: -${limitWidth /
    2}px; top: -${limitWidth / 2}px">
    <div class="${classNames(
        "cluster-marker-icon",
        className
    )}" style="width: ${limitWidth}px; height: ${limitWidth}px; background: ${clusterColor}; display: flex; align-items: center; justify-content: center; flex-direction: column;">
            <span style="font-weight: bold">${count}</span>
            ${isHongKong
            ? '<div style="font-size: 10px; margin-top: 2px;">(香港)</div>'
            : ""
        }
    </div>
</div>
`;
    clusterIcons[count] = L.divIcon({
        html: iconHtml
    });
    return clusterIcons[count];
};

const STR_LEN_MAX = 40;

// interface PopupTextProps {
//     location?: string;
// }

const PopupText = ({ location }) => (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>{`${sliceByLimit(location ?? "Unknown", STR_LEN_MAX)}`}</>
);

// interface ClusterMarkerProps {
//     clusterIndex: string | number;
//     cluster: ICluster;
//     latitude: number;
//     longitude: number;
//     pointCount: number;
//     clusterPoints: any[];
//     isLocNameDisplay?: boolean;
//     paletteStyle?: PaletteKey;
//     onClick: (cluster: ICluster) => void;
//     onTooltipOpen: (cluster: ICluster) => void;
//     showTooltip: boolean;
//     clusterIconClassName: string;
// }

const ClusterMarker = ({
    clusterIndex,
    cluster,
    latitude,
    longitude,
    pointCount,
    clusterPoints,
    isLocNameDisplay,
    paletteStyle,
    showTooltip,
    onClick,
    onTooltipOpen,
    clusterIconClassName
}) => {
    const { location } = cluster?.properties || {};
    const ref = useRef(null);

    // 判斷是否在香港
    const inHongKong = isInHongKong(latitude, longitude);
    // 或者使用距離判斷
    const nearHongKong = isNearHongKong(latitude, longitude, mapLatLongDefault);

    // 根據是否在香港設置不同的樣式
    const hongKongClassName = inHongKong ? "in-hong-kong" : "";
    // 或者
    const locationClassName = nearHongKong ? "near-hong-kong" : "";

    // 使用 CircleMarker 樣式
    return (
        <>
            <Marker
                ref={ref}
                className={`clusterMarker ${hongKongClassName} ${locationClassName}`}
                key={`cluster-${clusterIndex}-pointCount-${pointCount}`}
                position={L.latLng({ lat: latitude, lng: longitude })}
                icon={fetchClusterIcon({
                    count: cluster.properties.intensity,
                    size:
                        20 +
                        (cluster.properties.intensity / clusterPoints.length) *
                        100,
                    paletteStyle,
                    className: `${clusterIconClassName} ${hongKongClassName} ${locationClassName}`,
                    isHongKong: inHongKong
                })}
                eventHandlers={{
                    click: e => {
                        if (isFunction(onClick)) {
                            onClick(cluster);
                        }
                    }
                }}
            >
                {/* 在 tooltip 中顯示是否在香港 */}
                {showTooltip && (
                    <Tooltip
                        className="marker--tooltip"
                        onOpen={() => {
                            if (isFunction(onTooltipOpen)) {
                                onTooltipOpen(cluster);
                            }
                        }}
                    >
                        <span
                            style={{
                                fontWeight: "bold",
                                maxWidth: "50px",
                                whiteSpace: "pre"
                            }}
                        >
                            <PopupText location={location} />
                        </span>
                    </Tooltip>
                )}
            </Marker>
        </>
    );
};

export default ClusterMarker;
