import React, { useContext, useEffect, useState } from "react";
// ui
import { Button, Input, Label, Modal, Popup, Select } from "semantic-ui-react";
// lang
import { FormattedMessage, injectIntl, formatMessage } from "react-intl";
// api
import { Api, duplicateHkbdbData } from "../../../../api/hkbdb/Api";
// store
import { StoreContext } from "../../../../store/StoreProvider";
// common
import {
    bs64Encode,
    handleNameSpellCheck,
    isEmpty,
    isNotEmpty,
    isTrue
} from "../../../../common/codes";
import ModalYesNo from "../ModalYesNo";
// auth
import authority from "../../../../App-authority";
// helper
import { displayInstanceName } from "../helper";
import CustomAlertMessage from "../TableComp/CustomAlertMessage";
import { eventConfig } from "./EventConfig";
//
import {
    uriEncIdToNonEncId,
    idToUriEncBs64EncId,
    decodeURIComponentSafe
} from "../../../../common/codes/jenaHelper";
//
const CUSTOM_DUPLICATION = "Duplicate";
const customButtonStyle = {
    marginLeft: ".8em",
    marginTop: "1em",
    marginBottom: "1em",
    padding: ".3em"
};
//
// SPARQL 空格替換
// type: Person
// id: (聲音演出)_Furze_&_Karmonic_(藝術表演
// name: (聲音演出) Furze & Karmonic (藝術表演
const CustomDuplicateButton = ({ type, id, name, intl }) => {
    const [state] = useContext(StoreContext);
    const { uid, role } = state.user;
    const { personInformation: info } = state;
    const [nameId, setNameId] = useState("");
    //
    const [open, setOpen] = useState(false);
    const [openYesNo, setOpenYesNo] = useState(false);
    // Yes: true, No: false
    const [valYesNo, setValYesNo] = useState(false);
    //
    const [inputValue, setInputValue] = useState();
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 2 * 1000
    }));
    const nameValue = (name && name !== "" ? name : id) || "";

    useEffect(() => {
        if (!type || !info) return;
        const targetInfo = info[type.toLowerCase()];
        if (!isEmpty(targetInfo)) {
            targetInfo.forEach(el => {
                if (el["basicProperty"] && el["basicProperty"] === "nameId") {
                    setNameId(el["basicData"]);
                }
            });
        }
    }, [type, info]);

    useEffect(() => {
        // 選擇否
        if (valYesNo === false) {
            return;
        }

        const handleDuplicate = async () => {
            if (inputValue && nameId && nameValue) {
                const checkName = handleNameSpellCheck(nameValue);
                const lengthList = [...Array(parseInt(inputValue, 10)).keys()];
                // ['001', '002', '003', ... , '010']
                const getNbList = lengthList.map(st => {
                    const nbStr = (st + 1).toString().padStart(3, "0");
                    return nbStr;
                });
                // 先 urlEncode 再 bs64 encode
                const encodeSrc = idToUriEncBs64EncId(checkName);

                const entrySrc = {
                    srcId: `${eventConfig[type]}${encodeSrc}`
                };
                const promises = getNbList.map(nb => {
                    const getNameNumber = `${nameValue}${nb}`;
                    //
                    const encodeDst = idToUriEncBs64EncId(`${checkName}${nb}`);
                    const entryDst = {
                        srcId: `${eventConfig[type]}${encodeDst}`
                    };
                    const nameIdNumber = `${nameId}${nb}`;
                    //
                    return duplicateHkbdbData(
                        Api.duplicateInstance(),
                        entrySrc,
                        entryDst,
                        getNameNumber,
                        nameId,
                        nameIdNumber
                    );
                });

                const result = await Promise.all(promises).then(res => res);

                // 確認每個 promise 狀態，有一個不成功就回傳失敗
                const status = result.every(el => el.state === true);
                // 顯示訊息
                if (status) {
                    //
                    setAlertMsg(prevMsg => ({
                        ...prevMsg,
                        title: intl.formatMessage(
                            {
                                id:
                                    "people.alertMessage.title.duplicate.success",
                                defaultMessage: `Duplicate ｢{name}」successfully ...`
                            },
                            {
                                name: uriEncIdToNonEncId(nameValue)
                            }
                        ),
                        type: "success",
                        content: intl.formatMessage(
                            {
                                id:
                                    "people.alertMessage.content.duplicate.success",
                                defaultMessage: `Total: {name},Please search for confirmation.`
                            },
                            {
                                name: inputValue
                            }
                        ),
                        // 時間只是方便用來觸發更新而已
                        renderSignal: new Date().getTime(),
                        ttl: 15 * 1000
                    }));
                    setInputValue(null);
                    setValYesNo(false);
                } else {
                    setAlertMsg(prevMsg => ({
                        ...prevMsg,
                        title: intl.formatMessage(
                            {
                                id: "people.alertMessage.title.duplicate.error",
                                defaultMessage: `｢{name}」`
                            },
                            {
                                name: uriEncIdToNonEncId(nameValue)
                            }
                        ),
                        type: "error",
                        content: intl.formatMessage(
                            {
                                id:
                                    "people.alertMessage.content.duplicate.error",
                                defaultMessage: `Duplicate error ...`
                            },
                            {
                                name: uriEncIdToNonEncId(nameValue)
                            }
                        ),
                        // 時間只是方便用來觸發更新而已
                        renderSignal: new Date().getTime(),
                        ttl: 20 * 1000
                    }));
                    setInputValue(null);
                    setValYesNo(false);
                }
            }
        };
        handleDuplicate();
    }, [valYesNo]);
    // }, [inputValue, type]);
    //
    const handleOpen = () => {
        setOpen(true);
    };
    //
    const handleCancel = () => {
        setOpen(false);
    };

    const handleInputChange = (event, { value }) => {
        setInputValue(value);
    };
    //
    const handleOpenYesNo = () => {
        if (parseInt(inputValue, 10) === 0 || isNaN(parseInt(inputValue, 10))) {
            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: intl.formatMessage(
                    {
                        id: "people.input.title.duplicate.error",
                        defaultMessage: `Input error...`
                    },
                    {
                        name: inputValue
                    }
                ),
                type: "error",
                content: intl.formatMessage(
                    {
                        id: "people.input.content.duplicate.error",
                        defaultMessage: `Please enter number and that is not 0 ...`
                    },
                    {
                        name: inputValue
                    }
                ),
                // 時間只是方便用來觸發更新而已
                renderSignal: new Date().getTime(),
                ttl: 15 * 1000
            }));
            setInputValue(null);
        } else {
            setOpenYesNo(true);
        }
    };
    //
    if (
        !isTrue(process.env.REACT_APP_CRUD_NODE) ||
        !isNotEmpty(uid) ||
        !authority.People_Information.includes(role)
    ) {
        return null;
    }

    return (
        <div>
            <Modal
                size="small"
                open={open}
                onOpen={handleOpen}
                dimmer="inverted"
            >
                <Modal.Header>
                    <FormattedMessage
                        id={"people.Information.duplicate.person"}
                        defaultMessage={`Duplicate ｢{name}」to ...`}
                        values={{
                            name: displayInstanceName(
                                decodeURIComponentSafe(id),
                                name,
                                Api.getLocale()
                            )
                        }}
                    />
                </Modal.Header>
                <Modal.Content image>
                    <Modal.Description style={{ maxWidth: "100%" }}>
                        <CustomAlertMessage
                            alertMsg={alertMsg}
                            setAlertMsg={setAlertMsg}
                        />
                        <Input
                            fluid
                            labelPosition="left"
                            type="text"
                            size="large"
                        >
                            <Label>
                                <FormattedMessage
                                    id={"people.Information.label.count"}
                                    defaultMessage={"Count"}
                                />
                            </Label>
                            <Input
                                style={{ width: "100%" }}
                                placeholder={intl.formatMessage({
                                    id: "people.Information.input.placeholder",
                                    defaultMessage: "Type number to copy..."
                                })}
                                onChange={handleInputChange}
                            />
                        </Input>
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    <Button
                        disabled={!inputValue}
                        onClick={handleOpenYesNo}
                        color="green"
                    >
                        <FormattedMessage
                            id={"people.Information.button.duplicate"}
                            defaultMessage={`Duplicate`}
                        />
                    </Button>
                    <Button color="red" onClick={handleCancel}>
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={`Cancel`}
                        />
                    </Button>
                </Modal.Actions>
            </Modal>
            <ModalYesNo
                open={openYesNo}
                setOpen={setOpenYesNo}
                setYesNo={setValYesNo}
                type={type}
                modalHeader={intl.formatMessage(
                    {
                        id: `information.ModalYesNo.header.duplicate.${type.toLowerCase()}`,
                        defaultMessage: `{name} {type}`
                    },
                    {
                        name: CUSTOM_DUPLICATION,
                        type: type.toLowerCase()
                    }
                )}
            />
            <Popup
                content={
                    <FormattedMessage
                        id={"people.Information.duplicate.person"}
                        defaultMessage={`Duplicate ｢{name}」to ...`}
                        values={{
                            name: displayInstanceName(
                                decodeURIComponentSafe(id),
                                name,
                                Api.getLocale()
                            )
                        }}
                    />
                }
                key={"merge"}
                trigger={
                    <Button
                        size="mini"
                        color="green"
                        icon="code branch"
                        style={customButtonStyle}
                        onClick={handleOpen}
                    />
                }
            />
        </div>
    );
};

export default injectIntl(CustomDuplicateButton);
