import React, { useContext } from "react";

import { useHistory } from "react-router-dom";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusLangSwitcher from "../CusLangSwitcher";

import { StoreContext } from "../../../../store/StoreProvider";

const CusPrimaryHeader = ({ style = {} }) => {
    const classes = useStyles(style);
    const history = useHistory();
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    const handleHome = () => {
        history.push(`/${locale}/Hkvayb`);
    };
    const handleAbout = () => {
        history.push(`/${locale}/HkvaybAbout`);
    };
    const handleDescription = () => {
        history.push(`/${locale}/HkvaybDescription`);
    };

    return (
        <div className={classes.hkvayb_header}>
            <div className={classes.hkvayb_header_left}>
                <div className={classes.hkvayb_logo} onClick={handleHome} />
            </div>
            <div className={classes.hkvayb_header_right}>
                <div className={classes.hkvayb_header_right_border_item}>
                    <div className={classes.hkvayb_header_right_border_font}>
                        <CusLangSwitcher />
                    </div>
                </div>
                <div
                    className={classes.hkvayb_header_right_item}
                    onClick={handleDescription}
                >
                    <FormattedMessage
                        id="hkvayb.search.header.description"
                        defaultMessage="Remarks"
                    />
                </div>
                <div
                    className={classes.hkvayb_header_right_item}
                    onClick={handleAbout}
                >
                    <FormattedMessage
                        id="hkvayb.search.header.about"
                        defaultMessage="About Yearbook"
                    />
                </div>
            </div>
        </div>
    );
};

export default CusPrimaryHeader;
