import React, { useContext, useEffect, useState } from "react";
import { Table } from "semantic-ui-react";
import { safeGetProperty } from "../../../common/utils/safeGetProperty";
import { getUser } from "../../../../../api/firebase/realtimeDatabase";
import { StoreContext } from "../../../../../store/StoreProvider";
import CustomContentMenu from "../CustomContextMenuFlex";
import CustomTableLabel from "../CustomTableLabel";
import transfromSugPrefix from "../../../common/utils/transfromSugPrefix";
import config from "../../../../../config/config";
import { isEmpty } from "../../../../../common/codes";
import { convertSugTableHeader } from "../../../common/utils/convertSugOptions";
import SugContextMenuFlex from "./SugContextMenuFlex";

const SugFlexDeleteTable = ({
    data,
    property,
    headers,
    ontologyType,
    type
}) => {
    const [state] = useContext(StoreContext);
    const { personInformation } = state;
    const { suggestInfo } = personInformation;

    const [headerArr, setHeaderArr] = useState([]);
    const [suggesterName, setSuggesterName] = useState();

    useEffect(() => {
        let tmpArr = [];
        if (!headers) return;
        headers.map(i => {
            const tmp = safeGetProperty(property, i.split("__")[0]);
            tmpArr.push(tmp);
        });
        setHeaderArr(tmpArr);
    }, [headers]);

    const suggester = getUser(data.uid).then(res => {
        setSuggesterName(res?.displayName);
    });

    const hasAwardedForWorkArticle = isEmpty(suggestInfo)
        ? []
        : suggestInfo
              ?.filter(i => i.graph === data.graph)
              ?.filter(
                  ii =>
                      ii.prop === config.hasAwardedForWork &&
                      ii.val.includes("ART")
              );

    return (
        <>
            <Table celled padded style={{ width: "100%" }}>
                <Table.Header>
                    <Table.Row>
                        {Object.entries(
                            convertSugTableHeader(headers, ontologyType)
                        ).map(([key, value]) => {
                            return (
                                <Table.HeaderCell
                                    key={`table-header-cell-${key}`}
                                >
                                    {value}
                                </Table.HeaderCell>
                            );
                        })}
                    </Table.Row>
                </Table.Header>
                <Table.Body>
                    {type === "temporary" ? (
                        <>
                            <Table.Row key={`basic-table-row`}>
                                {headers.map(header => {
                                    const prop = header.split("__")[0];
                                    if (prop === "graph") return null;
                                    const val = data.val[prop];
                                    const hasAwardedForType = val?.includes(
                                        "ART/"
                                    )
                                        ? "ART"
                                        : "PUB";

                                    if (
                                        (header ===
                                            "hasAwardedForWork__Publication" &&
                                            hasAwardedForType === "ART") ||
                                        (header ===
                                            "hasAwardedForWork__Article" &&
                                            hasAwardedForType === "PUB")
                                    ) {
                                        return (
                                            <Table.Cell
                                                key={prop}
                                                textAlign="center"
                                            ></Table.Cell>
                                        );
                                    } else if (
                                        header === "hasAwardedForWork__Article"
                                    ) {
                                        return (
                                            <Table.Cell
                                                key={prop}
                                                textAlign="center"
                                            >
                                                {/* {val.slice(0, -3)} */}
                                                {val.replaceAll("ART/", "")}
                                            </Table.Cell>
                                        );
                                    } else {
                                        return (
                                            <Table.Cell
                                                key={prop}
                                                textAlign="center"
                                            >
                                                {transfromSugPrefix(val)}
                                            </Table.Cell>
                                        );
                                    }
                                })}
                                <Table.Cell textAlign="center">
                                    <CustomTableLabel
                                        value={`Suggester: ${suggesterName ||
                                            ""}`}
                                        color="orange"
                                    />
                                </Table.Cell>
                            </Table.Row>
                        </>
                    ) : (
                        <Table.Row>
                            {Object.entries(data?.val).map(([prop, val]) => {
                                // 針對曾獲獎項做區分處理
                                if (
                                    !isEmpty(hasAwardedForWorkArticle) &&
                                    prop === config.hasAwardedForWork
                                ) {
                                    return (
                                        <Table.Cell
                                            key={prop}
                                            textAlign="center"
                                        ></Table.Cell>
                                    );
                                } else {
                                    return (
                                        <Table.Cell
                                            key={prop}
                                            textAlign="center"
                                        >
                                            {transfromSugPrefix(val)}
                                        </Table.Cell>
                                    );
                                }
                            })}
                            {/* 曾獲獎項特別處理欄位 */}
                            {data.sheetName === config.DEF_AWDEVT_TYPE && (
                                <Table.Cell textAlign="center">
                                    {!isEmpty(hasAwardedForWorkArticle)
                                        ? // ? data?.val?.hasAwardedForWork.slice(
                                          //       0,
                                          //       -3
                                          //   )
                                          data?.val?.hasAwardedForWork.replaceAll(
                                              "ART/",
                                              ""
                                          )
                                        : ""}
                                </Table.Cell>
                            )}
                            <Table.Cell textAlign="center">
                                <CustomTableLabel
                                    value={`Suggester: ${suggesterName || ""}`}
                                    color="orange"
                                />
                            </Table.Cell>
                        </Table.Row>
                    )}
                </Table.Body>
            </Table>
        </>
    );
};
export default SugFlexDeleteTable;
