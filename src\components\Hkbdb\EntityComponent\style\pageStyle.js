import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles(theme => ({
    avatarRoot: {
        width: "45px",
        height: "45px",
        backgroundColor: "#c4c4c4",
        [theme.breakpoints.down("lg")]: {
            width: "45px",
            height: "45px"
        },
        [theme.breakpoints.down("md")]: {
            width: "40px",
            height: "40px"
        },
        [theme.breakpoints.down("sm")]: {
            width: "30px",
            height: "30px"
        },
        [theme.breakpoints.down("xs")]: {
            width: "30px",
            height: "30px"
        },
        margin: theme.spacing(1)
    },
    avatarCursor: {
        cursor: "pointer"
    },
    avatarPlaceholder: {
        width: "50px",
        height: "50px",
        borderRadius: "100%"
    },
    resultsEntity: {
        height: "calc(100vh - 300px)",
        overflow: "auto"
    },
    headerContainer: {
        maxWidth: "calc(75%)",
        display: "flex",
        alignItems: "center"
    },
    nameHeader: {
        width: "fit-content",
        textOverflow: "ellipsis",
        overflow: "hidden",
        whiteSpace: "nowrap",
        marginLeft: "15px",
        display: "inline- block",
        verticalAlign: "middle"
    }
}));

export default useStyles;
