// react
import React from "react";

// ui
import { Table } from "semantic-ui-react";

// custom
import CustomTableHead from "./CustomTableHead";
import CustomTableBody from "./CustomTableBody";

const index = () => {
    return (
        <React.Fragment>
            <Table celled selectable singleLine>
                <CustomTableHead />
                <CustomTableBody />
            </Table>
        </React.Fragment>
    );
};

export default index;
