import React, { useContext, useEffect } from "react";
// import { HomePageLayout } from "./HomePageLayout";
import { HomePageHeading } from "./subComponents/HomePageHeading";

import { ResponsiveContainer } from "../../layout/Layout";
import Footer from "../../layout/Footer";
import { checkLanguageWithPaths } from "../../common/codes/utils/languageTools";
// store
import Act from "../../store/actions";
import { StoreContext } from "../../store/StoreProvider";
// css
import "./homePage.scss";

const HomePage = ({ ...props }) => {
    const { location } = props;
    const [_, dispatch] = useContext(StoreContext);

    useEffect(() => {
        const currentLocale = checkLanguageWithPaths(location);
        dispatch({
            type: Act.SET_USER_LOCALE,
            payload: currentLocale
        });
    }, []);

    return (
        <div className={"homePage"}>
            <ResponsiveContainer
                header={HomePageHeading}
                withFooter={true}
                footer={Footer}
                {...props}
            >
                {/* if needed: HomePageLayout 放置網站簡介 */}
                {/* <HomePageLayout /> */}
            </ResponsiveContainer>
        </div>
    );
};

export default HomePage;
