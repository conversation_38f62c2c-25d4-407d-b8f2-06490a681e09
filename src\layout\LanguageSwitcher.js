import React, { useContext, useEffect, useState } from "react";
import { Button, Menu } from "semantic-ui-react";
import { useHistory } from "react-router-dom";
import { Api } from "../api/hkbdb/Api";
import { StoreContext } from "../store/StoreProvider";
import Act from "../store/actions";
import { changeLang } from "../common/codes/utils/languageTools";
import PropTypes from "prop-types";
import NormalAvatar from "../common/components/avatar/NormalAvatar";

// type LanguageSwitcherProps = {
//     onLocaleChanged: Function,
//     intl: Object,
// };

const LanguageSwitcher = ({ mobile }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    const [localLocale, setLocalLocale] = useState(locale);
    const history = useHistory();
    const onLocaleChanged = newLocale => {
        changeLang({
            history,
            currentLanguage: locale,
            nextLanguage: newLocale
        });

        dispatch({
            type: Act.SET_USER_LOCALE,
            payload: newLocale
        });
    };

    useEffect(() => {
        setLocalLocale(locale);
    }, [locale]);

    return (
        <Menu.Item style={{ height: "100%", padding: mobile ? "0" : "" }}>
            <Button.Group size="tiny">
                <Button
                    primary={localLocale === Api.locale_lang.LOCALE_ZH}
                    onClick={() => onLocaleChanged(Api.locale_lang.LOCALE_ZH)}
                    style={{
                        backgroundColor: "transparent",
                        color: locale === "zh-hans" ? "#104860" : "#89ACBB",
                        fontSize: "1rem",
                        padding: "13px 16px"
                    }}
                >
                    中文
                </Button>
                <span
                    style={{
                        display: "flex",
                        alignItems: "center",
                        fontSize: "1rem",
                        color: "#89ACBB"
                    }}
                >
                    /
                </span>
                <Button
                    primary={localLocale === Api.locale_lang.LOCALE_EN}
                    onClick={() => onLocaleChanged(Api.locale_lang.LOCALE_EN)}
                    style={{
                        backgroundColor: "transparent",
                        color: locale === "en" ? "#104860" : "#89ACBB",
                        fontSize: "1rem",
                        padding: "1rem"
                    }}
                >
                    EN
                </Button>
            </Button.Group>
        </Menu.Item>
    );
};

NormalAvatar.defaultProps = {
    mobile: true
};

NormalAvatar.propTypes = {
    mobile: PropTypes.bool
};

export default LanguageSwitcher;
