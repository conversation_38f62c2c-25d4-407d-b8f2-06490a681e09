import React from "react";

import useStyles from "./style";

const CusButton = ({
    basic,
    invert,
    borderInvert,
    label,
    onClick,
    style = {},
    ...rest
}) => {
    const classes = useStyles(style);
    let className;
    if (basic) {
        className = classes.hkvayb_buttom_basic;
    }
    if (invert) {
        className = classes.hkvayb_buttom_invert;
    }
    if (borderInvert) {
        className = classes.hkvayb_buttom_border_invert;
    }
    return (
        <button {...rest} className={className} onClick={onClick}>
            {label}
        </button>
    );
};

export default CusButton;
