import React from "react";
// import { type IPoint } from "@/app/[lang]/activity/replus2023/subComponent/TypeScriptProps.tsx";
//
import CircularProgress from "@mui/material/CircularProgress";
//
import { Popup } from "react-leaflet";
import isFunction from "lodash/isFunction";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";

const DataLoading = <CircularProgress size="16px" />;

// interface EasyPopupProp {
//   point: IPoint;
//   onPopupOpen?: (p: IPoint) => void;
// }
const HkbdbMapMarkerPopup = ({ point, onPopupOpen }) => {
    const { properties } = point || {};
    const {
        person,
        date,
        year,
        month,
        day,
        dateKey,
        place,
        placeKey,
        infoId,
        infoType,
        infoDescKey,
        infoDesc
    } = properties || {};
    const columns = [
        { label: "人物常見名稱", value: person },
        { label: placeKey, value: place },
        { label: dateKey, value: date },
        { label: infoDescKey, value: infoDesc }
    ];

    return (
        <Popup
            /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
            // @ts-expect-error
            onOpen={() => {
                if (isFunction(onPopupOpen)) {
                    onPopupOpen(point);
                }
            }}
        >
            <Box>
                <Box
                    sx={{
                        display: "flex",
                        // justifyContent: "space-between",
                        marginBottom: 1,
                        columnGap: "24px"
                    }}
                >
                    <Typography variant="subtitle1">
                        {point?.properties.location}
                    </Typography>
                    <Typography variant="subtitle1" sx={{ color: "#4183c4" }}>
                        {person}
                    </Typography>
                </Box>
                <Grid
                    container
                    columnSpacing={1}
                    flexDirection={"column"}
                    sx={{ minWidth: "184px" }}
                >
                    {columns
                        .filter(col => col.label && col.value)
                        .map((col, colIdx) => (
                            <Grid key={colIdx} item xs={12} sm={12} md={12}>
                                <Typography
                                    variant="caption"
                                    sx={{
                                        display: "inline-block",
                                        marginRight: "8px"
                                    }}
                                >
                                    {col.label}: {col.value}
                                </Typography>
                            </Grid>
                        ))}
                </Grid>
            </Box>
        </Popup>
    );
};

export default HkbdbMapMarkerPopup;
