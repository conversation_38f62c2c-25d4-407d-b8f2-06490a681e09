import React, { useEffect, useState, useMemo } from "react";
import Lightbox from "../lightBox/LightBox";
import { Api, readHkbdbData } from "../../../api/hkbdb/Api";
import { useParams } from "react-router-dom";

const InfoModal = ({ modalInfo, data, setFocusItem, onClose, type }) => {
    if (!modalInfo) return null;
    const { id, label, imageURL = [], order } = modalInfo;
    const [isLoading, setIsLoading] = useState(true);
    const [curData, setCurData] = useState([]);
    const { colType } = useParams();

    const transformedData = useMemo(() => {
        if (!curData.length) return [];
        return curData.map(item => {
            colType === "featuredPub"
                ? (item.imageURL = [item.imageURL])
                : (item.imageURL = item.imageURL.split(", "));
            return item;
        });
    }, [curData, colType]);

    const nextClick = () => {
        setFocusItem(data[+order]);
    };

    const prevClick = () => {
        setFocusItem(data[+order - 2]);
    };

    function combineObjects(arr) {
        let combined = {};

        arr.forEach(item => {
            if (
                Object.keys(combined).length === 0 ||
                combined.label === item.label
            ) {
                if (Object.keys(combined).length === 0) {
                    combined = { ...item };
                } else {
                    combined.hasPlaceOfPublication += `, ${item.hasPlaceOfPublication}`;
                }
            }
        });

        return [combined];
    }

    useEffect(() => {
        const getCurData = async () => {
            setIsLoading(true);
            const size = "400x400";
            const apiStr =
                colType === "featuredPub"
                    ? Api.getBookcoverInfo(size, id)
                    : Api.getManuscriptInfo(size, id);
            const { data } = await readHkbdbData(apiStr);
            const combinedData = combineObjects(data);
            setCurData(combinedData);
            setIsLoading(false);
        };
        getCurData();
    }, [id, colType]);

    if (isLoading || !transformedData.length) {
        return (
            <div
                style={{
                    position: "fixed",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    backgroundColor: "rgba(0, 0, 0, 0.8)",
                    zIndex: 9999
                }}
            ></div>
        );
    }

    return (
        <Lightbox
            key={order}
            images={transformedData[0]?.imageURL}
            title={label}
            imgInfo={modalInfo}
            onClose={onClose}
            onLeftClick={order > 0 ? prevClick : null}
            onRightClick={order !== data.length - 1 ? nextClick : null}
            clickOutsideToExit={false}
            carouselOn={curData[0]?.imageURL?.length > 0}
            type={type}
            curData={transformedData}
            colType={colType}
        />
    );
};

export default InfoModal;
