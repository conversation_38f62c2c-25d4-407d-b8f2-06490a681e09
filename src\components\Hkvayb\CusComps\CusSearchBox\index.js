import React, { useContext, useEffect, useState } from "react";

import { useHistory } from "react-router-dom";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

// custom
import CusInput from "../CusInput";
import CusButton from "../CusButton";
import CusDropdown from "../CusDropdown";
import CusCollapse from "../CusCollapse";

// config
import config from "../../config";

import debounce from "../../CusHooks/useDebounce/debounce";

// common
import { bs64Encode, safeGet } from "../../../../common/codes";

// api
import { url } from "../../../../api/hkvayb";

// store
import act from "../../../../store/actions";
import { StoreContext } from "../../../../store/StoreProvider";

const CusSearchBox = ({ style = {} }) => {
    const classes = useStyles(style);
    const history = useHistory();

    const [state, dispatch] = useContext(StoreContext);
    const { locale } = state.user;
    const { fields } = state.hkvaybSearch;
    const [fieldState, setFieldState] = useState({ ...fields });
    const options = safeGet(config.searchBoxOptions, [locale], {});

    const commaSymbol = config.symbol.comma;
    const spaceSymbol = config.symbol.space;

    const debTitle = debounce(fieldState.label, 700);
    const debAuthor = debounce(fieldState.author, 700);
    const debKeyword = debounce(fieldState.keyword, 700);
    const debOrganizer = debounce(fieldState.organizer, 700);

    const handleOnChange = event => {
        const targetKeys = event.target.name.split(commaSymbol);
        const targetValue = event.target.value;
        targetKeys.forEach(targetKey => {
            if (targetValue instanceof Array) {
                setFieldState(prevState => ({
                    ...prevState,
                    [targetKey]: targetValue
                        .map(item => item.label)
                        .join(commaSymbol)
                }));
            } else {
                setFieldState(prevState => ({
                    ...prevState,
                    [targetKey]: targetValue
                }));
            }
        });
    };

    const handleSearchButton = () => {
        const { category, talkCatType, awardCatType, ...rest } = fieldState;
        let tmpCategory;
        if (!category) {
            tmpCategory = options.category
                .map(item => item.label)
                .join(commaSymbol);
        } else {
            tmpCategory = category;
        }
        const lookupCategory = options.category.reduce(
            (prev, item) => ({ ...prev, [item.label]: item.value }),
            {}
        );
        const tmpFieldState = {
            ...rest,
            category: tmpCategory
                .split(commaSymbol)
                .map(c => `hkbdb:${safeGet(lookupCategory, [c], "")}`)
                .join(spaceSymbol),
            catType: [
                ...talkCatType.split(commaSymbol),
                ...awardCatType.split(commaSymbol)
            ]
                .filter(Boolean)
                .join(commaSymbol)
        };

        const apiUrl =
            locale === config.languages.zhHans
                ? url.hkvayb.SEARCH_ZH_IDS_INFORMATION
                : url.hkvayb.SEARCH_EN_IDS_INFORMATION;

        const tmpUrl = Object.keys(tmpFieldState).reduce((prevApiStr, key) => {
            const tmpValue = tmpFieldState[key] || "";
            return prevApiStr.replace(
                `{${key}}`,
                `${bs64Encode(
                    tmpValue.replaceAll(commaSymbol, `"${spaceSymbol}"`)
                )}`
            );
        }, apiUrl);

        dispatch({
            type: act.SET_HKVAYB_SEARCHBAR_QUERY_STRING,
            payload: tmpUrl
        });

        dispatch({
            type: act.SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE,
            payload: 1
        });

        history.push(`/${locale}/HkvaybResult`);
    };

    const handleResetButton = () => {
        setFieldState(prevState => ({
            ...Object.keys(prevState).reduce(
                (prevObj, key) => ({ ...prevObj, [key]: "" }),
                {}
            )
        }));
        dispatch({ type: act.SET_HKVAYB_SEARCHBAR_FIELDS_RESET });
    };

    useEffect(() => {
        dispatch({
            type: act.SET_HKVAYB_SEARCHBAR_FIELDS,
            payload: fieldState
        });
    }, [debKeyword, debTitle, debAuthor, debOrganizer]);

    useEffect(() => {
        dispatch({
            type: act.SET_HKVAYB_SEARCHBAR_FIELDS,
            payload: fieldState
        });
    }, [
        fieldState.year,
        fieldState.mediaType,
        fieldState.collectibleType,
        fieldState.education,
        fieldState.loc,
        fieldState.talkCatType,
        fieldState.awardCatType,
        fieldState.auctioneer,
        fieldState.category
    ]);

    useEffect(() => {
        setFieldState({ ...fields });
        return () => setFieldState({});
    }, []);

    return (
        <div className={classes.hkvayb_search_box}>
            <div className={classes.hkvayb_search_box_form_group_margin_bottom}>
                <div className={classes.hkvayb_search_box_form_sub_group}>
                    <CusInput
                        name="keyword"
                        value={fieldState.keyword}
                        placeholder={
                            <FormattedMessage
                                id="hkvayb.search.placeholder.keyword"
                                defaultMessage="Please enter a keyword"
                            />
                        }
                        onChange={handleOnChange}
                    />
                </div>
                <CusButton
                    basic
                    label={
                        <FormattedMessage
                            id="hkvayb.search.query.button"
                            defaultMessage="search"
                        />
                    }
                    onClick={handleSearchButton}
                />
            </div>
            <div className={classes.hkvayb_search_box_form_group}>
                <div className={classes.hkvayb_search_box_form_sub_group}>
                    <CusDropdown
                        name="category"
                        value={fieldState.category}
                        options={options.category}
                        placeholder={
                            <FormattedMessage
                                id="hkvayb.search.placeholder.category"
                                defaultMessage="Category (optional)"
                            />
                        }
                        onChange={handleOnChange}
                    />
                    <CusDropdown
                        name="year"
                        value={fieldState.year}
                        options={options.year}
                        placeholder={
                            <FormattedMessage
                                id="hkvayb.search.placeholder.year"
                                defaultMessage="Year (optional)"
                            />
                        }
                        onChange={handleOnChange}
                    />
                </div>
                <CusButton
                    invert
                    label={
                        <FormattedMessage
                            id="hkvayb.search.reset.button"
                            defaultMessage="reset"
                        />
                    }
                    onClick={handleResetButton}
                />
            </div>
            <div className={classes.hkvayb_search_box_form_group}>
                <CusCollapse>
                    <div
                        className={
                            classes.hkvayb_search_box_form_sub_group_margin_bottom
                        }
                    >
                        <CusInput
                            name="label"
                            value={fieldState.label}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.title"
                                    defaultMessage="Please enter a title"
                                />
                            }
                            onChange={handleOnChange}
                        />
                        <CusInput
                            name="author"
                            value={fieldState.author}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.author"
                                    defaultMessage="Please enter artist name"
                                />
                            }
                            onChange={handleOnChange}
                        />
                    </div>
                    <div
                        className={
                            classes.hkvayb_search_box_form_sub_group_margin_bottom
                        }
                    >
                        <CusInput
                            name="organizer"
                            value={fieldState.organizer}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.organizer"
                                    defaultMessage="Please enter organizer name"
                                />
                            }
                            onChange={handleOnChange}
                        />
                        <CusDropdown
                            name="loc"
                            value={fieldState.loc}
                            options={options.loc}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.loc"
                                    defaultMessage="loc (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                    </div>
                    <div
                        className={
                            classes.hkvayb_search_box_form_sub_group_margin_bottom
                        }
                    >
                        <CusDropdown
                            name="mediaType"
                            value={fieldState.mediaType}
                            options={options.mediaType}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.mediaType"
                                    defaultMessage="mediaType (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                        <CusDropdown
                            name="talkCatType"
                            value={fieldState.talkCatType}
                            options={options.talkCatType}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.talksSymposium"
                                    defaultMessage="talksSymposium (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                    </div>
                    <div
                        className={
                            classes.hkvayb_search_box_form_sub_group_margin_bottom
                        }
                    >
                        <CusDropdown
                            name="collectibleType"
                            value={fieldState.collectibleType}
                            options={options.collectibleType}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.collectibleType"
                                    defaultMessage="collectibleType (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                        <CusDropdown
                            name="awardCatType"
                            value={fieldState.awardCatType}
                            options={options.awardCatType}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.awardCatType"
                                    defaultMessage="awardCatType (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                    </div>
                    <div className={classes.hkvayb_search_box_form_sub_group}>
                        <CusDropdown
                            name="education"
                            value={fieldState.education}
                            options={options.education}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.education"
                                    defaultMessage="education (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                        <CusDropdown
                            name="auctioneer"
                            value={fieldState.auctioneer}
                            options={options.auctioneer}
                            placeholder={
                                <FormattedMessage
                                    id="hkvayb.search.placeholder.auctioneer"
                                    defaultMessage="auctioneer (optional)"
                                />
                            }
                            onChange={handleOnChange}
                        />
                    </div>
                </CusCollapse>
            </div>
        </div>
    );
};

export default CusSearchBox;
