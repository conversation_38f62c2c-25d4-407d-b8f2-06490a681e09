import { List, Segment } from "semantic-ui-react";
import { cvtOntoClassNameLocale } from "../ontoUtils/ontoCommon";
import React, { useContext } from "react";
import { StoreContext } from "../../../store/StoreProvider";

const ClassCategory = ({ active, setActive, infoList, onMenuClick }) => {
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;

    // 將 className 轉換成 中英文 list, 用於中英文切換
    const classNameIntl18 = infoList.reduce((initObj, cur) => {
        initObj[cur.className] = { en: cur.className, zh: cur.name };
        return initObj;
    }, {});

    return (
        <Segment>
            <List link>
                {infoList.map(info => {
                    return (
                        <List.Item
                            key={info.name}
                            as="a"
                            active={info.className === active}
                            data-name={info.className}
                            onClick={(e, value) => {
                                setActive(value["data-name"]);
                                onMenuClick();
                            }}
                        >
                            {cvtOntoClassNameLocale(
                                info.className,
                                classNameIntl18,
                                locale
                            )}
                        </List.Item>
                    );
                })}
            </List>
        </Segment>
    );
};

export default ClassCategory;
