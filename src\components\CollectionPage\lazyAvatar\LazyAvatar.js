import React from "react";
import LazyLoad from "react-lazyload";
import {
    LazyLoadComponent,
    LazyLoadImage
} from "react-lazy-load-image-component";

// css
import "./lazyAvatar.scss";

const LazyAvatar = ({ className, size, imgSrc, bgColor }) => {
    return (
        <div
            className={className}
            style={{ backgroundColor: bgColor, overflow: "hidden" }}
        >
            <LazyLoadComponent className={"lazyAvatar"}>
                {imgSrc && (
                    <LazyLoadImage src={imgSrc} width={size} alt="logo" />
                )}
            </LazyLoadComponent>
        </div>
    );
};

export default LazyAvatar;
