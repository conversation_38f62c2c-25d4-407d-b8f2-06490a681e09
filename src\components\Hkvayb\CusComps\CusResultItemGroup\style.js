import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_result_item_group: props => ({
        padding: "23.5px 24px 23.5px 24px",
        display: "flex",
        justifyContent: "space-between",
        flexWrap: "nowrap",
        alignItems: "center",
        "&:hover": {},
        "&:active": {
            backgroundColor: "rgb(179 179 179)"
        },
        ...props.hkvayb_result_item_group
    })
});

export default useStyles;
