node_modules/
npm-debug.log

.idea/
.vscode/
dist/
dist-ghost/
.DS_Store
.build/
build/
backup/

yarn-errpr.log

.env.production
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# === Katalon 測試專案 hkbdb-web 忽略設定 ===
tests/katalon/hkbdb-web/.classpath
tests/katalon/hkbdb-web/.project
tests/katalon/hkbdb-web/.cache/
tests/katalon/hkbdb-web/.gradle/
tests/katalon/hkbdb-web/bin/
tests/katalon/hkbdb-web/Libs/
tests/katalon/hkbdb-web/Reports/
tests/katalon/hkbdb-web/build/
tests/katalon/hkbdb-web/output/

# 保留 output/.gitkeep，不忽略它
!tests/katalon/hkbdb-web/output/.gitkeep