import Act from "../actions";

const initState = {
    keyword: "",
    types: [],
    fields: {
        title: "",
        author: "",
        mechanism: "",
        year: "",
        area: "",
        media: "",
        talksSymposiumType: "",
        collectibleType: "",
        awardEventType: "",
        educationType: "",
        auctionType: ""
    },
    result: {},
    count: 0,
    isLoading: false,
    error: {
        type: "",
        message: ""
    },
    reset: false,
    duration: "",
    currentPage: 0,
    rowsPerPage: 10
};

const searchPage2Reducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_SEARCHPAGE_SEARCHBAR_KEYWORD:
            return { ...state, keyword: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_TYPES:
            return { ...state, types: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_FIELDS:
            return { ...state, fields: { ...state.fields, ...action.payload } };
        case Act.CLE_SEARCHPAGE_SEARCHBAR_FIELDS:
            return { ...state, fields: { ...initState.fields } };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESULT:
            return { ...state, result: { ...action.payload } };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESULT_COUNT:
            return { ...state, count: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_ISLOADING:
            return { ...state, isLoading: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_ERROR:
            return { ...state, error: { ...state.error, ...action.payload } };
        case Act.CLE_SEARCHPAGE_SEARCHBAR_ERROR:
            return { ...state, error: initState.error };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESET:
            return { ...initState, reset: !state.reset };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESULT_DURATION:
            return { ...state, duration: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESULT_PAGE:
            return { ...state, currentPage: action.payload };
        case Act.SET_SEARCHPAGE_SEARCHBAR_RESULT_ROWS_PAGE:
            return { ...state, rowsPerPage: action.payload };
        default:
            return state;
    }
};

export default searchPage2Reducer;
