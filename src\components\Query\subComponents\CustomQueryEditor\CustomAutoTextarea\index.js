// react
import React, { useState, useEffect, useRef, useContext } from "react";

// custom css
import "./css/styles.css";

// common
import { isEmpty } from "../../../../commons";

// This TextArea doesn't work when keyDown the tab button, so we don't use it.
import { Form } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

const index = ({ rest }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { selectedQueryId, queries } = state.query;
    //
    const textareaRef = useRef(null);
    const callbacksList = useRef([]);
    const textAreaStyle = {
        width: "100%",
        fontSize: "small",
        minHeight: "160px"
    };
    const placeholderString = "Type Sparql Query, e.g. \n\nSELECT * \nWHERE {\n\t?s ?p ?o .\n}";
    const suggestions = [
        "SELECT",
        "WHERE",
        "GRAPH",
        "DISTINCT",
        "REDUCED",
        "COUNT",
        "SUM",
        "AVG",
        "MIN",
        "MAX",
        "ORDER BY",
        "GROUP BY",
        "<http://",
        "<https://",
        "OPTIONAL",
        "UNION",
        "LIMIT",
        "OFFSET ",
        "PREFIX",
        "FILTER",
        "sameTerm",
        "langMatches",
        "regex",
        "lang",
        "langMatches",
        "str",
        "AS",
        "isLiteral",
        "isBlank",
        "isIRI",
        "bound",
        "datatype",
        "SELECT * \nWHERE {\n\t?s ?p ?o .\n}"
    ];

    const [textareaState, setTextareaState] = useState({
        symbol: /[\s\n\t]/,
        activeSuggestion: 0,
        filteredSuggestions: [],
        showSuggestions: false,
        userInput: "",
        userInputLastWord: "",
        userInputLastWordPosition: ""
    });

    const handleTextareaStateChange = e => {
        // console.log("I am handleTextareaStateChange");
        const { symbol } = textareaState;
        // get textarea cursor position
        const { selectionStart: start, selectionEnd: end } = e.target;
        // 取得 input value
        const userInput = e.target.value;
        // 取得最後一組字詞
        let lastWord, lasWordPosition;
        if (start === end && start === userInput.length) {
            lastWord = userInput.split(symbol).pop() || "";
            lasWordPosition = "right";
        } else {
            const entryPoint = "@IAMHERE";
            const newUserInput = userInput.slice(0, start) + entryPoint + userInput.slice(end);
            lastWord = newUserInput.split(symbol).filter(word => word.includes(entryPoint))[0].replace(entryPoint, "");
            lasWordPosition = "middle";
        }
        // 透過最後一組字詞找出關鍵字
        const filteredSuggestions = suggestions.filter(
            suggestion =>
                // 是否有輸入新的關鍵字
                lastWord &&
                // 判斷是否有?(變數符號)
                !lastWord.includes("?") &&
                // 最少要有兩個字元才能出現建議清單
                lastWord.length >= 2 &&
                // 關鍵字是否出現再清單中
                suggestion.toLowerCase().indexOf(lastWord.toLowerCase()) > -1
        );
        // update textareaState
        setTextareaState({
            ...textareaState,
            activeSuggestion: 0,
            filteredSuggestions,
            showSuggestions: true,
            userInput: e.target.value,
            userInputLastWord: lastWord,
            userInputLastWordPosition: lasWordPosition
        });
        // save value to queryReducer state
        dispatch({
            type: Act.QUERY_QUERY_STRING_SET,
            payload: e.target.value
        });
    };

    const setTextareaStateWithCallback = (newState, callback) => {
        setTextareaState(newState);
        if (callback) callbacksList.current.push(callback);
    };

    useEffect(() => {
        callbacksList.current.forEach(callback => callback());
        callbacksList.current = [];
    }, [textareaState]);

    // handle keyDown event
    const handleKeyDown = e => {
        // get textarea cursor position
        const { selectionStart: start, selectionEnd: end } = e.target;
        // get textarea params
        const {
            activeSuggestion,
            filteredSuggestions,
            showSuggestions,
            userInput,
            userInputLastWord,
            userInputLastWordPosition
        } = textareaState;
        // handle Tab event
        if (e.keyCode === 9) {
            // cancel default event
            e.preventDefault();
            // update textarea input value
            setTextareaStateWithCallback(
                {
                    ...textareaState,
                    // add tab symbol.
                    userInput: userInput.slice(0, start) + "\t" + userInput.slice(end),
                },
                // using callback function when textarea cursor position has been changed.
                () => textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 1
            );
        }
        // handle Enter event with suggestions
        else if (e.keyCode === 13 && showSuggestions && !isEmpty(filteredSuggestions)) {
            // cancel default event
            e.preventDefault();
            // update userInput
            let newUserInput;
            if (userInputLastWordPosition === "right") {
                newUserInput = userInputLastWord.includes("?")
                    ? userInput
                    : userInput.slice(0, userInput.length - userInputLastWord.length) + filteredSuggestions[activeSuggestion];
                // update textareaState
                setTextareaState({
                    ...textareaState,
                    activeSuggestion: 0,
                    showSuggestions: false,
                    userInput: newUserInput
                });
            } else {
                const leftInput = userInput.slice(0, start - userInputLastWord.length);
                const middleInput = filteredSuggestions[activeSuggestion];
                const rightInput = userInput.slice(end);
                const newInputCursorPosition = (leftInput + middleInput).length;
                newUserInput = userInputLastWord.includes("?")
                    ? userInput
                    : leftInput + middleInput + rightInput;
                // update textareaState with callback
                setTextareaStateWithCallback(
                    {
                        ...textareaState,
                        activeSuggestion: 0,
                        showSuggestions: false,
                        userInput: newUserInput
                    },
                    // using callback function when textarea cursor position has been changed.
                    () => textareaRef.current.selectionStart = textareaRef.current.selectionEnd = newInputCursorPosition
                );
            }
        }
        // handle Enter event with "tab" symbol
        else if (e.keyCode === 13) {
            // cancel default event
            e.preventDefault();
            const tabSymbol = "\t";
            const leftTabCount = (userInput.slice(0, start).match(/{/g) || []).length;
            const rightTabCount = (userInput.slice(0, start).match(/}/g) || []).length;
            const tabCount = leftTabCount - rightTabCount;
            let newUserInput;
            if (userInput.slice(start - 1, start) === "{" && userInput.slice(start, start + 1) === "}") {
                newUserInput = userInput.slice(0, start) + "\n" + tabSymbol.repeat(tabCount) + "\n" + tabSymbol.repeat(tabCount-1) + userInput.slice(end);
            } else {
                newUserInput = userInput.slice(0, start) + "\n" + tabSymbol.repeat(tabCount) + userInput.slice(end);
            }
            // update textareaState with callback
            setTextareaStateWithCallback(
                {
                    ...textareaState,
                    // add tab symbol.
                    userInput: newUserInput,
                },
                // using callback function when textarea cursor position has been changed.
                () => textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 1 + tabCount
            );
        }
        else if (e.keyCode === 219) {
            // cancel default event
            e.preventDefault();
            const leftUserInput = userInput.slice(0, start) + "{";
            const rightUserInput = "}" + userInput.slice(end);
            const newUserInput = leftUserInput + rightUserInput;
            const newUserInputCursorPosition = leftUserInput.length;
            // update textareaState with callback
            setTextareaStateWithCallback(
                {
                    ...textareaState,
                    // add tab symbol.
                    userInput: newUserInput
                },
                // using callback function when textarea cursor position has been changed.
                () => textareaRef.current.selectionStart = textareaRef.current.selectionEnd = newUserInputCursorPosition
            );
        }
        // handle Up Arrow event
        else if (e.keyCode === 38 && showSuggestions && !isEmpty(filteredSuggestions)) {
            // cancel default event
            e.preventDefault();
            // 已經到第一個選項了不必再變更
            if (activeSuggestion === 0) {
                return;
            }
            // update textareaState
            setTextareaState({
                ...textareaState,
                activeSuggestion: activeSuggestion - 1
            });
        }
        // handle Down Arrow event
        else if (e.keyCode === 40 && showSuggestions && !isEmpty(filteredSuggestions)) {
            // cancel default event
            e.preventDefault();
            // 已經到最後一個選項了不必再變更
            if (activeSuggestion - 1 === filteredSuggestions.length) {
                return;
            }
            // update textareaState
            setTextareaState({
                ...textareaState,
                activeSuggestion: activeSuggestion + 1
            });
        }
        // handle Esc event
        else if (e.keyCode === 27 && showSuggestions && !isEmpty(filteredSuggestions)) {
            // update textareaState
            setTextareaState({
                ...textareaState,
                showSuggestions: false
            });
        }
    };

    const SuggestionsList = () => {
        // get some textarea params
        const {
            activeSuggestion,
            filteredSuggestions,
            showSuggestions,
            userInput
        } = textareaState;
        // return suggestions list
        if (showSuggestions && userInput) {
            if (filteredSuggestions.length) {
                return (
                    <ul className="suggestions">
                        {filteredSuggestions.map((suggestion, index) => {
                            let className;
                            // Flag the active suggestion with a class
                            if (index === activeSuggestion) {
                                className = "suggestion-active";
                            }
                            return (
                                <li
                                    className={className}
                                    key={suggestion}
                                    // onClick={handleOnClick}
                                >
                                    {suggestion}
                                </li>
                            );
                        })}
                    </ul>
                );
            }
        }
        return null;
    };
    //
    const handleLoadSelectedQuery = () => {
        if (!isEmpty(selectedQueryId)) {
            const selectedQuery = queries.filter(
                query => query.id === selectedQueryId
            )[0];
            if (!isEmpty(selectedQuery)) {
                if (selectedQuery.query) {
                    setTextareaState({
                        ...textareaState,
                        userInput: selectedQuery.query
                    });
                    // save value to queryReducer state
                    dispatch({
                        type: Act.QUERY_QUERY_STRING_SET,
                        payload: selectedQuery.query
                    });
                }
            }
        }
    };
    useEffect(() => {
        handleLoadSelectedQuery();
    }, [selectedQueryId]);
    //
    return (
        <Form>
            {/* Sqarql editor */}
            <textarea
                {...rest}
                rows="8"
                ref={textareaRef}
                style={textAreaStyle}
                placeholder={placeholderString}
                onKeyDown={handleKeyDown}
                onChange={handleTextareaStateChange}
                value={textareaState.userInput}
            />
            {/* show suggestions list */}
            <SuggestionsList />
        </Form>
    );
};

export default index;
