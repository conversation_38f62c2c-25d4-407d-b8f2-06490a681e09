// react
import React, { useContext, useState } from "react";

// ui
import { Button } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// common
import { isEmpty } from "../../../../../../common/codes";
import { injectIntl, FormattedMessage } from "react-intl";

const CustomCancelButton = () => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryString, selectedQueryId } = state.query;
    //
    const [isLoad, setIsLoad] = useState(false);
    // handel sena query
    const handleCancel = async () => {
        // clean query Id
        if (!isEmpty(selectedQueryId)) {
            dispatch({ type: Act.QUERY_SELECTED_QUERY_ID_CEL });
        }
    };
    //
    return (
        <Button
            size="tiny"
            onClick={handleCancel}
            disabled={isEmpty(queryString)}
            loading={isLoad}
        >
            <FormattedMessage
                id="custom.cancelButton"
                defaultMessage="Cancel"
            />
        </Button>
    );
};

export default injectIntl(CustomCancelButton);
