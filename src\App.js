import React, { useState, useEffect, useContext } from "react";
import { with<PERSON><PERSON><PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, Dimmer } from "semantic-ui-react";
import FirebaseLayer from "./components/authenticate/FirebaseLayer";
import ApiDataLayer from "./ApiDataLayer";

// store
import { StoreContext } from "./store/StoreProvider";

// component
import RouterManager from "./routes/RouterManager";

// hook
import useTokenReady from "./hook/useTokenReady";
import useLocaleReady from "./hook/useLocaleReady";

// css
import "./style/App.scss";
import "./style/firebaseui-styling.global.css";

// lang switch
import { addLocaleData, IntlProvider } from "react-intl";
import en from "react-intl/locale-data/en";
import zh from "react-intl/locale-data/zh";
import {
    checkLanguageWithPaths,
    changeLang
} from "./common/codes/utils/languageTools";
import translations from "./lang";
import Act from "./store/actions";
addLocaleData([...en, ...zh]);

const App = ({ location, history }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { main } = state;
    const { production } = main;
    const [loading, setLoading] = useState(true);
    const [showChildren, setShowChildren] = useState(false);
    // hook for token and locale
    const { tokenReady, axiosReady } = useTokenReady();
    const { localLocale, localeReady } = useLocaleReady();

    // // 是否為 production mode => production mode 追溯至 firebase 設定
    const isProductionMode = _production => {
        return _production && _production === "true";
    };

    useEffect(() => {
        const { pathname } = location;
        const pathLocale = checkLanguageWithPaths(pathname);

        /* 剛進入畫面時，如果沒有進入語系子 route */
        changeLang({
            history,
            currentLanguage: pathLocale,
            nextLanguage: pathLocale
        });
        dispatch({
            type: Act.SET_USER_LOCALE,
            payload: pathLocale
        });
    }, []);

    // 切換是否顯示 loading 及 FirebaseLayer 的 children
    useEffect(() => {
        if (isProductionMode(production)) {
            if (tokenReady && axiosReady && localeReady) {
                setLoading(false);
                setShowChildren(true);
            }
        } else {
            setLoading(false);
            setShowChildren(true);
        }
    }, [production, tokenReady, axiosReady, localeReady]);

    return (
        <IntlProvider locale={localLocale} messages={translations[localLocale]}>
            <React.Fragment>
                {loading && (
                    <React.Fragment>
                        <Dimmer active inverted>
                            <Loader inverted size={"large"}>
                                Loading
                            </Loader>
                        </Dimmer>
                    </React.Fragment>
                )}

                {/* FirebaseLayer: 使用者驗證, firestore, realtimeDB, storage */}
                <FirebaseLayer>
                    {/* 前置作業皆完成才會 fetch api & 顯示 children */}
                    {showChildren && (
                        <React.Fragment>
                            {/* ApiDataLayer: fetch hkbdb api */}
                            <ApiDataLayer
                                location={location}
                                history={history}
                            />

                            {/* RouterManager: 依路由顯示頁面 */}
                            <RouterManager />
                        </React.Fragment>
                    )}
                </FirebaseLayer>
            </React.Fragment>
        </IntlProvider>
    );
};

export default withRouter(App);
