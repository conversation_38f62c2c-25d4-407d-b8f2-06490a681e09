import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_footer_scroll_top: props => ({
        display: "flex",
        justifyContent: "flex-end",
        ...props.hkvayb_footer_scroll_top
    }),
    hkvayb_footer_scroll_top_buttom: props => ({
        width: "48px",
        height: "48px",
        margin: "8px 24px 0 0",
        padding: "8px",
        backgroundColor: "#333",
        cursor: "pointer",
        "&:hover": {
            backgroundColor: "#5e5e5e"
        },
        ...props.hkvayb_footer_scroll_top_buttom
    }),
    hkvayb_footer_scroll_top_arrow: props => ({
        width: "32px",
        height: "32px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/scroll_top_arrow_3x.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        ...props.hkvayb_footer_scroll_top_arrow
    }),
    hkvayb_footer: props => ({
        height: "137px",
        margin: "24px 0 0",
        padding: "40px 72px",
        backgroundColor: "#8d795c",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        alignContent: "center",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexWrap: "wrap",
            height: "unset"
        },
        ...props.hkvayb_footer
    }),
    hkvayb_footer_left_div: props => ({
        display: "flex",
        alignItems: "center",
        ...props.hkvayb_footer_left_div
    }),
    hkvayb_footer_left_logo: props => ({
        // width: "199px",
        width: "250px",
        height: "57px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/faa_logo.png')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "contain",
        backgroundPosition: "center",
        marginRight: "16px",
        ...props.hkvayb_footer_left_logo
    }),
    hkvayb_footer_right_logo: props => ({
        // width: "199px",
        width: "300px",
        height: "57px",
        backgroundImage:
            "url('https://fs-root.daoyidh.com/hkvayb/desktop/the_chinese_university_of_hong_kong_library_logo.png')",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        backgroundSize: "contain",
        ...props.hkvayb_footer_right_logo
    }),
    hkvayb_footer_copyright: props => ({
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.75",
        letterSpacing: "0.26px",
        textAlign: "left",
        color: "#333",
        ...props.hkvayb_footer_copyright
    })
});

export default useStyles;
