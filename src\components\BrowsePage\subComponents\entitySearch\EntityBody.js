import { List } from "semantic-ui-react";
import { orgUrl, peopleUrl } from "../../../../services/common";
import { CLASS_PREFIX } from "../../../../config/config-ontology";
import queryString from "query-string";
import { bs64Encode } from "../../../../common/codes";
import React, { useContext, useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import { incValueCount } from "../../../../api/firebase/firebaseAction";
import { StoreContext } from "../../../../store/StoreProvider";

/**
 * remove prefix form id
 *
 * @param {string} id. e.g. PER金庸
 * @param {string} prefix. e.g. PER
 * @returns {string} . e.g. 金庸
 */
const removeStartPrefix = (id, prefix) => {
    if (id && id.startsWith(prefix)) {
        return id.replace(prefix, "");
    }
    return id;
};

// 適用頁面: 搜尋頁,人物頁,組織頁
function EntityBody({ bindings, classType }) {
    const [state] = useContext(StoreContext);
    const { fbReducer } = state;
    // route
    const history = useHistory();
    //
    const [list, setList] = useState([]);

    //
    useEffect(() => {
        if (Array.isArray(bindings)) {
            const tmpList = bindings.map(b => {
                // 不管是 Person 或 Organization,
                // 第二次 fetch 詳細資料的 response.data 的 key 都是 perId, srcName
                let url = "";
                if ((classType || "").toLowerCase() === "person") {
                    url = peopleUrl(
                        removeStartPrefix(b.perId, CLASS_PREFIX.Person),
                        queryString.stringify({
                            name: bs64Encode(b.name || "")
                        })
                    );
                } else {
                    // Organization
                    url = orgUrl(
                        removeStartPrefix(b.perId, CLASS_PREFIX.Organization),
                        queryString.stringify({
                            name: bs64Encode(b.name || "")
                        })
                    );
                }
                return {
                    ...(b || {}),
                    url: url
                };
            });
            setList(tmpList);
        }
    }, [bindings]);
    //
    const onItemClick = item => e => {
        e.preventDefault();
        //
        if (item?.url) {
            // 另開分頁
            // window.open(item.url, "_blank", "noopener,noreferrer");

            // 熱門搜尋：文字雲
            incValueCount({ keyword: item, fbReducer });

            // 在原tab開啟
            history.push(item.url);
        }
    };

    if (!(Array.isArray(list) && list.length > 0)) return null;

    return (
        <List size="small" className={"entityBody__list"}>
            {list.map((b, idx) => {
                return (
                    <List.Item
                        key={idx}
                        value={b.name}
                        className={"entityBody__list--item"}
                    >
                        <List.Content
                            as="a"
                            href={b.url}
                            className={"entityBody__list--item-content"}
                            onClick={onItemClick(b)}
                        >
                            {b.name}
                        </List.Content>
                    </List.Item>
                );
            })}
        </List>
    );
}

export default EntityBody;
