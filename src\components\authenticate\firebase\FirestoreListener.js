import React, { useEffect, useContext } from "react";
import { StoreContext } from "../../../store/StoreProvider";
import { apiCollDocListener } from "./firestoreApi";
import role from "../../../App-role";
import authority from "../../../App-authority";
import Act from "../../../store/actions";
import userReducer from "../../../store/reducers/userReducer";
import { safeGet } from "../../../common/codes";

const FirestoreListener = ({ firestoreDb }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { role: userRole } = user;

    // get backend web url
    useEffect(() => {
        apiCollDocListener(firestoreDb, "api", "api-config")
            .then(res => {
                /* example
                {
	                backend-web: {
		                development: {
			                url: "https://hkbdb2-backend-web.daoyidh.com/"
		                },
		                production: {
			                url: "https://hkbdb-backend-web.daoyidh.com/"
		                }
	                }
                }
                */
                // 依照環境選取設定 url
                const backendSetting = safeGet(
                    res,
                    ["backend-web", process.env.REACT_APP_MODE],
                    {}
                );

                if (authority.BackendWeb.includes(userRole)) {
                    dispatch({
                        type: Act.SET_BACKEND_WEB,
                        payload: Object.assign({}, backendSetting)
                    });
                }

                // if (authority.BackendWeb.includes(userRole)) {
                //     dispatch({
                //         type: Act.SET_BACKEND_WEB,
                //         payload: Object.assign({}, res["backend-web"])
                //     });
                // }

                // 正式站使用 firestore doc[api-config].production. true => 代表開啟 token 驗證
                // 測試站使用 firestore doc[api-config].develop. if true => 代表開啟 token 驗證
                dispatch({
                    type: Act.SET_PRODUCTION,
                    payload:
                        process.env.REACT_APP_MODE === "production"
                            ? res["production"]
                            : res["develop"]
                });
            })
            .catch(err => {
                console.log(err);
            });
    }, [authority, userRole]);

    useEffect(() => {
        // 從 firestore 取得 email 設定
        apiCollDocListener(firestoreDb, "web", "email-config")
            .then(data => {
                dispatch({
                    type: Act.SETTINGS_EMAIL_CONFIG,
                    payload: data
                });
            })
            .catch(err => {
                console.log(err);
            });
        // 從 firestore 取得 signup 設定(包含 allowable email doamin,termsOfService, privacyPolicy)
        apiCollDocListener(firestoreDb, "web", "signup-config")
            .then(data => {
                dispatch({
                    type: Act.SETTINGS_SIGNUP_CONFIG,
                    payload: data
                });
            })
            .catch(err => {
                console.log(err);
            });
    }, []);

    return null;
};

export default FirestoreListener;
