// react
import React from "react";

// ui
import { Container } from "semantic-ui-react";

// custom
import CusDurationSS from "./CusDurationSS";
import CustomTable from "./CustomTable";
import CustomPagination from "./CustomPagination";

const CustomQueryResult = () => {
    //
    const customDivStyle = {
        width: "100%",
        overflowX: "auto"
    };
    //
    return (
        <Container textAlign="center">
            <div style={customDivStyle}>
                <CusDurationSS />
                <CustomTable />
            </div>
            <br />
            <CustomPagination />
        </Container>
    );
};

export default CustomQueryResult;
