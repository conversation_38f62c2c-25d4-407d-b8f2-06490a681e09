// light style for societyNetwork
import React from "react";
import { FormattedMessage } from "react-intl";
import config from "../../../config/config";

export const ontoGraphStyle = {
    display: "flex",
    justifyContent: "center",
    margin: "10px",
    backgroundColor: "white",
    border: "1px solid #c9c9c9",
    height: "100%",
    width: "100%",
    borderRadius: "5px"
};

const personList = [
    {
        name: "基本資料",
        className: "Person"
    },
    {
        name: "人名資料",
        className: "NameNode"
    },
    {
        name: "學歷",
        className: "EducationEvent"
    },
    {
        name: "工作經驗",
        className: "EmploymentEvent"
    },
    {
        name: "出版著作",
        className: "Publication"
    },
    {
        name: "單篇文章",
        className: "Article"
    },
    {
        name: "其它作品",
        className: "OtherWork"
    },
    {
        name: "相關組織",
        className: "OrganizationEvent"
    },
    {
        name: "人際關係",
        className: "RelationEvent"
    },
    {
        name: "關涉事件",
        className: "Event"
    },
    {
        name: "曾獲獎項",
        className: "AwardEvent"
    }
];

const orgList = [
    {
        name: "基本資料",
        className: "Organization"
    },
    {
        name: "組織名稱",
        className: "NameNode"
    },
    {
        name: "組織成員",
        className: "OrganizationEvent"
    },
    {
        name: "出版著作",
        className: "Publication"
    },
    {
        name: "單篇文章",
        className: "Article"
    },
    {
        name: "其它作品",
        className: "OtherWork"
    },
    {
        name: "關涉事件",
        className: "Event"
    }
];

const otherList = [
    {
        name: "地點",
        className: "Place"
    },
    {
        name: "時間",
        className: "DateEvent"
    },
    {
        name: "洲",
        className: "Continent"
    },
    {
        name: "國家",
        className: "Nation"
    },
    {
        name: "省分",
        className: "Province"
    },
    {
        name: "城市",
        className: "City"
    },
    {
        name: "鄉鎮",
        className: "Township"
    }
];
//
const ontoConfig = {
    graphList: personList.concat(orgList).concat(otherList),
    ontoTitle: [
        {
            title: (
                <FormattedMessage
                    id={"ontology.person"}
                    defaultMessage={"Person"}
                />
            ),
            type: config.DEF_PER_DOMAIN,
            content: personList
        },
        {
            title: (
                <FormattedMessage
                    id={"ontology.organization"}
                    defaultMessage={"Organization"}
                />
            ),
            type: config.DEF_ORG_DOMAIN,
            content: orgList
        },
        {
            title: (
                <FormattedMessage
                    id={"ontology.other"}
                    defaultMessage={"Other"}
                />
            ),
            type: config.DEF_OTR_DOMAIN,
            content: otherList
        }
    ]
};

export default ontoConfig;
