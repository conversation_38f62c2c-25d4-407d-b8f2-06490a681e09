import { defineMessages } from "react-intl";

const intlMsgs = defineMessages({
    information: {
        id: "people.Information",
        defaultMessage: "Information"
    },
    relation: {
        id: "people.Relation",
        defaultMessage: "Relation"
    },
    timeline: {
        id: "people.timeline",
        defaultMessage: "Timeline"
    },
    SNA: {
        id: "people.sna",
        defaultMessage: "SNA"
    },
    familyTree: {
        id: "people.familyTree",
        defaultMessage: "FamilyTree"
    },
    basic: {
        id: "people.Information.basic",
        defaultMessage: "Basic"
    },
    imprisonment: {
        id: "people.Information.imprisonment",
        defaultMessage: "Imprisonment"
    },
    family: {
        id: "people.Information.family",
        defaultMessage: "Family"
    },
    education: {
        id: "people.Information.education",
        defaultMessage: "Education"
    },
    employment: {
        id: "people.Information.employment",
        defaultMessage: "Employment"
    },
    membership: {
        id: "people.Information.membership",
        defaultMessage: "Membership"
    },
    publication: {
        id: "people.Information.publication",
        defaultMessage: "Publication"
    },
    article: {
        id: "people.Information.article",
        defaultMessage: "Article"
    },
    event: {
        id: "people.Information.event",
        defaultMessage: "Event"
    },
    award: {
        id: "people.Information.award",
        defaultMessage: "Award"
    },
    otherwork: {
        id: "people.Information.otherwork",
        defaultMessage: "Other Work"
    },
    status: {
        id: "people.Information.status",
        defaultMessage: "Status"
    },
    comment: {
        id: "people.Information.comment",
        defaultMessage: "Comment"
    },
    source: {
        id: "people.Information.source",
        defaultMessage: "Source"
    },
    snaTarget: {
        id: "people.sna.target",
        defaultMessage: "Target："
    },
    snaPeople: {
        id: "people.sna.people",
        defaultMessage: "People："
    },
    snaOrganization: {
        id: "people.sna.organization",
        defaultMessage: "Organization："
    },
    snaMember: {
        id: "people.sna.member",
        defaultMessage: "Member："
    },
    snaLinkLabel: {
        id: "people.sna.linkLabel",
        defaultMessage: "Link label："
    },
    member: {
        id: "organization.Information.member",
        defaultMessage: "Member"
    },
    nameNode: {
        id: "people.Information.nameNode",
        defaultMessage: "NameNode"
    },
    nameNode_org: {
        id: "people.Information.nameNode_org",
        defaultMessage: "NameNode"
    }
});

export { intlMsgs };
