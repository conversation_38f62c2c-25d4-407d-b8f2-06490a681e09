import React, { useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

// custom
import ProFileTable from "./ProFileTable";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

// common
import { isEmpty } from "../../../../common/codes";

// api
import { updateUser } from "../../../../api/firebase/realtimeDatabase";

const EditButton = ({ user }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { account } = state;
    const { changedRole } = account;
    const { uid, role } = changedRole;
    // button
    const [open, setOpen] = useState(false);

    const handleUpdate = async () => {
        if (!isEmpty(uid) && !isEmpty(changedRole)) {
            const data = { role };
            // const data = {
            //     info: {
            //         role
            //     }
            // };

            // update
            await updateUser(uid, data);
            // update user role completed
            dispatch({
                type: Act.FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL,
                payload: `update-${uid}-${role}-${new Date().getTime()}`
            });
            // alert message
            const message = {
                title: "Update User",
                success: 1,
                error: 0,
                renderSignal: `update-user-${new Date().getTime()}`
            };
            // alert message dispatch
            dispatch({
                type: Act.DATA_MESSAGE,
                payload: message
            });
            // close modal
            setOpen(false);
        }
    };

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            trigger={<Button>編輯</Button>}
        >
            <Modal.Header>個人資料</Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description>
                    <ProFileTable key={user.uid} user={user} />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleUpdate} color="green">
                    確認
                </Button>
                <Button onClick={() => setOpen(false)} color="red">
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default EditButton;
