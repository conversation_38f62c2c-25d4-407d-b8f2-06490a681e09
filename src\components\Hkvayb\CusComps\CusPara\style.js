import { makeStyles } from "@material-ui/styles";

const useStyles = makeStyles({
    root: {},
    hkvayb_div_wrap: props => ({
        display: "flex",
        marginBlockStart: "1.25rem",
        marginBlockEnd: "1.25rem",
        alignItems: "center",
        // whiteSpace: "nowrap",
        flexWrap: "wrap",
        ...props.hkvayb_div_wrap
    }),
    hkvayb_div_nowrap: props => ({
        display: "flex",
        marginBlockStart: "1.25rem",
        marginBlockEnd: "1.25rem",
        alignItems: "center",
        // eslint-disable-next-line
        ["@media screen and (max-width: 600px)"]: {
            flexWrap: "wrap"
        },
        ...props.hkvayb_div_nowrap
    })
});

export default useStyles;
