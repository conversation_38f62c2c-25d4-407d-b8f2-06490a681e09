import React, { useContext } from "react";
import { Route, Switch } from "react-router-dom";
import { StoreContext } from "../store/StoreProvider";
import routes, { ROUTE_ID } from "../App-route";
import RouteProtectedHoc from "./RouteProtectedHoc";
import role from "../App-role";
import NotFound from "../pages/NotFound";
import NotAllow from "../pages/NotAllow";

const RouterManager = () => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const safeRole = user && user.role ? user.role : role.anonymous;

    return (
        <Switch>
            {routes &&
                routes.map(route => {
                    if (route.authority.includes(safeRole) && route.public) {
                        return (
                            <RouteProtectedHoc
                                // exact
                                exact={route.id !== ROUTE_ID.MapGuide} // 為 MapGuide 路由關閉 exact 匹配
                                key={route.id}
                                path={route.path}
                                // public={route.public}
                                component={route.component}
                            />
                        );
                    } else {
                        return (
                            <Route
                                exact
                                key={route.id}
                                path={route.path}
                                component={NotAllow}
                            />
                        );
                    }
                })}

            <Route component={NotFound} />
        </Switch>
    );
};
export default RouterManager;
