import React, { Fragment, useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";

// custom
import CustomMenu from "./CustomMenuFlexDelete";
import CustomAlertMessage from "./CustomAlertMessage";

// lang
import { FormattedMessage } from "react-intl";

// common
import { isEmpty, isNotEmpty, safeGet } from "../../../../common/codes";

// api
import { Api, deleteHkbdbData, readHkbdbData } from "../../../../api/hkbdb/Api";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { bs64DecodeId } from "../../../../common/codes/jenaHelper";
import { EVT_PREFIX } from "../../../../config/config-ontology";

const CustomDeleteModalFlex = ({
    open,
    setOpen,
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    //
    const [state, dispatch] = useContext(StoreContext);
    const { personInformation, information, user } = state;
    const { paginations } = information;
    const { localActivePage } = safeGet(paginations, [ontologyType], {});
    const infoReloadFunc = safeGet(
        personInformation,
        ["infoReloadFunc", ontologyType],
        () => {}
    );
    const infoSearchFunc = safeGet(
        personInformation,
        ["infoSearchFunc", ontologyType],
        () => {}
    );
    //
    const [isLoading, setIsLoading] = useState(() => false);
    //
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 5 * 1000
    }));
    //
    const [deleteResults, setDeleteResults] = useState(() => ({
        deletedRowIds: [],
        success: 0,
        failed: 0
    }));
    //
    const handleInitDeletedData = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            deletedData: []
        }));
    };
    const handleInitIsDeleted = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            isDeleted: false,
            deletedRowIds: []
        }));
    };
    const handleInitResult = () => {
        setDeleteResults(prevState => ({
            ...prevState,
            deletedRowIds: [],
            success: 0,
            failed: 0
        }));
    };
    const handleRefresh = () => {
        if (deleteResults.success) {
            // 通知 API 重新去讀取，由於資料更動兩個都要重新讀取資料
            // console.log(localActivePage);
            infoReloadFunc(localActivePage);
            infoSearchFunc();
            //
            dispatch({
                type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                payload: {
                    target: null,
                    signal: `delete-${new Date().getTime()}`
                }
            });
        }
    };
    //
    const handleClose = () => {
        //
        handleRefresh();
        // init status
        handleInitDeletedData();
        handleInitIsDeleted();
        handleInitResult();
        // close modal
        setOpen(false);
    };
    const handleOpen = () => {
        // open modal
        setOpen(true);
    };
    const reformatData = async data => {
        //
        const { srcId, propertyBindRangeStr, values, graph } = data;
        // // 為了不影響原本的資料，先copy 一次
        let newValues = [...values.map(obj => safeGet(obj, ["value"], ""))];
        // console.log("newValues", newValues);
        // 查詢所有內容的 ID
        if (!`${propertyBindRangeStr}`.startsWith("label_")) {
            //
            const [propName, propRange] = propertyBindRangeStr.split("__");
            //
            const promises = newValues.map(value => {
                const apiStr = Api.findIdByValue()
                    .replace("{class}", propRange)
                    .replace("{keyword}", value);
                return readHkbdbData(apiStr);
            });
            //
            const results = await Promise.allSettled(promises).then(
                results => results
            );
            //
            results.forEach((item, idx) => {
                const { status, value } = item;
                if (status === "fulfilled") {
                    const objId = safeGet(value, ["data", 0, "value"], "");
                    if (isNotEmpty(objId)) {
                        newValues[idx] = objId;
                    }
                }
            });
        }
        let entry = {
            graph,
            srcId,
            classType: ontologyType,
            value: { [propertyBindRangeStr]: newValues }
        };
        //
        if (ontologyType === "member") {
            const getEvent = bs64DecodeId(srcId).split("__")[0];
            if (getEvent) {
                const typeObj = EVT_PREFIX.find(
                    item => item.prefix === getEvent
                );
                const type = typeObj?.eventType;

                entry = {
                    ...entry,
                    classType: type
                };
            }
        }
        //
        return entry;
    };
    const handleDelete = async () => {
        setIsLoading(true);

        if (!isEmpty(editData.deletedData)) {
            //
            const deletedRowIds = editData.deletedData;
            //
            const promises = deletedRowIds.map(async rowId => {
                const entry = await reformatData(
                    editData.rowData.find(item => item.rowId === rowId)
                );
                return deleteHkbdbData(Api.restfulHKBDB(), entry);
            });
            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);
            //
            results.forEach((res, idx) => {
                // console.log(res);
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的
                    deleteResults.deletedRowIds.push(deletedRowIds[idx]);
                    // 紀錄成功資料筆數
                    deleteResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    deleteResults.failed++;
                }
            });
            //
            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: "更新",
                type: deleteResults.failed ? "error" : "success",
                content: `已刪除: ${deleteResults.success}, 已失敗: ${deleteResults.failed}`,
                renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
            }));
            //
            setEditData(prevEditData => ({
                ...prevEditData,
                isDeleted: true,
                deletedRowIds: [...deleteResults.deletedRowIds]
            }));
            //
            console.log(deleteResults);
        }
        //
        setIsLoading(false);
        // init status
        handleInitDeletedData();
        // setOpen(false);
    };
    const handleCancel = () => {
        // init status
        handleClose();
        // close modal
        setOpen(false);
    };
    //
    const SwitchButton = () => {
        if (editData.isDeleted && isEmpty(editData.deletedData)) {
            return (
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            return (
                <Fragment>
                    <Button
                        loading={isLoading}
                        disabled={isEmpty(editData.deletedData)}
                        onClick={handleDelete}
                        color="red"
                    >
                        <FormattedMessage
                            id={"people.Information.button.delete"}
                            defaultMessage={"Delete"}
                        />
                    </Button>
                    <Button onClick={handleCancel} color="green">
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };

    const modalContentStyle = {
        width: "100%"
    };

    return (
        <Modal open={open} onClose={handleClose} onOpen={handleOpen}>
            {/* <pre>{JSON.stringify(editData, null, 2)}</pre> */}
            <Modal.Header>
                <FormattedMessage
                    id={"people.Information.header.delete.content"}
                    defaultMessage={"Delete Content"}
                />
            </Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description style={modalContentStyle}>
                    {/* alert */}
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    {/* content */}
                    <CustomMenu
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <SwitchButton />
            </Modal.Actions>
        </Modal>
    );
};

export default CustomDeleteModalFlex;
