import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusAlbum from "../CusAlbum";
import CusAuctionTable from "../CusAuctionTable";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

import { safeGet } from "../../../../common/codes";

const CusAuction = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const width = 310;
    const height = 232;
    const defObj = { type: null, value: [] };

    const eventId = safeGet(data, ["eventId"], []);

    const bilingFunc = bilingual(defObj);

    const [labelZh, labelEn] = bilingFunc(data, "label");
    const [dateZh, dateEn] = bilingFunc(data, "hasCollectedIn");
    const [postIdZh, postIdEn] = bilingFunc(data, "postId");
    const [lotTotalZh, lotTotalEn] = bilingFunc(data, "lotTotal");
    const [photoIdZh, photoIdEn] = bilingFunc(data, "photoId");
    const [auctionNoZh, auctionNoEn] = bilingFunc(data, "auctionNo");
    const [startDateZh, startDateEn] = bilingFunc(data, "hasStartDate");
    const [saleTotalZh, saleTotalEn] = bilingFunc(data, "saleTotal");
    const [auctioneerZh, auctioneerEn] = bilingFunc(data, "hasAuctionner");

    const [year, month, day] = `${dateZh.value}`.split("-");

    return (
        <div className={classes.hkvayb_exhibition}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <CusAlbum
                    value={photoIdZh.value}
                    path={`auctions/${year}`}
                    backupPath={"auctions"}
                    width={width}
                    height={height}
                />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.auction.auctioneer"
                    defaultMessage="Auctioneer : "
                />
                <CusValue {...auctioneerZh} />
                <CusValue prefix="/" defVal="" {...auctioneerEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.auction.auctionNo"
                    defaultMessage="Auction No : "
                />
                <CusValue {...auctionNoZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.auction.startDate"
                    defaultMessage="Date : "
                />
                <CusValue {...startDateZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.auction.saleTotal"
                    defaultMessage="Sale Total : HK$"
                />
                <CusValue {...saleTotalZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.auction.lotTotal"
                    defaultMessage="Lot Total : "
                />
                <CusValue {...lotTotalZh} />
            </CusPara>
            <CusAuctionTable eventId={eventId} />
        </div>
    );
};

export default CusAuction;
