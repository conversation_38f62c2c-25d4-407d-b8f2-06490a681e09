// eslint-disable-next-line import/no-extraneous-dependencies
import isString from "lodash/isString";
// eslint-disable-next-line import/no-extraneous-dependencies
// import isArray from "lodash/isArray";

/**
 * 依據 limit 切割 字串或 list
 *
 * @list {string | []}  陣列 或 字串
 * @limit {integer}  陣列的 limit 或 字串的 limit
 * @slice {boolean}  是否要切割
 *
 * 範例:
 * 1. ["aaa","bbb",....]  => return ["aaa"]
 * 2. "abcdefg......." => "abcdefg"
 * */
const sliceByLimit = (list, limit, slice) => {
    if (isString(list)) {
        return `${list.substring(0, limit)} ...`;
    }
    // if (!isArray(list)) return null;
    if (slice) return list;
    // if (limit === undefined || limit === null) return list;
    // const safeLimit = Number.parseInt(limit, 10);
    return list.slice(0, limit);
};

export default sliceByLimit;
