import Act from "../actions";

const initState = {
    results: {
        vars: [],
        bindings: []
    },
    // timelineData stands for person or organization.
    timelineData: {
        vars: null, // []
        bindings: null // []
    },
    familyTreeData: {
        indis: [],
        fams: []
    },
    SNAData: {
        nodes: [],
        links: []
    },
    fetchSnaDataStatus: {
        step1: false,
        step2: false
    },
    step1SnaData: {
        nodes: [],
        links: []
    },
    step2SnaData: {
        nodes: [],
        links: []
    },
    depth: 1,
    genealogy: "",
    persons: null,
    graphs: null,
    birthDate: null,
    deathDate: null,
    fetchDataLoading: true,
    headers: {},
    nameNode: [],
    nameNodeLoading: false,
    nameNodeIds: [],
    nameNodeIdsLoading: false,
    infoIds: {},
    infoSearchIds: {},
    infoData: {},
    infoSearchData: {},
    infoPage: {},
    infoSearchPage: {},
    infoLoad: {},
    infoSearchLoad: {},
    infoReloadFunc: () => {},
    infoSearchFunc: () => {},
    suggestInfo: [], // 新增列在Table的suggester data
    tempUpdateSuggestInfo: {
        // fixed table
        person: [],
        article: [],
        publication: [],
        educationevent: [],
        organizationevent: [],
        employmentevent: [],
        awardevent: [],
        relationevent: [],
        event: [],
        otherwork: [],
        namenode: []
    }, // 暫存的suggestion
    isLoadingSugData: [],
    entryGraphForCreatingSuggestions: []
};

const personInfoReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.USER_CLEAR_CACHE:
            return {
                ...initState
            };
        case Act.USER_FETCH_INFO:
            return {
                ...state,
                ...action.payload
            };
        case Act.USER_FETCH_INFO_HEADER:
            return {
                ...state,
                headers: {
                    ...state.headers,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_IDS_INFO:
            return {
                ...state,
                infoIds: {
                    ...state.infoIds,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_SEARCH_IDS_INFO:
            return {
                ...state,
                infoSearchIds: {
                    ...state.infoSearchIds,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_DATA_INFO:
            return {
                ...state,
                infoData: {
                    ...state.infoData,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_SEARCH_DATA_INFO:
            return {
                ...state,
                infoSearchData: {
                    ...state.infoSearchData,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_DATA_LOAD:
            return {
                ...state,
                infoLoad: {
                    ...state.infoLoad,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_SEARCH_DATA_LOAD:
            return {
                ...state,
                infoSearchLoad: {
                    ...state.infoSearchLoad,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_DATA_PAGE_TOTAL_INFO:
            return {
                ...state,
                infoPage: {
                    ...state.infoPage,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_SEARCH_DATA_PAGE_TOTAL_INFO:
            return {
                ...state,
                infoSearchPage: {
                    ...state.infoSearchPage,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_DATA_RELOAD_FUNC:
            return {
                ...state,
                infoReloadFunc: {
                    ...state.infoReloadFunc,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_DATA_SEARCH_FUNC:
            return {
                ...state,
                infoSearchFunc: {
                    ...state.infoSearchFunc,
                    ...action.payload
                }
            };
        case Act.USER_FETCH_NAME_NODE:
            return {
                ...state,
                nameNode: [...action.payload]
            };
        case Act.USER_FETCH_NAME_NODE_LOADING:
            return {
                ...state,
                nameNodeLoading: action.payload
            };
        case Act.USER_FETCH_NAME_NODE_IDS:
            return {
                ...state,
                nameNodeIds: [...action.payload]
            };
        case Act.USER_FETCH_NAME_NODE_IDS_LOADING:
            return {
                ...state,
                nameNodeIdsLoading: action.payload
            };
        case Act.USER_FETCH_INFO_LOADING:
            return {
                ...state,
                fetchDataLoading: action.payload
            };
        case Act.USER_FETCH_TIMELINE_DATA:
            return {
                ...state,
                timelineData: action.payload.timelineData
            };

        case Act.USER_FETCH_SNA_DATA:
            return {
                ...state,
                SNAData: action.payload
            };
        case Act.SET_SNA_DEPTH:
            return {
                ...state,
                depth: action.payload
            };
        case Act.USER_FETCH_GENEALOGY:
            return {
                ...state,
                genealogy: action.payload
            };
        case Act.FETCH_SNA_DATA_STATUS:
            return {
                ...state,
                fetchSnaDataStatus: action.payload
            };
        case Act.SET_SETP1_SNA_DATA:
            return {
                ...state,
                step1SnaData: action.payload
            };
        case Act.SET_SETP2_SNA_DATA:
            return {
                ...state,
                step2SnaData: action.payload
            };
        case Act.SET_SUGGESTINFO:
            return {
                ...state,
                suggestInfo: action.payload
            };
        // case Act.SET_TEMP_UPDATE_SUGGESTINFO:
        //     console.log("action.payload", action.payload);
        //     const { classType, value } = action.payload;
        //
        //     const processedValue = Object.keys(value).reduce((acc, key) => {
        //         // const cleanKey = key.split("__")[0];
        //         acc[key] = value[key];
        //         // Array.isArray(value[key])
        //         // ? value[key].join(",")
        //         // : value[key];
        //         return acc;
        //     }, {});
        //
        //     // 添加唯一 ID
        //     const valueWithId = {
        //         ...processedValue,
        //         id:
        //             Date.now() +
        //             Math.random()
        //                 .toString(36)
        //                 .substr(2, 9)
        //     };
        //
        //     return {
        //         ...state,
        //         tempUpdateSuggestInfo: {
        //             ...state.tempUpdateSuggestInfo,
        //             [classType]: state.tempUpdateSuggestInfo[classType]
        //                 ? [
        //                       ...state.tempUpdateSuggestInfo[classType],
        //                       valueWithId
        //                   ]
        //                 : [valueWithId]
        //         }
        //     };

        case Act.SET_TEMP_UPDATE_SUGGESTINFO:
            const { classType, value, graph } = action.payload; // 解構 graph

            const processedValue = Object.keys(value).reduce((acc, key) => {
                acc[key] = value[key];
                return acc;
            }, {});

            const valueWithId = {
                ...processedValue,
                id:
                    Date.now() +
                    Math.random()
                        .toString(36)
                        .substr(2, 9),
                graph // 每個suggestions都須有獨立的graph
            };

            return {
                ...state,
                tempUpdateSuggestInfo: {
                    ...state.tempUpdateSuggestInfo,
                    [classType]: state.tempUpdateSuggestInfo[classType]
                        ? [
                              ...state.tempUpdateSuggestInfo[classType],
                              valueWithId
                          ]
                        : [valueWithId]
                }
            };

        case Act.UPDATE_TEMP_UPDATE_SUGGESTINFO:
            return {
                ...state,
                tempUpdateSuggestInfo: action.payload
            };

        case Act.RESET_TEMP_UPDATE_SUGGESTINFO:
            return {
                ...state,
                tempUpdateSuggestInfo: {
                    // fixed table
                    person: [],
                    relationevent: []
                }
            };

        case Act.SET_ENTRY_GRAPH_FOR_CREATING_SUGGESTIONS:
            return {
                ...state,
                entryGraphForCreatingSuggestions: action.payload
            };

        case Act.SET_IS_LOADING_SUG_DATA:
            return {
                ...state,
                isLoadingSugData: action.payload
            };

        default:
            return state;
    }
};

export default personInfoReducer;
