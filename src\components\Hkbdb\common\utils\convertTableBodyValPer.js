import { bs64Decode } from "../../../../common/codes";

const { GV } = require("../../../../common/codes/globalVars");
const {
    findPropertyRange,
    convertDateEvent2
} = require("../../../../common/codes");

/**
 * @description 根據資料型別("range")做畫面字串對應顯示處理
 * "range" => 從propArr === ontologyDefined[ontologyType] 找 property名稱
 * */
const convertTableBodyVal = (val, property, propArr) => {
    if (!val) {
        return val;
    }

    // 找property被定義的型別
    const range = findPropertyRange(property, propArr);
    switch (range) {
        case GV.DATE_EVENT:
            return convertDateEvent2(val);
        default:
            return val.includes("PLA") ? decodeURI(val.substring(3)) : val;
    }
};

export default convertTableBodyVal;
