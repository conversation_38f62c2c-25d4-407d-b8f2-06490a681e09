import React, { useState } from "react";
// ui
import Button from "@mui/material/Button";
// import LoadingButton from "@mui/lab/LoadingButton";
import Badge from "@mui/material/Badge";
// style
import { createStyles, makeStyles, styled } from "@material-ui/styles";
import PropTypes from "prop-types";
import classNames from "classnames";
import Typography from "@mui/material/Typography";
// config

// useStyle
const useStyles = makeStyles(theme =>
    createStyles({
        root: {}
    })
);

const basicButtonProps = {
    padding: "9px 12px",
    borderRadius: "8px"
};

const StyledButtonContained = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    boxShadow: "0 8px 16px 0 rgba(59, 191, 240, 0.16)",
    "&.MuiButton-root": {
        backgroundColor: "#5b9fd7",
        color: "#fff",
        boxShadow: "0 8px 16px 0 rgba(59, 191, 240, 0.16)",
        "&:hover": {
            backgroundColor: "#4183c4",
            color: "#fff"
        }
    },
    "&.MuiButton-contained": {}
}));

const StyledButtonGradient = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    "&.MuiButton-root": {
        backgroundImage: "linear-gradient(to bottom, #5b9fd7, #4183c4)",
        color: "#fff",
        "&:hover": {
            backgroundImage: "linear-gradient(to bottom, #4183c4, #4183c4)"
        }
    }
}));

const StyledButtonNeutral = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    "&.MuiButton-root": {
        backgroundColor: "#e6e9ef",
        color: "#121212",
        "&:hover": {
            backgroundColor: "#c9c9c9"
        }
    }
}));

const StyledButtonOutlined = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    "&.MuiButton-root": {
        border: "solid 1px #e6e9ef",
        backgroundColor: "#fff",
        color: "#121212",
        "&:hover": {
            backgroundColor: "#e6e9ef"
        }
    }
}));

const StyledButtonText = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    "&.MuiButton-root": {
        backgroundColor: "#fff",
        color: "#121212",
        "&:hover": {
            backgroundColor: "#f7f8fa"
        }
    }
}));

const StyledButtonDisabled = styled(Button)(({ theme }) => ({
    ...basicButtonProps,
    "&.MuiButton-root": {
        backgroundColor: "#e6e9ef",
        color: "#c9c9c9"
    }
}));

// const StyledButtonLoading = styled(LoadingButton)(({ theme }) => ({
//     ...basicButtonProps,
//     padding: "9px 12px",
//     borderRadius: "8px",
//     "&.MuiButton-root": {
//         backgroundColor: "#e6e9ef",
//         color: "#c9c9c9"
//     }
// }));

const StyledBadge = styled(Badge)(({ theme }) => ({
    "& .MuiBadge-badge": {
        backgroundColor: "#ffb629",
        color: "#fff"
    }
}));

//
const ButtonMain = ({ badgeProps, buttonProps }) => {
    // style
    const classes = useStyles();
    // props
    const {
        text,
        className,
        variant,
        startIcon,
        ...buttonPropsRest
    } = buttonProps;
    const { badgeContent, ...badgePropRest } = badgeProps;
    // local state

    const handleShowBadge = () =>
        badgeContent !== undefined && badgeContent !== null;

    const getProps = () => ({
        ...buttonPropsRest,
        startIcon,
        className: classNames(className)
    });

    const getLoadingBtnProps = () => ({
        ...buttonPropsRest,
        loadingPosition: startIcon ? "start" : "center",
        startIcon,
        className: classNames(className)
    });

    const getCustomButton = () => {
        let CustomButton;
        let customProps = getProps();
        switch (variant) {
            case "contained":
                CustomButton = StyledButtonContained;
                break;
            case "gradient":
                CustomButton = StyledButtonGradient;
                break;
            case "neutral":
                CustomButton = StyledButtonNeutral;
                break;
            case "outlined":
                CustomButton = StyledButtonOutlined;
                break;
            case "text":
                CustomButton = StyledButtonText;
                break;
            case "disabled":
                CustomButton = StyledButtonDisabled;
                break;
            // case "loading":
            //     CustomButton = StyledButtonLoading;
            //     // LoadingButton 的 props 客製化
            //     customProps = getLoadingBtnProps();
            //     break;
            default:
                CustomButton = StyledButtonGradient;
        }
        return (
            (handleShowBadge() && (
                <StyledBadge badgeContent={badgeContent} {...badgePropRest}>
                    <CustomButton {...customProps}>{text}</CustomButton>
                </StyledBadge>
            )) || <CustomButton {...customProps}>{text}</CustomButton>
        );
    };

    return getCustomButton();
};

ButtonMain.propTypes = {
    badgeProps: PropTypes.objectOf(PropTypes.any),
    buttonProps: PropTypes.objectOf(PropTypes.any)
};

ButtonMain.defaultProps = {
    badgeProps: {},
    buttonProps: {}
};

export default ButtonMain;
