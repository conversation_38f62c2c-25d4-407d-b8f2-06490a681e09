import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
// ui
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import InputAdornment from "@mui/material/InputAdornment";
import OutlinedInput from "@mui/material/OutlinedInput";
// style
import { createStyles, makeStyles, styled } from "@material-ui/styles";

// config

// useStyle
const useStyles = makeStyles(theme =>
    createStyles({
        root: {}
    })
);

const StyledInput = styled(OutlinedInput)(({ theme }) => ({
    "& .MuiOutlinedInput-input": {
        borderRadius: 4,
        position: "relative",
        fontSize: 16,
        width: "240px",
        height: "32px",
        padding: "9px 12px"
    }
}));

//
const InputPlusIcon = ({ label, ...rest }) => {
    // style
    const classes = useStyles();
    // props
    const { startAdornmentIcon, color, placeholder, type, ...restProps } = rest;
    // local state

    return (
        <Box>
            {label && <InputLabel>{label}</InputLabel>}
            <StyledInput
                id="input-with-icon-adornment"
                color={color}
                placeholder={placeholder}
                type={type || "text"}
                startAdornment={
                    <InputAdornment position="start">
                        {startAdornmentIcon}
                    </InputAdornment>
                }
                {...restProps}
            />
        </Box>
    );
};

InputPlusIcon.propTypes = {
    label: PropTypes.string
};

InputPlusIcon.defaultProps = {
    label: null
};

export default InputPlusIcon;
