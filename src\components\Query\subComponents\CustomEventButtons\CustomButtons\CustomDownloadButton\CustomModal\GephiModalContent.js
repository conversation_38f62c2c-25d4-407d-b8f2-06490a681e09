// react
import React, { useContext, useState, useEffect } from "react";
// ui
import { Button, Dropdown, Modal, Select, Table } from "semantic-ui-react";
// api
import { Api, queryHkbdbData } from "../../../../../../../api/hkbdb/Api";
// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
// common
import { isEmpty } from "../../../../../../../common/codes";

import { downloadGephi } from "../exportUtils";

const GephiModalContent = ({ onCancelBtnClick, onModalClose }) => {
    // get state
    const [state, dispatch] = useContext(StoreContext);
    const { queryString, queryResult } = state.query;
    //
    const [isLoad, setIsLoad] = useState(false);
    const [headerMap, setHeaderMap] = useState(null);
    // headerMap => 用於定義 each of bindings 的 哪一個 key 為 source, 哪一個 key 為 target
    // 哪一個 key 為 op
    // e.g. { source: "personId", target: "title", op: "place"}
    const [finishHeaderMap, setFinishHeaderMap] = useState(false);
    const [selectOpts, setSelectOpts] = useState([]);
    const [results, setResults] = useState(null);
    const [error, setError] = useState(null);
    const [sourceVal, setSourceVal] = useState("");
    const [opVal, setOpVal] = useState("");
    const [targetVal, setTargetVal] = useState("");
    const [isEditing, setIsEditing] = useState(false);

    const fetchData = async (queryString, limit, offset) => {
        try {
            const api = Api.getQueryAndCount();
            const result = await queryHkbdbData(
                api,
                queryString,
                limit,
                offset
            );
            return Promise.resolve(result);
        } catch (err) {
            setError(err);
        }
    };

    // 先定義 selection options
    useEffect(() => {
        if (!(queryResult && queryResult.head && queryResult.data)) return;
        const { head, data } = queryResult;
        // set selection options
        const keys = head || [];
        const _opts = keys.map((key, i) => ({
            key: `${key}-${i}`,
            value: key,
            text: key
        }));
        setResults(data);
        setSelectOpts(_opts);
    }, [queryResult]);

    const onExportBtnClick = async () => {
        if (!finishHeaderMap) return;
        if (queryString) {
            // 取得全部資料
            fetchData(queryString, -1, 0).then(result => {
                // // result: {
                // //  data: [{},{},...]
                // //  head: ["","",...]
                // // }
                if (!isEmpty(result)) {
                    const { data } = result;
                    if (data && headerMap) {
                        downloadGephi(data, headerMap);
                    }
                }
            });
        }

        setIsLoad(true);
        onModalClose();
    };

    const onResetBtnClick = () => {
        setSourceVal(null);
        setOpVal(null);
        setTargetVal(null);
        setHeaderMap(null);
    };

    const headersIdx = {
        source: "source",
        target: "target",
        op: "op"
    };

    const handleDropChange = (header, { value }) => {
        if (!(header && value)) return;
        if (header === headersIdx.source) setSourceVal(value);
        if (header === headersIdx.target) setTargetVal(value);
        if (header === headersIdx.op) setOpVal(value);

        // headerMap: {source: "poet", op: "preface", target: "title"}
        const _headerMap = Object.assign({}, headerMap);
        _headerMap[header] = value;
        setHeaderMap(_headerMap);
    };

    // 若 三個選項(source, op, target) 接編輯完成, 變更 finishHeaderMap 的狀態
    useEffect(() => {
        if (!headerMap) {
            setFinishHeaderMap(false);
            return;
        }
        let finish = true;
        const keys = Object.keys(headerMap);
        Object.keys(headersIdx).forEach(key => {
            if (keys.indexOf(key) < 0) {
                finish = false;
            }
        });
        setFinishHeaderMap(finish);
    }, [headerMap]);

    useEffect(() => {
        if (!isEmpty(sourceVal) || !isEmpty(opVal) || !isEmpty(targetVal)) {
            setIsEditing(true);
        } else {
            setIsEditing(false);
        }
    }, [sourceVal, opVal, targetVal]);

    return (
        <React.Fragment>
            <Modal.Header>Gephi Setting</Modal.Header>
            <Modal.Content image>
                <Modal.Description>
                    <h5>
                        Please set source, target and op before export gephi.
                    </h5>
                    <Table>
                        <Table.Header>
                            <Table.Row>
                                <Table.HeaderCell>Source</Table.HeaderCell>
                                <Table.HeaderCell>OP</Table.HeaderCell>
                                <Table.HeaderCell>Target</Table.HeaderCell>
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>
                            <Table.Row>
                                <Table.Cell>
                                    <Dropdown
                                        placeholder="Select Source"
                                        selection
                                        onChange={(e, data) =>
                                            handleDropChange(
                                                headersIdx.source,
                                                data
                                            )
                                        }
                                        value={sourceVal}
                                        options={selectOpts.filter(
                                            opt =>
                                                opt.value !== targetVal &&
                                                opt.value !== opVal
                                        )}
                                    />
                                </Table.Cell>
                                <Table.Cell>
                                    <Dropdown
                                        placeholder="Select OP"
                                        selection
                                        onChange={(e, data) =>
                                            handleDropChange(
                                                headersIdx.op,
                                                data
                                            )
                                        }
                                        value={opVal}
                                        options={selectOpts.filter(
                                            opt =>
                                                opt.value !== sourceVal &&
                                                opt.value !== targetVal
                                        )}
                                    />
                                </Table.Cell>
                                <Table.Cell>
                                    <Dropdown
                                        placeholder="Select Target"
                                        selection
                                        onChange={(e, data) =>
                                            handleDropChange(
                                                headersIdx.target,
                                                data
                                            )
                                        }
                                        value={targetVal}
                                        options={selectOpts.filter(
                                            opt =>
                                                opt.value !== sourceVal &&
                                                opt.value !== opVal
                                        )}
                                    />
                                </Table.Cell>
                            </Table.Row>
                        </Table.Body>
                    </Table>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    color={"blue"}
                    onClick={onExportBtnClick}
                    loading={isLoad}
                    disabled={!finishHeaderMap}
                >
                    Export Gephi
                </Button>
                <Button
                    color={"teal"}
                    onClick={onResetBtnClick}
                    loading={isLoad}
                    disabled={!isEditing}
                >
                    Reset
                </Button>
                <Button color={"grey"} onClick={() => onCancelBtnClick()}>
                    Cancel
                </Button>
            </Modal.Actions>
        </React.Fragment>
    );
};

export default GephiModalContent;
