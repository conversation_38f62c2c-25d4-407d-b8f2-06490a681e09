import Act from "../actions";
const initState = {
    label: {
        essay: true,
        publicIssue: true,
        awardEvent: true,
        educationEvent: true,
        publication: true,
        auction: true,
        talksSymposium: true,
        exhibition: true,
        venueInHK: true
    },
    // label: {
    //     essay: false,
    //     publicIssue: false,
    //     awardEvent: false,
    //     educationEvent: false,
    //     publication: false,
    //     auction: false,
    //     talksSymposium: false,
    //     exhibition: false,
    //     venueInHK: false
    // },
    labelTranslate: {
        essay: "專題論述",
        publicIssue: "公眾議題",
        awardEvent: "藝術獎項",
        educationEvent: "藝術教育",
        publication: "藝術論著",
        auction: "藝術拍賣",
        talksSymposium: "藝術講座/研討會",
        exhibition: "藝術展覽",
        venueInHK: "香港展覽場地"
    },
    count: 9,
    keyWord: "",
    isSearch: false,
    searchDataCount: 0, // 紀錄收尋結果資料數量
    pageNumber: 1, // 紀錄頁碼，用來判斷顯示資料區間，預設顯示第一頁
    checkSelectAll: true,
    // checkSelectAll: false
    duration: 0
};

const searchPageReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_LABEL:
            return Object.assign({}, state, {
                label: action.payload
            });
        case Act.MINUS_COUNT:
            return Object.assign({}, state, {
                count: state.count - 1
            });
        case Act.SET_COUNT:
            return Object.assign({}, state, {
                count: action.payload
            });
        case Act.SET_KEYWORD:
            return Object.assign({}, state, {
                keyWord: action.payload
            });
        case Act.SET_ISSEARCH:
            return Object.assign({}, state, {
                isSearch: action.payload
            });
        case Act.SET_SEARCHDATACOUNT: // 紀錄搜尋資料數量
            return Object.assign({}, state, {
                searchDataCount: action.payload
            });
        case Act.SET_PAGENUMBER: // 設定換頁號碼
            return Object.assign({}, state, {
                pageNumber: action.payload
            });
        case Act.SET_CHECKSELECTALL: // set select all icon
            return Object.assign({}, state, {
                checkSelectAll: action.payload
            });
        case Act.SET_DURATION: // set duration search time
            return Object.assign({}, state, {
                duration: action.payload
            });
        default:
            return state;
    }
};
export default searchPageReducer;
