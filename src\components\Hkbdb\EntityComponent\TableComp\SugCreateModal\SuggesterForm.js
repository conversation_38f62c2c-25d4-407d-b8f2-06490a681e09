import React from "react";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";

// semantic ui
import { Message, Table } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../../../common/codes";

// component
import CustomInput from "../SugComponents/SugInputFlexForm";
// import CustomInput from "../CustomInputFlexForm";
import CustomDateInput from "../SugComponents/SugDateInput";
// import CustomDateInput from "../CustomDateInput";
import CustomDropdown from "../CustomDropdownFlexCreate";
import checkIsDisableDropdown from "../../../common/utils/checkIsDisableDropdown";
import SugDropdownCreate from "../SugComponents/SugDropdownCreate";

function SuggesterForm({
    editableProps,
    createData,
    setCreateData,
    createGraphData,
    ontologyType
}) {
    const checkRelationDisable = ppt => {
        const curCreateData = createData.willCreatedData?.value;

        if (!curCreateData) return false;

        const allEmpty = arr => arr.every(e => e.length === 0);
        const isWillCreateDataAllEmpty = allEmpty(Object.values(curCreateData));

        function hasNonEmptyValueForKey(obj, key) {
            if (key in obj) {
                return Array.isArray(obj[key]) && obj[key].length > 0;
            }
            return false;
        }

        if (isWillCreateDataAllEmpty) return false;

        return !(
            !isWillCreateDataAllEmpty &&
            hasNonEmptyValueForKey(curCreateData, ppt.value)
        );
    };

    return (
        <Table celled>
            <Table.Body>
                {/* show selected value by dropdown */}
                {isEmpty(editableProps) ? (
                    <Table.Row>
                        <Table.Cell>
                            <Message
                                warning
                                header={
                                    <FormattedMessage
                                        id="people.Information.formTable.item"
                                        defaultMessage="There are no property in Form Table"
                                    />
                                }
                            />
                        </Table.Cell>
                    </Table.Row>
                ) : (
                    editableProps.map((property, idx) => {
                        //
                        const { range, value } = property;
                        // console.log(property);
                        //
                        switch (range) {
                            case "string":
                            case "float":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        // disabled={isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            case "DateEvent":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        // disabled={isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomDateInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            default:
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        // disabled={checkIsDisableDropdown(
                                        //     createData,
                                        //     value
                                        // )}
                                        disabled={
                                            ontologyType === "relationevent"
                                                ? checkRelationDisable(property)
                                                : checkIsDisableDropdown(
                                                      createData,
                                                      value
                                                  )
                                        }
                                    >
                                        <Table.Cell>
                                            {/* <CustomDropdown */}
                                            {/*    property={property} */}
                                            {/*    createData={createData} */}
                                            {/*    setCreateData={setCreateData} */}
                                            {/*    createGraphData={ */}
                                            {/*        createGraphData */}
                                            {/*    } */}
                                            {/* /> */}
                                            <SugDropdownCreate
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                                createGraphData={
                                                    createGraphData
                                                }
                                                ontologyType={ontologyType}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                        }
                    })
                )}
            </Table.Body>
        </Table>
    );
}

SuggesterForm.propTypes = {
    editableProps: PropTypes.arrayOf(PropTypes.objectOf(PropTypes.string)),
    createData: PropTypes.objectOf(
        PropTypes.oneOfType([
            PropTypes.arrayOf(
                PropTypes.oneOfType([
                    PropTypes.string,
                    PropTypes.objectOf(PropTypes.string)
                ])
            ),
            PropTypes.bool,
            PropTypes.string,
            PropTypes.objectOf(
                PropTypes.oneOfType([
                    PropTypes.any, // createData.perInfo 包含的型別太多種了
                    PropTypes.bool,
                    PropTypes.func,
                    PropTypes.string,
                    PropTypes.arrayOf(PropTypes.string),
                    PropTypes.objectOf(PropTypes.string),
                    PropTypes.arrayOf(PropTypes.objectOf(PropTypes.string))
                ])
            )
        ])
    ),
    setCreateData: PropTypes.func
};

SuggesterForm.defaultProps = {
    editableProps: [],
    createData: {},
    setCreateData: () => {}
};

export default SuggesterForm;
