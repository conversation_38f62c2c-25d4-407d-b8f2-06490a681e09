.slick-slider {
  overflow: hidden;
  cursor: pointer;
}

.slick-center .slick-current{
  h1{
    font-size: 1.8rem;
    color:#104860;
  }
}

.slick-center {
  transition: all 0.3s ease;
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  transform: scale(1.1);
  font-weight: bold;
}

.slick-slide{
  h1{
    font-size: 1.2rem;
  }
  color: #336F89;
}

.focuspoint{
    &__box{
      width: 80%;
    }
    &__header{
    display: flex;
    align-items: center;
    font-size: 34px !important;
    justify-content: center;
    margin-bottom: 3rem;
    font-weight: 600 !important;
    color:#104860 !important;
    @media screen and (max-width: 576px){
      font-size: 24px !important;
    }
      span{
        font-weight: bold;
      }
  }
  &__subheader{
    span{
      color:#104860 !important;
      font-size: 24px !important;
    }
  }
  &__firstSwiper{
    max-width: 240px;
    max-height: 240px;
    z-index: 999;
    //margin-top: 4rem !important;
    margin: 4rem auto 0 auto !important;
    .slick-slide {
      width: 240px !important;
      height:240px;
    }
  }
  &__secondSwiper{
    width: 100%;
    margin-top: 4rem;
    z-index: 999;
    @media screen and (max-width: 576px){
      margin-top: 2rem;
    }
    .slick-current{
      display: flex;
      justify-content: center;
      align-items: center;
      h1{
        transition: font-size 0.3s ease;
        font-size: 1.6rem;
        color:#104860;
        font-weight: bold;
        //margin-bottom: 5rem;
        display: flex;
        align-items: center;
        @media screen and (max-width: 576px){
          font-size: 20px;
        }
        &::after {
          content: "";
          display: inline-block;
          width: 24px;
          height: 24px;
          background-image: url(../../images/icon_link/icon_link.svg);
          background-size: contain;
          background-repeat: no-repeat;
          margin-left: 10px;
          @media screen and (max-width: 576px){
            width: 16px;
            height: 16px;
            margin-left: 5px;
          }
        }
      }
    }
    &__container{
      width: 100%;
      height:60px;
      display:flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding-left: 1rem;
      //@media screen and (max-width: 576px){
      //  padding-left: 1rem;
      //}
    }
  }
}

@media screen and (max-width: 773px){
  .focuspoint{
    &__box{
      width: 100%;
      //max-width: 100%;
      //overflow: hidden;
    }
  }
}

//.swiper__inside--div{
//  //margin: 1rem 1rem;
//  //width:130%;
//}
