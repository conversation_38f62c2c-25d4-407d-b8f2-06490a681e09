// 透過 API 取得 Storage images
const getImage = url => {
    const imageUrl = process.env.REACT_APP_IMAGE_NODE;

    if (!url) {
      console.warn("getImage: No URL provided");
      return Promise.resolve('');
    }
  
    // Ensure single slash between imageUrl and url
    const cleanUrl = url.startsWith('/') ? url.slice(1) : url;
    const fullUrl = `${imageUrl}/${cleanUrl}`;
  
    // console.log("getImage →", fullUrl);
    return Promise.resolve(fullUrl);
};

// 透過 API 取得 Storage files
const getSwgJson = url => {
    const imageUrl = process.env.REACT_APP_IMAGE_NODE;

    if (!url) {
      console.warn("getSwgJson: No URL provided");
      return Promise.resolve('');
    }
  
    // Ensure single slash between imageUrl and url
    const cleanUrl = url.startsWith('/') ? url.slice(1) : url;
    const fullUrl = `${imageUrl}/${cleanUrl}`;
  
    // console.log("getSwgJson →", fullUrl);
    return Promise.resolve(fullUrl);
};

export { getImage, getSwgJson };
