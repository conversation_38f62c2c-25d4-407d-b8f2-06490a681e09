import React, { useEffect, useState } from "react";

// ui
import { Input } from "semantic-ui-react";

// common comp
import CustomDebounce from "./CustomDeBounce";

// common func
import { isNotEmpty } from "../../../../common/codes";

const CustomInput = ({
    rowIdx,
    graph,
    eventId,
    propertyBindRangeStr,
    editData,
    setEditData,
    ontologyType
}) => {
    //
    // const [state, dispatch] = useContext(StoreContext);
    //
    const [inputValue, setInputValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debInputValue = CustomDebounce(inputValue, 500);
    //
    const handleUpdateState = () => {
        const createdData = {
            // 注意: 這裡 rowIdx 會自動被轉為 string，object' key 只允許字串內容
            // 後續使用 rowIdx 時要注意
            [rowIdx]: {
                graph,
                srcId: eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: [
                    {
                        label: debInputValue,
                        value: debInputValue
                    }
                ]
            }
        };
        // update useState
        if (isNotEmpty(debInputValue)) {
            // update editData state
            setEditData(prevEditData => {
                return {
                    ...prevEditData,
                    createdData: {
                        // old changed items
                        ...prevEditData.createdData,
                        // new changed item
                        ...createdData
                    }
                };
            });
        } else {
            setEditData(prevEditData => {
                //
                const {
                    [rowIdx]: _,
                    ...restCreatedData
                } = prevEditData.createdData;
                //
                return {
                    ...prevEditData,
                    createdData: {
                        ...restCreatedData
                    }
                };
            });
        }
    };
    //
    const handleChange = (event, { value }) => {
        // console.log(value);
        setInputValue(value);
    };
    //
    useEffect(() => {
        handleUpdateState();
    }, [debInputValue]);
    //
    return (
        <Input
            fluid
            type="text"
            onChange={handleChange}
            disabled={editData.isUpdated || editData.isCreated}
        />
    );
};

export default CustomInput;
