import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

import CusPara from "../CusPara";
import CusValue from "../CusValue";
import CusBilingualTitle from "../CusBilingualTitle";

import { bilingual } from "../../util";

const CusVenueInHK = ({ data, style = {} }) => {
    const classes = useStyles(style);
    const defObj = { type: null, value: [] };

    const bilingFunc = bilingual(defObj);

    const [telZh, telEn] = bilingFunc(data, "tel");
    const [faxZh, faxEn] = bilingFunc(data, "fax");
    const [labelZh, labelEn] = bilingFunc(data, "hasVenue");
    const [emailZh, emailEn] = bilingFunc(data, "email");
    const [remarkZh, remarkEn] = bilingFunc(data, "remark");
    const [addressZh, addressEn] = bilingFunc(data, "address");
    const [websiteZh, websiteEn] = bilingFunc(data, "website");

    return (
        <div className={classes.hkvayb_essay}>
            <CusBilingualTitle
                titleZh={labelZh.value}
                titleEn={labelEn.value}
            />
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.label"
                    defaultMessage="Name : "
                />
                <CusValue {...labelZh} />
                <CusValue prefix="/" defVal="" {...labelEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.address"
                    defaultMessage="Address : "
                />
                <CusValue {...addressZh} />
                <CusValue prefix="/" defVal="" {...addressEn} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.tel"
                    defaultMessage="Telephone : "
                />
                <CusValue {...telZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.fax"
                    defaultMessage="Fax : "
                />
                <CusValue {...faxZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.website"
                    defaultMessage="Website : "
                />
                <CusValue {...websiteZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.email"
                    defaultMessage="Email : "
                />
                <CusValue {...emailZh} />
            </CusPara>
            <CusPara>
                <FormattedMessage
                    id="hkvayb.search.venue.remark"
                    defaultMessage="Remark : "
                />
                <CusValue {...remarkZh} />
                <CusValue prefix="/" defVal="" {...remarkEn} />
            </CusPara>
        </div>
    );
};

export default CusVenueInHK;
