import React, { useEffect, useState } from "react";
import { Mo<PERSON>, Button } from "semantic-ui-react";

const SugSubmitHintModal = ({ isOpen, setIsOpen }) => {
    return (
        <Modal size="tiny" open={isOpen} onClose={() => setIsOpen(false)}>
            <Modal.Header>提交成功</Modal.Header>
            <Modal.Content>
                <p>
                    您的提交已送出，感謝閣下提供的建議，我們會盡快處理及回覆。
                </p>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    positive
                    content="確定"
                    onClick={() => setIsOpen(false)}
                />
            </Modal.Actions>
        </Modal>
    );
};

export default SugSubmitHintModal;
