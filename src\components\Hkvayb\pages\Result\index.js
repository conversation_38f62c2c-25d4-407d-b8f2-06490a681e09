import React, { useContext } from "react";

import { useHistory } from "react-router-dom";

import { FormattedMessage } from "react-intl";

import config from "../../config";

import CusSecondaryHeader from "../../CusComps/CusSecondaryHeader";
import CusFooter from "../../CusComps/CusFooter";
import CusSearchBox from "../../CusComps/CusSearchBox";
import CusResultStats from "../../CusComps/CusResultStats";
import CusResults from "../../CusComps/CusResults";
import CusResultItem from "../../CusComps/CusResultItem";
import CusLoading from "../../CusComps/CusLoading";
import CusLineDivider from "../../CusComps/CusLineDivider";
import Cus<PERSON>ineArrow from "../../CusComps/CusLineArrow";
import CusResultItemGroup from "../../CusComps/CusResultItemGroup";
import CusPagination from "../../CusComps/CusPagination";

import useFetch from "../../CusHooks/useFetch";

import { bs64Encode, safeGet } from "../../../../common/codes";

import act from "../../../../store/actions";
import { url } from "../../../../api/hkvayb";
import { StoreContext } from "../../../../store/StoreProvider";

const coverSecondaryHeaderStyle = {
    hkvayb_header_background: {
        backgroundSize: "100% 278px"
    }
};

const coverSearchBoxStyle = {
    hkvayb_search_box: {
        margin: "unset"
    }
};

const coverResultsStyle = {
    hkvayb_content: {
        justifyContent: "center",
        alignItems: "center",
        minHeight: "384px"
    },
    hkvayb_div: {
        marginTop: "26px"
    }
};

const CusResult = ({ style = {}, ...props }) => {
    const {
        match: {
            params: { category: urlParamCategory }
        }
    } = props;

    const history = useHistory();
    const [state, dispatch] = useContext(StoreContext);
    const { locale } = state.user;
    const { queryString, limit, activePage, fields } = state.hkvaybSearch;

    const commaSymbol = config.symbol.comma;
    const spaceSymbol = config.symbol.space;

    let tmpQueryString;
    if (urlParamCategory) {
        let newFields = {
            ...fields,
            catType: "",
            category: `hkbdb:${urlParamCategory || ""}`
        };
        const apiUrl =
            locale === config.languages.zhHans
                ? url.hkvayb.SEARCH_ZH_IDS_INFORMATION
                : url.hkvayb.SEARCH_EN_IDS_INFORMATION;
        tmpQueryString = Object.keys(newFields).reduce((prevApiStr, key) => {
            const tmpValue = newFields[key] || "";
            return prevApiStr.replace(
                `{${key}}`,
                `${bs64Encode(
                    tmpValue.replaceAll(commaSymbol, `"${spaceSymbol}"`)
                )}`
            );
        }, apiUrl);
    }

    const apiUrl = (urlParamCategory ? tmpQueryString : queryString)
        .replace("{limit}", limit)
        .replace("{offset}", limit * (activePage - 1));

    const { data, loading } = useFetch(apiUrl);

    const handleOnPageChange = (event, page) => {
        if (Number.isInteger(page)) {
            dispatch({
                type: act.SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE,
                payload: page
            });
        }
    };

    const handleOnPageSelect = event => {
        const page = parseInt(event.target.value[0].label);
        if (Number.isInteger(page)) {
            dispatch({
                type: act.SET_HKVAYB_SEARCHBAR_ACTIVE_PAGE,
                payload: page
            });
        }
    };

    const handleItemOnClick = event => {
        const classId = safeGet(event, ["classId"]);
        const category = safeGet(event, ["category"]);
        if (classId && category) {
            dispatch({
                type: act.SET_HKVAYB_SEARCHBAR_SELECTED_ID,
                payload: classId
            });
            history.push(`/${locale}/hkvaybDetail/${category}/${classId}`);
        }
    };

    const ResultTemplate = React.useCallback(({ children }) => {
        return (
            <div>
                <CusSecondaryHeader
                    style={coverSecondaryHeaderStyle}
                    label={
                        <FormattedMessage
                            id="hkvayb.search.header.result"
                            defaultMessage="Search Results"
                        />
                    }
                >
                    <CusSearchBox style={coverSearchBoxStyle} />
                    {children}
                </CusSecondaryHeader>
                <CusFooter />
            </div>
        );
    }, []);

    if (loading) {
        return (
            <ResultTemplate>
                <CusResults style={coverResultsStyle}>
                    <CusResultItem>
                        <CusLoading />
                    </CusResultItem>
                    <CusResultItem>
                        <FormattedMessage
                            id="hkvayb.search.result.loading"
                            defaultMessage="loading..."
                        />
                    </CusResultItem>
                </CusResults>
            </ResultTemplate>
        );
    }

    if (!data || (data?.data || []).length === 0) {
        return (
            <ResultTemplate>
                <CusResults style={coverResultsStyle}>
                    <CusResultItem>
                        <FormattedMessage
                            id="hkvayb.search.result.noResult"
                            defaultMessage="no result"
                        />
                    </CusResultItem>
                </CusResults>
            </ResultTemplate>
        );
    }

    return (
        <ResultTemplate>
            <CusResultStats count={data.total} />
            <CusResults>
                {data.data &&
                    data.data.length > 0 &&
                    data.data.map((item, index) => {
                        const {
                            category,
                            label,
                            label_en: labelEn,
                            academicDegree,
                            venue,
                            ...rest
                        } = item;
                        return (
                            <div
                                key={`${category}-${label}`}
                                onClick={() => handleItemOnClick(item)}
                            >
                                <CusResultItemGroup
                                    style={{
                                        hkvayb_result_item_group: {
                                            "&:hover": {
                                                cursor: "pointer",
                                                backgroundImage: `url("${config.imageUrls[category]}")`,
                                                backgroundSize: "cover"
                                            }
                                        }
                                    }}
                                >
                                    <div>
                                        <CusResultItem
                                            primary
                                            label={safeGet(
                                                config.category,
                                                [locale, category],
                                                ""
                                            )}
                                        />
                                        <CusResultItem
                                            secondary
                                            label={
                                                label ||
                                                academicDegree ||
                                                labelEn ||
                                                venue
                                            }
                                        />
                                        {rest &&
                                            // eslint-disable-next-line
                                            safeGet(config.defaultItem, [category, locale], []).map(defaultKey => {
                                                // eslint-disable-next-line
                                                const _value = safeGet(rest, [defaultKey.en], "");
                                                if (_value) {
                                                    return (
                                                        // eslint-disable-next-line
                                                        <CusResultItem key={`${defaultKey.zh}-${_value}`}>
                                                            {`${defaultKey.zh}: `}
                                                            {`${_value}`}
                                                        </CusResultItem>
                                                    );
                                                }
                                            })}
                                    </div>
                                    <CusLineArrow />
                                </CusResultItemGroup>
                                {index !== data.data.length - 1 && (
                                    <CusLineDivider />
                                )}
                            </div>
                        );
                    })}
            </CusResults>
            <CusPagination
                limit={limit}
                activePage={activePage}
                pages={data.total}
                onPageChange={handleOnPageChange}
                onPageSelect={handleOnPageSelect}
            />
        </ResultTemplate>
    );
};

export default CusResult;
