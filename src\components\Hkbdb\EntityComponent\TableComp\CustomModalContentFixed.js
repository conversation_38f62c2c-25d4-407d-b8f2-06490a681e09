import React, { Fragment } from "react";

// ui
import { Divider, Label, Segment } from "semantic-ui-react";

// custom
import CustomForm from "./CustomFormFixedCreate";
import CustomPropertyDropdown from "./CustomPropertyDropdownFixedCreate";

// intl
import { FormattedMessage } from "react-intl";

const CustomModalContent = ({
    createData,
    setCreateData,
    ontologyDomain,
    ontologyType
}) => {
    //
    return (
        <Fragment>
            <Segment>
                <Label attached="top left">
                    <FormattedMessage
                        id={"people.Information.formTable"}
                        defaultMessage={"Form Table"}
                    />
                </Label>
                {/* show selected property */}
                <CustomForm
                    createData={createData}
                    setCreateData={setCreateData}
                />
                {/* <pre>{JSON.stringify(createData.ontology, null, 2)}</pre> */}
            </Segment>
            <Divider />
            <Segment>
                <Label attached="top left">
                    <FormattedMessage
                        id={"people.Information.properties"}
                        defaultMessage={"Properties"}
                    />
                </Label>
                {/* show all property */}
                <CustomPropertyDropdown
                    createData={createData}
                    setCreateData={setCreateData}
                    ontologyDomain={ontologyDomain}
                    ontologyType={ontologyType}
                />
            </Segment>
        </Fragment>
    );
};

export default CustomModalContent;
