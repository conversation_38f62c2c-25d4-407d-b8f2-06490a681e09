/**
 * 取得 collection 中的所有 docs
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param docName => firestore docName, e.g. "read"
 * @param callback => func.
 */
const apiColListener = async (
    firestoreDb,
    collectionName,
    docName,
    callback
) => {
    if (!(firestoreDb && collectionName && docName)) {
        const errMsg = {
            error: "parameters required: firestoreDb, collectionName, docName"
        };
        if (callback) return callback(null, errMsg);
        return Promise.reject(errMsg);
    }
    const colRef = firestoreDb.collection(collectionName);
    try {
        const colRef = firestoreDb.collection(collectionName);
        const result = await colRef.get();

        if (!result.exists) {
            const errMsg = { error: "No such collection!" };
            if (callback) return callback(null, errMsg);
            return Promise.reject(errMsg);
        } else {
            const apiObj = {};
            result.docs.forEach(x => {
                apiObj[x.id] = x.data();
            });

            if (callback) return callback(apiObj, null);
            return Promise.resolve(apiObj);
        }
    } catch (err) {
        if (callback) return callback(null, err);
        return Promise.reject(err);
    }
};

/**
 * 取得 collection 中的單一文件
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param docName => firestore docName, e.g. "read"
 * @param callback (optional)=> func.
 */
const apiCollDocListener = async (
    firestoreDb,
    collectionName,
    docName,
    callback
) => {
    if (!(firestoreDb && collectionName && docName)) {
        const errMsg = {
            error:
                "Parameters required in apiCollDocListener(): firestoreDb, collectionName, docName"
        };
        if (callback) return callback(null, errMsg);
        return Promise.reject(errMsg);
    }
    try {
        const docRef = firestoreDb.collection(collectionName).doc(docName);
        const doc = await docRef.get();
        if (!doc.exists) {
            const errMsg = { error: "No such document!" };
            if (callback) return callback(null, errMsg);
            return Promise.reject(errMsg);
        } else {
            // console.log('Document data:', doc.data());
            if (callback) return callback(doc, null);
            return Promise.resolve(doc.data());
        }
    } catch (err) {
        if (callback) return callback(null, err);
        return Promise.reject(err);
    }
};

module.exports = { apiColListener, apiCollDocListener };
