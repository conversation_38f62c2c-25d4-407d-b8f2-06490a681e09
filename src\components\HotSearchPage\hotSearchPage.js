import React, { useEffect, useState, useContext } from "react";
// store
import { StoreContext } from "../../store/StoreProvider";

// PropTypes
import PropTypes from "prop-types";

// ui
import { Container, Header } from "semantic-ui-react";

// css
import "./hotSearch.scss";

import { ResponsiveContainer } from "../../layout/Layout";
import { FormattedMessage, injectIntl } from "react-intl";
import Footer from "../../layout/Footer";

// ReactWordcloud 套件
import ReactWordcloud from "react-wordcloud";
import "tippy.js/dist/tippy.css";
import "tippy.js/animations/scale.css";
import { orgUrl, peopleUrl } from "../../services/common";
import { CLASS_PREFIX } from "../../config/config-ontology";
import queryString from "query-string";
import { bs64Encode } from "../../common/codes";

const HotSearchPage = props => {
    const [state] = useContext(StoreContext);
    const { fbReducer } = state;
    let realData = fbReducer.wordCloud;

    // 直接使用套件變數名稱words存入作家姓名等資料
    const [words, setWords] = useState([]);

    useEffect(() => {
        if (realData !== null) {
            const theData = Object.values(realData);
            // console.log("theData===", theData);
            getUrl(theData);
        }
    }, [realData]);
    const removeStartPrefix = (id, prefix) => {
        if (id && id.startsWith(prefix)) {
            return id.replace(prefix, "");
        }
        return id;
    };
    const getUrl = datas => {
        const url = "";

        // function for url encode
        if (Array.isArray(datas)) {
            // console.log("datas", datas);
            const tmpList = datas.map(b => {
                // console.log(b);
                // 不管是 Person 或 Organization,
                // 第二次 fetch 詳細資料的 response.data 的 key 都是 perId, srcName
                let url = "";
                if ((b.type || "").toLowerCase() === "person") {
                    // console.log("is person==", b.classType);
                    url = peopleUrl(
                        removeStartPrefix(b.id, CLASS_PREFIX.Person),
                        queryString.stringify({
                            name: bs64Encode(b.keyword || "")
                        })
                    );
                } else {
                    // Organization
                    url = orgUrl(
                        removeStartPrefix(b.id, CLASS_PREFIX.Organization),
                        queryString.stringify({
                            name: bs64Encode(b.keyword || "")
                        })
                    );
                }
                return {
                    ...(b || {}),
                    url: url
                };
            });

            let allAuthors = [];
            tmpList.map(list => {
                let theWriter = {
                    text: list.keyword,
                    value: list.count,
                    perId: list.id,
                    classType: list.type,
                    url: list.url
                };
                allAuthors.push(theWriter);
            });
            setWords(allAuthors);
        }
    };

    //= ============套件CSS設定=============
    const options = {
        // colors: [
        //     "#1f77b4",
        //     "#ff7f0e",
        //     "#2ca02c",
        //     "#d62728",
        //     "#9467bd",
        //     "#8c564b"
        // ], // 套件預設字色
        colors: [
            "#8D1C5E",
            "#B64A58",
            "#515151",
            "#A3B542",
            "#8F7B5A",
            "#FE6948"
        ], // 指定字色
        fontFamily: "Noto Sans TC",
        // fontSizes: [8, 60],
        fontSizes: [21, 70],
        fontStyle: "normal",
        fontWeight: "normal",
        padding: 6,
        scale: "linear",
        transitionDuration: 1000,
        rotations: 2,
        rotationAngles: [0, 0],
        enableTooltip: false
    };
    const Callbacks = {
        onWordClick: word => {
            // history.push('/zh-hans/people/JUU2JTk4JTkzJUU5JTg3JTkx?name=5piT6YeR');
            window.open(word.url, "_blank", "noopener noreferrer");
        }
    };

    const setPosition = {
        display: "flex",
        justifyContent: "center"
    };
    const isMobile = window.innerWidth <= 576;
    // const isPad = window.innerWidth <= 767 && window.innerWidth >= 576;
    // const isLaptop = window.innerWidth <= 992 && window.innerWidth >= 767;

    return (
        <ResponsiveContainer
            // 頁面的內容顯示在背景圖前面
            header={() => (
                <Container
                    className={"hotSearchPage"}
                    style={{
                        // 比照focusPointPage位置
                        marginTop: `calc(50vh - ${
                            isMobile ? 150 + 130 : 150 + 180
                        }px)`,
                        textAlign: "center",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center"
                    }}
                >
                    <Header
                        as="h2"
                        textAlign="center"
                        className={"fsHeader mtHeader mb-0"}
                    >
                        <FormattedMessage
                            id="menu.hotSearch"
                            defaultMessage="Top Searches"
                        />
                    </Header>
                    <div style={setPosition} className={"mtSvg"}>
                        <div className={"setSize"}>
                            <ReactWordcloud
                                words={words}
                                options={options}
                                callbacks={Callbacks}
                                style={{ width: "100%", height: "100%" }}
                            />
                        </div>
                    </div>
                </Container>
            )}
            withFooter={true}
            footer={Footer}
            {...props}
        >
            {/* 頁面的內容顯示在背景圖下方 */}
            {/* <Container className={"hotSearchPage"}> */}
            {/*    <Header */}
            {/*        as="h2" */}
            {/*        textAlign="center" */}
            {/*        className={"fsHeader headerLineHeight mtHeader mb-0"} */}
            {/*    > */}
            {/*        熱門搜尋 */}
            {/*    </Header> */}
            {/*    <div style={setPosition} className={"mtSvg"}> */}
            {/*        <div className={"setSize"}> */}
            {/*            <ReactWordcloud */}
            {/*                words={words} */}
            {/*                options={options} */}
            {/*                callbacks={Callbacks} */}
            {/*                style={{ width: "100%", height: "100%" }} */}
            {/*            /> */}
            {/*        </div> */}
            {/*    </div> */}
            {/* </Container> */}
        </ResponsiveContainer>
    );
};

HotSearchPage.propTypes = {
    match: PropTypes.shape({
        params: PropTypes.shape({
            name: PropTypes.string
        })
    }),
    user: PropTypes.shape({
        permission: PropTypes.number
    }),
    history: PropTypes.object
};
export default injectIntl(HotSearchPage);
