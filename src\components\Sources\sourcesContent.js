import React from "react";
import { FormattedHTMLMessage, FormattedMessage } from "react-intl";

export const intro = {
    title: (
        <FormattedMessage
            id="sources.intro-title"
            defaultMessage="Data sources"
        />
    ),
    // todo: 等客戶提供資料簡介，更新至 src/lang/en.json
    content: (
        <FormattedHTMLMessage
            id="sources.intro-content"
            defaultMessage={"Data sources description"}
        />
    )
};

// todo: 等客戶提供資料簡介，更新至 src/lang/en.json
export const sources = [
    {
        code: "AUDA",
        title: (
            <FormattedMessage
                id="sources.auda-title"
                defaultMessage={"Author Data"}
            />
        ),
        content: (
            <FormattedHTMLMessage
                id="sources.auda-content"
                defaultMessage={"database description"}
            />
        )
    },
    {
        code: "HKLIT",
        title: (
            <FormattedMessage
                id="sources.hklit-title"
                defaultMessage={"HKLit"}
            />
        ),
        content: (
            <FormattedHTMLMessage
                id="sources.hklit-content"
                defaultMessage={"database description"}
            />
        )
    },
    {
        code: "HKWRPR",
        title: (
            <FormattedMessage
                id="sources.hkwpr-title"
                defaultMessage={"HK Writers Profile"}
            />
        ),
        content: (
            <FormattedHTMLMessage
                id="sources.hkwpr-content"
                defaultMessage={"database description"}
            />
        )
    },
    {
        code: "ABCWHKP",
        title: (
            <FormattedMessage
                id="sources.abcwhkp-title"
                defaultMessage={
                    "An Annotated Bibliography of the Classical Writings of Hong Kong Poets"
                }
            />
        ),
        content: (
            <FormattedHTMLMessage
                id="sources.abcwhkp-content"
                defaultMessage={"database description"}
            />
        )
    }
];
