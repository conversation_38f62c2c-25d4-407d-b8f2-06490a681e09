import Act from "../actions";
import { getReaderPermission } from "../../services/permission";
import { Api } from "../../api/hkbdb/Api";
import role from "../../App-role";

const initState = {
    // displayName: "demo",
    // email: "<EMAIL>",
    // isAnonymous: false,
    // readablePeople: [],
    // readableOrganization: [],
    // permission: getReaderPermission(),
    role: role.anonymous,
    locale: Api.locale_lang.LOCALE_DEFAULT,
    // 紀錄使用者在 information 頁面時所顯示的排序(column)
    settings: { tableSortedRecords: { information: {} } },
    token: null
};

const userReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.DEMO_USER:
            return { ...state, ...action.payload };
        case Act.DEMO_USER_CLEAN:
            return {};
        case Act.SET_USER_LOCALE:
            return { ...state, locale: action.payload };

        case Act.FIREBASE_LOGIN_USER:
            return { ...state, ...action.payload };
        case Act.FIREBASE_USER_TOKEN:
            return { ...state, token: action.payload };
        case Act.FIREBASE_LOGOUT_USER:
            // 如果以英文語系進入畫面，在未登入的情況下，語系會被設定回 default
            initState.locale = state.locale
                ? state.locale
                : Api.locale_lang.LOCALE_DEFAULT;
            return { ...initState };
        case Act.USER_USER_SETTINGS_SET:
            return {
                ...state,
                settings: {
                    ...state.settings,
                    tableSortedRecords: action.payload
                }
            };
        default:
            return state;
    }
};

export default userReducer;
