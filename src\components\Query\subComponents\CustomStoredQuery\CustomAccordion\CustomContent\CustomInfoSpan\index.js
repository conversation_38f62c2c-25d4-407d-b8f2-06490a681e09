// react
import React from "react";

// ui
import { Container } from "semantic-ui-react";
import { FormattedMessage, injectIntl } from "react-intl";

const CustomInfoSpan = ({ displayName, translation, email }) => {
    const customSpanStyle = {
        paddingRight: ".5em",
        fontWeight: "600",
        fontSize: "x-small"
    };
    const customDivStyle = {
        fontSize: "x-small"
    };
    return (
        <Container>
            <span style={customSpanStyle}>
                <FormattedMessage id="custom.author" defaultMessage="Author" />
            </span>
            <div style={customDivStyle}>{displayName}</div>
            {/* <br /> */}
            <span style={customSpanStyle}>
                <FormattedMessage
                    id="custom.translation"
                    defaultMessage="Title"
                />
            </span>
            <div style={customDivStyle}>{translation}</div>
            {/* <br /> */}
            <span style={customSpanStyle}>
                <FormattedMessage id="custom.email" defaultMessage="Email" />
            </span>
            <div style={customDivStyle}>
                <a href={"mailto:" + email}>{email}</a>
            </div>
        </Container>
    );
};

export default injectIntl(CustomInfoSpan);
