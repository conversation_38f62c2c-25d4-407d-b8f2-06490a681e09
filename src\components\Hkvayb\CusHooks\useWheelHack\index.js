import React from "react";

const useWheelHack = (timeout = 300) => {
    const wheelTimeout = React.useRef();

    // block the body from scrolling while wheelTimeout is set
    React.useEffect(() => {
        const maybeCancelWheel = e =>
            wheelTimeout.current && e.preventDefault();
        window.addEventListener("wheel", maybeCancelWheel, {
            passive: false
        });
        return () => window.removeEventListener("wheel", maybeCancelWheel);
    }, []);

    // return a function that can be used to prevent scrolling for timeout ms
    return () => {
        clearTimeout(wheelTimeout.current);
        wheelTimeout.current = setTimeout(() => {
            wheelTimeout.current = false;
        }, timeout);
    };
};

export default useWheelHack;
