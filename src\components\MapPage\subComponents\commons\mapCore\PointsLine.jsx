/* eslint-disable no-unused-vars, react/prop-types */
import React, { useEffect, useMemo, useCallback } from "react";
import { useMap } from "react-leaflet";
import L from "leaflet";
import "leaflet-arrowheads"; // 目前無使用於此組件
import { circleStyleDef } from "../config";

// 基礎樣式選項
export const basicOptions = {
    color: "black",
    weight: 2,
    opacity: 0.5
};

/**
 * 安全地將值解析為浮點數
 * @param {string|number} value - 輸入值
 * @returns {number} 解析後的數字，若無效則返回 0
 */
const safeParseFloat = value => {
    const num = typeof value === "string" ? parseFloat(value) : value;
    return Number.isNaN(num) ? 0 : num;
};

/**
 * PointsLineDiagnostic 元件
 * 繪製兩點之間的線條，可選擇啟用縮短和箭頭功能以進行效能診斷。
 *
 * @param {object} props - 元件屬性
 * @param {object} props.srcPoint - 起點座標 { lat: number|string, long: number|string }
 * @param {object} props.dstPoint - 終點座標 { lat: number|string, long: number|string }
 * @param {object} [props.style] - 線條樣式 { color?: string, weight?: number, dashArray?: string }
 * @param {boolean} [props.enableShortening=false] - 是否啟用線條端點縮短功能
 * @param {boolean} [props.enableArrowheads=false] - 是否啟用線條終點箭頭
 */
const PointsLine = props => {
    const {
        srcPoint,
        dstPoint,
        style,
        enableShortening = false, // 控制是否縮短，預設 false
        enableArrowheads = false // 控制是否有箭頭，預設 false
    } = props;

    const map = useMap();

    // console.log('enableArrowheads', enableArrowheads)
    // 1. 使用 useMemo 緩存解析後的 polyLine 座標
    // 只有當 srcPoint 或 dstPoint 的 lat/long 改變時才重新計算
    const polyLine = useMemo(() => {
        const srcLat = safeParseFloat(srcPoint?.lat);
        const srcLng = safeParseFloat(srcPoint?.long);
        const dstLat = safeParseFloat(dstPoint?.lat);
        const dstLng = safeParseFloat(dstPoint?.long);

        // 檢查座標有效性
        if (
            srcPoint == null ||
            dstPoint == null ||
            isNaN(srcLat) ||
            isNaN(srcLng) ||
            isNaN(dstLat) ||
            isNaN(dstLng)
        ) {
            console.warn("PointsLineDiagnostic: 無效座標。", {
                srcPoint,
                dstPoint
            });
            return null; // 返回 null 表示無效
        }

        return [
            [srcLat, srcLng],
            [dstLat, dstLng]
        ];
    }, [srcPoint?.lat, srcPoint?.long, dstPoint?.lat, dstPoint?.long]); // 精確依賴

    // 從 style prop 或預設值中解構樣式屬性
    const {
        color = basicOptions.color,
        weight = basicOptions.weight,
        dashArray = null
    } = style || {};

    // 2. 使用 useCallback 包裝 shortenPolyline 函數 (只有在 enableShortening 為 true 時才實際需要)
    // 依賴 map 實例
    const shortenPolyline = useCallback(
        (start, end, shortenPx) => {
            // 基本的有效性檢查
            if (
                !map ||
                !start ||
                !end ||
                isNaN(start.lat) ||
                isNaN(start.lng) ||
                isNaN(end.lat) ||
                isNaN(end.lng)
            ) {
                console.warn("ShortenPolyline: 無效輸入", { start, end });
                return null; // 返回 null 表示無法計算
            }
            try {
                const latLngToPixel = latLng => map.latLngToLayerPoint(latLng);
                const pixelToLatLng = point => map.layerPointToLatLng(point);

                const startPixel = latLngToPixel(
                    L.latLng(start.lat, start.lng)
                );
                const endPixel = latLngToPixel(L.latLng(end.lat, end.lng));

                const direction = {
                    x: endPixel.x - startPixel.x,
                    y: endPixel.y - startPixel.y
                };
                const length = Math.sqrt(direction.x ** 2 + direction.y ** 2);

                // 如果線條長度小於等於需要縮短的總長度，則無法有效縮短
                if (length <= 2 * shortenPx + 1e-6) {
                    // 加上一個小容差
                    console.warn("ShortenPolyline: 縮短距離超過線長", {
                        length,
                        shortenPx
                    });
                    return null; // 返回 null 表示無法縮短
                }

                const unitVector = {
                    x: direction.x / length,
                    y: direction.y / length
                };
                const shortenVector = {
                    x: unitVector.x * shortenPx,
                    y: unitVector.y * shortenPx
                };

                const newStartPixel = {
                    x: startPixel.x + shortenVector.x,
                    y: startPixel.y + shortenVector.y
                };
                const newEndPixel = {
                    x: endPixel.x - shortenVector.x,
                    y: endPixel.y - shortenVector.y
                };

                const newStart = pixelToLatLng(newStartPixel);
                const newEnd = pixelToLatLng(newEndPixel);

                return [newStart, newEnd]; // 返回縮短後的 [起點, 終點]
            } catch (error) {
                console.error("ShortenPolyline 計算錯誤:", error);
                return null; // 出錯時返回 null
            }
        },
        [map]
    );

    // 3. 主要的 useEffect，負責在地圖上繪製和移除線條
    useEffect(() => {
        // 基本條件檢查
        if (!map || !polyLine) {
            return;
        }

        let line = null; // 用於儲存創建的 Leaflet 線條實例
        try {
            // 準備線條樣式
            const lineOptions = { ...basicOptions, color, weight, dashArray };

            // 決定最終要繪製的線條座標
            let finalLineCoords = polyLine; // 預設使用原始座標

            // 如果啟用了縮短功能
            if (enableShortening) {
                const shortened = shortenPolyline(
                    { lat: polyLine[0][0], lng: polyLine[0][1] },
                    { lat: polyLine[1][0], lng: polyLine[1][1] },
                    circleStyleDef.radius
                );
                // 如果縮短成功，則使用縮短後的座標
                if (shortened) {
                    finalLineCoords = shortened;
                } else {
                    // 如果縮短失敗 (例如線太短)，可以選擇不繪製或繪製原始線條
                    console.warn("無法縮短線條，將使用原始座標 (或不繪製)。", {
                        polyLine
                    });
                    // return; // 如果選擇不繪製，可以在此返回
                }
            }

            // 創建 L.polyline 實例
            line = L.polyline(finalLineCoords, lineOptions);

            // 如果啟用了箭頭功能
            if (enableArrowheads) {
                line.arrowheads({
                    yawn: 60,
                    fill: true,
                    size: "10px",
                    color: color, // 箭頭顏色與線條一致
                    frequency: "endonly"
                });
            }

            // 將線條添加到地圖
            line.addTo(map);
        } catch (e) {
            console.error("創建或添加線條失敗:", e, {
                polyLine,
                style,
                enableShortening,
                enableArrowheads
            });
        }

        // 清理函數：當元件卸載或依賴項改變時執行
        return () => {
            if (line && map && map.hasLayer(line)) {
                try {
                    line.remove(); // 從地圖移除線條
                } catch (removeError) {
                    console.error("移除線條時出錯:", removeError);
                }
            }
        };
        // 4. 精確指定依賴項
        // 依賴 map 實例, 計算好的 polyLine 座標, 是否啟用縮短/箭頭, 以及線條樣式屬性
        // shortenPolyline 函數本身也作為依賴項
    }, [
        map,
        polyLine,
        enableShortening,
        enableArrowheads,
        color,
        weight,
        dashArray,
        shortenPolyline
    ]);

    // 此元件本身不渲染任何 DOM 元素
    return null;
};

export default PointsLine;
