import React from "react";

import { FormattedMessage } from "react-intl";

import CusTitle from "../CusTitle";
import CusSwiper from "../CusSwiper";

import useStyles from "./style";

const CusTour = ({ data, style = {} }) => {
    const classes = useStyles(style);
    return (
        <div className={classes.hkvayb_tour}>
            <CusTitle
                label={
                    <FormattedMessage
                        id="hkvayb.search.tour.quick"
                        defaultMessage="Quick Tour"
                    />
                }
            />
            <CusSwiper lineArrow underLine data={data} />
        </div>
    );
};

export default CusTour;
