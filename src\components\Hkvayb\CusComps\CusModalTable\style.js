import { makeStyles } from "@material-ui/styles";

const useStyle = makeStyles({
    root: {},
    hkvayb_tableTitle: props => ({
        width: "200px",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.5",
        letterSpacing: "0.32px",
        textAlign: "left",
        verticalAlign: "top",
        color: "#333",
        ...props.hkvayb_tableTitle
    }),
    hkvayb_tableContent: props => ({
        width: "255px",
        fontFamily: "NotoSansHK",
        fontSize: "16px",
        fontWeight: "normal",
        fontStretch: "normal",
        fontStyle: "normal",
        lineHeight: "1.5",
        letterSpacing: "0.32px",
        verticalAlign: "top",
        color: "#333",
        ...props.hkvayb_tableContent
    })
});

export default useStyle;
