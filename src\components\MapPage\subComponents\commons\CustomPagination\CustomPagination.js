import React from "react";
import PropTypes from "prop-types";

import { Icon } from "semantic-ui-react";

// scss
import "./CustomPagination.scss";

// components
import PerPgNumDd from "./PerPgNumDD";
import PgDD from "./PgDD";
import Pagination from "@mui/material/Pagination";

function CustomPagination(props) {
    const { handlePage, currentPage, totalPages, handlePerPageNum } = props;
    const { handleDDPage, pageOption } = props;
    return (
        <div className="Pagination">
            {/* <div className="chooseRange"> */}
            {/*    <PerPgNumDd */}
            {/*        handlePerPageNum={handlePerPageNum} */}
            {/*        pageOption={pageOption} */}
            {/*    /> */}
            {/* </div> */}
            <Pagination
                // activePage={currentPage}
                // ellipsisItem={{
                //     content: <Icon name="ellipsis horizontal" />,
                //     icon: true
                // }}
                // firstItem={{
                //     content: <Icon name="angle double left" />,
                //     icon: true
                // }}
                // lastItem={{
                //     content: <Icon name="angle double right" />,
                //     icon: true
                // }}
                // prevItem={{ content: <Icon name="angle left" />, icon: true }}
                // nextItem={{ content: <Icon name="angle right" />, icon: true }}
                // totalPages={totalPages}
                // onPageChange={handlePage}

                page={currentPage}
                count={totalPages}
                shape="rounded"
                onChange={handlePage}
                sx={{
                    "& .MuiPaginationItem-root": {
                        color: "#104860"
                    },
                    "& .MuiPaginationItem-root.Mui-selected": {
                        backgroundColor: "#104860",
                        color: "white",
                        "&:hover": {
                            backgroundColor: "#043348"
                        }
                    },
                    "& .MuiPaginationItem-previousNext": {
                        color: "rgb(182,227,245)"
                    }
                }}
            />
            <div className="selectPage">
                <PgDD
                    handleDDPage={handleDDPage}
                    totalPages={totalPages}
                    currentPage={currentPage}
                />
            </div>
        </div>
    );
}

CustomPagination.propTypes = {
    /** page change callback */
    handlePage: PropTypes.func,
    /** 目前頁碼 */
    currentPage: PropTypes.number,
    /** 總頁數 */
    totalPages: PropTypes.number,
    /** 控制每頁幾筆的callback */
    handlePerPageNum: PropTypes.func,
    /** 控制下拉選單選擇頁碼的callback */
    handleDDPage: PropTypes.func,
    /** 一頁顯示幾筆選項 */
    pageOption: PropTypes.arrayOf(PropTypes.number)
};

CustomPagination.defaultProps = {
    /** page change callback */
    handlePage: () => {},
    /** 目前頁碼 */
    currentPage: 1,
    /** 總頁數 */
    totalPages: 1,
    /** 控制每頁幾筆的callback */
    handlePerPageNum: () => {},
    /** 控制下拉選單選擇頁碼的callback */
    handleDDPage: () => {},
    /** 一頁顯示幾筆選項 */
    pageOption: [5, 10, 15]
};

export default CustomPagination;
