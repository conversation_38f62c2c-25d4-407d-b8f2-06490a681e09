import React from "react";

import {
    Accordion,
    AccordionActions,
    FormControlLabel,
    makeStyles,
    AccordionSummary,
    Divider
} from "@material-ui/core";

import ExpandMoreIcon from "@material-ui/icons/ExpandMore";

import CusInput from "./CusInput";
import CusChips from "./CusChips";
import CusReset from "./CusReset";
import CusSubmit from "./CusSubmit";
import CusTextFields from "./CusTextFields";

const useStyles = makeStyles(theme => ({
    root: {
        width: "100%"
    },
    heading: {
        fontSize: theme.typography.pxToRem(15)
    },
    secondaryHeading: {
        fontSize: theme.typography.pxToRem(15),
        color: theme.palette.text.secondary
    },
    icon: {
        verticalAlign: "bottom",
        height: 20,
        width: 20
    },
    details: {
        alignItems: "center"
    },
    column: {
        flexBasis: "50%"
    },
    leftColumn: {
        flexBasis: "70%"
    },
    rightColumn: {
        flexBasis: "30%"
    },
    helper: {
        borderLeft: `2px solid ${theme.palette.divider}`,
        padding: theme.spacing(1, 2)
    },
    link: {
        color: theme.palette.primary.main,
        textDecoration: "none",
        "&:hover": {
            textDecoration: "underline"
        }
    },
    fullwidth: {
        width: "100%"
    },
    accordionActionChip: {
        justifyContent: "center"
    },
    divider: {
        height: 28,
        margin: 4
    }
}));

const CusSearchBar = () => {
    const classes = useStyles();
    // todo fetch data
    const mockData = [
        { label: "專題論述", value: "Essay" },
        { label: "公眾議題", value: "PublicIssue" },
        { label: "藝術論著", value: "Publication" },
        { label: "藝術展覽", value: "Exhibition" },
        { label: "藝術講座／研討會", value: "TalksSymposium" },
        { label: "藝術獎項", value: "AwardEvent" },
        { label: "藝術教育", value: "EducationEvent" },
        { label: "香港展覽場地", value: "VenueInHK" },
        { label: "藝術拍賣", value: "Auction" }
    ];
    // todo fetch data
    const searchData = [
        { label: "標題", value: "title", type: "search" },
        { label: "作者/ 藝術家", value: "author", type: "search" },
        { label: "機構", value: "mechanism", type: "search" },
        {
            label: "年分",
            value: "year",
            type: "select",
            options: Array(2019 - 1999 + 1)
                .fill()
                .map((_, idx) => 1999 + idx)
                .map(year => ({
                    label: year,
                    value: year
                }))
        },
        {
            label: "地區",
            value: "area",
            type: "select",
            options: [
                { label: "本地", value: "local" },
                { label: "非本地", value: "nonLocal" }
            ]
        },
        {
            label: "展覽媒體",
            value: "media",
            type: "select",
            options: [
                { label: "文物", value: "Antiquities" },
                { label: "建築", value: "Architecture" },
                { label: "陶藝", value: "Ceramics" },
                { label: "兒童藝術", value: "Children Art" },
                {
                    label: "書畫篆字",
                    value: "Chinese Painting, Calligraphy and Seal Carving"
                },
                { label: "漫畫", value: "Comics" },
                { label: "設計", value: "Design" },
                { label: "電子藝術", value: "Electronic Art" },
                { label: "電子媒介", value: "Electronic Media" },
                { label: "綜合展", value: "Mixed Art Forms" },
                {
                    label: "混合媒介及裝置",
                    value: "Mixed Media and Installation"
                },
                { label: "新媒體藝術", value: "New Media Art" },
                { label: "繪畫", value: "Painting" },
                { label: "行為藝術", value: "Performance Arts" },
                { label: "攝影", value: "Photography" },
                { label: "版畫", value: "Prints" },
                { label: "雕塑", value: "Sculpture" },
                { label: "其他", value: "Others" }
            ]
        },
        {
            label: "藝術講座／研討會類別",
            value: "talksSymposiumType",
            type: "select",
            options: [
                { label: "講座", value: "Lectures" },
                { label: "研討會", value: "Seminar" },
                { label: "工作坊", value: "Workshops" },
                { label: "藝術家分享", value: "Artist Sharing" },
                { label: "放映", value: "Screening" },
                { label: "其他活動地點", value: "Others" }
            ]
        },
        {
            label: "藝術論著類別",
            value: "publicationType",
            type: "select",
            options: [
                { label: "報章", value: "Newspaper" },
                { label: "期刊", value: "Periodical" },
                { label: "書籍", value: "Books" },
                { label: "圖錄/作品集", value: "Catalogues" },
                { label: "學術論文", value: "學術論文" },
                { label: "其他", value: "Others" }
            ]
        },
        {
            label: "藝術獎項",
            value: "awardEventType",
            type: "select",
            options: [
                { label: "藝術家留駐計畫", value: "Artists-in-residency" },
                { label: "比賽", value: "Competitions" },
                { label: "實習計畫", value: "internship" },
                { label: "獎項", value: "Prizes" },
                { label: "獎學金/獎助金", value: "Scholarship/ Fellowship" }
            ]
        },
        {
            label: "教育院校",
            value: "educationType",
            type: "select",
            options: [
                { label: "耀中社區書院", value: "ORG耀中社區書院" },
                { label: "明愛白英奇專業學校", value: "ORG明愛白英奇專業學校" },
                { label: "香港城市大學", value: "ORG香港城市大學" },
                { label: "CO1設計學校", value: "ORGCO1設計學校" },
                {
                    label: "香港城市大學專上學院(CCCU)",
                    value: "ORG香港城市大學專上學院"
                },
                { label: "大一藝術設計學院", value: "ORG大一藝術設計學院" },
                { label: "香港藝術學院", value: "ORG香港藝術學院" },
                { label: "香港浸會大學", value: "ORG香港浸會大學" },
                { label: "香港專業進修學校", value: "ORG香港專業進修學校" },
                {
                    label: "香港理工大學香港專上學院(HKCC)",
                    value: "ORG香港理工大學香港專上學院"
                },
                { label: "香港知專設計學院", value: "ORG香港知專設計學院" },
                { label: "香港專業教育學院", value: "ORG香港專業教育學院" },
                { label: "香港設計學院", value: "ORG香港設計學院" },
                {
                    label: "香港公開大學李嘉誠專業進修學院",
                    value: "ORG香港公開大學李嘉誠專業進修學院"
                },
                { label: "嶺南大學", value: "ORG嶺南大學" },
                {
                    label: "澳洲皇家墨爾本理工大學",
                    value: "ORG澳洲皇家墨爾本理工大學"
                },
                { label: "薩凡納藝術設計大學", value: "ORG薩凡納藝術設計大學" },
                {
                    label: "香港城市大學專業進修學院(SCOPE)",
                    value: "ORG香港城市大學專業進修學院"
                },
                {
                    label: "香港中文大學專業進修學院(SCS)",
                    value: "ORG香港中文大學專業進修學院"
                },
                {
                    label: "香港浸會大學持續教育學院(SCE)",
                    value: "ORG香港浸會大學持續教育學院"
                },
                {
                    label: "香港大學專業進修學院(SPACE)",
                    value: "ORG香港大學專業進修學院"
                },
                {
                    label: "香港理工大學專業進修學院(SPEED)",
                    value: "ORG香港理工大學專業進修學院"
                },
                { label: "香港中文大學", value: "ORG香港中文大學" },
                { label: "香港教育學院", value: "ORG香港教育學院" },
                { label: "香港理工大學", value: "ORG香港理工大學" },
                { label: "香港大學", value: "ORG香港大學" },
                {
                    label: "英國赫德斯菲爾德大學",
                    value:
                        "ORG明愛白英奇專業學校、英國赫德斯菲爾德大學_(University_of_Huddersfield)"
                }
            ]
        },
        {
            label: "藝術拍賣",
            value: "auctionType",
            type: "select",
            options: [
                { label: "香港佳士得", value: "ORG香港佳士得" },
                { label: "香港蘇富比", value: "ORG香港蘇富比" }
            ]
        }
    ];
    //
    return (
        <div className={classes.root}>
            <Accordion defaultExpanded>
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1c-content"
                    id="panel1c-header"
                >
                    <FormControlLabel
                        className={classes.fullwidth}
                        aria-label="Acknowledge"
                        onClick={event => event.stopPropagation()}
                        onFocus={event => event.stopPropagation()}
                        control={
                            <React.Fragment>
                                {/* Search keyword */}
                                <CusInput />
                                <Divider
                                    className={classes.divider}
                                    orientation="vertical"
                                />
                                {/* Search Submit */}
                                <CusSubmit />
                                {/* Search Reset */}
                                <CusReset />
                            </React.Fragment>
                        }
                        label={undefined}
                    />
                </AccordionSummary>
                <Divider />
                <AccordionActions className={classes.accordionActionChip}>
                    {/* Search Types */}
                    <CusChips data={mockData} />
                </AccordionActions>
                <Divider />
                <AccordionActions>
                    {/* Search Options */}
                    <CusTextFields data={searchData} />
                </AccordionActions>
            </Accordion>
        </div>
    );
};

export default CusSearchBar;
