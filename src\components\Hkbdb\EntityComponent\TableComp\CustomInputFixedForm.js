import React, { useEffect, useState } from "react";

// ui
import { Icon, Input, Label } from "semantic-ui-react";

// custom
import CustomDebounce from "./CustomDeBounce";

// common
import { isEmpty } from "../../../../common/codes";

const CustomInput = ({ property, createData, setCreateData }) => {
    //
    const { ontologyType } = createData;
    const { value: propertyName, label, required } = property;
    const oriPropName = propertyName.split("__")[0];
    //
    const [selectedValue, setSelectedValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSelectedValue = CustomDebounce(selectedValue, 500);
    //
    const handleUpdateState = () => {
        if (!isEmpty(propertyName) && !isEmpty(debSelectedValue)) {
            setCreateData(prevData => ({
                ...prevData,
                willCreatedData: {
                    ...prevData.willCreatedData,
                    classType: ontologyType,
                    value: {
                        ...prevData.willCreatedData.value,
                        [propertyName]: debSelectedValue
                    }
                }
            }));
        } else {
            console.log("param error, propertyName: ", propertyName);
        }
    };
    //
    const handleChange = (event, { value }) => {
        // console.log(value);
        if (!isEmpty(value)) {
            setSelectedValue(value);
        }
    };
    //
    useEffect(() => {
        handleUpdateState();
    }, [debSelectedValue]);
    //
    return (
        <Input
            fluid
            labelPosition="left"
            type="text"
            onChange={handleChange}
            placeholder={`${oriPropName}`}
        >
            <Label>
                {label}
                {required && (
                    <Icon
                        style={{ marginLeft: "1.5em" }}
                        color="red"
                        name="asterisk"
                        size="mini"
                    />
                )}
            </Label>
            <input />
        </Input>
    );
};

export default CustomInput;
