import React from "react";

import { Message, Table } from "semantic-ui-react";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomDropdown from "./CustomDropdownFixedCreate";
import CustomGraphDropdown from "./CustomGraphDropdown";
import CustomInput from "./CustomInputFixedForm";
import CustomDateInput from "./CustomDateInput";

// common
import { isEmpty } from "../../../../common/codes";
import CustomCreateCoordinatesModal from "./CustomCreateCoordinatesModal";

const CustomForm = ({ createData, setCreateData }) => {
    //
    return (
        <Table celled>
            <Table.Body>
                <Table.Row disabled={createData.isCreated}>
                    <Table.Cell>
                        {/* <pre>{JSON.stringify(createData.selectedProperties, null, 2)}</pre> */}
                        {/* select graph */}
                        <CustomGraphDropdown setCreateData={setCreateData} />
                    </Table.Cell>
                </Table.Row>
                {/* show selected value by dropdown */}
                {isEmpty(createData.selectedProperties) ? (
                    <Table.Row disabled={createData.isCreated}>
                        <Table.Cell>
                            <Message
                                warning
                                // header="目前沒有任何的 Property。"
                                header={
                                    <FormattedMessage
                                        id={"people.Information.formTable.item"}
                                        defaultMessage={
                                            "There are no property in Form Table"
                                        }
                                    />
                                }
                            />
                        </Table.Cell>
                    </Table.Row>
                ) : (
                    createData.selectedProperties.map((property, idx) => {
                        //
                        const { range } = property;
                        //
                        switch (range) {
                            case "Place":
                            case "Organization":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={createData.isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomCreateCoordinatesModal
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            case "string":
                            case "float":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={createData.isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            case "DateEvent":
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={createData.isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomDateInput
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                            default:
                                return (
                                    <Table.Row
                                        key={`create-property-form-${idx}`}
                                        disabled={createData.isCreated}
                                    >
                                        <Table.Cell>
                                            <CustomDropdown
                                                property={property}
                                                createData={createData}
                                                setCreateData={setCreateData}
                                            />
                                        </Table.Cell>
                                    </Table.Row>
                                );
                        }
                    })
                )}
            </Table.Body>
        </Table>
    );
};

export default CustomForm;
