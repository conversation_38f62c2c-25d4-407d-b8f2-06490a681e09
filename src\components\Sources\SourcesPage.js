import React, { Component } from "react";
import {
    <PERSON>,
    Container,
    Di<PERSON>r,
    <PERSON><PERSON>,
    Header,
    Image,
    Segment
} from "semantic-ui-react";
import { injectIntl } from "react-intl";
import { ResponsiveContainer } from "../../layout/Layout";
import DatasetPage from "./DatasetPage";
import DatasetLinkPage from "./DatasetLinkPage";

class SourcesPage extends Component {
    render() {
        const link = this.props.match.params.link;
        return (
            <ResponsiveContainer {...this.props}>
                <Segment vertical>
                    {link ? <DatasetLinkPage link={link} /> : <DatasetPage />}
                </Segment>
            </ResponsiveContainer>
        );
    }
}

export default injectIntl(SourcesPage);
