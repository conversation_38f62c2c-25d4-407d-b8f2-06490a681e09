// firebase
import firebase from "firebase/app";
import "firebase/database";
// commons code
import { isEmpty } from "../../../../common/codes";

// get user from realtime database
export const getUser = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`/users/${safeUid}`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

// set user to realtime database
export const setUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`/users/${safeUid}`)
        .set(data);
};

// update user to realtime database
export const updateUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}`)
        .update(data);
};
