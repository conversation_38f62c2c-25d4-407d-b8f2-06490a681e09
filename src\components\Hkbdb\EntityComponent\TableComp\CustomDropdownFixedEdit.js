import React, { useContext, useEffect, useState } from "react";

// react select
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable/dist/react-select.esm";

// lang
import { FormattedMessage } from "react-intl";

// custom
import CustomDebounce from "./CustomDeBounce";

// common
import { isEmpty, isNotEmpty, safeGet } from "../../../../common/codes";

// common comp
import MenuList from "./MenuList";
import MenuListFooter from "./MenuListFooter";

// api
import { Api, doRestCreate, readHkbdbData } from "../../../../api/hkbdb/Api";
import { fetchOptionList } from "../commonAction";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import allRoles from "../../../../App-role";

const CustomDropdown = ({
    rowIdx,
    graph,
    eventId,
    editData,
    setEditData,
    defaultValue,
    ontologyType,
    propRange,
    propertyBindRangeStr
}) => {
    //
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { ontologyDefined } = state.property;
    //
    const [inputValue, setInputValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debInputValue = CustomDebounce(inputValue, 1000);
    //
    const [optionState, setOptionState] = useState(() => {
        const defaultOptions = defaultValue.map(opt => ({
            label: opt.label,
            value: opt.value
        }));

        return {
            isLoading: false,
            options: [],
            value: [...defaultOptions],
            addTimes: 1,
            limit: 10
        };
    });
    //
    const handleInputChange = value => {
        setInputValue(value);
    };
    //
    const handleChange = selectValue => {
        //
        selectValue = isEmpty(selectValue) ? [] : selectValue;
        // keep input value when it changed
        setOptionState(prevCreateState => ({
            ...prevCreateState,
            value: selectValue
        }));
        // change content
        const changedData = {
            // 注意: 這裡 rowIdx 會自動被轉為 string，object' key 只允許字串內容
            // 後續使用 rowIdx 時要注意
            [rowIdx]: {
                graph,
                srcId: eventId,
                classType: ontologyType,
                propertyBindRangeStr,
                values: selectValue
            }
        };
        // update useState
        if (isNotEmpty(safeGet(changedData, [rowIdx, "values"], []))) {
            // update editData state
            setEditData(prevEditData => {
                return {
                    ...prevEditData,
                    changedData: {
                        // old changed items
                        ...prevEditData.changedData,
                        // new changed item
                        ...changedData
                    }
                };
            });
        } else {
            setEditData(prevEditData => {
                //
                const {
                    [rowIdx]: _,
                    ...restChangedData
                } = prevEditData.changedData;
                //
                return {
                    ...prevEditData,
                    changedData: {
                        ...restChangedData
                    }
                };
            });
        }
    };
    //
    const handleCreate = async inputValue => {
        //
        let objValue = {};
        //
        const foundClassName = Object.keys(ontologyDefined).find(
            key =>
                `${key}`.toLowerCase().indexOf(`${propRange}`.toLowerCase()) !==
                -1
        );
        //
        if (isNotEmpty(foundClassName)) {
            //
            const targetData = safeGet(ontologyDefined, [foundClassName], []);
            //
            const foundLabel = targetData.find(
                item => item.property === "label"
            );
            //
            const foundBestknownName = targetData.find(
                item => item.property === "bestKnownName"
            );
            //
            if (isNotEmpty(foundLabel)) {
                objValue = {
                    ...objValue,
                    [`label_${propRange}`]: [inputValue]
                };
            }
            //
            if (isNotEmpty(foundBestknownName)) {
                objValue = {
                    ...objValue,
                    bestKnownName: [inputValue]
                };
            }
            // label 或 bestKnownName 都不存在則預設使用 label
            if (isEmpty(foundLabel) && isEmpty(foundBestknownName)) {
                objValue = {
                    ...objValue,
                    [`label_${propRange}`]: [inputValue]
                };
            }
        } else {
            // 連類別都找不到的話，預設就用 label
            objValue = {
                ...objValue,
                [`label_${propRange}`]: [inputValue]
            };
        }
        //
        const changedData = {
            graph: "not_needed",
            srcId: "not_needed",
            classType: propRange,
            value: objValue
        };
        //
        // const createdData = {
        //     graph,
        //     srcId: eventId,
        //     classType: ontologyType,
        //     propertyBindRangeStr,
        //     values: [inputValue]
        // };
        //
        if (!isEmpty(changedData)) {
            // open isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: true
            }));
            // call create api
            const result = await doRestCreate(user, changedData);
            //
            // console.log(result);
            if (result.state) {
                //
                const apiStr = Api.findIdByValue()
                    .replace("{class}", propRange)
                    .replace("{keyword}", inputValue);
                //
                const result = await readHkbdbData(apiStr);
                //
                const objId = safeGet(result, ["data", 0, "value"], "");
                // console.log("objId", objId);
                //
                if (isNotEmpty(objId)) {
                    // add new option
                    const newOption = {
                        label: inputValue,
                        value: objId
                    };
                    // console.log(newOption);
                    // update state
                    setOptionState(prevCreateState => ({
                        ...prevCreateState,
                        options: [...prevCreateState.options, newOption],
                        value: [...prevCreateState.value, newOption]
                    }));
                    // update editData state
                    setEditData(prevEditData => ({
                        ...prevEditData,
                        changedData: {
                            // old changedData records
                            ...prevEditData.changedData,
                            // new ChangedData record
                            ...{
                                [rowIdx]: {
                                    graph,
                                    srcId: eventId,
                                    classType: ontologyType,
                                    propertyBindRangeStr,
                                    values: [
                                        // to change object list to string list
                                        ...optionState.value.map(
                                            item => item.value
                                        ),
                                        // new add item
                                        objId
                                    ]
                                }
                            }
                        }
                    }));
                } else {
                    console.log("handleCreate failed to add item:", inputValue);
                }
                // console.log(result);
            }
            // close isLoading state
            setOptionState(prevCreateState => ({
                ...prevCreateState,
                isLoading: false
            }));
        }
        // console.log(createdData);
    };
    //
    const customStyles = {
        container: styles => ({
            ...styles
            // margin: "-9px",
            // maxWidth: "500px"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            // borderStyle: "none",
            // borderRadius: "unset",
            backgroundColor: controlColor
        })
    };
    //
    const addOption = () => {
        //
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(propRange, debInputValue, limitValue, 30 * 1000)
            .then(res => {
                //
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                //
                setOptionState(prevState => ({
                    ...prevState,
                    options: [...optionState.value, ...newData],
                    addTimes: prevState.addTimes + 1,
                    isLoading: false
                }));
            })
            .catch(error => {
                //
                console.error(error.message);
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    isLoading: false
                }));
            });
    };
    //
    useEffect(() => {
        //
        if (isEmpty(propRange)) {
            return;
        }
        // open dropdown Loading
        setOptionState(prevState => ({
            ...prevState,
            isLoading: true
        }));
        //
        const limitValue = optionState.addTimes * optionState.limit;
        //
        fetchOptionList(propRange, debInputValue, limitValue, 30 * 1000)
            .then(res => {
                //
                const newData = res.data.map(item => {
                    const { label, value, propertyLabel, subLabels } = item;
                    if (isNotEmpty(propertyLabel) && isNotEmpty(subLabels)) {
                        const maxNumber = 5;
                        const subLabelsArr = subLabels.split("@split");
                        const hitLabelsArr = [
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) !== -1
                            ),
                            ...subLabelsArr.filter(
                                _label => _label.indexOf(debInputValue) === -1
                            )
                        ];
                        const newSubLabels = hitLabelsArr
                            .slice(0, maxNumber)
                            .join(", ");
                        const dotdotdot =
                            subLabelsArr.length > maxNumber ? "..." : "";
                        return {
                            label: `${label}(${propertyLabel}: ${newSubLabels}${dotdotdot})`,
                            value
                        };
                    } else {
                        return {
                            label,
                            value
                        };
                    }
                });
                //
                setOptionState(prevState => ({
                    ...prevState,
                    options: [...optionState.value, ...newData],
                    addTimes: prevState.addTimes + 1,
                    isLoading: false
                }));
            })
            .catch(() => {
                // close dropdown Loading
                setOptionState(prevState => ({
                    ...prevState,
                    isLoading: false
                }));
            });
    }, [debInputValue, propRange]);
    //
    return (
        <CreatableSelect
            isMulti
            isClearable
            styles={customStyles}
            isDisabled={editData.isUpdated || editData.isCreated}
            isLoading={optionState.isLoading}
            options={optionState.options}
            value={optionState.value}
            onChange={handleChange}
            onInputChange={handleInputChange}
            onCreateOption={handleCreate}
            components={{
                MenuList,
                MenuListFooter: (
                    <MenuListFooter
                        options={optionState.options}
                        onClick={addOption}
                    />
                )
            }}
            placeholder={
                <FormattedMessage
                    id={"people.Information.dropDown.placeholder"}
                    defaultMessage={"Select..."}
                />
            }
            filterOption={createFilter({ ignoreAccents: false })}
        />
    );
};

export default CustomDropdown;
