// react
import React, { useState, useContext, useEffect } from "react";

// debounce for react
import CustomDebounce from "./CustomDeBounce";

// ui
import { Input } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { injectIntl } from "react-intl";

const CustomSearch = ({ intl }) => {
    // store
    const [state, dispatch] = useContext(StoreContext);
    //
    const [searchValue, setSearchValue] = useState("");
    // 重新包裝一下 searchValue, 當 searchvalue 不再發生變化時
    const debSearchValue = CustomDebounce(searchValue, 500);
    //
    const handleOnClick = () => {
        // dispatch search keyword
        dispatch({
            type: Act.QUERY_SEARCH_KEYWORD_SET,
            payload: searchValue
        });
    };
    //
    const handleChange = (event, { value }) => {
        setSearchValue(value);
    };
    //
    const handleInputKeyPress = event => {
        if (event.key === "Enter") {
            // dispatch search keyword
            dispatch({
                type: Act.QUERY_SEARCH_KEYWORD_SET,
                payload: searchValue
            });
        }
    };
    //
    useEffect(() => {
        handleOnClick();
    }, [debSearchValue]);
    //
    return (
        <Input
            fluid
            action={{
                icon: "search",
                onClick: handleOnClick
            }}
            placeholder={intl.formatMessage({
                id: "query.search",
                defaultMessage: "Search..."
            })}
            onChange={handleChange}
            onKeyPress={handleInputKeyPress}
        />
    );
};

export default injectIntl(CustomSearch);
