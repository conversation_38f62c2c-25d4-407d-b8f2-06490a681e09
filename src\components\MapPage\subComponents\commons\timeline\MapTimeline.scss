*{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.seachInputContainerTimeline {
  .icon, .ui, .input {
    display: none;
    .prompt {
      display: none;
    }
  }
  .results {
    max-height: calc(60vh);
    overflow: auto;
    scrollbar-width: auto;
  }
}

.mapMask{
  width: 100vw;
  height: 100%;
  position: absolute;
  z-index: 3000;
  //background-color: black;
  cursor: default;
}


.timeline{
  display: flex;
  bottom: 0px;
  .showYear{
    width: 10vw;
    font-size: 1.5rem;
    padding: 20px 5px 5px 15px;
    .input{
      position: static;
      float: none;
      width: 80px;
      font-size: 1.2rem;
      line-height: 0;
      input{
        padding: .67857143em 0.4em;
      }
    }
    span{
      display: inline-block;
      margin: 0.5rem 0;
    }
    .message{
      margin: 0;
      font-size: 0.8rem;
    }

  }
  .svgDiv{
    justify-content: space-evenly;
    width: 90vw;
    height: 20vh;
    padding: 1.5vw 0;
    display: flex;
    position: relative;
    a{
      align-self: center;
      width: 3vw;
      height: 3vw;
      font-size: 2.5rem;
      //border: 1px blue solid;
    }
    .canvasDiv{
      width: 95%;
      height: 100%;
      display: flex;
      justify-content: center;
      .canvas{
        height: 100%;
        width: 95%;
        text{
          font-size: 1rem;
        }
        rect{
          cursor: pointer;
        }
      }
    }

    #popup{
      position: absolute;
      z-index: 3000;
      width: 50px;
      height: 20px;
      background-color: lightgray;
      font-size: 0.5rem;
      text-align: center;
      opacity: 0;
      border-radius: 5px;
    }

    .btn{
      width: 5%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
