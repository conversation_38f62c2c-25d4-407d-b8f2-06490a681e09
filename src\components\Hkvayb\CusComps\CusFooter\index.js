import React from "react";

import { FormattedMessage } from "react-intl";

import useStyles from "./style";

const CusFooter = () => {
    const classes = useStyles();
    const scrollTop = () =>
        window.scrollTo({
            top: 0,
            left: 0,
            behavior: "smooth"
        });
    return (
        <div>
            <div className={classes.hkvayb_footer_scroll_top}>
                <div
                    className={classes.hkvayb_footer_scroll_top_buttom}
                    onClick={scrollTop}
                >
                    <div className={classes.hkvayb_footer_scroll_top_arrow} />
                </div>
            </div>
            <div className={classes.hkvayb_footer}>
                <div className={classes.hkvayb_footer_left_div}>
                    <div className={classes.hkvayb_footer_left_logo} />
                </div>
                <div className={classes.hkvayb_footer_copyright}>
                    <FormattedMessage
                        id="hkvayb.search.copyright"
                        defaultMessage="© 2022 All Rights Reserved. The Chinese University of Hong Kong Library"
                    />
                </div>
                <div className={classes.hkvayb_footer_right_logo} />
            </div>
        </div>
    );
};

export default CusFooter;
