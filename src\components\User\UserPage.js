import React, { useContext, useEffect } from "react";
// import { HomePageLayout } from "./HomePageLayout";
import User from "./User";
import { ResponsiveContainer } from "../../layout/Layout";
// store
import { StoreContext } from "../../store/StoreProvider";

const UserPage = ({ ...props }) => {
    const { location } = props;
    const [_, dispatch] = useContext(StoreContext);

    return (
        <ResponsiveContainer {...props}>
            <User />
        </ResponsiveContainer>
    );
};

export default UserPage;
