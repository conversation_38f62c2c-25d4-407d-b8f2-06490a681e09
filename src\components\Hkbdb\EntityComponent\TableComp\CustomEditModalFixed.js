import React, { Fragment, useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";

// custom
import CustomMenu from "./CustomMenuFixedEdit";
import CustomAlertMessage from "./CustomAlertMessage";

import { FormattedMessage } from "react-intl";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// common
import { safeGet, isEmpty } from "../../../../common/codes";
import { _reformatData } from "../commonAction";

// api
import {
    Api,
    createHkbdbData,
    doRestCreate,
    updateHkbdbData
} from "../../../../api/hkbdb/Api";
import Act from "../../../../store/actions";
import base64url from "base64url";
import axios from "axios";
import { HAS_COORDS_TYPE } from "../constants";

const CustomEditModalFixed = ({
    open,
    setOpen,
    editData,
    setEditData,
    ontologyDomain,
    ontologyType
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const [isLoading, setIsLoading] = useState(() => false);
    const [updateResults, setUpdateResults] = useState(() => ({
        updatedRowIds: [],
        success: 0,
        failed: 0
    }));
    const [alertMsg, setAlertMsg] = useState(() => ({
        title: "",
        content: "",
        type: "",
        renderSignal: "",
        ttl: 3 * 1000
    }));

    const handleInitCreatedData = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            createdData: {}
        }));
    };
    const handleInitChangedData = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            changedData: {}
        }));
    };
    const handleInitIsUpdated = () => {
        // init changedData
        setEditData(prevEditData => ({
            ...prevEditData,
            isUpdated: false,
            updatedRowIds: []
        }));
    };
    const handleInitResult = () => {
        if (updateResults.success) {
            // 通知 API 重新拉取
            dispatch({
                type: Act.INFORMATION_DATA_RENDER_SIGNAL_SET,
                payload: {
                    target: ontologyType,
                    signal: `updated-${new Date().getTime()}`
                }
            });
            // 通知編輯後是否顯示 isLoading(這是區域重新載入，不是整個頁面，e.g. information's publication)
            dispatch({
                type: Act.INFORMATION_DATA_IS_LOADING_SET,
                payload: true
            });
        }
    };
    //
    const handleClose = () => {
        // init status
        handleInitCreatedData();
        handleInitChangedData();
        handleInitIsUpdated();
        handleInitResult();
        // close modal
        setOpen(false);
    };
    const handleOpen = () => {
        // open modal
        setOpen(true);
    };

    const createLocationLabel = async (data, locType) => {
        if (isEmpty(data) || !locType)
            return { state: false, error: "缺少資料" };

        try {
            const { graph, values } = data;
            const graphStr = Array.isArray(graph) ? graph[0] : graph;
            const encodedGraph = base64url.encode(graphStr);

            const graphLocations = await axios.get(
                locType === "Place"
                    ? Api.getGraphPlace(encodedGraph)
                    : Api.getGraphOrganization(encodedGraph)
            );

            const locationList = graphLocations.data.data;
            const locationMap = new Map(
                locationList.map(item => [item.id, item.label])
            );

            const createResults = [];

            for (const { label, value } of values) {
                const hasId = locationMap.has(value);
                const labelValue = locationMap.get(value);
                const needToCreate =
                    !hasId || !labelValue || !labelValue.trim();

                if (needToCreate) {
                    const location = value.replace(/^(ORG|PLA)/, "");
                    const srcId = `${
                        locType === "Place" ? "PLA" : "ORG"
                    }${base64url.encode(location)}`;

                    const createEntry = {
                        graph,
                        srcId,
                        classType: locType,
                        value: {
                            [locType === "Place"
                                ? "label_Place"
                                : "bestKnownName"]: [label]
                        }
                    };

                    const res = await createHkbdbData(
                        Api.restfulHKBDB(),
                        createEntry
                    );

                    createResults.push(res?.state === true);
                }
            }

            return { state: createResults.every(Boolean) };
        } catch (e) {
            console.log("createLocationLabel error:: ", e);
            return { state: false, error: e.message };
        }
    };

    const handleUpdate = async () => {
        setIsLoading(true);

        if (!isEmpty(editData.changedData)) {
            const editDataRowIds = Object.keys(
                editData.changedData
            ).map(rowIdx => parseInt(rowIdx, 10));

            const tasks = [];

            for (const rowId of editDataRowIds) {
                const entrySrc = _reformatData(
                    editData.rowData[rowId],
                    ontologyType
                );
                const entryDst = _reformatData(
                    editData.changedData[rowId],
                    ontologyType
                );

                // Place 或 Organization 有新增，另外建label
                const [, propRange] = editData.changedData[
                    rowId
                ].propertyBindRangeStr.split("__");

                if (HAS_COORDS_TYPE.includes(propRange)) {
                    tasks.push(
                        createLocationLabel(
                            editData.changedData[rowId],
                            propRange
                        )
                    );
                }

                tasks.push(
                    updateHkbdbData(Api.restfulHKBDB(), entrySrc, entryDst)
                );
            }

            const results = await Promise.allSettled(tasks);

            results.forEach((res, idx) => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的
                    updateResults.updatedRowIds.push(editDataRowIds[idx]);
                    // 紀錄成功資料筆數
                    updateResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    updateResults.failed++;
                }
            });
            //
            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: "更新",
                type: "success",
                content: `已更新: ${updateResults.success}, 已失敗: ${updateResults.failed}`,
                renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
            }));
            // 用來在 ui 顯示那些資料更新成功
            setEditData(prevEditData => ({
                ...prevEditData,
                isUpdated: true,
                updatedRowIds: [...updateResults.updatedRowIds]
            }));
        }

        if (!isEmpty(editData.createdData)) {
            const editDataRowIds = Object.keys(
                editData.createdData
            ).map(rowIdx => parseInt(rowIdx, 10));

            const promises = editDataRowIds.map(rowId => {
                let entry = _reformatData(
                    editData.createdData[rowId],
                    ontologyType
                );

                if (ontologyType === "relationevent") {
                    const perBKey = safeGet(
                        editData,
                        ["rowData", rowId, "propertyBindRangeStr"],
                        ""
                    );
                    const perBvalue = safeGet(
                        editData,
                        ["rowData", rowId, "values"],
                        ""
                    );

                    // 20230606: relationevent 一欄只會有一個人名
                    entry = {
                        ...entry,
                        value: {
                            ...entry.value,
                            [perBKey]: perBvalue[0].value
                        }
                    };
                }

                return doRestCreate(user, entry);
            });
            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);

            results.forEach((res, idx) => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && value.state) {
                    // ps. 這裡的 idx 只是迴圈的順序跟要被更新資料的 rowId 是不同的
                    updateResults.updatedRowIds.push(editDataRowIds[idx]);
                    // 紀錄成功資料筆數
                    updateResults.success++;
                } else {
                    // 紀錄失敗資料筆數
                    updateResults.failed++;
                }
            });

            setAlertMsg(prevMsg => ({
                ...prevMsg,
                title: "更新",
                type: "success",
                content: `已更新: ${updateResults.success}, 已失敗: ${updateResults.failed}`,
                renderSignal: new Date().getTime() // 時間只是方便用來觸發更新而已
            }));
            // 用來在 ui 顯示那些資料更新成功
            setEditData(prevEditData => ({
                ...prevEditData,
                isUpdated: true,
                updatedRowIds: [...updateResults.updatedRowIds]
            }));
        }

        setIsLoading(false);
        // init status
        handleInitChangedData();
        // close modal
        // handleClose();
    };
    const handleCancel = () => {
        // init status
        handleInitChangedData();
        handleInitCreatedData();
        // open modal
        setOpen(false);
    };

    const SwitchButton = () => {
        if (editData.isUpdated && isEmpty(editData.changedData)) {
            return (
                <Button onClick={handleClose} color="green">
                    <FormattedMessage
                        id={"people.Information.button.close"}
                        defaultMessage={"Close"}
                    />
                </Button>
            );
        } else {
            return (
                <Fragment>
                    <Button
                        loading={isLoading}
                        disabled={
                            isEmpty(editData.changedData) &&
                            isEmpty(editData.createdData)
                        }
                        onClick={handleUpdate}
                        color="green"
                    >
                        <FormattedMessage
                            id={"people.Information.button.update"}
                            defaultMessage={"Update"}
                        />
                    </Button>
                    <Button onClick={handleCancel} color="red">
                        <FormattedMessage
                            id={"people.Information.button.cancel"}
                            defaultMessage={"Cancel"}
                        />
                    </Button>
                </Fragment>
            );
        }
    };

    const modalContentStyle = {
        width: "100%"
    };

    return (
        <Modal open={open} onClose={handleClose} onOpen={handleOpen}>
            {/* <pre>{JSON.stringify(state.information, null, 2)}</pre> */}
            {/* <pre>{JSON.stringify(editData, null, 2)}</pre> */}
            <Modal.Header>
                <FormattedMessage
                    id={"people.Information.header.edit.content"}
                    defaultMessage={"Edit Content"}
                />
            </Modal.Header>
            <Modal.Content image>
                <Modal.Description style={modalContentStyle}>
                    {/* alert */}
                    <CustomAlertMessage
                        alertMsg={alertMsg}
                        setAlertMsg={setAlertMsg}
                    />
                    {/* content */}
                    <CustomMenu
                        editData={editData}
                        setEditData={setEditData}
                        ontologyDomain={ontologyDomain}
                        ontologyType={ontologyType}
                    />
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <SwitchButton />
            </Modal.Actions>
        </Modal>
    );
};

export default CustomEditModalFixed;
