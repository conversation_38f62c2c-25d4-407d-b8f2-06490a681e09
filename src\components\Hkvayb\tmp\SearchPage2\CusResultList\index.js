import React, { Fragment, useContext, useState, useEffect } from "react";

import { makeStyles } from "@material-ui/core/styles";

import {
    Avatar,
    List,
    ListItem,
    ListItemText,
    ListItemAvatar
} from "@material-ui/core";

import ImageIcon from "@material-ui/icons/Image";

import CusLoading from "./CusLoading";
import CusDuration from "./CusDuration";

import { useHistory } from "react-router-dom";

import { isEmpty, safeGet } from "../../../../../common/codes";

import { StoreContext } from "../../../../../store/StoreProvider";

const useStyles = makeStyles(theme => ({
    root: {
        width: "100%",
        backgroundColor: theme.palette.background.paper
    }
}));

const CusResultList = () => {
    const classes = useStyles();
    const history = useHistory();
    const [state] = useContext(StoreContext);
    const {
        result,
        isLoading,
        error,
        currentPage,
        rowsPerPage
    } = state.searchPage2;
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const [data, setData] = useState([]);

    const handleListItemClick = (event, index, id) => {
        setSelectedIndex(index);
        history.push(`/zh-hans/hkvayb/detail/${id}`);
    };

    useEffect(() => {
        setData(
            Object.keys(result).reduce((prevObj, type, typeIdx) => {
                return [
                    ...prevObj,
                    ...safeGet(result, [type], []).map(item => ({
                        id:
                            safeGet(item, ["classId"], "") ||
                            safeGet(item, ["eventId"], ""),
                        title:
                            safeGet(item, ["title_zh"], "") ||
                            safeGet(item, ["title_en"], ""),
                        date: safeGet(item, ["year"], ""),
                        type
                    }))
                ];
            }, [])
        );
    }, [result]);

    if (isLoading) {
        return <CusLoading />;
    }

    // todo error alert
    if (error && error.type && error.message) {
        return <div>{JSON.stringify(error)}</div>;
    }

    if (isEmpty(data)) {
        return (
            <List>
                <ListItem>
                    <div style={{ fontSize: "1rem" }}>
                        <p>
                            《香港視覺藝術年鑑》於2000年初次出版，中英文雙語印行，圖文並茂，為迄今最全面的香港視覺藝術編年工具書。
                        </p>
                        <p>
                            《年鑑》由香港中文大學藝術系出版，分為專題論述和視藝記事兩大部份，編集全年本地藝術家於本地及海外的視藝活動，並結合學者專家的分析論文。一為宏觀主觀的觀察陳述，一為微觀客觀的數據縷述，兩者互相依存，互為補充。
                        </p>
                    </div>
                </ListItem>
            </List>
        );
    }

    return (
        <Fragment>
            <CusDuration />
            <List className={classes.root}>
                {data &&
                    data
                        .slice(
                            currentPage * rowsPerPage,
                            currentPage * rowsPerPage + rowsPerPage
                        )
                        .map((item, itemIdx) => {
                            return (
                                <ListItem
                                    key={itemIdx}
                                    button
                                    selected={selectedIndex === itemIdx}
                                    onClick={event =>
                                        handleListItemClick(
                                            event,
                                            itemIdx,
                                            item.id
                                        )
                                    }
                                >
                                    <ListItemAvatar>
                                        <Avatar>
                                            <ImageIcon />
                                        </Avatar>
                                    </ListItemAvatar>
                                    <ListItemText
                                        primary={item.title}
                                        secondary={item.date}
                                    />
                                </ListItem>
                            );
                        })}
            </List>
        </Fragment>
    );
};

export default CusResultList;
