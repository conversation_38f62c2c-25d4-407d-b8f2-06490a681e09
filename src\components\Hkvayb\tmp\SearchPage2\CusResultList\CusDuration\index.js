import React, { useContext } from "react";

import { List, ListItem } from "@material-ui/core";

import { StoreContext } from "../../../../../../store/StoreProvider";

const CusDuration = () => {
    const [state] = useContext(StoreContext);
    const { count, duration } = state.searchPage2;

    if (count === 0) {
        return null;
    }

    return (
        <List>
            <ListItem>
                <div style={{ fontSize: "1rem" }}>
                    <p>
                        總共有 {count} 筆資料 ({duration} 秒)
                    </p>
                </div>
            </ListItem>
        </List>
    );
};

export default CusDuration;
